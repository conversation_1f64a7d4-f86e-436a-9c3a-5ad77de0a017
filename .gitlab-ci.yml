sonarqube-check:
  image: maven:3.6.3-jdk-8-slim
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - mvn verify sonar:sonar -Dmaven.test.skip=true
  allow_failure: false
  only:
    - merge_requests
    - master
    - dev
    - int