package com.gwmfc.constant;

/**
 * <AUTHOR>
 * @Classname downLoadTemplateTypeEnum
 * @Description 下载模板类型
 * @Date 2024/4/17 10:54
 */
public enum DownLoadTemplateTypeEnum {
    /**
     * IRR批量测算数据导入模板-本品
     */
    BP_IRR_BATCH_CAL_IMPORT_TEMPLATE("bpIrrBatch","com.gwmfc.dto.IrrBatchCalTemplateDto"),

    /**
     * IRR批量测算数据导入模板-全品
     */
    QP_IRR_BATCH_CAL_IMPORT_TEMPLATE("qpIrrBatch","com.gwmfc.dto.IrrBatchCalTemplateDto"),

    /**
     * IRR批量测算数据导入模板-二手车
     */
    ESC_IRR_BATCH_CAL_IMPORT_TEMPLATE("escIrrBatch","com.gwmfc.dto.IrrBatchCalTemplateDto"),

    /**
     * 提案测算数据导入模板
     */
    PROPOSAL_CAL_IMPORT_TEMPLATE("proposalCal","com.gwmfc.dto.ProposalCalTemplateDto"),
    /**
     * 利润测算销量数据导入模板
     */
    PROFIT_CAL_SALES_IMPORT_TEMPLATE("profitCalSales","com.gwmfc.dto.ProfitCalSalesTemplateDto"),
    /**
     * 提前结清测算数据导入模板
     */
    EARLY_SQUARE_RATIO_IMPORT_TEMPLATE("earlySquareRatio","com.gwmfc.dto.EarlySquareRatioTemplateDto");

    private final String type;

    private final String className;

    DownLoadTemplateTypeEnum(String type, String className) {
        this.type = type;
        this.className = className;
    }

    public String type() {
        return type;
    }

    public String className() {
        return className;
    }

    public static DownLoadTemplateTypeEnum getDownLoadTemplateTypeEnumByType(String type) {
        for (DownLoadTemplateTypeEnum downLoadTemplateTypeEnum : DownLoadTemplateTypeEnum.values()) {
            if (downLoadTemplateTypeEnum.type().equals(type)) {
                return downLoadTemplateTypeEnum;
            }
        }
        return null;
    }


}
