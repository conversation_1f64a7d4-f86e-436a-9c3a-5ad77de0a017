package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.gwmfc.constant.ProductProposalApproveEnum;
import com.gwmfc.dao.GrossDomesticProductDao;
import com.gwmfc.dao.GrossDomesticProductYearDao;
import com.gwmfc.entity.data.GrossDomesticProductEntity;
import com.gwmfc.entity.data.GrossDomesticProductYearEntity;
import com.gwmfc.entity.data.YearConsumptionIndexEntity;
import com.gwmfc.util.GsonUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2024年03月07日 17:46
 */
@Service
public class GrossDomesticProductService {
    @Resource
    private GrossDomesticProductDao grossDomesticProductDao;

    @Resource
    private GrossDomesticProductYearDao grossDomesticProductYearDao;

    @Resource
    private RestTemplate restTemplate;

    public void catchGrossDomesticPriceIndexYear(Integer frequency, String userName) {
        JsonArray jsonArray0 = new JsonArray();
        JsonObject jsonObject0 = new JsonObject();
        jsonObject0.addProperty("wdcode","zb");
        jsonObject0.addProperty("valuecode","A020201");
        JsonObject jsonObject1 = new JsonObject();
        jsonObject1.addProperty("wdcode","sj");
        jsonObject1.addProperty("valuecode","LAST"+frequency);
        jsonArray0.add(jsonObject0);
        jsonArray0.add(jsonObject1);
        String json0 = jsonArray0.toString();

        // 添加键值对
        List<String> property = new ArrayList<>();

        property.add("setGrossNationalIncomeIndex");
        property.add("setGrossDomesticProductDeflator");
        property.add("setValueAddedIndexOfPrimaryIndustry");
        property.add("setValueAddedIndexOfSecondaryIndustry");
        property.add("setValueAddedIndexOfTertiaryIndustry");
        property.add("setGdpPerCapitaIndex");

        List<HttpMessageConverter<?>> messageConverters0 = restTemplate.getMessageConverters();

        for (int i = 0; i < messageConverters0.size(); i++) {
            HttpMessageConverter<?> httpMessageConverter = messageConverters0.get(i);
            if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
                messageConverters0.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
            }
        }
        ResponseEntity<String> strbody0=restTemplate.getForEntity("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgnd&rowcode=zb&colcode=sj&wds=[]&dfwds={dfwds}&k1="+System.currentTimeMillis()+"&h=1",String.class,json0);
        ABo aBo0 = GsonUtil.gsonToBean(strbody0.getBody(),ABo.class);
        List<Datanode> datanodeList0 = aBo0.getReturndata().getDatanodes();
        Integer monthNum = aBo0.getReturndata().getWdnodes().get(1).getNodes().size();
        ArrayList resArrayList = new ArrayList<GrossDomesticProductYearEntity>(monthNum);
        for (int i=0;i<monthNum;i++) {
            resArrayList.add(new GrossDomesticProductYearEntity());
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        // 集合分片
        List<List<Datanode>> newList = Lists.partition(datanodeList0, monthNum);
        // 打印分片集合
        AtomicInteger j = new AtomicInteger();
        newList.forEach(objectList -> {
            for (int i=0;i<objectList.size();i++) {
                GrossDomesticProductYearEntity grossDomesticProductEntity = (GrossDomesticProductYearEntity) resArrayList.get(i);
                // 获取Person类的Class对象
                Class<?> clazz = grossDomesticProductEntity.getClass();
                Method setNameMethod = null;
                Method setNameMethod1 = null;
                Method setNameMethod2 = null;
                Method setNameMethod3 = null;
                try {
                    setNameMethod = clazz.getMethod(property.get(j.get()), String.class);
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
                Datanode datanode = objectList.get(i);
                String dateStr = datanode.getWds().get(1).getValuecode();
                dateStr = dateStr.substring(0, 4);
                //判断该月在不在
                if (grossDomesticProductYearDao.selectDataDateExist(dateStr)!=null) {
                    continue;
                }
                try {
                    if (grossDomesticProductEntity.getDataDate() != null) {
                        String compareDateStr = datanode.getWds().get(1).getValuecode();
                        compareDateStr = compareDateStr.substring(0, 4);
                        if (compareDateStr.equals(grossDomesticProductEntity.getDataDate())) {
                            // 调用setName方法设置name属性的值
                            DecimalFormat df = new DecimalFormat("#.0");
                            String formattedNumber = df.format(Double.parseDouble(datanode.getData().getData()));
                            setNameMethod.invoke(grossDomesticProductEntity, formattedNumber);
                        }
                    }
                    if (grossDomesticProductEntity.getDataDate() == null) {
                        setNameMethod1 = clazz.getMethod("setDataDate", String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod1.invoke(grossDomesticProductEntity, dateStr);
                        setNameMethod2 = clazz.getMethod("setCreateUser", String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod2.invoke(grossDomesticProductEntity, userName);
                        setNameMethod3 = clazz.getMethod("setCreateTime", Long.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod3.invoke(grossDomesticProductEntity, Long.parseLong(dateTimeFormatter.format(LocalDateTime.now())));
                        setNameMethod = clazz.getMethod(property.get(j.get()), String.class);
                        // 调用setName方法设置name属性的值
                        // 调用setName方法设置name属性的值
                        DecimalFormat df = new DecimalFormat("#.0");
                        String formattedNumber = df.format(Double.parseDouble(datanode.getData().getData()));
                        setNameMethod.invoke(grossDomesticProductEntity, formattedNumber);
                    }
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }

            }
            j.getAndIncrement();
        });
        resArrayList.forEach(grossDomesticProductEntity -> {
            GrossDomesticProductYearEntity grossDomesticProductEntityTmp = (GrossDomesticProductYearEntity) grossDomesticProductEntity;
            if (StringUtils.isNotEmpty(grossDomesticProductEntityTmp.getDataDate())) {
                grossDomesticProductYearDao.insert((GrossDomesticProductYearEntity) grossDomesticProductEntity);
            }
        });
    }

    public void catchGrossDomesticProductQuarter(Integer frequency, String userName) {
        JsonArray jsonArray0 = new JsonArray();
        JsonObject jsonObject0 = new JsonObject();
        jsonObject0.addProperty("wdcode","zb");
        jsonObject0.addProperty("valuecode","A0101");
        JsonObject jsonObject1 = new JsonObject();
        jsonObject1.addProperty("wdcode","sj");
        jsonObject1.addProperty("valuecode","LAST"+frequency);
        jsonArray0.add(jsonObject0);
        jsonArray0.add(jsonObject1);
        String json0 = jsonArray0.toString();

        // 添加键值对
        List<String> property = new ArrayList<>();

        property.add("setQuarterlyGrossDomesticProduct");
        property.add("setAccumulatedValueOfGrossDomesticProduct");
        property.add("setCurrentQuarterlyValueOfAddedValueOfPrimaryIndustry");
        property.add("setAccumulatedValueOfAddedValueOfPrimaryIndustry");
        property.add("setQuarterlyValueOfAddedValueOfSecondaryIndustry");
        property.add("setAccumulatedValueOfAddedValueOfSecondaryIndustry");

        property.add("setQuarterlyValueOfAddedValueOfTertiaryIndustry");
        property.add("setAccumulatedValueOfAddedValueOfTertiaryIndustry");

        property.add("setQuarterlyValueOfAddedValueOfAgriculture");
        property.add("setAccumulatedValueOfAddedValueOfAgriculture");

        property.add("setQuarterlyValueOfIndustrialAddedValue");
        property.add("setAccumulatedValueOfIndustrialAddedValue");

        property.add("setQuarterlyValueOfManufacturingAddedValue");
        property.add("setAccumulatedValueOfManufacturingAddedValue");

        property.add("setQuarterlyValueOfConstructionAddedValue");
        property.add("setAccumulatedValueOfConstructionAddedValue");

        property.add("setWholesaleRetailAddedValueForQuarter");
        property.add("setCumulativeValueAddedInWholesaleRetailTrade");

        property.add("setQuarterlyValueAddedOfTransportationStoragePostal");
        property.add("setAccumulatedValueAddedOfTransportationStoragePostal");

        property.add("setQuarterlyValueOfHotelAndCateringIndustryAddedValue");
        property.add("setAccumulatedValueOfHotelAndCateringIndustryAddedValue");

        property.add("setQuarterlyValueOfFinancialIndustryAddedValue");
        property.add("setAccumulatedValueOfFinancialIndustryAddedValue");

        property.add("setQuarterlyValueOfRealEstateAddedValue");
        property.add("setAccumulatedValueOfRealEstateAddedValue");

        property.add("setQuarterlyValueOfInformationTechnologyAddedValue");
        property.add("setAccumulatedValueOfInformationTechnologyAddedValue");

        property.add("setQuarterlyValueOfLeasingAndBusinessServiceAddedValue");
        property.add("setAccumulatedValueOfLeasingAndBusinessServiceAddedValue");

        property.add("setQuarterlyValueOfOtherIndustryAddedValue");
        property.add("setAccumulatedValueOfOtherIndustryAddedValue");

        List<HttpMessageConverter<?>> messageConverters0 = restTemplate.getMessageConverters();

        for (int i = 0; i < messageConverters0.size(); i++) {
            HttpMessageConverter<?> httpMessageConverter = messageConverters0.get(i);
            if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
                messageConverters0.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
            }
        }
        ResponseEntity<String> strbody0=restTemplate.getForEntity("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgjd&rowcode=zb&colcode=sj&wds=[]&dfwds={dfwds}&k1="+System.currentTimeMillis()+"&h=1",String.class,json0);
        ABo aBo0 = GsonUtil.gsonToBean(strbody0.getBody(),ABo.class);
        List<Datanode> datanodeList0 = aBo0.getReturndata().getDatanodes();
        Integer monthNum = aBo0.getReturndata().getWdnodes().get(1).getNodes().size();
        ArrayList resArrayList = new ArrayList<GrossDomesticProductEntity>(monthNum);
        for (int i=0;i<monthNum;i++) {
            resArrayList.add(new GrossDomesticProductEntity());
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        // 集合分片
        List<List<Datanode>> newList = Lists.partition(datanodeList0, monthNum);
        // 打印分片集合
        AtomicInteger j = new AtomicInteger();
        newList.forEach(objectList -> {
            for (int i=0;i<objectList.size();i++) {
                GrossDomesticProductEntity grossDomesticProductEntity = (GrossDomesticProductEntity) resArrayList.get(i);
                // 获取Person类的Class对象
                Class<?> clazz = grossDomesticProductEntity.getClass();
                Method setNameMethod = null;
                Method setNameMethod1 = null;
                Method setNameMethod2 = null;
                Method setNameMethod3 = null;
                try {
                    setNameMethod = clazz.getMethod(property.get(j.get()), String.class);
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
                Datanode datanode = objectList.get(i);
                String dateStr = datanode.getWds().get(1).getValuecode();
                dateStr = dateStr.substring(0, 4) + "-" + changeMonth(dateStr.substring(4));
                //判断该月在不在
                if (grossDomesticProductDao.selectDataDateExist(dateStr)!=null) {
                    continue;
                }
                try {
                    if (grossDomesticProductEntity.getDataDate() != null) {
                        String compareDateStr = datanode.getWds().get(1).getValuecode();
                        compareDateStr = compareDateStr.substring(0, 4) + "-" + changeMonth(compareDateStr.substring(4));
                        if (compareDateStr.equals(grossDomesticProductEntity.getDataDate())) {
                            // 调用setName方法设置name属性的值
                            setNameMethod.invoke(grossDomesticProductEntity, datanode.getData().getData());
                        }
                    }
                    if (grossDomesticProductEntity.getDataDate() == null) {
                        setNameMethod1 = clazz.getMethod("setDataDate", String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod1.invoke(grossDomesticProductEntity, dateStr);
                        setNameMethod2 = clazz.getMethod("setCreateUser", String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod2.invoke(grossDomesticProductEntity, userName);
                        setNameMethod3 = clazz.getMethod("setCreateTime", Long.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod3.invoke(grossDomesticProductEntity, Long.parseLong(dateTimeFormatter.format(LocalDateTime.now())));
                        setNameMethod = clazz.getMethod(property.get(j.get()), String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod.invoke(grossDomesticProductEntity, datanode.getData().getData());
                    }
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }

            }
            j.getAndIncrement();
        });
        resArrayList.forEach(grossDomesticProductEntity -> {
            GrossDomesticProductEntity grossDomesticProductEntityTmp = (GrossDomesticProductEntity) grossDomesticProductEntity;
            if (StringUtils.isNotEmpty(grossDomesticProductEntityTmp.getDataDate())) {
                grossDomesticProductDao.insert((GrossDomesticProductEntity) grossDomesticProductEntity);
            }
        });
    }

    private final static String A = "A";
    private final static String B = "B";
    private final static String C = "C";
    private final static String D = "D";
    private String changeMonth(String month) {
        switch (month) {
            case A: return "03";
            case B: return "06";
            case C: return "09";
            case D: return "12";
        }
        return null;
    }
}
