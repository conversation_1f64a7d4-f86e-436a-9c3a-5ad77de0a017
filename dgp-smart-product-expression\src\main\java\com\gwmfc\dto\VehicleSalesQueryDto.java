package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Date: 2023/11/9
 * @Author: z<PERSON><PERSON>yu
 */

@Data
@ApiModel(description = "商用车数据")
public class VehicleSalesQueryDto {

    @ApiModelProperty(value = "编号")
    private Long id;

    @ApiModelProperty(value = "类型(1:商用车销量,2:卡车销量,3:新能源销量,4:客车厂商销量,5:卡车厂商销量)")
    private Integer type;

    @ApiModelProperty(value = "更新月份")
    private String updateMonth;

    @ApiModelProperty(value = "商用车类型")
    private String commercialVehicleType;

    @ApiModelProperty(value = "销量")
    private String sales;

    @ApiModelProperty(value = "图片地址")
    private String imageUrl;

    @ApiModelProperty(value = "排名")
    private String salesRank;

    @ApiModelProperty(value = "渗透率")
    private String penetrationRate;

    @ApiModelProperty("数据开始日期(查询起始时间)")
    private String startDate;

    @ApiModelProperty("数据开始日期(查询结束时间)")
    private String endDate;
}
