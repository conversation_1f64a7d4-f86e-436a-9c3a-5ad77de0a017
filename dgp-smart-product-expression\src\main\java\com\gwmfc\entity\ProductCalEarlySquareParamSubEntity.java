package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductCalEarlySquareParamSubEntity
 * @Description TODO
 * @Date 2024/7/3 15:23
 */
@Data
@TableName("product_cal_early_square_param_sub")
public class ProductCalEarlySquareParamSubEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "basic_info_id", comment = "基本参数id")
    private Long basicInfoId;

    @TableFieldMapping(value = "product_classification", comment = "产品分类")
    private String productClassification;

    @TableFieldMapping(value = "financing_type", comment = "融资类型")
    private String financingType;

    @TableFieldMapping(value = "early_square_probability_classification", comment = "提前结清概率分类")
    private String earlySquareProbabilityClassification;

    @TableFieldMapping(value = "agent_type", comment = "代理商类型")
    private String agentType;

    @TableFieldMapping(value = "track", comment = "赛道")
    private String track;

    @TableFieldMapping(value = "is_corp_to_corp", comment = "是否总对总")
    private Integer isCorpToCorp;

    @TableFieldMapping(value = "probability_pam", comment = "概率参数")
    private String probabilityPam;

    @TableFieldMapping(value = "hand_charge_and_commission_rebate_pam", comment = "手续费和扣返比例参数")
    private String handChargeAndCommissionRebatePam;

    @TableFieldMapping(value = "basic_commission_rebate_ratio_rule", comment = "基础佣金扣返比例规则")
    private String basicCommissionRebateRatioRule;

    @TableFieldMapping(value = "early_square_hand_charge_ratio_rule", comment = "提前结清手续费比例规则")
    private String earlySquareHandChargeRatioRule;

    @TableFieldMapping(value = "early_square_probability", comment = "提前结清概率")
    private String earlySquareProbability;

    @TableFieldMapping(value = "early_square_basic_commission_rebate_ratio", comment = "提前结清基础佣金扣返比例")
    private String earlySquareBasicCommissionRebateRatio;

    @TableFieldMapping(value = "early_square_hand_charge_ratio", comment = "提前结清手续费比例")
    private String earlySquareHandChargeRatio;

    @TableFieldMapping(value = "create_time", comment = "创建时间")
    private Date createTime;

    @TableFieldMapping(value = "create_user", comment = "创建用户")
    private String createUser;

    @TableFieldMapping(value = "update_time", comment = "更新时间")
    private Date updateTime;

    @TableFieldMapping(value = "update_user", comment = "更新用户")
    private String updateUser;
}
