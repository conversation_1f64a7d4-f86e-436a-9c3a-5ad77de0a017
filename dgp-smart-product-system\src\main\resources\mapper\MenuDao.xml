<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.gwmfc.dao.MenuDao">
    <select id="listMenuByUserId" resultType="com.gwmfc.entity.MenuDO">
        select distinct
		m.menu_id , parent_id, name, url,
		perms,`type`,icon,order_num,gmt_create, gmt_modified, route_name, external_link
		from sys_menu m
		left
		join sys_role_menu rm on m.menu_id = rm.menu_id left join
		sys_user_role ur
		on rm.role_id =ur.role_id where ur.user_id = #{userId}
		and
		m.type in(0,1,2)
		order by
		m.order_num
    </select>

	<delete id="deleteRoleMenuByMenuId" >
		delete from sys_role_menu where menu_id=#{menuId}
	</delete>

	<select id="listMenuIdByRoleId" resultType="java.lang.Long">
		select m.menu_id
		from sys_menu m where m.menu_id in
		(select menu_id from sys_role_menu where role_id = #{roleId} )
	</select>
</mapper>