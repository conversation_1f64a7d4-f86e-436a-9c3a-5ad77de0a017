package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.dao.UsedCarWeeklyTradingDao;
import com.gwmfc.dto.UsedCarQueryDto;
import com.gwmfc.entity.data.UsedCarWeeklyTradingEntity;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.util.StatusCodeEnum;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-10-31 09:57
 */
@Slf4j
@Service
public class UsedCarWeeklyTradingService extends ServiceImpl<UsedCarWeeklyTradingDao, UsedCarWeeklyTradingEntity> {

    /**
     * 分页查询
     */
    public IPage<UsedCarWeeklyTradingEntity> page(UsedCarQueryDto query, Integer current, Integer size) {
        IPage<UsedCarWeeklyTradingEntity> page = new Page<>(current, size);
        QueryWrapper<UsedCarWeeklyTradingEntity> wrapper = new QueryWrapper<>(new UsedCarWeeklyTradingEntity());
        wrapper.orderByDesc("start_date").orderByDesc("id");
        if (StringUtils.isNotEmpty(query.getStartDate()) && StringUtils.isNotEmpty(query.getEndDate())) {
            wrapper.between("start_date", query.getStartDate(), query.getEndDate());
        } else if (StringUtils.isNotEmpty(query.getStartDate())) {
            wrapper.ge("start_date", query.getStartDate());
        } else if (StringUtils.isNotEmpty(query.getEndDate())) {
            wrapper.le("start_date", query.getEndDate());
        }
        wrapper.eq("type", query.getType());
        return baseMapper.selectPage(page, wrapper);
    }

    /**
     * 批量保存
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<UsedCarWeeklyTradingEntity> list) {
        log.info("batchSave: UsedCarWeeklyTradingEntity.size:{}", list.size());
        if (!CollectionUtils.isEmpty(list)) {
            return this.saveBatch(list);
        } else {
            throw new SystemRuntimeException(StatusCodeEnum.SUCCESS.getCode(), "无新增数据");
        }
    }

    /**
     * 判断是否有重复数据
     */
    public Boolean dataExist(UsedCarWeeklyTradingEntity usedCarWeeklyTradingEntity) {
        UsedCarWeeklyTradingEntity weeklyTradingEntity = new UsedCarWeeklyTradingEntity();
        weeklyTradingEntity.setStartDate(usedCarWeeklyTradingEntity.getStartDate());
        weeklyTradingEntity.setEndDate(usedCarWeeklyTradingEntity.getEndDate());
        weeklyTradingEntity.setType(usedCarWeeklyTradingEntity.getType());
        weeklyTradingEntity.setRegion(usedCarWeeklyTradingEntity.getRegion());
        weeklyTradingEntity.setAvgDailyTrading(usedCarWeeklyTradingEntity.getAvgDailyTrading());
        QueryWrapper<UsedCarWeeklyTradingEntity> wrapper = new QueryWrapper<>(weeklyTradingEntity);
        if (baseMapper.selectCount(wrapper) > 0) {
            return false;
        }
        return true;
    }

    /**
     * 查询单个数据
     */
    public UsedCarWeeklyTradingEntity getOne(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 更新
     */
    public Integer update(UsedCarWeeklyTradingEntity usedCarWeeklyTrading) {
        return baseMapper.updateById(usedCarWeeklyTrading);
    }


    /**
     * 删除
     *
     * @param id
     */
    public Integer remove(Long id) {
        return baseMapper.deleteById(id);
    }
}
