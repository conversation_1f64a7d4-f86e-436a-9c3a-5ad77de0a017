package com.gwmfc.dto;

import com.gwmfc.entity.ProductActuarialBpCalPlanAssembleDimensionDetailEntity;
import com.gwmfc.entity.ProductActuarialBpCalPlanIndexEntity;
import com.gwmfc.entity.ProductActuarialBpCalPlanMainEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductActuarialBpCalPlanAssembleDimensionDetailDto
 * @Description TODO
 * @Date 2025/4/25 13:37
 */
@Data
@ApiModel(value = "政策测算dto")
public class ProductActuarialBpCalPlanAssembleDimensionDetailDto {
    private ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity;
    private ProductActuarialBpCalPlanIndexEntity productActuarialBpCalPlanIndexEntity;
    private List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList;
}
