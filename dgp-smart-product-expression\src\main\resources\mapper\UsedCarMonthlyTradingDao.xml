<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.UsedCarMonthlyTradingDao">

    <select id="selectUsedCarMonthlyTradingPageVo"
            resultType="com.gwmfc.entity.data.UsedCarMonthlyTradingEntity">
        SELECT id,
        update_month,
        data_date,
        type,
        region,
        useful_life,
        car_type,
        vehicle_type,
        trading_volume,
        trading_proportion,
        transfer_rate,
        img_url,
        create_user,
        create_time,
        update_user,
        update_time
        FROM used_car_monthly_trading
        <where>
            <if test="type != '' and type != null">
                type = #{type}
            </if>
            <if test="startDate != '' and startDate != null or endDate != '' and endDate != null">
                AND CASE
                WHEN INSTR(data_date, '.1-1') > 0 THEN
                REPLACE(data_date, '.1-1', '-1')
                WHEN INSTR(data_date, '.1-') > 0 THEN
                REPLACE(data_date, '.1-', '-0')
                ELSE
                data_date
                END
                <choose>
                    <when test="startDate != '' and startDate != null and endDate != '' and endDate != null">
                        BETWEEN #{startDate} AND #{endDate}
                    </when>
                    <when test="startDate != '' and startDate != null">
                        &gt;= #{startDate}
                    </when>
                    <when test="endDate != '' and endDate != null">
                        &lt;= #{endDate}
                    </when>
                </choose>
            </if>
            ORDER BY data_date DESC, id DESC
        </where>
    </select>
</mapper>
