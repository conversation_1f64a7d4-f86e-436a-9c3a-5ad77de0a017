package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年02月28日 17:13
 */
@Data
@TableName("month_consumption_index")
@ExcelIgnoreUnannotated
public class MonthConsumptionIndexEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("居民消费价格分类指数(上年同月=100)")
    @TableFieldMapping(value = "same_month_last_year_consumer_price_sub_index", comment = "居民消费价格分类指数(上年同月=100)")
    private String sameMonthLastYearConsumerPriceSubIndex;

    @ExcelProperty("居民消费价格分类指数(上年同期=100)")
    @TableFieldMapping(value = "same_period_last_year_consumer_price_sub_index", comment = "居民消费价格分类指数(上年同期=100)")
    private String samePeriodLastYearConsumerPriceSubIndex;

    @ExcelProperty("居民消费价格分类指数(上月=100)")
    @TableFieldMapping(value = "last_month_consumer_price_sub_index", comment = "居民消费价格分类指数(上月=100)")
    private String lastMonthConsumerPriceSubIndex;

    @ExcelProperty("工业生产者出厂价格指数(上年同月=100)")
    @TableFieldMapping(value = "same_month_last_year_producer_price_index", comment = "工业生产者出厂价格指数(上年同月=100)")
    private String sameMonthLastYearProducerPriceIndex;

    @ExcelProperty("工业生产者出厂价格指数(上年同期=100)")
    @TableFieldMapping(value = "same_period_last_year_producer_price_index", comment = "工业生产者出厂价格指数(上年同期=100)")
    private String samePeriodLastYearProducerPriceIndex;

    @ExcelProperty("工业生产者出厂价格指数(上月=100)")
    @TableFieldMapping(value = "last_month_producer_price_index", comment = "工业生产者出厂价格指数(上月=100)")
    private String lastMonthProducerPriceIndex;
}
