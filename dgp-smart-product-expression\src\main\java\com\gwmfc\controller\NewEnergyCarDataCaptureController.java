package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.service.NewEnergyCarDataCaptureService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Classname NewEnergyCarDataCaptureController
 * @Description TODO
 * @Date 2025/1/13 11:19
 */
@Api(tags = "新能源汽车数据采集")
@RestController
@RefreshScope
@RequestMapping("/newEnergyCarData")
public class NewEnergyCarDataCaptureController {
    @Autowired
    private NewEnergyCarDataCaptureService newEnergyCarDataCaptureService;

    @ApiOperation("数据获取")
    @GetMapping("/capture")
    public Result newEnergyCarDataCapture(@RequestParam String tableName,@RequestParam String date, @CurrentUser User user) {
        return newEnergyCarDataCaptureService.newEnergyCarDataCapture(tableName, date, user);
    }

}
