package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * @Date: 2023/11/29
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 */

@Data
@ApiModel(description = "商用车、二手车excel导出条件查询")
public class VehicleExcelQueryDto {

    /**
     * 表类型
     */
    @ApiModelProperty("表类型(1:商用车；2:二手车周度；3:二手车月度)")
    private Integer tableType;

    /**
     * 条件参数
     */
    @ApiModelProperty("条件参数")
    private Map<String, Object> conditionParams;
}
