package com.gwmfc.schedule;

import com.gwmfc.service.MonthlyUsedCarTransactionVolumeService;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Calendar;

/**
 * <AUTHOR>
 * @date 2024年03月22日 10:52
 */
@Component
@Slf4j
public class MonthlyUsedCarTransactionVolumeSchedule {
    @Resource
    private MonthlyUsedCarTransactionVolumeService monthlyUsedCarTransactionVolumeService;

    @Scheduled(cron = "0 5 0 * * ?")
    @SchedulerLock(name = "catchUsedCarTransactionVolumeMonth", lockAtMostFor = "PT1H", lockAtLeastFor = "PT1H")
    public void catchMonthlyUsedCarTransactionVolume() {
        int year = Calendar.getInstance().get(Calendar.YEAR);
        Calendar calendar = Calendar.getInstance();
        int month = calendar.get(Calendar.MONTH);
        if (month == 0) {
            year = year - 1;
            month = 12;
        }
        String[] tableNameArray = {"cpcaautod_new_energy_sales_volume","cpcaautod_monthly_sales_volume"};
        int finalYear = year;
        String monthStr;
        if (month < 10) {
            monthStr = "0".concat(String.valueOf(month));
        } else {
            monthStr = String.valueOf(month);
        }
        Arrays.asList(tableNameArray).forEach(tableName -> {
            try {
                monthlyUsedCarTransactionVolumeService.catchMonthlyUsedCarTransactionVolume(String.valueOf(finalYear),monthStr,tableName, "Scheduled");
            } catch (Exception e) {
                log.error("{}",e);
            }
        });
        log.info("catchMonthlyUsedCarTransactionVolume:{},{}",finalYear,month);
    }
}
