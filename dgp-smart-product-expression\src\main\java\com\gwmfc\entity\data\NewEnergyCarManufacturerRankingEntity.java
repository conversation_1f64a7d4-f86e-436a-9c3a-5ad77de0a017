package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname NewEnergyCarManufacturerRankingEntity
 * @Description TODO
 * @Date 2025/1/18 14:45
 */
@Data
@TableName("new_energy_car_manufacturer_ranking")
@ExcelIgnoreUnannotated
public class NewEnergyCarManufacturerRankingEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期")@TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("销量类型")
    @TableFieldMapping(value = "sales_type", comment = "销量类型")
    private String salesType;

    @ExcelProperty("排名")
    @TableFieldMapping(value = "ranking", comment = "排名")
    private String ranking;

    @ExcelProperty("车企")
    @TableFieldMapping(value = "automobile_enterprise", comment = "车企")
    private String automobileEnterprise;

    @ExcelProperty("销量")
    @TableFieldMapping(value = "sales_volume", comment = "销量")
    private String salesVolume;

    @ExcelProperty("份额")
    @TableFieldMapping(value = "share", comment = "份额")
    private String share;

    @ExcelProperty("图片地址")
    @TableFieldMapping(value = "picture_url", comment = "图片地址")
    private String pictureUrl;
}
