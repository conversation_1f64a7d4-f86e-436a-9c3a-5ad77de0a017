node {
   stage('prepare') {
        if(build_env == "dev"){
          http_proxy=''
          https_proxy=''
//           replica = 1
          nacosServer = sh (script: 'echo ${DEV_NACOS_SERVER}', returnStdout: true).trim()
          checkout([$class: 'GitSCM', branches: [[name: 'origin/dev']], doGenerateSubmoduleConfigurations: false, extensions: [], submoduleCfg: [], userRemoteConfigs: [[credentialsId: 'f1d85477-04f9-4b6b-9f81-5b76d20d44b7', url: 'http://**********/gwmfc/dgp-smart-product.git']]])
        }
        if(build_env == "uat"){
           http_proxy=''
           https_proxy=''
//            replica =2
           nacosServer = sh (script: 'echo ${UAT_NACOS_SERVER}', returnStdout: true).trim()
           checkout([$class: 'GitSCM', branches: [[name: 'origin/int']], doGenerateSubmoduleConfigurations: false, extensions: [], submoduleCfg: [], userRemoteConfigs: [[credentialsId: 'f1d85477-04f9-4b6b-9f81-5b76d20d44b7', url: 'http://**********/gwmfc/dgp-smart-product.git']]])
        }
        if(build_env == "prod"){
           http_proxy='http://squid.gwmfc.com:8128'
           https_proxy='http://squid.gwmfc.com:8128'
           replica =2
           nacosServer = sh (script: 'echo ${PROD_NACOS_SERVER}', returnStdout: true).trim()
           checkout([$class: 'GitSCM', branches: [[name: 'origin/master']], doGenerateSubmoduleConfigurations: false, extensions: [], submoduleCfg: [], userRemoteConfigs: [[credentialsId: '63dc30d8-b3a6-4780-88b8-e76d7002cacf', url: 'git@**********:gwmfc/dgp-smart-product.git']]])
        }

        jvmMem="${jvmMem}"
        replica="${replica}"
        kube_master = sh (script: 'echo ${KUBE_MASTER}', returnStdout: true).trim()
        dockerHub = sh (script: 'echo ${DOCKER_HUB}', returnStdout: true).trim()
        dockerHubUser = sh (script: 'echo ${DOCKER_HUB_USER}', returnStdout: true).trim()
        dockerHubPss = sh (script: 'echo ${DOCKER_HUB_PSS}', returnStdout: true).trim()
        depUser = sh (script: 'echo ${DEPLOY_USER}', returnStdout: true).trim()
        depPass = sh (script: 'echo ${DEPLOY_PASS}', returnStdout: true).trim()
        echo "========================================="
        echo "DOCKER HUB: ${dockerHub}"
        echo "PROJECT NAME: ${project_name}"
        echo "BUILD ENV: ${build_env}"
        echo "NACOS SERVER: ${nacosServer}"
        echo "git branch"
         sh "git branch"
        echo "========================================="
   }
   stage('build') {
       if(project_name == 'dgp-smart-product-common'){
          sh "mvn clean install -N -Dmaven.test.skip=true"
          sh "mvn clean package -pl ${project_name} -am -Dmaven.test.skip=true -Dmvn.url=${mvn_url} deploy"
       }else{
          sh "mvn clean install -pl ${project_name} -am -amd -Dmaven.test.skip=true -Dmvn.url=${mvn_url} -P ${build_env}"
          pom_path = sh (script: 'echo ${project_name}/pom.xml', returnStdout: true).trim()
          pom = readMavenPom file: pom_path
          app_name = "${pom.artifactId}"
          source_file= "${pom.artifactId}/target/*.jar"
          jar_file="${pom.artifactId}.jar"
          commit_id = sh (script: 'git rev-parse --short HEAD', returnStdout: true).trim()
          echo "commit id: $commit_id"
          docker_img_name = "${dockerHub}:8083/${app_name}"
          echo "docker-img-name: ${docker_img_name}"
          sh "docker build --build-arg JVM_MEM=${jvmMem} --build-arg APP_NAME=${app_name} --build-arg SOURCE_FILE=${source_file} --build-arg JAR_FILE=${jar_file} --build-arg DOCKERHUB=${dockerHub} --build-arg http_proxy=${http_proxy} --build-arg https_proxy=${https_proxy} -t ${docker_img_name}:${commit_id} ."
          sh "docker login -u ${dockerHubUser} -p ${dockerHubPss} ${dockerHub}:8083"
          sh "docker push ${docker_img_name}:${commit_id}"
          echo "delete images"
          sh "chmod 777 ./DeleteImages.sh"
          sh (script: "./DeleteImages.sh ${app_name}")
          image_name = docker_img_name.replace("/","\\/")+":"+commit_id
          sh "sed -e 's/#appName/${app_name}/' -e 's/#env/${build_env}/' -e 's/#image/${image_name}/' -e 's/#port/${port}/' -e 's/#NACOS_SERVER/${nacosServer}/' -e 's/#replica/${replica}/' -e 's/#limitCpu/${limitCpu}/' -e 's/#limitMem/${limitMem}/' deployment-template.yaml > ${app_name}-${build_env}.yaml"
          echo "---------------------------"
          sh "sed -e 's/#appName/${app_name}/' -e 's/#port/${port}/' -e 's/#env/${build_env}/' service-template.yaml > ${app_name}-${build_env}-svc.yaml"
          echo "============================="
          yaml=app_name+'-'+build_env+'.yaml'
          svcYaml=app_name+'-'+build_env+'-svc.yaml'

       }
   }
   stage('deploy'){
      if(project_name == 'dgp-smart-product-common'){
         echo "deploy ${project_name} successful"
      }else{
        path='/gwmfc/k8s/yaml/'+build_env
        def remote = [:]
        remote.name = kube_master
        remote.host = kube_master
        remote.user = depUser
        remote.password = depPass
        remote.allowAnyHosts = true
        sshPut remote: remote, from: yaml, into: path
        sshPut remote: remote, from: svcYaml, into: path
        sshCommand remote: remote, command: "kubectl apply -f /gwmfc/k8s/yaml/${build_env}/${app_name}-${build_env}-svc.yaml"
        sshCommand remote: remote, command: "kubectl apply -f /gwmfc/k8s/yaml/${build_env}/${app_name}-${build_env}.yaml"
      }
   }
}
