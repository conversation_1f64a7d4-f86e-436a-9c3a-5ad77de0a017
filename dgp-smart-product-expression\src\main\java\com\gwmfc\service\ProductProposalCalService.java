package com.gwmfc.service;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.dozermapper.core.DozerBeanMapperBuilder;
import com.github.dozermapper.core.Mapper;
import com.google.common.base.CaseFormat;
import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.bo.ProductProposalHistoryQueryBo;
import com.gwmfc.constant.DownLoadTemplateTypeEnum;
import com.gwmfc.constant.ProductCalPamTypeEnum;
import com.gwmfc.dao.*;
import com.gwmfc.domain.User;
import com.gwmfc.dto.*;
import com.gwmfc.entity.*;
import com.gwmfc.listener.ProductCalImportListener;
import com.gwmfc.service.calV2.ProductCalculateV2Service;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.OkHttpUtils;
import com.gwmfc.util.Result;
import com.gwmfc.util.WordGeneratorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.beans.PropertyDescriptor;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @Classname ProductProposalCalService
 * @Description TODO
 * @Date 2023/10/18 17:19
 */
@Service
@Slf4j
@RefreshScope
public class ProductProposalCalService {
    @Value("${irr-range-num}")
    private String irrRangeNum;

    @Value("${risk-loss-tolerate-url}")
    private String riskLossTolerateUrl;

    @Autowired
    private ProductCalculateService productCalculateService;

    @Autowired
    private ProductCalculateV2Service productCalculateV2Service;

    @Autowired
    private ProductCalBasicInfoDao productCalBasicInfoDao;

    @Autowired
    private ProductCalBasicParamDao productCalBasicParamDao;

    @Autowired
    private ProductCalEarlySquareParamDao productCalEarlySquareParamDao;

    @Autowired
    private ProductCalRepaymentMethodParamDao productCalRepaymentMethodParamDao;

    @Autowired
    private ProductCalTaxFeeParamDao productCalTaxFeeParamDao;

    @Autowired
    private ProductProposalHistoryDao productProposalHistoryDao;

    @Autowired
    private ProductProposqlCalDao productProposqlCalDao;

    @Value("${preCalSpecialParam}")
    private String preCalSpecialParam;

    @Value("${smartHistoryDataPerCount}")
    private Integer smartHistoryDataPerCount;

    @Autowired
    private ProductProposalDao productProposalDao;

    @Resource
    private FtpService ftpService;

    @Value("${irrBatchCalTemplate.bp.filePath}")
    private String irrBatchCalTemplatePathBp;

    @Value("${irrBatchCalTemplate.qp.filePath}")
    private String irrBatchCalTemplatePathQp;

    @Value("${irrBatchCalTemplate.esc.filePath}")
    private String irrBatchCalTemplatePathEsc;

    @Value("${proposalCalTemplate.filePath}")
    private String proposalCalTemplatePath;

    @Value("${profitCalSalesTemplate.filePath}")
    private String profitCalSalesTemplatePath;

    @Value("${earlySquareRatioTemplate.filePath}")
    private String earlySquareRatioTemplatePath;

    @Value("${irrBatchCalTemplate.bp.fileName}")
    private String irrBatchCalTemplateNameBp;

    @Value("${irrBatchCalTemplate.qp.fileName}")
    private String irrBatchCalTemplateNameQp;

    @Value("${irrBatchCalTemplate.esc.fileName}")
    private String irrBatchCalTemplateNameEsc;

    @Value("${proposalCalTemplate.fileName}")
    private String proposalCalTemplateName;

    @Value("${profitCalSalesTemplate.fileName}")
    private String profitCalSalesTemplateName;

    @Value("${earlySquareRatioTemplate.fileName}")
    private String earlySquareRatioTemplateName;

    @Value("${riskCalTimeOut}")
    private long riskCalTimeOut;
    @Value("${lossRateCalMaxTimes}")
    private Integer lossRateCalMaxTimes;


    public Result calculateIrr(ProductCalculateRangeDto productCalculateRangeDto){
        log.info("calculate irr start");
        Result result = new Result();
        List<ProductCalResultRangeDto> list = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        try{
            ProductCalculateDto productCalculateDto = new ProductCalculateDto();
            BeanUtils.copyProperties(productCalculateRangeDto,productCalculateDto);
            String item = productCalculateRangeDto.getItem();
            if(StringUtils.hasText(item)){
                item = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, item);
            }
            String valueLow = productCalculateRangeDto.getValueLow();
            String valueHigh = productCalculateRangeDto.getValueHigh();

            Integer isUnionLoan = null;
            if(productCalculateRangeDto.getProductCalUnionLoanParamDto() != null && productCalculateRangeDto.getProductCalUnionLoanParamDto().getBankLoanRatio() != null){
                isUnionLoan = 1;
            }
            Integer considerEarlySquare = productCalculateDto.getProductCalBasicInfoDto().getConsiderEarlySquare();
            /**
             * 查询存在的最新模板
             */
            Result resultReplace = replaceProductCalculateDto(productCalculateDto,isUnionLoan);
            if(resultReplace.getCode()!=200){
                return resultReplace;
            }

            /**
             * 计算范围条目item对应范围内的20条irr测算值
             */
            BigDecimal twBigDecimal = BigDecimal.valueOf(Integer.valueOf(irrRangeNum)-1);
            BigDecimal divide;
            if(item != null){
                BigDecimal bigDecimalHigh;
                BigDecimal bigDecimalLow;
                if("timeLimit".equals(item)){
                    bigDecimalHigh = BigDecimal.valueOf(Integer.valueOf(valueHigh));
                    bigDecimalLow = BigDecimal.valueOf(Integer.valueOf(valueLow));
                    BigDecimal subtract = bigDecimalHigh.subtract(bigDecimalLow);
                    divide = subtract.divide(twBigDecimal,0, RoundingMode.HALF_UP);
                }else{
                    bigDecimalHigh = BigDecimal.valueOf(Double.valueOf(valueHigh));
                    bigDecimalLow = BigDecimal.valueOf(Double.valueOf(valueLow));
                    BigDecimal subtract = bigDecimalHigh.subtract(bigDecimalLow);
                    divide = subtract.divide(twBigDecimal,4, RoundingMode.HALF_UP);
                }
                Mapper mapper = DozerBeanMapperBuilder.buildDefault();
                ProductCalculateDto productCalculateDtoBak = mapper.map(productCalculateDto,ProductCalculateDto.class);

                replaceByItemtype(productCalculateDto,item,productCalculateRangeDto.getItemType(),valueLow);
                log.info("calculate item:{},value:{} irr",item,valueLow);
                Result resultIrrLow = new Result();
                if(considerEarlySquare == 1){
                    resultIrrLow = productCalculateV2Service.calculateAllUnionParam(productCalculateDto);
                }else{
                    resultIrrLow = productCalculateService.calculateAllParam(productCalculateDto);
                }
                if(resultIrrLow.getCode().equals(FAIL.getCode())){
                    return resultIrrLow;
                }
                ProductCalculateDto dataLow = (ProductCalculateDto)resultIrrLow.getData();
                ProductCalResultRangeDto productCalResultRangeDtoLow = new ProductCalResultRangeDto();
                BeanUtils.copyProperties(dataLow.getProductCalResultInfoDto(),productCalResultRangeDtoLow);
                productCalResultRangeDtoLow.setItem(item);
                productCalResultRangeDtoLow.setValue(valueLow);
                list.add(productCalResultRangeDtoLow);
                for(int i=1;i<=(Integer.valueOf(irrRangeNum)-2);i++){
                    ProductCalResultRangeDto productCalResultRangeDto = new ProductCalResultRangeDto();
                    BigDecimal multiply = divide.multiply(BigDecimal.valueOf(i));
                    Object value = bigDecimalLow.add(multiply).toString();
                    productCalculateDto = mapper.map(productCalculateDtoBak,ProductCalculateDto.class);
                    replaceByItemtype(productCalculateDto,item,productCalculateRangeDto.getItemType(),value);
                    log.info("calculate item:{},value:{} irr",item,value);
                    Result resultIrrMiddle = new Result();
                    if(considerEarlySquare == 1){
                        resultIrrMiddle = productCalculateV2Service.calculateAllUnionParam(productCalculateDto);
                    }else{
                        resultIrrMiddle = productCalculateService.calculateAllParam(productCalculateDto);
                    }
                    if(resultIrrMiddle.getCode().equals(FAIL.getCode())){
                        return resultIrrMiddle;
                    }
                    ProductCalculateDto dataMiddle = (ProductCalculateDto)resultIrrMiddle.getData();
                    BeanUtils.copyProperties(dataMiddle.getProductCalResultInfoDto(),productCalResultRangeDto);
                    productCalResultRangeDto.setItem(item);
                    productCalResultRangeDto.setValue(String.valueOf(value));
                    list.add(productCalResultRangeDto);
                }
                productCalculateDto = mapper.map(productCalculateDtoBak,ProductCalculateDto.class);                replaceByItemtype(productCalculateDto,item,productCalculateRangeDto.getItemType(),valueHigh);
                log.info("calculate item:{},value:{} irr",item,valueHigh);
                Result resultIrrHigh = new Result();
                if(considerEarlySquare == 1){
                    resultIrrHigh = productCalculateV2Service.calculateAllUnionParam(productCalculateDto);
                }else{
                    resultIrrHigh = productCalculateService.calculateAllParam(productCalculateDto);
                }
                if(resultIrrHigh.getCode().equals(FAIL.getCode())){
                    return resultIrrHigh;
                }
                ProductCalculateDto dataHigh = (ProductCalculateDto)resultIrrHigh.getData();
                ProductCalResultRangeDto productCalResultRangeDtoHigh = new ProductCalResultRangeDto();
                BeanUtils.copyProperties(dataHigh.getProductCalResultInfoDto(),productCalResultRangeDtoHigh);
                productCalResultRangeDtoHigh.setItem(item);
                productCalResultRangeDtoHigh.setValue(valueHigh);
                list.add(productCalResultRangeDtoHigh);
                result.setCode(SUCCESS.getCode());
                result.setMessage(SUCCESS.getDesc());
                result.setData(list);
            }else{
                List<ProductCalProfitDetailDto> productCalProfitDetailDtoList = productCalculateRangeDto.getProductCalProfitDetailDtoList();
                ProductCalResultRangeDto productCalResultRangeDto = new ProductCalResultRangeDto();
                if(productCalProfitDetailDtoList != null && productCalProfitDetailDtoList.size()>0){
                    ProductCalProfitDto productCalProfitDto = new ProductCalProfitDto();
                    productCalProfitDto.setProductCalculateDto(productCalculateDto);
                    productCalProfitDto.setProductCalProfitDetailDtoList(productCalProfitDetailDtoList);
                    Result resultProfit = productCalculateService.calulateProfitByAmount(productCalProfitDto);
                    if(resultProfit.getCode()!=200){
                        log.info("计算利润失败："+resultProfit.getMessage());
                        result.setCode(resultProfit.getCode());
                        result.setMessage(resultProfit.getMessage());
                        return result;
                    }
                    ProductCalProfitDto productCalProfitDtoResult = (ProductCalProfitDto)resultProfit.getData();
                    result.setCode(SUCCESS.getCode());
                    result.setMessage(SUCCESS.getDesc());
                    result.setData(productCalProfitDtoResult);
                }else{
                    Result resultIrr = new Result();
                    if(considerEarlySquare == 1){
                        resultIrr = productCalculateV2Service.calculateAllUnionParam(productCalculateDto);
                    }else{
                        resultIrr = productCalculateService.calculateAllParam(productCalculateDto);
                    }
                    if(resultIrr.getCode()!=200){
                        log.info("计算irr失败："+resultIrr.getMessage());
                        result.setCode(resultIrr.getCode());
                        result.setMessage(resultIrr.getMessage());
                        return result;
                    }
                    ProductCalculateDto data = (ProductCalculateDto)resultIrr.getData();
                    BeanUtils.copyProperties(data.getProductCalResultInfoDto(),productCalResultRangeDto);
                    productCalResultRangeDto.setIrrTemplateId(productCalculateDto.getProductCalBasicInfoDto().getId());
                    if(productCalculateRangeDto.getContractProportion()!=null){
                        productCalResultRangeDto.setContractProportion(productCalculateRangeDto.getContractProportion());
                        productCalResultRangeDto.setWeightingIrr(productCalculateService.dealScale(2,productCalResultRangeDto.getResultIrr()*productCalculateRangeDto.getContractProportion()));
                    }
                    /**
                     * 标识是预测算未传值的时候进行单个测算和 IRR测算区分
                     */
                    if("ycs".equals(productCalculateRangeDto.getSource())){
                        list.add(productCalResultRangeDto);
                        result.setData(list);
                    }else{
                        result.setData(productCalResultRangeDto);
                    }
                    result.setCode(SUCCESS.getCode());
                    result.setMessage(SUCCESS.getDesc());
                }
            }
        }catch (Exception e){
            log.error("calculate irr error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("calculate irr error");
        }
        long endTime = System.currentTimeMillis();
        log.info("calculate irr end");
        log.info("耗时："+(endTime-startTime)/1000);
        return result;
    }

    public Result replaceProductCalculateDto(ProductCalculateDto productCalculateDto,Integer isUnionLoan){
        Result result = new Result();
        try{
            ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
            Integer bussType = productCalBasicInfoDto.getBusinessType();
            Integer repaymentMethod = productCalBasicInfoDto.getRepaymentMethod();
            Integer considerEarlySquare = productCalBasicInfoDto.getConsiderEarlySquare();


            long querryTemplateStartTime = System.currentTimeMillis();
            List<ProductCalBasicInfoEntity> productCalBasicInfoEntityList = productProposqlCalDao.getProductCalBasicInfoEntityList(bussType,repaymentMethod,considerEarlySquare,isUnionLoan);
            log.info("querryTemplate cost time: "+(System.currentTimeMillis()-querryTemplateStartTime)+"ms");

            if(productCalBasicInfoEntityList == null || productCalBasicInfoEntityList.size() == 0){
                result.setCode(FAIL.getCode());
                result.setMessage("该业务类型，还款方式没有对应IRR测算模板");
                return result;
            }

            /**
             * 基本信息
             */
            ProductCalBasicInfoEntity productCalBasicInfoEntity = productCalBasicInfoEntityList.get(0);
            productCalBasicInfoDto.setId(productCalBasicInfoEntity.getId());

            /**
             * 基本参数
             */
            ProductCalBasicParamDto productCalBasicParamDto = new ProductCalBasicParamDto();
            QueryWrapper queryWrapperBasicParam = new QueryWrapper();
            queryWrapperBasicParam.eq("basic_info_id",productCalBasicInfoEntity.getId());
            List<ProductCalBasicParamEntity> productCalBasicParamEntityList = productCalBasicParamDao.selectList(queryWrapperBasicParam);
            if(productCalBasicParamEntityList != null && productCalBasicParamEntityList.size()>0){
                List<Map<String, Object>> jsonMapList = productCalBasicParamEntityList.stream().map(productCalBasicParamEntity -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalBasicParamEntity.getItem()), productCalBasicParamEntity.getValue());
                    return map;
                }).collect(Collectors.toList());
                transParamEntityListToDto(jsonMapList,productCalBasicParamDto);
                if(productCalculateDto.getProductCalBasicParamDto()!=null){
                    BeanUtils.copyProperties(productCalculateDto.getProductCalBasicParamDto(),productCalBasicParamDto,getNullPropertyNames(productCalculateDto.getProductCalBasicParamDto()));
                }
            }
            productCalculateDto.setProductCalBasicParamDto(productCalBasicParamDto);



            /**
             * 税费参数
             */
            ProductCalTaxFeeParamDto productCalTaxFeeParamDto = new ProductCalTaxFeeParamDto();
            QueryWrapper queryWrapperTaxFeeParam = new QueryWrapper();
            queryWrapperTaxFeeParam.eq("basic_info_id",productCalBasicInfoEntity.getId());
            List<ProductCalTaxFeeParamEntity> productCalTaxFeeParamEntityList = productCalTaxFeeParamDao.selectList(queryWrapperTaxFeeParam);
            if(productCalTaxFeeParamEntityList != null && productCalTaxFeeParamEntityList.size()>0){
                List<Map<String, Object>> jsonMapList = productCalTaxFeeParamEntityList.stream().map(productCalTaxFeeParamEntity -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalTaxFeeParamEntity.getItem()), productCalTaxFeeParamEntity.getValue());
                    return map;
                }).collect(Collectors.toList());
                transParamEntityListToDto(jsonMapList,productCalTaxFeeParamDto);
                if(productCalculateDto.getProductCalTaxFeeParamDto()!=null){
                    BeanUtils.copyProperties(productCalculateDto.getProductCalTaxFeeParamDto(),productCalTaxFeeParamDto,getNullPropertyNames(productCalculateDto.getProductCalTaxFeeParamDto()));
                }
            }
            productCalculateDto.setProductCalTaxFeeParamDto(productCalTaxFeeParamDto);

            /**
             * 还款方式参数
             */
            ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto = new ProductCalRepaymentMethodParamDto();
            QueryWrapper queryWrapperRepaymentMethodParam = new QueryWrapper();
            queryWrapperRepaymentMethodParam.eq("basic_info_id",productCalBasicInfoEntity.getId());
            List<ProductCalRepaymentMethodParamEntity> productCalRepaymentMethodParamEntityList = productCalRepaymentMethodParamDao.selectList(queryWrapperRepaymentMethodParam);
            if(productCalRepaymentMethodParamEntityList != null && productCalRepaymentMethodParamEntityList.size()>0){
                List<Map<String, Object>> jsonMapList = productCalRepaymentMethodParamEntityList.stream().map(productCalRepaymentMethodParamEntity -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalRepaymentMethodParamEntity.getItem()), productCalRepaymentMethodParamEntity.getValue());
                    return map;
                }).collect(Collectors.toList());
                transParamEntityListToDto(jsonMapList,productCalRepaymentMethodParamDto);
                if(productCalculateDto.getProductCalRepaymentMethodParamDto()!=null){
                    BeanUtils.copyProperties(productCalculateDto.getProductCalRepaymentMethodParamDto(),productCalRepaymentMethodParamDto);
                }
            }
            productCalculateDto.setProductCalRepaymentMethodParamDto(productCalRepaymentMethodParamDto);

            /**
             * 提前还款参数
             */
            if(considerEarlySquare == 0){
                ProductCalEarlySquareParamDto productCalEarlySquareParamDto = new ProductCalEarlySquareParamDto();
                QueryWrapper queryWrapperEarlySquareParam = new QueryWrapper();
                queryWrapperEarlySquareParam.eq("basic_info_id",productCalBasicInfoEntity.getId());
                List<ProductCalEarlySquareParamEntity> productCalEarlySquareParamEntityList = productCalEarlySquareParamDao.selectList(queryWrapperEarlySquareParam);
                if(productCalEarlySquareParamEntityList != null && productCalEarlySquareParamEntityList.size()>0){
                    List<Map<String, Object>> jsonMapList = productCalEarlySquareParamEntityList.stream().map(productCalEarlySquareParamEntity -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalEarlySquareParamEntity.getItem()), productCalEarlySquareParamEntity.getValue());
                        return map;
                    }).collect(Collectors.toList());
                    transParamEntityListToDto(jsonMapList,productCalEarlySquareParamDto);
                    if(productCalculateDto.getProductCalEarlySquareParamDto()!=null){
                        BeanUtils.copyProperties(productCalculateDto.getProductCalEarlySquareParamDto(),productCalEarlySquareParamDto,getNullPropertyNames(productCalculateDto.getProductCalEarlySquareParamDto()));
                    }
                }
                productCalculateDto.setProductCalEarlySquareParamDto(productCalEarlySquareParamDto);
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productCalBasicInfoEntity);
        }catch (Exception e){
            log.error("模板替换异常",e);
            result.setCode(FAIL.getCode());
            result.setMessage("模板替换异常");
        }
        return result;
    }

    public void replaceByItemtype(ProductCalculateDto productCalculateDto,String item,String itemType,Object value) throws Exception{
        if(itemType.equals(ProductCalPamTypeEnum.BASIC_PAM.type())){
            replaceParamVlaue(productCalculateDto.getProductCalBasicParamDto(),item,value);
        }else if(itemType.equals(ProductCalPamTypeEnum.TAX_FEE_PAM.type())){
            replaceParamVlaue(productCalculateDto.getProductCalTaxFeeParamDto(),item,value);
        }else if(itemType.equals(ProductCalPamTypeEnum.REPAYMENTY_METHOD_PAM.type())){
            replaceParamVlaue(productCalculateDto.getProductCalRepaymentMethodParamDto(),item,value);
        }else{
            replaceParamVlaue(productCalculateDto.getProductCalEarlySquareParamDto(),item,value);
        }
    }

    public void transParamEntityListToDto(List<Map<String,Object>> paramEntityList, Object paramDto) throws Exception{
        for (Map<String, Object> map : paramEntityList) {
            Field[] declaredFields = paramDto.getClass().getDeclaredFields();
            for (Field declaredField : declaredFields) {
                declaredField.setAccessible(true);
                if(map.get(declaredField.getName()) != null && !((String)map.get(declaredField.getName())).isEmpty()){
                    if(Integer.class == declaredField.getType()){
                        declaredField.set(paramDto,Integer.valueOf((String)map.get(declaredField.getName())));
                    }else{
                        declaredField.set(paramDto,Double.valueOf((String)map.get(declaredField.getName())));
                    }
                }
            }

        }
    }

    public void replaceParamVlaue(Object obj,String item, Object value) throws Exception{
        Field field = obj.getClass().getDeclaredField(item);
        field.setAccessible(true);
        if(Integer.class == field.getType()){
            field.set(obj,Integer.valueOf((String)value));
        }else{
            field.set(obj,Double.valueOf((String)value));
        }
    }

    public String[] getNullPropertyNames(Object source){
        String[] preCalSpecialParamArr = preCalSpecialParam.split(",");
        List<String> list = Arrays.asList(preCalSpecialParamArr);
        final BeanWrapper src = new BeanWrapperImpl(source);
        PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> nullNames = new HashSet<>();
        for (PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if(list.contains(pd.getName())&&srcValue == null){
                nullNames.add(pd.getName());
            }
        }
        String[] result = new String[nullNames.size()];
        return nullNames.toArray(result);
    }

    public Result getHistoryProductAvgValue(ProductProposalHistoryQueryDto productProposalHistoryQueryDto){
        Result result = new Result();
        long startTime = System.currentTimeMillis();
        try{
            ProductProposalHistoryQueryBo historyData = productProposalHistoryDao.getHistoryData(productProposalHistoryQueryDto);
            log.info("数据库查询历史数据耗时:{}s",(System.currentTimeMillis()-startTime)/1000);

            Integer queryCount = historyData.getContractNum();
            if(queryCount==0){
                result.setCode(FAIL.getCode());
                result.setMessage("按条件搜索合同量为0，请核实！");
                return result;
            }
            productProposalHistoryQueryDto.setContractNum(String.valueOf(queryCount));

            Integer bussTypeContractNum = productProposalHistoryDao.getBussTypeContractNum(productProposalHistoryQueryDto.getBusinessType());

            if(bussTypeContractNum==0){
                result.setCode(FAIL.getCode());
                result.setMessage("该业务类型合同量查询为0，请核实！");
                return result;
            }

            String contractProportion = BigDecimal.valueOf(queryCount).divide(BigDecimal.valueOf(bussTypeContractNum),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
            productProposalHistoryQueryDto.setContractProportion(contractProportion);

            log.info("求合计算平均值等指标开始");
            Integer contractNum = queryCount;
            /**
             * 计算平均车价
             */
            String avgCarPrice = BigDecimal.valueOf(historyData.getSumVehiclePrice()).divide(BigDecimal.valueOf(contractNum),2,BigDecimal.ROUND_HALF_UP).toString();
            productProposalHistoryQueryDto.setAvgCarPrice(avgCarPrice);

            /**
             * 平均贷期
             */
            String avgLoanTrm = BigDecimal.valueOf(historyData.getSumLoanTrm()).divide(BigDecimal.valueOf(contractNum),0,BigDecimal.ROUND_UP).toString();
            productProposalHistoryQueryDto.setAvgLoanTrm(avgLoanTrm);

            /**
             * 平均总利率
             */
            String avgTotalRate = BigDecimal.valueOf(historyData.getSumTotalRate()).divide(BigDecimal.valueOf(contractNum),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
            productProposalHistoryQueryDto.setAvgTotalRate(avgTotalRate);

            /**
             * 平均客户利率
             */
            String avgCustomerRate = BigDecimal.valueOf(historyData.getSumCustomerRte()).divide(BigDecimal.valueOf(contractNum),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
            productProposalHistoryQueryDto.setAvgCustomerRate(avgCustomerRate);

            /**
             * 总贷额、平均贷额
             */
            Double sumLoanAmtTotal = historyData.getSumLoanAmt();
            String totalAmtStr = BigDecimal.valueOf(sumLoanAmtTotal).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            String avgLoanAmt = BigDecimal.valueOf(sumLoanAmtTotal).divide(BigDecimal.valueOf(contractNum),2,BigDecimal.ROUND_HALF_UP).toString();
            productProposalHistoryQueryDto.setTotalLoanAmt(totalAmtStr);
            productProposalHistoryQueryDto.setAvgLoanAmt(avgLoanAmt);

            /**
             * 平均首付比例
             */
            String avgPayPct = BigDecimal.valueOf(historyData.getSumPayPct()).divide(BigDecimal.valueOf(contractNum),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
            productProposalHistoryQueryDto.setAvgPayPct(avgPayPct);

            /**
             * 平均附加贷金额
             */
            String avgAddCreAmt = BigDecimal.valueOf(historyData.getSumAddCreAmt()).divide(BigDecimal.valueOf(contractNum),2,BigDecimal.ROUND_HALF_UP).toString();
            productProposalHistoryQueryDto.setAvgAddCreAmt(avgAddCreAmt);

            /**
             * 平均附加贷比列
             */
            String avgAddCreRatio = null;
            if(sumLoanAmtTotal!=0){
                avgAddCreRatio = BigDecimal.valueOf(historyData.getSumAddCreAmt()).divide(BigDecimal.valueOf(sumLoanAmtTotal),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
            }
            productProposalHistoryQueryDto.setAvgAddCreRatio(avgAddCreRatio);

            /**
             * 不良贷款率
             */
            Double sumRemainPrincipalAmtTotal = historyData.getSumRemainPrincipalAmt();
            if(sumRemainPrincipalAmtTotal!=0){
                String badLoanRate = BigDecimal.valueOf(historyData.getSumBadRemainPrincipalAmt()).divide(BigDecimal.valueOf(sumRemainPrincipalAmtTotal),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                productProposalHistoryQueryDto.setBadLoanRate(badLoanRate);

                /**
                 * 核销前近18期30+逾期率
                 */

                String beforeHx18Times30dOverDueRate = BigDecimal.valueOf(historyData.getBeforeHx18times30dRemainPrincipalAmt()).divide(BigDecimal.valueOf(sumRemainPrincipalAmtTotal),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                productProposalHistoryQueryDto.setBeforeHx18Times30dOverDueRate(beforeHx18Times30dOverDueRate);

                /**
                 * 核销前近12期30+逾期率
                 */
                String beforeHx12Times30dOverDueRate = BigDecimal.valueOf(historyData.getBeforeHx12times30dRemainPrincipalAmt()).divide(BigDecimal.valueOf(sumRemainPrincipalAmtTotal),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                productProposalHistoryQueryDto.setBeforeHx12Times30dOverDueRate(beforeHx12Times30dOverDueRate);

                /**
                 * 核销前30+逾期率
                 */
                String beforeHx30dOverDueRate = BigDecimal.valueOf(historyData.getBeforeHx30dRemainPrincipalAmt()).divide(BigDecimal.valueOf(sumRemainPrincipalAmtTotal),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                productProposalHistoryQueryDto.setBeforeHx30dOverDueRate(beforeHx30dOverDueRate);

                /**
                 * 核销后近18期30+逾期率
                 */
                String afterHx18Times30dOverDueRate = BigDecimal.valueOf(historyData.getAfterHx18times30dRemainPrincipalAmt()).divide(BigDecimal.valueOf(sumRemainPrincipalAmtTotal),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                productProposalHistoryQueryDto.setAfterHx18Times30dOverDueRate(afterHx18Times30dOverDueRate);

                /**
                 * 核销后近12期30+逾期率
                 */
                String afterHx12Times30dOverDueRate = BigDecimal.valueOf(historyData.getAfterHx12times30dRemainPrincipalAmt()).divide(BigDecimal.valueOf(sumRemainPrincipalAmtTotal),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                productProposalHistoryQueryDto.setAfterHx12Times30dOverDueRate(afterHx12Times30dOverDueRate);

                /**
                 * 核销后30+逾期率
                 */
                String afterHx30dOverDueRate = BigDecimal.valueOf(historyData.getAfterHx30dRemainPrincipalAmt()).divide(BigDecimal.valueOf(sumRemainPrincipalAmtTotal),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                productProposalHistoryQueryDto.setAfterHx30dOverDueRate(afterHx30dOverDueRate);
            }
            if(sumLoanAmtTotal != 0){
                String avgLossRate = BigDecimal.valueOf(historyData.getSumLoss()).divide(BigDecimal.valueOf(sumLoanAmtTotal),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                productProposalHistoryQueryDto.setAvgLossRate(avgLossRate);

                /**
                 * 平均万元收益
                 */
                String avg10ThondsIncom = BigDecimal.valueOf(historyData.getSumMarginContribution()).multiply(BigDecimal.valueOf(10000)).divide(BigDecimal.valueOf(sumLoanAmtTotal),2,BigDecimal.ROUND_HALF_UP).toString();
                productProposalHistoryQueryDto.setAvg10ThondsIncom(avg10ThondsIncom);

                /**
                 * 平均万元销售利润
                 */
                String avg10ThondsSalProfit = BigDecimal.valueOf(historyData.getSumSaleProfit()).multiply(BigDecimal.valueOf(10000)).divide(BigDecimal.valueOf(sumLoanAmtTotal),2,BigDecimal.ROUND_HALF_UP).toString();
                productProposalHistoryQueryDto.setAvg10ThondsSalProfit(avg10ThondsSalProfit);
            }
            String avgBussRebeat = BigDecimal.valueOf(historyData.getSumRebeatRewPct()).divide(BigDecimal.valueOf(contractNum),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
            productProposalHistoryQueryDto.setAvgBussRebeat(avgBussRebeat);
            log.info("求合计算平均值等指标结束,耗时:{}s",(System.currentTimeMillis()-startTime)/1000);

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productProposalHistoryQueryDto);
            long endTime = System.currentTimeMillis();
            log.info("getHistoryProductAvgValue total cost: "+(endTime-startTime)/1000+"s");
        }catch(Exception e){
            log.error("查询历史数据计算指标失败",e);
            result.setCode(FAIL.getCode());
            result.setMessage("查询历史数据计算指标失败");
        }
        return result;
    }

    public Result getRiskLossTolerate(ProductCalRiskLossDto productCalRiskLossDto){
        Result result = new Result();
        try{
            log.info("getRiskLossTolerate start");
//            String riskLossList = OkHttpUtils.doPost(riskLossTolerateUrl,requestJson,"application/json",null,riskCalTimeOut);
            Double targetIrr = productCalRiskLossDto.getTargetIrr();
            log.info("targetIrr is :{}",targetIrr);
            List<ProductCalRiskLossCalBodyDto> productCalRiskLossCalBodyDtoList = productCalRiskLossDto.getProductCalRiskLossCalBodyDtoList();
            List<ProductCalRiskLossReturnDto> productCalRiskLossReturnDtoList = new ArrayList<>();
            if(productCalRiskLossCalBodyDtoList!=null && productCalRiskLossCalBodyDtoList.size()>0){
                for (ProductCalRiskLossCalBodyDto productCalRiskLossCalBodyDto : productCalRiskLossCalBodyDtoList) {
                    log.info("第一条开始计算，id为：{}",productCalRiskLossCalBodyDto.getId());

                    ProductCalRiskLossReturnDto productCalRiskLossReturnDto = new ProductCalRiskLossReturnDto();
                    List<ProductCalRiskLossReturnDetailDto> productCalRiskLossReturnDetailDtoList = new ArrayList<>();
                    Double minKey = 0.0;
                    Double step = 1.5;

                    /**
                     * 计算 lossRate = 0 的irr
                     */
                    ProductCalculateRangeDto productCalculateRangeDto = new ProductCalculateRangeDto();
                    BeanUtils.copyProperties(productCalRiskLossCalBodyDto,productCalculateRangeDto);
                    boolean zeroLossRateFlag = dealZeroLossRateIrr(minKey,targetIrr,productCalculateRangeDto,productCalRiskLossReturnDto,productCalRiskLossReturnDtoList,productCalRiskLossReturnDetailDtoList,productCalRiskLossCalBodyDto);
                    if(zeroLossRateFlag == false){
                        continue;
                    }

                    /**
                     * step = 1.5 寻找irr小于 targetIrr的临界lossRate
                     */

                    Double lossrate = minKey;
                    boolean calStepFlag = true;
                    Double maxKey = 0.0;
                    Integer count = 1;
                    while (true){
                        log.info("第二步计算irr首次小于目标irr的风险损失，第：{} 次计算，",count);
                        count++;
                        lossrate+=step;
                        productCalculateRangeDto.getProductCalBasicParamDto().setLossRate(lossrate);
                        Result riskLossStepResult = calculateIrr(productCalculateRangeDto);
                        if(riskLossStepResult.getCode().equals(SUCCESS.getCode())){
                            ProductCalResultRangeDto productCalResultRangeDto = (ProductCalResultRangeDto)riskLossStepResult.getData();
                            Double resultIrr;
                            /**
                             * 如果是联合贷反算使用的是综合IRR
                             */
                            if(productCalResultRangeDto.getResultIntegrativeIrr()!=null){
                                resultIrr = productCalResultRangeDto.getResultIntegrativeIrr();
                            }else{
                                resultIrr = productCalResultRangeDto.getResultIrr();
                            }
                            ProductCalRiskLossReturnDetailDto productCalRiskLossReturnDetailDto = new ProductCalRiskLossReturnDetailDto();
                            productCalRiskLossReturnDetailDto.setLossRate(lossrate);
                            productCalRiskLossReturnDetailDto.setIrr(resultIrr);
                            productCalRiskLossReturnDetailDto.setTimes(count);
                            productCalRiskLossReturnDetailDtoList.add(productCalRiskLossReturnDetailDto);
                            log.info("风险损失：{}，对应的irr：{}",lossrate,resultIrr);
                            if(resultIrr<targetIrr){
                                maxKey = lossrate;
                                break;
                            }else if(resultIrr.equals(targetIrr)){
                                productCalRiskLossReturnDto.setCode(SUCCESS.getCode());
                                productCalRiskLossReturnDto.setId(productCalRiskLossCalBodyDto.getId());
                                productCalRiskLossReturnDto.setProductCalRiskLossReturnDetailDtoList(productCalRiskLossReturnDetailDtoList);
                                productCalRiskLossReturnDtoList.add(productCalRiskLossReturnDto);
                                calStepFlag = false;
                                break;
                            }else{
                                if(count >= lossRateCalMaxTimes){
                                    productCalRiskLossReturnDto.setCode(FAIL.getCode());
                                    productCalRiskLossReturnDto.setMessage("测算次数超过配置最大测算次数："+lossRateCalMaxTimes);
                                    productCalRiskLossReturnDto.setId(productCalRiskLossCalBodyDto.getId());
                                    productCalRiskLossReturnDtoList.add(productCalRiskLossReturnDto);
                                    calStepFlag = false;
                                    break;
                                }

                                minKey = lossrate;
                                continue;
                            }
                        }else{
                            productCalRiskLossReturnDto.setCode(FAIL.getCode());
                            productCalRiskLossReturnDto.setMessage(riskLossStepResult.getMessage());
                            productCalRiskLossReturnDto.setId(productCalRiskLossCalBodyDto.getId());
                            productCalRiskLossReturnDtoList.add(productCalRiskLossReturnDto);
                            calStepFlag = false;
                            break;
                        }
                    }

                    if(calStepFlag == false){
                        continue;
                    }

                    /**
                     * 拿到minKey和maxKey 使用二分法得到目标irr的lossrate
                     */
                    boolean calMidFlag = true;
                    Double midKey = 0.0;
                    while (minKey <= maxKey) {
                        log.info("第三步二分法计算目标irr对应的lossrate，第{}次计算",count);
                        midKey = (BigDecimal.valueOf(minKey).add(BigDecimal.valueOf(maxKey))).divide(BigDecimal.valueOf(2)).setScale(5, BigDecimal.ROUND_HALF_UP).doubleValue();
                        count++;
                        productCalculateRangeDto.getProductCalBasicParamDto().setLossRate(midKey);
                        Result riskLossMidResult = calculateIrr(productCalculateRangeDto);

                        if(riskLossMidResult.getCode().equals(SUCCESS.getCode())){
                            ProductCalResultRangeDto productCalResultRangeDto = (ProductCalResultRangeDto)riskLossMidResult.getData();
                            Double resultIrr;

                            /**
                             * 如果是联合贷反算使用的是综合IRR
                             */
                            if(productCalResultRangeDto.getResultIntegrativeIrr()!=null){
                                resultIrr = productCalResultRangeDto.getResultIntegrativeIrr();
                            }else{
                                resultIrr = productCalResultRangeDto.getResultIrr();
                            }

                            ProductCalRiskLossReturnDetailDto productCalRiskLossReturnDetailDto = new ProductCalRiskLossReturnDetailDto();
                            productCalRiskLossReturnDetailDto.setLossRate(midKey);
                            productCalRiskLossReturnDetailDto.setIrr(resultIrr);
                            productCalRiskLossReturnDetailDto.setTimes(count);
                            productCalRiskLossReturnDetailDtoList.add(productCalRiskLossReturnDetailDto);
                            log.info("风险损失二分法中间值：{}，对应的irr为：{}",midKey,resultIrr);
                            if (resultIrr.equals(targetIrr)) {
                                break;
                            } else if (resultIrr < targetIrr) {
                                maxKey = midKey;
                            } else {
                                minKey = midKey;
                            }

                            if(count >= lossRateCalMaxTimes){
                                productCalRiskLossReturnDto.setCode(FAIL.getCode());
                                productCalRiskLossReturnDto.setMessage("测算次数超过配置最大测算次数："+lossRateCalMaxTimes);
                                productCalRiskLossReturnDto.setId(productCalRiskLossCalBodyDto.getId());
                                productCalRiskLossReturnDtoList.add(productCalRiskLossReturnDto);
                                calMidFlag = false;
                                break;
                            }

                        }else{
                            productCalRiskLossReturnDto.setCode(FAIL.getCode());
                            productCalRiskLossReturnDto.setMessage(riskLossMidResult.getMessage());
                            productCalRiskLossReturnDto.setId(productCalRiskLossCalBodyDto.getId());
                            productCalRiskLossReturnDtoList.add(productCalRiskLossReturnDto);
                            calMidFlag = false;
                            break;
                        }
                    }

                    if(calMidFlag == false){
                        continue;
                    }
                    productCalRiskLossReturnDto.setCode(SUCCESS.getCode());
                    productCalRiskLossReturnDto.setId(productCalRiskLossCalBodyDto.getId());
                    productCalRiskLossReturnDto.setProductCalRiskLossReturnDetailDtoList(productCalRiskLossReturnDetailDtoList);
                    productCalRiskLossReturnDtoList.add(productCalRiskLossReturnDto);

                }
            }
            log.info("result is {}",productCalRiskLossReturnDtoList);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productCalRiskLossReturnDtoList);
            log.info("getRiskLossTolerate end");
        }catch(Exception e){
            log.error("getRiskLossTolerate error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("getRiskLossTolerate error");
        }
        return result;

    }

    public boolean dealZeroLossRateIrr(Double minKey,Double targetIrr,ProductCalculateRangeDto productCalculateRangeDto,ProductCalRiskLossReturnDto productCalRiskLossReturnDto,List<ProductCalRiskLossReturnDto> productCalRiskLossReturnDtoList,List<ProductCalRiskLossReturnDetailDto> productCalRiskLossReturnDetailDtoList,ProductCalRiskLossCalBodyDto productCalRiskLossCalBodyDto){
        log.info("第一步，计算风险损失为0的时候的irr");
        productCalculateRangeDto.getProductCalBasicParamDto().setLossRate(minKey);
        Double zeroResultIrr = 0.0;
        Result riskLossResult = calculateIrr(productCalculateRangeDto);
        if(riskLossResult.getCode().equals(SUCCESS.getCode())){
            ProductCalResultRangeDto productCalResultRangeDto = (ProductCalResultRangeDto)riskLossResult.getData();
            /**
             * 如果是联合贷反算使用的是综合IRR
             */
            if(productCalResultRangeDto.getResultIntegrativeIrr()!=null){
                zeroResultIrr = productCalResultRangeDto.getResultIntegrativeIrr();
            }else{
                zeroResultIrr = productCalResultRangeDto.getResultIrr();
            }

            log.info("风险损失为0的irr：{}",zeroResultIrr);

            ProductCalRiskLossReturnDetailDto productCalRiskLossReturnDetailDto = new ProductCalRiskLossReturnDetailDto();
            productCalRiskLossReturnDetailDto.setLossRate(minKey);
            productCalRiskLossReturnDetailDto.setIrr(zeroResultIrr);
            productCalRiskLossReturnDetailDto.setTimes(1);
            productCalRiskLossReturnDetailDtoList.add(productCalRiskLossReturnDetailDto);

            if(zeroResultIrr<targetIrr){
                productCalRiskLossReturnDto.setCode(FAIL.getCode());
                productCalRiskLossReturnDto.setMessage("计算失败，风险损失率为0的时候irr为"+zeroResultIrr+"，小于"+targetIrr);
                productCalRiskLossReturnDto.setId(productCalRiskLossCalBodyDto.getId());
                productCalRiskLossReturnDtoList.add(productCalRiskLossReturnDto);
                return false;
            }else if(zeroResultIrr.equals(targetIrr)){
                productCalRiskLossReturnDto.setCode(SUCCESS.getCode());
                productCalRiskLossReturnDto.setId(productCalRiskLossCalBodyDto.getId());
                productCalRiskLossReturnDto.setProductCalRiskLossReturnDetailDtoList(productCalRiskLossReturnDetailDtoList);
                productCalRiskLossReturnDtoList.add(productCalRiskLossReturnDto);
                return false;
            }
        }else{
            productCalRiskLossReturnDto.setCode(FAIL.getCode());
            productCalRiskLossReturnDto.setMessage(riskLossResult.getMessage());
            productCalRiskLossReturnDto.setId(productCalRiskLossCalBodyDto.getId());
            productCalRiskLossReturnDtoList.add(productCalRiskLossReturnDto);
            return false;
        }
        return true;
    }

    public Result getGroupWeightingIrr(List<ProductCalculateGroupDto> productCalculateGroupDtoList,@CurrentUser User user){
        Result result = new Result();
        try{
            long startTime = System.currentTimeMillis();
            log.info("批量计算IRR/方案提案IRR测算开始，用户：{}",user.getName());
            List<ProductCalGroupWeightingIrrDto> productCalGroupWeightingIrrDtoList = new ArrayList<>();
            BigDecimal groupWeightingIrr = new BigDecimal(0.0);
            boolean groupIrrFlag = false;
            for (ProductCalculateGroupDto productCalculateGroupDto : productCalculateGroupDtoList) {
                List<ProductCalculateRangeDto> productCalculateRangeDtoList = productCalculateGroupDto.getProductCalculateRangeDtoList();
                Double groupProportion = productCalculateGroupDto.getGroupProportion();
                if(groupProportion != null){
                    groupIrrFlag = true;
                }
                String groupName= productCalculateGroupDto.getGroupName();

                ProductCalGroupWeightingIrrDto productCalGroupWeightingIrrDto = new ProductCalGroupWeightingIrrDto();
                productCalGroupWeightingIrrDto.setGroupName(groupName);
                productCalGroupWeightingIrrDto.setGroupProportion(groupProportion);
                List<ProductCalResultRangeDto> productCalResultRangeDtoList = new ArrayList<>();
                if(productCalculateRangeDtoList!=null && productCalculateRangeDtoList.size()>0){
                    int num = 1;
                    BigDecimal totalWeightingIrr = new BigDecimal(0.0);
                    for (ProductCalculateRangeDto productCalculateRangeDto : productCalculateRangeDtoList) {
                        Result resultIrr = calculateIrr(productCalculateRangeDto);
                        if(resultIrr.getCode() != 200){
                            result.setCode(resultIrr.getCode());
                            result.setMessage("第"+num+"条计算失败，"+resultIrr.getMessage());
                            return result;
                        }
                        ProductCalResultRangeDto productCalResultRangeDto = (ProductCalResultRangeDto)resultIrr.getData();
                        if(productCalResultRangeDto.getWeightingIrr() != null){
                            /**
                             * 如果是联合贷，使用综合Irr计算加权Irr，否则使用resultIrr计算加权irr
                             */
                            Double irr;
                            if(productCalculateRangeDto.getProductCalUnionLoanParamDto() != null && productCalculateRangeDto.getProductCalUnionLoanParamDto().getBankLoanRatio() != null){
                                irr = productCalResultRangeDto.getResultIntegrativeIrr();
                            }else{
                                irr = productCalResultRangeDto.getResultIrr();
                            }
                            totalWeightingIrr = totalWeightingIrr.add(BigDecimal.valueOf(BigDecimal.valueOf(irr).multiply(BigDecimal.valueOf(productCalResultRangeDto.getContractProportion())).doubleValue()));
                        }
                        productCalResultRangeDtoList.add(productCalResultRangeDto);
                        num++;
                    }
                    Double weightingIrr = productCalculateService.dealScale(2,totalWeightingIrr.doubleValue());
                    List<ProductCalResultRangeDto> collect = productCalResultRangeDtoList.stream().map(productCalResultRangeDto -> {
                        productCalResultRangeDto.setWeightingIrr(weightingIrr);
                        return productCalResultRangeDto;
                    }).collect(Collectors.toList());
                    productCalGroupWeightingIrrDto.setProductCalResultRangeDtoList(collect);

                    if(groupIrrFlag){
                        groupWeightingIrr = groupWeightingIrr.add(BigDecimal.valueOf(BigDecimal.valueOf(weightingIrr).multiply(BigDecimal.valueOf(groupProportion)).doubleValue()));
                    }
                }
                productCalGroupWeightingIrrDtoList.add(productCalGroupWeightingIrrDto);
            }
            if(groupIrrFlag){
                Double groupWeightingIrrDou = productCalculateService.dealScale(2,groupWeightingIrr.doubleValue());
                productCalGroupWeightingIrrDtoList = productCalGroupWeightingIrrDtoList.stream().map(productCalGroupWeightingIrrDto -> {
                    productCalGroupWeightingIrrDto.setGroupWeightingIrr(groupWeightingIrrDou);
                    return productCalGroupWeightingIrrDto;
                }).collect(Collectors.toList());
            }
            log.info("批量计算IRR/方案提案IRR测算结束，用户：{},共计耗时：{}ms",user.getName(),System.currentTimeMillis()-startTime);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productCalGroupWeightingIrrDtoList);
        }catch (Exception e){
            log.error("getGroupWeightingAvgIrr error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("getGroupWeightingAvgIrr error");
        }
        return result;
    }
    public Result getAmountProfit(List<ProductCalculateRangeDto> productCalculateRangeDtoList){
        Result result = new Result();
        List<ProductCalProfitCollectDto> productCalProfitCollectDtoListResult = new ArrayList<>();
        List<ProductCalProfitCollectDto> productCalProfitCollectDtoListReturn = new ArrayList<>();
        if(productCalculateRangeDtoList!=null && productCalculateRangeDtoList.size()>0){
            int num = 1;
            for (ProductCalculateRangeDto productCalculateRangeDto : productCalculateRangeDtoList) {
                Result resultProfit = calculateIrr(productCalculateRangeDto);
                if(resultProfit.getCode() != 200){
                    result.setCode(resultProfit.getCode());
                    result.setMessage("第"+num+"条计算失败，"+resultProfit.getMessage());
                    return result;
                }
                ProductCalProfitDto productCalProfitDto = (ProductCalProfitDto)resultProfit.getData();
                productCalProfitCollectDtoListResult.addAll(productCalProfitDto.getProductCalProfitCollectDtoList());
                num++;
            }
        }
        Map<Integer, List<ProductCalProfitCollectDto>> collect = productCalProfitCollectDtoListResult.stream().collect(Collectors.groupingBy(ProductCalProfitCollectDto::getPutMonth));
        Set<Integer> putMonthSet = collect.keySet();
        for (Integer putMonth : putMonthSet) {
            List<ProductCalProfitCollectDto> productCalProfitCollectDtoList = collect.get(putMonth);
            Map<String, List<ProductCalProfitCollectDto>> collectDueYear = productCalProfitCollectDtoList.stream().collect(Collectors.groupingBy(ProductCalProfitCollectDto::getDueYear));
            Set<String> dueYearSet = collectDueYear.keySet();
            for (String dueYear : dueYearSet) {
                ProductCalProfitCollectDto productCalProfitCollectDto = new ProductCalProfitCollectDto();
                productCalProfitCollectDto.setPutMonth(putMonth);
                productCalProfitCollectDto.setDueYear(dueYear);
                productCalProfitCollectDto.setProfitMoney(collectDueYear.get(dueYear).stream().mapToDouble(ProductCalProfitCollectDto::getProfitMoney).sum());
                productCalProfitCollectDtoListReturn.add(productCalProfitCollectDto);
            }
        }
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        result.setData(productCalProfitCollectDtoListReturn);
        return result;
    }

    /**
     * 生成IRR对应word/pdf文档，并支持下载
     * @return
     */
    public void generateIrrWord(String id, HttpServletResponse response){
        try{
            Integer column = 15;
            ProductProposalEntity productProposalEntity = productProposalDao.selectById(id);
            /**
             * 是否加权 1-加权   0-不加权
             */
            String name = productProposalEntity.getName();
            /**
             * 是否组间加权
             */
            //是否组间加权
            Integer weightedBetweenGroupsOrNot = productProposalEntity.getWeightedBetweenGroupsOrNot();
            if(weightedBetweenGroupsOrNot == 1){
                column+=3;
            }
            String productProposalTemplateGroupJson = productProposalEntity.getProductProposalTemplateGroupJson();

            String productElementsDescription = productProposalEntity.getProductElementsDescription();
            String riskControlStrategyDescription = productProposalEntity.getRiskControlStrategyDescription();
            String businessPolicyDescription = productProposalEntity.getBusinessPolicyDescription();
            String instruction1 = "1.基础数据："+productElementsDescription+"<w:br />";
            String instruction2 = "2.商务返利："+businessPolicyDescription+"<w:br />";
            String instruction3 = "3.风险损失："+riskControlStrategyDescription+"<w:br />";
            String instruction4 = "4.其他成本：依据各部门联合提供的其他成本数据测算";
            String instruction = instruction1+instruction2+instruction3+instruction4;
            List<ProductProposalGroupTemplateJsonDto> productProposalGroupTemplateJsonDtos = GsonUtil.jsonToList(productProposalTemplateGroupJson, ProductProposalGroupTemplateJsonDto.class);

            Map<String,Object> map = new HashMap<>();
            String templateFileName = "irrCalResult.ftl";
            List<ProductProposalResultWordDto> productProposalResultWordDtoList = new ArrayList<>();
            int index = 1;
            String firstEleName = "";
            List<String> addElementNames = new ArrayList<>();
            Integer weightedComputationOrNot = 0;
            Integer groupSize = 1;

            if(productProposalGroupTemplateJsonDtos!=null && productProposalGroupTemplateJsonDtos.size()>0){
                groupSize = productProposalGroupTemplateJsonDtos.size();
                if(groupSize>1){
                    column+=1;
                }
                for (ProductProposalGroupTemplateJsonDto productProposalGroupTemplateJsonDto : productProposalGroupTemplateJsonDtos) {
                    Double groupWeightingIrr = productProposalGroupTemplateJsonDto.getGroupWeightingIrr();
                    String groupName = productProposalGroupTemplateJsonDto.getGroupName();
                    String weightedBetweenGroupsRatio = productProposalGroupTemplateJsonDto.getWeightedBetweenGroupsRatio();
                    //是否组内加权
                    Integer weightedOrNot = productProposalGroupTemplateJsonDto.getWeightedOrNot();
                    if(weightedOrNot == 1){
                        weightedComputationOrNot = 1;
                    }
                    List<ProductProposalTemplateJsonDto> productProposalTemplateJsonDtos = productProposalGroupTemplateJsonDto.getProductProposalTemplateList();
                    for (ProductProposalTemplateJsonDto productProposalTemplateJsonDto : productProposalTemplateJsonDtos) {
                        List<String> addElementValues = new ArrayList<>();
                        ProductProposalResultWordDto productProposalResultWordDto = new ProductProposalResultWordDto();
                        productProposalResultWordDto.setGroupName(groupName);
                        productProposalResultWordDto.setGroupWeightingIrr(groupWeightingIrr+"%");
                        productProposalResultWordDto.setGroupProportion(weightedBetweenGroupsRatio+"%");
                        if(StringUtils.hasText(productProposalTemplateJsonDto.getBrand())){
                            firstEleName = "品牌";
                            productProposalResultWordDto.setFirstEleValue(productProposalTemplateJsonDto.getBrand());
                        }else if(StringUtils.hasText(productProposalTemplateJsonDto.getVehicleType())){
                            firstEleName = "车辆类型";
                            productProposalResultWordDto.setFirstEleValue(productProposalTemplateJsonDto.getVehicleType());
                        }
                        Map<String, String> selfDefinedParameter = productProposalTemplateJsonDto.getSelfDefinedParameter();
//                        if(selfDefinedParameter!=null){
//                            Set<String> keySet = selfDefinedParameter.keySet();
//                            for (String key : keySet) {
//                                if(index == 1){
//                                    addElementNames.add(key);
//                                    column = column+1;
//                                }
//                                addElementValues.add(selfDefinedParameter.get(key));
//                            }
//                            productProposalResultWordDto.setAddElementValues(addElementValues);
//                        }
                        productProposalResultWordDto.setLoanMoney(productProposalTemplateJsonDto.getLoanAmount().toString());
                        productProposalResultWordDto.setCustomerInterestRate(productProposalTemplateJsonDto.getCustomerInterestRate().toString()+"%");
                        productProposalResultWordDto.setActualInterestRate(productProposalTemplateJsonDto.getSettledRate().toString()+"%");
                        productProposalResultWordDto.setTimeLimit(String.valueOf(productProposalTemplateJsonDto.getPaymentDays().intValue()));

                        /**
                         * 使用bigDecimal进行计算
                         */
                        BigDecimal baseCalRatio = BigDecimal.valueOf(0.0);
                        Double dealerBasicCommissionRatio = (productProposalTemplateJsonDto.getBaseCommissionRatio()==null?baseCalRatio:BigDecimal.valueOf(productProposalTemplateJsonDto.getBaseCommissionRatio())).add((productProposalTemplateJsonDto.getLadderBonusRatio()==null?baseCalRatio:BigDecimal.valueOf(productProposalTemplateJsonDto.getLadderBonusRatio()))).add((productProposalTemplateJsonDto.getPromotionBonusProportion()==null?baseCalRatio:BigDecimal.valueOf(productProposalTemplateJsonDto.getPromotionBonusProportion()))).doubleValue();
                        productProposalResultWordDto.setDealerBasicCommissionRatio(dealerBasicCommissionRatio+"%");
                        productProposalResultWordDto.setLossRate(productProposalTemplateJsonDto.getRiskLossRate().toString()+"%");
                        productProposalResultWordDto.setResultIrr(productProposalTemplateJsonDto.getIrr()==null?null:productProposalTemplateJsonDto.getIrr().toString()+"%");
                        productProposalResultWordDto.setResultRoa(productProposalTemplateJsonDto.getRoa()==null?null:productProposalTemplateJsonDto.getRoa().toString()+"%");
                        productProposalResultWordDto.setResultNetProfitMoney(productProposalTemplateJsonDto.getNetMargin()==null?null:productProposalTemplateJsonDto.getNetMargin().toString());
                        productProposalResultWordDto.setContractProportion(productProposalTemplateJsonDto.getContractsProportion()==null?null:productProposalTemplateJsonDto.getContractsProportion().toString()+"%");
                        productProposalResultWordDto.setWeightingIrr((productProposalTemplateJsonDto.getWeightedIrr()==null?null:productProposalTemplateJsonDto.getWeightedIrr().toString())+"%");
                        productProposalResultWordDto.setSpecialReward(productProposalTemplateJsonDto.getPersonalReward()==null?null:productProposalTemplateJsonDto.getPersonalReward().toString());
                        productProposalResultWordDto.setIndex(index);

                        /**
                         * 查询模板对应的特殊奖励、固定成本
                         */
                        Long templateId = productProposalTemplateJsonDto.getIrrTemplateId();
                        ProductCalBasicParamDto productCalBasicParamDto = new ProductCalBasicParamDto();
                        QueryWrapper queryWrapperBasicParam = new QueryWrapper();
                        queryWrapperBasicParam.eq("basic_info_id",templateId);
                        queryWrapperBasicParam.eq("item","fixed_cost");
                        List<ProductCalBasicParamEntity> productCalBasicParamEntityList = productCalBasicParamDao.selectList(queryWrapperBasicParam);
                        Double reward = 0.0;
                        Double otherCost = 0.0;
                        if(productCalBasicParamEntityList!=null&&productCalBasicParamEntityList.size()>0){
                            otherCost = Math.abs(Double.valueOf(productCalBasicParamEntityList.get(0).getValue()));
                        }
                        productProposalResultWordDto.setFixedCost(otherCost==null?"0":otherCost.toString());
                        productProposalResultWordDtoList.add(productProposalResultWordDto);
                        index++;
                    }
                }
            }

            for(int i =0;i<productProposalResultWordDtoList.size();i++){
                productProposalResultWordDtoList.get(i).getStrMap().put("pre","0");
                productProposalResultWordDtoList.get(i).getStrMap().put("now","0");
                productProposalResultWordDtoList.get(i).getStrMap().put("next","0");
                if(i<productProposalResultWordDtoList.size()-1){
                    //当前和下一个相同就合并，将now设置为1
                    if(groupSize>1&&productProposalResultWordDtoList.get(i).getGroupName().equals(productProposalResultWordDtoList.get(i+1).getGroupName())){
                        productProposalResultWordDtoList.get(i).getStrMap().put("now","1");
                    }
                }
            }

            for(int i=0;i<productProposalResultWordDtoList.size();i++){
                //保存他的前一个元素是否需要合并
                if(i>0){
                    productProposalResultWordDtoList.get(i).getStrMap().put("pre",productProposalResultWordDtoList.get(i-1).getStrMap().get("now"));
                }
                //保存他的后一个元素是否需要合并
                if(i<productProposalResultWordDtoList.size()-1){
                    productProposalResultWordDtoList.get(i).getStrMap().put("next",productProposalResultWordDtoList.get(i+1).getStrMap().get("now"));
                }
            }

            map.put("tatalColum",column);
            map.put("addElementNames",addElementNames);
            map.put("firstEleName",firstEleName);
            map.put("values",productProposalResultWordDtoList);
            map.put("productsName",name+"IRR测算结果");
            map.put("instructions",instruction);
            map.put("weightedComputationOrNot",weightedComputationOrNot);
            map.put("weightedBetweenGroupsOrNot",weightedBetweenGroupsOrNot);
            map.put("groupSize",groupSize);

            String fileName = name+"IRR测算结果";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            fileName = fileName+".doc";
            response.setCharacterEncoding("UTF-8");
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);
            OutputStream outputStream = response.getOutputStream();
            WordGeneratorUtil.createDoc(map,templateFileName,outputStream);
            outputStream.flush();
            outputStream.close();
        }catch(Exception e){
            log.error("generateIrrWord error",e);
        }
    }

    public void downLoadIrrBatchCalTemplate(String sourceType, HttpServletResponse response){
        try{
            String filePath = "";
            String fileName = "";
            if(DownLoadTemplateTypeEnum.BP_IRR_BATCH_CAL_IMPORT_TEMPLATE.type().equals(sourceType)){
                filePath = irrBatchCalTemplatePathBp;
                fileName = irrBatchCalTemplateNameBp;
            }else if(DownLoadTemplateTypeEnum.QP_IRR_BATCH_CAL_IMPORT_TEMPLATE.type().equals(sourceType)){
                filePath = irrBatchCalTemplatePathQp;
                fileName = irrBatchCalTemplateNameQp;
            }else if(DownLoadTemplateTypeEnum.ESC_IRR_BATCH_CAL_IMPORT_TEMPLATE.type().equals(sourceType)){
                filePath = irrBatchCalTemplatePathEsc;
                fileName = irrBatchCalTemplateNameEsc;
            }else if(DownLoadTemplateTypeEnum.PROPOSAL_CAL_IMPORT_TEMPLATE.type().equals(sourceType)){
                filePath = proposalCalTemplatePath;
                fileName = proposalCalTemplateName;
            }else if(DownLoadTemplateTypeEnum.PROFIT_CAL_SALES_IMPORT_TEMPLATE.type().equals(sourceType)){
                filePath = profitCalSalesTemplatePath;
                fileName = profitCalSalesTemplateName;
            }else if(DownLoadTemplateTypeEnum.EARLY_SQUARE_RATIO_IMPORT_TEMPLATE.type().equals(sourceType)){
                filePath = earlySquareRatioTemplatePath;
                fileName = earlySquareRatioTemplateName;
            }

            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);
            ftpService.downloadFile(filePath, response.getOutputStream());
        }catch(Exception e){
            log.error("downLoadIrrBatchCalTemplate error",e);
        }
    }

    public Result upLoadIrrBatchCalFile(String sourceType,MultipartFile file){
        Result result = new Result();
        try{
            /**
             * 获取对应的处理类的名称
             */
            String className = DownLoadTemplateTypeEnum.getDownLoadTemplateTypeEnumByType(sourceType).className();
            Class classImport = Class.forName(className);

            List list = new ArrayList<>();
            EasyExcel.read(file.getInputStream(), new ProductCalImportListener(classImport,list)).headRowNumber(1).sheet(0).doRead();
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(list);
        }catch (Exception e){
            log.error("upLoadIrrBatchCalFile error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("excel解析失败："+e.getMessage()+"");
        }
        return result;
    }
}
