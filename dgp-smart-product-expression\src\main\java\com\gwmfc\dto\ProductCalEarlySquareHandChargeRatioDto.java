package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalEarlySquareBasicCommissionRebateRatioDto
 * @Description
 * @Date 2024/6/25 14:04
 */
@Data
@ApiModel(value = "提前结清手续费比例")
public class ProductCalEarlySquareHandChargeRatioDto {
    @ApiModelProperty("结清期次")
    private Integer payoutRentalId;

    @ApiModelProperty("提前结清手续费比例")
    private Double earlySquareHandChargeRatio;
}
