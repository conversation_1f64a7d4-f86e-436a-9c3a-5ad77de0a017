package com.gwmfc.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * 流通协会商用车销量
 *
 * @Date: 2024/01/31
 * @Author: z<PERSON><PERSON>yu
 */

@Getter
@AllArgsConstructor
public enum VehicleSalesTableEnum {

    COMMERCIAL_VEHICLE_SALES {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("update_month", "更新月份");
            headMap.put("data_date", "数据日期");
            headMap.put("commercial_vehicle_type", "商用车类型");
            headMap.put("sales", "销量（万辆）");
            headMap.put("image_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    },
    TRUCK_SALES {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("update_month", "更新月份");
            headMap.put("data_date", "数据日期");
            headMap.put("commercial_vehicle_type", "车辆类型");
            headMap.put("sales", "销量（辆）");
            headMap.put("image_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    },
    NEW_ENERGY_VEHICLE_SALES {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("update_month", "更新月份");
            headMap.put("data_date", "数据日期");
            headMap.put("commercial_vehicle_type", "商用车类型");
            headMap.put("sales", "销量（辆）");
            headMap.put("penetration_rate", "渗透率");
            headMap.put("image_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    },
    PASSENGER_BUS_MANUFACTURER_SALES {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("update_month", "更新月份");
            headMap.put("data_date", "数据日期");
            headMap.put("sales_rank", "排行");
            headMap.put("manufacturer", "厂商");
            headMap.put("sales", "销量（辆）");
            headMap.put("image_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    },
    TRUCK_MANUFACTURER_SALES {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("update_month", "更新月份");
            headMap.put("data_date", "数据日期");
            headMap.put("manufacturer", "厂商");
            headMap.put("sales", "销量（万辆）");
            headMap.put("sales_rank", "排行");
            headMap.put("image_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    };

    public abstract void getHeader(Map<String, String> headMap);
}
