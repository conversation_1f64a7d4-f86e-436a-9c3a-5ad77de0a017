package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductBusinessLineDto;
import com.gwmfc.dto.ProductLineDto;
import com.gwmfc.entity.ProductBusinessLineEntity;
import com.gwmfc.entity.ProductLineEntity;
import com.gwmfc.service.ProductMatrixService;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Classname ProductMatrixController
 * @Description 产品矩阵controller
 * @Date 2024/8/12 9:33
 */
@Api(tags = "产品矩阵")
@RestController
@RequestMapping("/product/matrix")
@Slf4j
public class ProductMatrixController {
    @Autowired
    private ProductMatrixService productMatrixService;

    @ApiOperation("业务条线列表")
    @PostMapping("/businessLine/getBusinessLineList")
    public Result getBusinessLineList(@RequestBody PageForm<ProductBusinessLineDto> pageFormReq) {
        return productMatrixService.getBusinessLineList(pageFormReq.getParam(), pageFormReq.getCurrent(), pageFormReq.getSize());
    }

    @ApiOperation("业务条线保存")
    @PostMapping("/businessLine/save")
    public Result saveBusinessLine(@RequestBody ProductBusinessLineDto productBusinessLineDto, @CurrentUser User user){
        return productMatrixService.saveBusinessLine(productBusinessLineDto,user);
    }

    @ApiOperation("业务条线删除")
    @GetMapping("/businessLine/delete")
    public Result deleteBusinessLine(@RequestParam(value = "id") Integer id){
        return productMatrixService.deleteBusinessLine(id);
    }

    @ApiOperation("业务条线更新")
    @PostMapping("/businessLine/update")
    public Result updateBusinessLine(@RequestBody ProductBusinessLineEntity productBusinessLineEntity, @CurrentUser User user){
        return productMatrixService.updateBusinessLine(productBusinessLineEntity,user);
    }

    @ApiOperation("业务条线详情")
    @GetMapping("/businessLine/getBusinessLineDetail")
    public Result getBusinessLineDetail(Integer id){
        return productMatrixService.getBusinessLineDetail(id);
    }

    @ApiOperation("业务条线一键更新")
    @GetMapping("/businessLine/oneClickUpdate")
    public Result oneClickUpdateBusinessLine(@CurrentUser User user){
        return productMatrixService.oneClickUpdateBusinessLine(user);
    }

    @ApiOperation("产品条线列表")
    @PostMapping("/productLine/getProductLineList")
    public Result getProductLineList(@RequestBody PageForm<ProductLineDto> pageFormReq){
        return productMatrixService.getProductLineList(pageFormReq.getParam(), pageFormReq.getCurrent(), pageFormReq.getSize());
    }

    @ApiOperation("产品条线保存")
    @PostMapping("/productLine/save")
    public Result saveProductLine(@RequestBody ProductLineDto productLineDto, @CurrentUser User user){
        return productMatrixService.saveProductLine(productLineDto,user);
    }

    @ApiOperation("产品条线删除")
    @GetMapping("/productLine/delete")
    public Result deleteProductLine(@RequestParam(value = "id") Integer id){
        return productMatrixService.deleteProductLine(id);
    }

    @ApiOperation("产品条线更新")
    @PostMapping("/productLine/update")
    public Result updateProductLine(@RequestBody ProductLineDto productLineDto, @CurrentUser User user){
        return productMatrixService.updateProductLine(productLineDto,user);
    }

    @ApiOperation("产品条线详情")
    @GetMapping("/productLine/getProductLineDetail")
    public Result getProductLineDetail(Integer id){
        return productMatrixService.getProductLineDetail(id);
    }

    @ApiOperation("获取业务线排序")
    @GetMapping("/businessLine/getBusinessLineSort")
    public Result getBusinessLineSort(){
        return productMatrixService.getBusinessLineSort();
    }

    @ApiOperation("产品矩阵导出")
    @GetMapping("/productMatrix/exportData")
    public void exportCalData(@RequestParam(required = false) String bussType,HttpServletResponse response){
        productMatrixService.exportCalData(bussType,response);
    }
}
