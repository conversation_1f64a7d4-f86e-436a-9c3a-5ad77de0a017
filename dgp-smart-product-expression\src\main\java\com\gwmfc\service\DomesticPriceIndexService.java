package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.gwmfc.dao.MonthConsumptionIndexDao;
import com.gwmfc.dao.YearConsumptionIndexDao;
import com.gwmfc.entity.data.MonthConsumptionIndexEntity;
import com.gwmfc.entity.data.YearConsumptionIndexEntity;
import com.gwmfc.util.GsonUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2024年03月07日 17:13
 */
@Service
public class DomesticPriceIndexService {
    @Resource
    private MonthConsumptionIndexDao monthConsumptionIndexDao;
    @Resource
    private YearConsumptionIndexDao yearConsumptionIndexDao;
    @Resource
    private RestTemplate restTemplate;

    public void catchDomesticPriceIndex(Integer frequency, String tableName, String userName) {
        if (tableName.equals("month_consumption_index")) {
            catchMonthDomesticPriceIndex(frequency, userName);
        } else if (tableName.equals("year_consumption_index")) {
            catchYearDomesticPriceIndex(frequency, userName);
        }
    }

    public void catchMonthDomesticPriceIndex(Integer frequency, String userName) {
        Map<String, String> map = new HashMap<>();
        // 添加键值对
        map.put("setSamePeriodLastYearConsumerPriceSubIndex", "A010201");
        map.put("setLastMonthConsumerPriceSubIndex", "A010301");
        map.put("setSameMonthLastYearProducerPriceIndex", "A010801");
        map.put("setSamePeriodLastYearProducerPriceIndex", "A010804");
        map.put("setLastMonthProducerPriceIndex", "A010807");

        JsonArray jsonArray0 = new JsonArray();
        JsonObject jsonObject0 = new JsonObject();
        jsonObject0.addProperty("wdcode","zb");
        jsonObject0.addProperty("valuecode","A010101");
        JsonObject jsonObject1 = new JsonObject();
        jsonObject1.addProperty("wdcode","sj");
        jsonObject1.addProperty("valuecode","LAST"+frequency);
        jsonArray0.add(jsonObject0);
        jsonArray0.add(jsonObject1);
        String json0 = jsonArray0.toString();

        List<HttpMessageConverter<?>> messageConverters0 = restTemplate.getMessageConverters();
        for (int i = 0; i < messageConverters0.size(); i++) {
            HttpMessageConverter<?> httpMessageConverter = messageConverters0.get(i);
            if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
                messageConverters0.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
            }
        }
        ResponseEntity<String> strbody0=restTemplate.getForEntity("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds={dfwds}&k1="+System.currentTimeMillis()+"&h=1",String.class,json0);
        ABo aBo0 = GsonUtil.gsonToBean(strbody0.getBody(),ABo.class);
        List<Datanode> datanodeList0 = aBo0.getReturndata().getDatanodes();
        Integer monthNum = aBo0.getReturndata().getWdnodes().get(1).getNodes().size();
        ArrayList arrayList1 = new ArrayList<MonthConsumptionIndexEntity>(monthNum);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        for(int i=0; i<monthNum; i++) {
            MonthConsumptionIndexEntity monthConsumptionIndexEntity = new MonthConsumptionIndexEntity();
            // 获取Person类的Class对象
            Class<?> clazz = monthConsumptionIndexEntity.getClass();
            Datanode datanode = datanodeList0.get(i);
            String dateStr = datanode.getWds().get(1).getValuecode();
            dateStr = dateStr.substring(0, 4) + "-" + dateStr.substring(4);
            //判断该月在不在
            if (monthConsumptionIndexDao.selectDataDateExist(dateStr)!=null) {
                continue;
            }
            // 获取Person类的setName方法
            Method setNameMethod = null;
            Method setNameMethod1 = null;
            Method setNameMethod2 = null;
            Method setNameMethod3 = null;
            try {
                setNameMethod = clazz.getMethod("setSameMonthLastYearConsumerPriceSubIndex", String.class);
                // 调用setName方法设置name属性的值
                setNameMethod.invoke(monthConsumptionIndexEntity, datanode.getData().getData());
                setNameMethod1 = clazz.getMethod("setDataDate", String.class);
                // 调用setName方法设置name属性的值
                setNameMethod1.invoke(monthConsumptionIndexEntity, dateStr);
                setNameMethod2 = clazz.getMethod("setCreateUser", String.class);
                // 调用setName方法设置name属性的值
                setNameMethod2.invoke(monthConsumptionIndexEntity, userName);
                setNameMethod3 = clazz.getMethod("setCreateTime", Long.class);
                setNameMethod3.invoke(monthConsumptionIndexEntity, Long.parseLong(dateTimeFormatter.format(LocalDateTime.now())));
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            arrayList1.add(monthConsumptionIndexEntity);
        }

        map.forEach((k,v) -> {
            JsonArray jsonArray = new JsonArray();
            JsonObject jsonObjectZb = new JsonObject();
            jsonObjectZb.addProperty("wdcode","zb");
            jsonObjectZb.addProperty("valuecode", v);
            JsonObject jsonObjectSj = new JsonObject();
            jsonObjectSj.addProperty("wdcode","sj");
            jsonObjectSj.addProperty("valuecode","LAST"+frequency);
            jsonArray.add(jsonObjectZb);
            jsonArray.add(jsonObjectSj);
            String json = jsonArray.toString();

            List<HttpMessageConverter<?>> messageConverters = restTemplate.getMessageConverters();
            for (int i = 0; i < messageConverters.size(); i++) {
                HttpMessageConverter<?> httpMessageConverter = messageConverters.get(i);
                if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
                    messageConverters.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
                }
            }
            ResponseEntity<String> strbody=restTemplate.getForEntity("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds={dfwds}&k1="+System.currentTimeMillis()+"&h=1",String.class,json);
            ABo aBo = GsonUtil.gsonToBean(strbody.getBody(),ABo.class);
            List<Datanode> datanodeList = aBo.getReturndata().getDatanodes();

            for (int i = 0; i< arrayList1.size(); i++) {
                MonthConsumptionIndexEntity monthConsumptionIndexEntity = (MonthConsumptionIndexEntity) arrayList1.get(i);
                // 获取Person类的Class对象
                Class<?> clazz = monthConsumptionIndexEntity.getClass();
                Datanode datanode = datanodeList.get(i);
                // 获取Person类的setName方法
                Method setNameMethod = null;
                try {
                    String dateStr = datanode.getWds().get(1).getValuecode();
                    dateStr = dateStr.substring(0, 4) + "-" + dateStr.substring(4);
                    if (dateStr.equals(monthConsumptionIndexEntity.getDataDate())) {
                        setNameMethod = clazz.getMethod(k, String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod.invoke(monthConsumptionIndexEntity, datanode.getData().getData());
                    }
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }

        });
        arrayList1.forEach(monthConsumptionIndex -> {
            monthConsumptionIndexDao.insert((MonthConsumptionIndexEntity) monthConsumptionIndex);
        });
    }

    public void catchYearDomesticPriceIndex(Integer frequency, String userName) {
        JsonArray jsonArray0 = new JsonArray();
        JsonObject jsonObject0 = new JsonObject();
        jsonObject0.addProperty("wdcode","zb");
        jsonObject0.addProperty("valuecode","A0901");
        JsonObject jsonObject1 = new JsonObject();
        jsonObject1.addProperty("wdcode","sj");
        jsonObject1.addProperty("valuecode","LAST"+frequency);
        jsonArray0.add(jsonObject0);
        jsonArray0.add(jsonObject1);
        String json0 = jsonArray0.toString();

        // 添加键值对
        List<String> property = new ArrayList<>();
        property.add("setConsumerPriceSubIndex");
        property.add("setUrbanConsumerPriceIndex");
        property.add("setRuralConsumerPriceIndex");
        property.add("setRetailPriceIndex");
        property.add("setProducerPriceIndex");
        property.add("setPurchasingPriceIndustrialProducerIndex");
        property.add("setFixedAssetInvestmentPriceIndex");

        List<HttpMessageConverter<?>> messageConverters0 = restTemplate.getMessageConverters();

        for (int i = 0; i < messageConverters0.size(); i++) {
            HttpMessageConverter<?> httpMessageConverter = messageConverters0.get(i);
            if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
                messageConverters0.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
            }
        }
        ResponseEntity<String> strbody0=restTemplate.getForEntity("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgnd&rowcode=sj&colcode=zb&wds=[]&dfwds={dfwds}&k1="+System.currentTimeMillis()+"&h=1",String.class,json0);
        ABo aBo0 = GsonUtil.gsonToBean(strbody0.getBody(),ABo.class);
        List<Datanode> datanodeList0 = aBo0.getReturndata().getDatanodes();
        Integer monthNum = aBo0.getReturndata().getWdnodes().get(1).getNodes().size();
        ArrayList resArrayList = new ArrayList<YearConsumptionIndexEntity>(monthNum);
        for (int i=0;i<monthNum;i++) {
            resArrayList.add(new YearConsumptionIndexEntity());
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        // 集合分片
        List<List<Datanode>> newList = Lists.partition(datanodeList0, monthNum);
        // 打印分片集合
        AtomicInteger j = new AtomicInteger();
        newList.forEach(objectList -> {
            for (int i=0;i<objectList.size();i++) {
                YearConsumptionIndexEntity monthConsumptionIndexEntity = (YearConsumptionIndexEntity) resArrayList.get(i);
                // 获取Person类的Class对象
                Class<?> clazz = monthConsumptionIndexEntity.getClass();
                Method setNameMethod = null;
                Method setNameMethod1 = null;
                Method setNameMethod2 = null;
                Method setNameMethod3 = null;
                try {
                    setNameMethod = clazz.getMethod(property.get(j.get()), String.class);
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
                Datanode datanode = objectList.get(i);
                //判断该月在不在
                if (yearConsumptionIndexDao.selectDataDateExist(datanode.getWds().get(1).getValuecode())!=null) {
                    continue;
                }
                try {
                    if (monthConsumptionIndexEntity.getDataDate() != null) {
                        if (datanode.getWds().get(1).getValuecode().equals(monthConsumptionIndexEntity.getDataDate())) {
                            // 调用setName方法设置name属性的值
                            setNameMethod.invoke(monthConsumptionIndexEntity, datanode.getData().getData());
                        }
                    }
                    if (monthConsumptionIndexEntity.getDataDate() == null) {
                        setNameMethod1 = clazz.getMethod("setDataDate", String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod1.invoke(monthConsumptionIndexEntity, datanode.getWds().get(1).getValuecode());
                        setNameMethod = clazz.getMethod(property.get(j.get()), String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod.invoke(monthConsumptionIndexEntity, datanode.getData().getData());
                        setNameMethod2 = clazz.getMethod("setCreateUser", String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod2.invoke(monthConsumptionIndexEntity, userName);
                        setNameMethod3 = clazz.getMethod("setCreateTime", Long.class);
                        setNameMethod3.invoke(monthConsumptionIndexEntity, Long.parseLong(dateTimeFormatter.format(LocalDateTime.now())));
                    }
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }

            }
            j.getAndIncrement();
        });
        resArrayList.forEach(yearConsumptionIndexEntityTmp -> {
            YearConsumptionIndexEntity yearConsumptionIndexEntity = (YearConsumptionIndexEntity) yearConsumptionIndexEntityTmp;
            if (StringUtils.isNotEmpty(yearConsumptionIndexEntity.getDataDate())) {
                yearConsumptionIndexDao.insert(yearConsumptionIndexEntity);
            }
        });
    }

}
