package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Date: 2023/11/8
 * @Author: zhangxinyu
 */

@Data
@TableName("commercial_vehicle_sales")
@ApiModel(value="商用车数据对象", description="商用车数据，卡车销量，新能源销量，客车厂商销量，卡车厂商销量")
public class VehicleSalesEntity {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "编号")
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "类型")
    @ApiModelProperty(value = "类型(1:商用车销量,2:卡车销量,3:新能源销量,4:客车厂商销量,5:卡车厂商销量)")
    private Integer type;

    @ExcelProperty(value = "更新月份")
    @ApiModelProperty(value = "更新月份")
    @TableFieldMapping(value = "update_month", comment = "更新月份", queryItem = true)
    private String updateMonth;

    @ExcelProperty(value = "数据日期")
    @ApiModelProperty(value = "数据日期")
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty(value = "车辆类型")
    @ApiModelProperty(value = "车辆类型")
    @TableFieldMapping(value = "commercial_vehicle_type", comment = "车辆类型", queryItem = true)
    private String commercialVehicleType;

    @ExcelProperty(value = "厂商")
    @ApiModelProperty(value = "厂商")
    @TableFieldMapping(value = "manufacturer", comment = "厂商", queryItem = true)
    private String manufacturer;

    @ExcelProperty(value = "销量")
    @ApiModelProperty(value = "销量")
    @TableFieldMapping(value = "sales", comment = "销量", queryItem = true)
    private String sales;

    @ExcelProperty(value = "渗透率")
    @ApiModelProperty(value = "渗透率")
    @TableFieldMapping(value = "penetration_rate", comment = "渗透率", queryItem = true)
    private String penetrationRate;

    @ExcelProperty(value = "图片地址")
    @ApiModelProperty(value = "图片地址")
    private String imageUrl;

    @ExcelProperty(value = "排名")
    @ApiModelProperty(value = "排名")
    private String salesRank;

    /**
     * 创建人
     */
    @ExcelIgnore
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    /**
     * 更新人
     */
    @ExcelIgnore
    private String updateUser;

    /**
     * 更新时间
     */
    @ExcelIgnore
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private String updateTime;
}
