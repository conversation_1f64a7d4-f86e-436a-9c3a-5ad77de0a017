package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * 小蜜蜂申请数据
 *
 * 数据源：邮箱
 *
 * 数据格式：excel
 *
 * 更新频率：每天
 *
 * 获取方式：自动
 */
@Data
@TableName("little_bee_application")
@ExcelIgnoreUnannotated
public class LittleBeeApplicationEntity extends BaseEntity {

  @ExcelIgnore
  @TableId(type = IdType.AUTO)
  private Long id;

  @ExcelProperty("提交日期") @TableFieldEnumMapping(dateEnum = true)
  @TableFieldMapping(value = "data_date", comment = "提交日期", queryItem = true)
  private String dataDate;

  @ExcelProperty("资方额度核准日期")
  @TableFieldMapping(value = "approval_date", comment = "资方额度核准日期")
  private String approvalDate;

  @ExcelProperty("资方名称")
  @TableFieldMapping(value = "sponsor_name", comment = "资方名称", queryItem = true)
  private String sponsorName;

  @ExcelProperty("资方标签")
  @TableFieldMapping(value = "sponsor_label", comment = "资方标签")
  private String sponsorLabel;

  @ExcelProperty("审批状态")
  @TableFieldMapping(value = "approval_status", comment = "审批状态")
  private String approvalStatus;

  @ExcelProperty("取消时间")
  @TableFieldMapping(value = "cancel_time", comment = "取消时间")
  private String cancelTime;

  @ExcelProperty("资方状态")
  @TableFieldMapping(value = "capital_state", comment = "资方状态")
  private String capitalState;

  @ExcelProperty("资方信审状态")
  @TableFieldMapping(value = "capital_credit_state", comment = "资方信审状态")
  private String capitalCreditStatus;

  @ExcelProperty("经销商")
  @TableFieldMapping(value = "franchiser", comment = "经销商")
  private String franchiser;

  @ExcelProperty("经销商省市")
  @TableFieldMapping(value = "franchiser_province", comment = "经销商省市")
  private String franchiserProvince;

  @ExcelProperty("品牌")
  @TableFieldMapping(value = "brand", comment = "品牌", queryItem = true)
  private String brand;

  @ExcelProperty("车型")
  @TableFieldMapping(value = "vehicle_model", comment = "车型", queryItem = true)
  private String vehicleModel;

  @ExcelProperty("款式")
  @TableFieldMapping(value = "style", comment = "款式")
  private String style;

  @ExcelProperty("产品名称")
  @TableFieldMapping(value = "product_name", comment = "产品名称", queryItem = true)
  private String productName;

  @ExcelProperty("车辆价格")
  @TableFieldMapping(value = "vehicle_price", comment = "车辆价格")
  private String vehiclePrice;

  @ExcelProperty("首付比例")
  @TableFieldMapping(value = "initial_payment_ratio", comment = "首付比例")
  private String initialPaymentRatio;

  @ExcelProperty("贷款额")
  @TableFieldMapping(value = "loan_amounts", comment = "贷款额")
  private String loanAmounts;

  @ExcelProperty("贴息额")
  @TableFieldMapping(value = "discount_interest_amount", comment = "贴息额")
  private String discountInterestAmount;


  @ExcelProperty("月供金额")
  @TableFieldMapping(value = "monthly_payment", comment = "月供金额")
  private String monthlyPayment;

  @ExcelProperty("贷款期数")
  @TableFieldMapping(value = "loan_periods", comment = "贷款期数")
  private String loanPeriods;

}
