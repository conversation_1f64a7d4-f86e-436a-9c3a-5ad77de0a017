package com.gwmfc.service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.gwmfc.dao.OtherPartyInfoExcelDao;
import com.gwmfc.entity.OtherPartyInfoExcelEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Service
public class ExcelService {
    @Autowired
    private OtherPartyInfoExcelDao otherPartyInfoExcelDao;

    @Transactional
    public void parseAndSaveExcel(MultipartFile file) throws Exception {
        List<OtherPartyInfoExcelEntity> entityList = new ArrayList<>();
        try (InputStream is = file.getInputStream(); Workbook workbook = new XSSFWorkbook(is)) {
            Sheet sheet = workbook.getSheetAt(0);
            int rowCount = sheet.getPhysicalNumberOfRows();
            int currentRow = 0;
            int tableType = 1;
            while (currentRow < rowCount) {
                Row titleRow = sheet.getRow(currentRow);
                String tableTitle = titleRow.getCell(0).getStringCellValue();
                Row headerRow = sheet.getRow(currentRow + 1);
                List<String> colLabels = new ArrayList<>();
                int colCount = headerRow.getPhysicalNumberOfCells();
                for (int i = 1; i < colCount; i++) {
                    String colLabel = headerRow.getCell(i).getStringCellValue();
                    if(StringUtils.isNotBlank(tableTitle)){
                        colLabels.add(colLabel);
                    }
                }
                colCount = colLabels.size();
                int dataStart = currentRow + 2;
                int dataEnd = dataStart;
                while (dataEnd < rowCount) {
                    Row r = sheet.getRow(dataEnd);
                    if (r == null || r.getCell(0) == null)
                        break;
                    String v = r.getCell(0).getStringCellValue();
                    if (v != null && (v.startsWith("综合返利") || v.startsWith("返车商") || v.startsWith("商务返利")))
                        break;
                    dataEnd++;
                }
                for (int i = dataStart; i < dataEnd; i++) {
                    Row row = sheet.getRow(i);
                    String rowLabel = row.getCell(0).getStringCellValue();
                    for (int j = 1; j < colCount; j++) {
                        Cell cell = row.getCell(j);
                        String value = cell == null ? "" : getCellString(cell);
                        OtherPartyInfoExcelEntity entity = new OtherPartyInfoExcelEntity();
                        entity.setTableTitle(tableTitle);
                        entity.setTableType(tableType);
                        entity.setRowLabel(rowLabel);
                        entity.setColLabel(colLabels.get(j - 1));
                        entity.setValue(value);
                        entityList.add(entity);
                    }
                }
                currentRow = dataEnd;
                tableType++;
            }
        }
        // 批量保存
        if (!entityList.isEmpty()) {
            for (OtherPartyInfoExcelEntity entity : entityList) {
                otherPartyInfoExcelDao.insert(entity);
            }
        }
    }
    private String getCellString(Cell cell) {
        if (cell == null) return "";
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    public static void main(String[] args) {
        ExcelService service = new ExcelService();
        try {
            // 直接读取本地文件进行测试
            java.io.File file = new java.io.File("C:\\Users\\<USER>\\Downloads\\样例导出1747804129643.xlsx");
            // java.io.File file = new java.io.File("D:\\test2\\excel_generation\\导出数据\\样例导出1747795251367.xlsx");
            try (java.io.FileInputStream fis = new java.io.FileInputStream(file);
                 org.apache.poi.ss.usermodel.Workbook workbook = new org.apache.poi.xssf.usermodel.XSSFWorkbook(fis)) {
                Sheet sheet = workbook.getSheetAt(0);
                int rowCount = sheet.getPhysicalNumberOfRows();
                int currentRow = 0;
                int tableType = 1;
                while (currentRow < rowCount) {
                    // 1. 读取表格标题（合并单元格的首行）
                    Row titleRow = sheet.getRow(currentRow);
                    String tableTitle = titleRow.getCell(0).getStringCellValue();
                    // 2. 读取表头（横轴）
                    Row headerRow = sheet.getRow(currentRow + 1);
                    java.util.List<String> colLabels = new java.util.ArrayList<>();
                    int colCount = headerRow.getPhysicalNumberOfCells();
                    for (int i = 1; i < colCount; i++) {
                        String colLabel = headerRow.getCell(i).getStringCellValue();
                        if (StringUtils.isNotBlank(tableTitle)) {
                            colLabels.add(colLabel);
                        }
                    }
                    colCount = colLabels.size();
                    int dataStart = currentRow + 2;
                    int dataEnd = dataStart;
                    // 找到下一个标题行或表尾
                    while (dataEnd < rowCount) {
                        Row r = sheet.getRow(dataEnd);
                        if (r == null || r.getCell(0) == null) break;
                        String v = r.getCell(0).getStringCellValue();
                        if (v != null && (v.startsWith("综合返利") || v.startsWith("返车商") || v.startsWith("商务返利")))
                            break;
                        dataEnd++;
                    }
                    for (int i = dataStart; i < dataEnd; i++) {
                        Row row = sheet.getRow(i);
                        String rowLabel = row.getCell(0).getStringCellValue();
                        for (int j = 1; j < colCount; j++) {
                            Cell cell = row.getCell(j);
                            String value = cell == null ? "" : service.getCellString(cell);
                            System.out.printf("table_title=%s, table_type=%d, row_label=%s, col_label=%s, value=%s\n",
                                    tableTitle, tableType, rowLabel, colLabels.get(j - 1), value);
                        }
                    }
                    // 跳到下一个表格
                    currentRow = dataEnd;
                    tableType++;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
