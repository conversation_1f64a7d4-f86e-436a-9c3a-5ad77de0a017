package com.gwmfc.service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.gwmfc.dao.OtherPartyInfoExcelDao;
import com.gwmfc.entity.OtherPartyInfoExcelEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.awt.Color;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Service
public class ExcelService {
    @Autowired
    private OtherPartyInfoExcelDao otherPartyInfoExcelDao;

    @Transactional
    public void parseAndSaveExcel(MultipartFile file) throws Exception {
        List<OtherPartyInfoExcelEntity> entityList = new ArrayList<>();
        try (InputStream is = file.getInputStream(); Workbook workbook = new XSSFWorkbook(is)) {
            Sheet sheet = workbook.getSheetAt(0);
            int rowCount = sheet.getPhysicalNumberOfRows();
            int currentRow = 0;
            int tableType = 1;
            while (currentRow < rowCount) {
                Row titleRow = sheet.getRow(currentRow);
                String tableTitle = titleRow.getCell(0).getStringCellValue();
                Row headerRow = sheet.getRow(currentRow + 1);
                List<String> colLabels = new ArrayList<>();
                int colCount = headerRow.getPhysicalNumberOfCells();
                for (int i = 1; i < colCount; i++) {
                    String colLabel = headerRow.getCell(i).getStringCellValue();
                    if(StringUtils.isNotBlank(tableTitle)){
                        colLabels.add(colLabel);
                    }
                }
                colCount = colLabels.size();
                int dataStart = currentRow + 2;
                int dataEnd = dataStart;
                while (dataEnd < rowCount) {
                    Row r = sheet.getRow(dataEnd);
                    if (r == null || r.getCell(0) == null)
                        break;
                    String v = r.getCell(0).getStringCellValue();
                    if (v != null && (v.startsWith("综合返利") || v.startsWith("返车商") || v.startsWith("商务返利")))
                        break;
                    dataEnd++;
                }
                for (int i = dataStart; i < dataEnd; i++) {
                    Row row = sheet.getRow(i);
                    String rowLabel = row.getCell(0).getStringCellValue();
                    for (int j = 1; j < colCount; j++) {
                        Cell cell = row.getCell(j);
                        String value = cell == null ? "" : getCellString(cell);
                        OtherPartyInfoExcelEntity entity = new OtherPartyInfoExcelEntity();
                        entity.setTableTitle(tableTitle);
                        entity.setTableType(tableType);
                        entity.setRowLabel(rowLabel);
                        entity.setColLabel(colLabels.get(j - 1));
                        entity.setValue(value);
                        entityList.add(entity);
                    }
                }
                currentRow = dataEnd;
                tableType++;
            }
        }
        // 批量保存
        if (!entityList.isEmpty()) {
            for (OtherPartyInfoExcelEntity entity : entityList) {
                otherPartyInfoExcelDao.insert(entity);
            }
        }
    }
    private String getCellString(Cell cell) {
        if (cell == null) return "";
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    public static void main(String[] args) {
        ExcelService service = new ExcelService();
        try {
            // 直接读取本地文件进行测试
            java.io.File file = new java.io.File("C:\\Users\\<USER>\\Downloads\\样例导出1747804129643.xlsx");
            // java.io.File file = new java.io.File("D:\\test2\\excel_generation\\导出数据\\样例导出1747795251367.xlsx");
            try (java.io.FileInputStream fis = new java.io.FileInputStream(file);
                 org.apache.poi.ss.usermodel.Workbook workbook = new org.apache.poi.xssf.usermodel.XSSFWorkbook(fis)) {
                Sheet sheet = workbook.getSheetAt(0);
                int rowCount = sheet.getPhysicalNumberOfRows();
                int currentRow = 0;
                int tableType = 1;
                while (currentRow < rowCount) {
                    // 1. 读取表格标题（合并单元格的首行）
                    Row titleRow = sheet.getRow(currentRow);
                    String tableTitle = titleRow.getCell(0).getStringCellValue();
                    // 2. 读取表头（横轴）
                    Row headerRow = sheet.getRow(currentRow + 1);
                    java.util.List<String> colLabels = new java.util.ArrayList<>();
                    int colCount = headerRow.getPhysicalNumberOfCells();
                    for (int i = 1; i < colCount; i++) {
                        String colLabel = headerRow.getCell(i).getStringCellValue();
                        if (StringUtils.isNotBlank(tableTitle)) {
                            colLabels.add(colLabel);
                        }
                    }
                    colCount = colLabels.size();
                    int dataStart = currentRow + 2;
                    int dataEnd = dataStart;
                    // 找到下一个标题行或表尾
                    while (dataEnd < rowCount) {
                        Row r = sheet.getRow(dataEnd);
                        if (r == null || r.getCell(0) == null) break;
                        String v = r.getCell(0).getStringCellValue();
                        if (v != null && (v.startsWith("综合返利") || v.startsWith("返车商") || v.startsWith("商务返利")))
                            break;
                        dataEnd++;
                    }
                    for (int i = dataStart; i < dataEnd; i++) {
                        Row row = sheet.getRow(i);
                        String rowLabel = row.getCell(0).getStringCellValue();
                        for (int j = 1; j < colCount; j++) {
                            Cell cell = row.getCell(j);
                            String value = cell == null ? "" : service.getCellString(cell);
                            System.out.printf("table_title=%s, table_type=%d, row_label=%s, col_label=%s, value=%s\n",
                                    tableTitle, tableType, rowLabel, colLabels.get(j - 1), value);
                        }
                    }
                    // 跳到下一个表格
                    currentRow = dataEnd;
                    tableType++;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 样式封装
    private static class Styles {
        final XSSFCellStyle blueHeader, greenHeader, titleCell, yellowCell, normalCell;
        Styles(Workbook workbook) {
            Font headerFont = workbook.createFont();
            headerFont.setFontName("微软雅黑");
            headerFont.setFontHeightInPoints((short) 16);
            headerFont.setBold(true);

            Font titleFont = workbook.createFont();
            titleFont.setFontName("微软雅黑");
            titleFont.setFontHeightInPoints((short) 11);
            titleFont.setBold(true);

            Font contentFont = workbook.createFont();
            contentFont.setFontName("微软雅黑");
            contentFont.setFontHeightInPoints((short) 11);
            contentFont.setBold(false);

            blueHeader = (XSSFCellStyle) workbook.createCellStyle();
            blueHeader.setFillForegroundColor(new XSSFColor(new Color(0, 176, 240), null));
            blueHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            blueHeader.setAlignment(HorizontalAlignment.LEFT);
            blueHeader.setVerticalAlignment(VerticalAlignment.CENTER);
            blueHeader.setBorderBottom(BorderStyle.THIN);
            blueHeader.setBorderTop(BorderStyle.THIN);
            blueHeader.setBorderLeft(BorderStyle.THIN);
            blueHeader.setBorderRight(BorderStyle.THIN);
            blueHeader.setFont(headerFont);

            greenHeader = (XSSFCellStyle) workbook.createCellStyle();
            greenHeader.setFillForegroundColor(new XSSFColor(new Color(198, 224, 180), null));
            greenHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            greenHeader.setAlignment(HorizontalAlignment.LEFT);
            greenHeader.setVerticalAlignment(VerticalAlignment.CENTER);
            greenHeader.setBorderBottom(BorderStyle.THIN);
            greenHeader.setBorderTop(BorderStyle.THIN);
            greenHeader.setBorderLeft(BorderStyle.THIN);
            greenHeader.setBorderRight(BorderStyle.THIN);
            greenHeader.setFont(headerFont);

            titleCell = (XSSFCellStyle) workbook.createCellStyle();
            titleCell.setFillForegroundColor(new XSSFColor(new Color(201, 201, 201), null));
            titleCell.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            titleCell.setAlignment(HorizontalAlignment.CENTER);
            titleCell.setVerticalAlignment(VerticalAlignment.CENTER);
            titleCell.setBorderBottom(BorderStyle.THIN);
            titleCell.setBorderTop(BorderStyle.THIN);
            titleCell.setBorderLeft(BorderStyle.THIN);
            titleCell.setBorderRight(BorderStyle.THIN);
            titleCell.setFont(titleFont);

            yellowCell = (XSSFCellStyle) workbook.createCellStyle();
            yellowCell.setFillForegroundColor(new XSSFColor(new Color(255, 255, 0), null));
            yellowCell.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            yellowCell.setAlignment(HorizontalAlignment.LEFT);
            yellowCell.setVerticalAlignment(VerticalAlignment.CENTER);
            yellowCell.setBorderBottom(BorderStyle.THIN);
            yellowCell.setBorderTop(BorderStyle.THIN);
            yellowCell.setBorderLeft(BorderStyle.THIN);
            yellowCell.setBorderRight(BorderStyle.THIN);
            yellowCell.setFont(contentFont);

            normalCell = (XSSFCellStyle) workbook.createCellStyle();
            normalCell.setAlignment(HorizontalAlignment.CENTER);
            normalCell.setVerticalAlignment(VerticalAlignment.CENTER);
            normalCell.setBorderBottom(BorderStyle.THIN);
            normalCell.setBorderTop(BorderStyle.THIN);
            normalCell.setBorderLeft(BorderStyle.THIN);
            normalCell.setBorderRight(BorderStyle.THIN);
            normalCell.setFont(contentFont);
        }
    }

    // 创建合并标题行
    private static void createMergedTitleRow(Sheet sheet, int rowIndex, int mergeLength, String title, CellStyle style) {
        Row row = sheet.createRow(rowIndex);
        Cell cell = row.createCell(0);
        cell.setCellValue(title);
        cell.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, mergeLength));
    }

    // 创建表头行
    private static void createHeaderRow(Sheet sheet, int rowIndex, List<String> headers, CellStyle style) {
        Row row = sheet.createRow(rowIndex);
        for (int i = 0; i < headers.size(); i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(headers.get(i));
            cell.setCellStyle(style);
        }
    }

    // 创建数据行
    private static void createDataRows(Sheet sheet, int startRow, List<String> rowLabels, int colCount, CellStyle labelStyle, CellStyle cellStyle) {
        for (int i = 0; i < rowLabels.size(); i++) {
            Row row = sheet.createRow(startRow + i);
            Cell cell = row.createCell(0);
            cell.setCellValue(rowLabels.get(i));
            cell.setCellStyle(labelStyle);
            for (int j = 1; j < colCount; j++) {
                row.createCell(j).setCellStyle(cellStyle);
            }
        }
    }

    /**
     * 生成样例Excel文件
     */
    public byte[] generateSampleExcel(String title, String otherParty, List<String> otherPartyRate, List<String> tgwRate, List<String> otherPartyRewardType,
                                      List<String> tgwRewardType, List<String> otherPartyCustomRating, List<String> tgwCustomRating) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");
        for (int i = 0; i < 20; i++) {
            sheet.setColumnWidth(i, 10 * 256);
        }
        Styles styles = new Styles(workbook);
        int index = 0;

        // 第一张表
        createMergedTitleRow(sheet, index++, otherPartyRate.size(), "综合返利差额-[长城-" + otherParty + "]-" + title, styles.blueHeader);
        List<String> header1 = new ArrayList<>();
        header1.add("奖励类型");
        header1.addAll(otherPartyRate);
        createHeaderRow(sheet, index++, header1, styles.titleCell);
        createDataRows(sheet, index, otherPartyRewardType, header1.size(), styles.titleCell, styles.normalCell);
        index += otherPartyRewardType.size();

        // 第二张表
        createMergedTitleRow(sheet, index++, otherPartyRate.size(), "返车商综合返利差额-[长城-" + otherParty + "]-" + title, styles.blueHeader);
        List<String> header2 = new ArrayList<>();
        header2.add("评级");
        header2.addAll(otherPartyRate);
        createHeaderRow(sheet, index++, header2, styles.titleCell);
        createDataRows(sheet, index, otherPartyCustomRating, header2.size(), styles.titleCell, styles.normalCell);
        index += otherPartyCustomRating.size();

        // 第三张表
        createMergedTitleRow(sheet, index++, tgwRate.size()+2, "商务返利-长城-" + title, styles.greenHeader);
        List<String> header3 = new ArrayList<>();
        header3.add("奖励类型");
        header3.addAll(tgwRate);
        header3.add("可得性");
        header3.add("可得性说明");
        createHeaderRow(sheet, index++, header3, styles.titleCell);
        createDataRows(sheet, index, tgwRewardType, header3.size(), styles.titleCell, styles.normalCell);
        index += tgwRewardType.size();

        // 第四张表
        createMergedTitleRow(sheet, index++, tgwRate.size()+2, "综合返利-长城-" + title, styles.greenHeader);
        List<String> header4 = new ArrayList<>();
        header4.add("评级");
        header4.addAll(tgwRate);
        header4.add("可得性");
        header4.add("可得性说明");
        createHeaderRow(sheet, index++, header4, styles.titleCell);
        createDataRows(sheet, index, tgwCustomRating, header4.size(), styles.titleCell, styles.normalCell);
        index += tgwCustomRating.size();

        // 第五张表
        createMergedTitleRow(sheet, index++, otherPartyRate.size()+2, "商务返利-" + otherParty + "-" + title, styles.blueHeader);
        List<String> header5 = new ArrayList<>();
        header5.add("奖励类型");
        header5.addAll(otherPartyRate);
        header5.add("可得性");
        header5.add("可得性说明");
        createHeaderRow(sheet, index++, header5, styles.titleCell);
        createDataRows(sheet, index, otherPartyRewardType, header5.size(), styles.titleCell, styles.normalCell);
        index += otherPartyRewardType.size();

        // 第六张表
        createMergedTitleRow(sheet, index++, otherPartyRate.size()+2, "综合返利-" + otherParty + "-" + title, styles.blueHeader);
        List<String> header6 = new ArrayList<>();
        header6.add("评级");
        header6.addAll(otherPartyRate);
        header6.add("可得性");
        header6.add("可得性说明");
        createHeaderRow(sheet, index++, header6, styles.titleCell);
        createDataRows(sheet, index, otherPartyCustomRating, header6.size(), styles.titleCell, styles.normalCell);
        index += otherPartyCustomRating.size();

        // 第七张表
        createMergedTitleRow(sheet, index++, tgwRate.size()+2, "返车商-长城-" + title, styles.greenHeader);
        List<String> header7 = new ArrayList<>();
        header7.add("评级");
        header7.addAll(tgwRate);
        header7.add("可得性");
        header7.add("可得性说明");
        createHeaderRow(sheet, index++, header7, styles.titleCell);
        createDataRows(sheet, index, tgwCustomRating, header7.size(), styles.titleCell, styles.normalCell);
        index += tgwCustomRating.size();

        // 第八张表
        createMergedTitleRow(sheet, index++, otherPartyRate.size()+2, "返车商-" + otherParty + "-" + title, styles.greenHeader);
        List<String> header8 = new ArrayList<>();
        header8.add("评级");
        header8.addAll(otherPartyRate);
        header8.add("可得性");
        header8.add("可得性说明");
        createHeaderRow(sheet, index++, header8, styles.titleCell);
        createDataRows(sheet, index, otherPartyRewardType, header8.size(), styles.titleCell, styles.normalCell);

        // 将工作簿写入字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
            return outputStream.toByteArray();
        } finally {
            workbook.close();
            outputStream.close();
        }
    }
}
