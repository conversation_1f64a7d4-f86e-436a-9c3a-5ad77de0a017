package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname MenuDO
 * @Date 2021/8/5 11:41
 */
@Data
@TableName("sys_menu")
public class MenuDO {

    @TableId(type = IdType.AUTO)
    private Long menuId;
    /**
     * 父菜单ID，一级菜单为0
     */
    @TableField("parent_id")
    private Long parentId;
    /**
     * 菜单名称
     */
    @TableField("name")
    private String name;
    /**
     * 菜单地址
     */
    @TableField("url")
    private String url;
    /**
     * 授权信息
     */
    @TableField("perms")
    private String perms;
    /**
     * 类型：菜单，页面，按钮
     */
    @TableField("type")
    private Integer type;
    /**
     * 菜单图标
     */
    @TableField("icon")
    private String icon;
    /**
     * 菜单顺序
     */
    @TableField("order_num")
    private Integer orderNum;
    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private Date gmtCreate;
    /**
     * 修改时间
     */
    @TableField("gmt_modified")
    private Date gmtModified;

    /**
     * 路由名称
     */
    @TableField("route_name")
    private String routeName;

    /**
     * 外链
     */
    @TableField("external_link")
    private String externalLink;
}
