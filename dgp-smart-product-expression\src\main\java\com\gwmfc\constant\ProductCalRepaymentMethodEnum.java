package com.gwmfc.constant;

/**
 * <AUTHOR>
 * @Classname ProductCalRepaymentMethod
 * @Description 还款方式
 * @Date 2023/9/22 14:26
 */
public enum ProductCalRepaymentMethodEnum {
    /**
     * 等额本息
     */
    DEBX(0),

    /**
     * 分段贷
     */
    FDD(1),

    /**
     * 尾款贷
     */
    WKD(2),

    /**
     * 等额本金
     */
    DEBJ(3),

    /**
     * 结构化贷款
     */
    JGHDK(4),

    /**
     * 阶梯贷
     */
    JTD(5),

    /**
     * 等本等息
     */
    DBDX(6),

    /**
     * 本金等比分段贷
     */
    BJDBFDD(7);

    private final Integer type;

    ProductCalRepaymentMethodEnum(Integer type) {
        this.type = type;
    }

    public Integer type() {
        return type;
    }

    public static ProductCalRepaymentMethodEnum getProductCalRepaymentMethodEnumByType(Integer type) {
        for (ProductCalRepaymentMethodEnum productCalRepaymentMethodEnum : ProductCalRepaymentMethodEnum.values()) {
            if (productCalRepaymentMethodEnum.type().equals(type)) {
                return productCalRepaymentMethodEnum;
            }
        }
        return null;
    }
}
