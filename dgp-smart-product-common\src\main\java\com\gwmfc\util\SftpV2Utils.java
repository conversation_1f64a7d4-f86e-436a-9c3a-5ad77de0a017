package com.gwmfc.util;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.*;
import java.util.Properties;
import java.util.Vector;

/**
 * <AUTHOR>
 * @Classname SftpV2Utils
 * @Description TODO
 * @Date 2022/1/29 9:24
 */
@Slf4j
public class SftpV2Utils {
    public static  final Logger logger = LoggerFactory.getLogger(SftpV2Utils.class);

    private Session session = null;

    private Channel channel = null;

    private Integer timeout=30000;

    private JSch jSch;

    public SftpV2Utils(){
       this.jSch = new JSch();
    }

    public ChannelSftp getChannel(String username, String password, String ip, int port) throws JSchException {
        session = jSch.getSession(username, ip, port);
        if (password != null) {
            session.setPassword(password);
        }
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        config.put("PreferredAuthentications", "publickey,keyboard-interactive,password");
        session.setConfig(config);
        session.setTimeout(timeout);
        session.connect();
        channel = session.openChannel("sftp");
        channel.connect();
        return (ChannelSftp) channel;
    }

    public void createDirSyncFile(String directory,String okFile,String username, String password, String ip, Integer port) {
        ChannelSftp channelSftp = null;
        Boolean flag = false;
        try {
            channelSftp = getChannel(username, password, ip, port);
            Vector<?> vector = channelSftp.ls(directory);
            if (vector != null) {
                flag = true;
            }
        } catch (Exception e) {
            log.error(" ls {} exception {}",directory,e);
            flag = false;
        }
        if(!flag){
            try {
                if(channelSftp == null){
                    channelSftp = getChannel(username, password, ip, port);
                }
                channelSftp.mkdir(directory);
                execute("chmod 777 "+directory);
            } catch (Exception e) {
                log.error("mkdir {} exception",directory,e);
            }
        }
        if(StringUtils.hasText(okFile)){
            execute("touch "+okFile);
        }
    }

    public void execute(String command){
        ChannelExec channelExec = null;
        try{
            channelExec = (ChannelExec) session.openChannel("exec");
            channelExec.setCommand(command);
            InputStream in = channelExec.getInputStream();
            InputStream err = channelExec.getErrStream();
            channelExec.connect(5000);
        }catch (Exception e){
            log.error("execute command exception {}",e);
        }
    }

    public void closeChannel() {
        if (channel != null) {
            channel.disconnect();
        }
        if (session != null) {
            session.disconnect();
            log.info("close ssh session ");
        }
    }




    public void deleteSFTP(String directory, String username, String password, String ip, Integer port) throws JSchException {
        ChannelSftp channelSftp = null;
        channelSftp = getChannel(username, password, ip, port);
        try {
            if (isDirExist(directory,username, password, ip, port)) {
                Vector<ChannelSftp.LsEntry> vector = channelSftp.ls(directory);
                // 文件，直接删除
                if (vector.size() == 1) {
                    channelSftp.rm(directory);
                } else if (vector.size() == 2) {
                    // 空文件夹，直接删除
                    channelSftp.rmdir(directory);
                } else {
                    String fileName = "";
                    // 删除文件夹下所有文件
                    for (ChannelSftp.LsEntry en : vector) {
                        fileName = en.getFilename();
                        if (".".equals(fileName) || "..".equals(fileName)) {
                            continue;
                        } else {
                            deleteSFTP(directory + "/" + fileName,username, password, ip, port);
                        }
                    }
                    // 删除文件夹
                    channelSftp.rmdir(directory);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public boolean isDirExist(String directory, String username, String password, String ip, Integer port) {
        ChannelSftp channelSftp = null;
        boolean flag =false;
        try {
            channelSftp = getChannel(username, password, ip, port);
            Vector<?> vector = channelSftp.ls(directory);
            if(vector != null){
                flag =true;
            }
        } catch (Exception e) {
            flag = false;
        }finally {
            channelSftp.disconnect();
        }
        return flag;
    }


    public void mkdirDir(ChannelSftp channelSftp, String[] dirs, String tempPath, int length, int index) {
        index++;
        if (index < length) {
            // 目录不存在，则创建文件夹
            tempPath += "/" + dirs[index];
        }
        try {
            logger.info("检测目录[" + tempPath + "]");
            channelSftp.cd(tempPath);
            if (index < length) {
                mkdirDir(channelSftp, dirs, tempPath, length, index);
            }
        } catch (SftpException ex) {
            logger.warn("创建目录[" + tempPath + "]");
            try {
                channelSftp.mkdir(tempPath);
                channelSftp.cd(tempPath);
            } catch (SftpException e) {
                e.printStackTrace();
                logger.error("创建目录[" + tempPath + "]失败,异常信息[" + e.getMessage() + "]");

            }
            logger.info("进入目录[" + tempPath + "]");
            mkdirDir(channelSftp, dirs, tempPath, length, index);
        }
    }

    public String uploadBySftp(File uploadFile, String dstDirPath, String username, String password, String ip, Integer port) {

        ChannelSftp channelSftp = null;

        String dstFilePath; // 目标文件名(带路径)，如： D:\\file\\file.doc,这个路径应该是远程目标服务器下要保存的路径
        FileInputStream fileInputStream = null;
        try {
            // 一、 获取channelSftp对象
            channelSftp = getChannel(username, password, ip, port);
            channelSftp.setFilenameEncoding("UTF-8");
            // 二、 判断远程路径dstDirPath是否存在(通道配置的路径)
            try {
                // TODO: 2020/12/10 实现递归创建目录的功能
                Vector dir = channelSftp.ls(dstDirPath);
                // 如果路径不存在，则创建
                if (dir == null) {
                    channelSftp.mkdir(dstDirPath);
                }
            } catch (SftpException e) { // 如果dstDirPath不存在，则会报错，此时捕获异常并创建dstDirPath路径
                // 此时创建路径如果再报错，即创建失败，则抛出异常
                try {
                    channelSftp.mkdir(dstDirPath);
                } catch (SftpException e1) {
                    e1.printStackTrace();
                }
                logger.error("read sftp  file exception",e);
            }
            // 三、 推送文件
            dstFilePath = dstDirPath + uploadFile.getName();
            // 推送: dstFilePath——传送过去的文件路径(全路径),采用默认的覆盖式推送 jsch触发推送操作
            fileInputStream = new FileInputStream(uploadFile);
            channelSftp.put(fileInputStream, dstFilePath);
            return dstFilePath;
        } catch (Exception e) {
            logger.error("upload file to sftp exception",e);
            e.printStackTrace();
        } finally {
            closeChannel();
        }
        return null;
    }



    public String uploadContentBySftp(String uploadFileContent, String dstDirPath, String scriptName, String username, String password, String ip, Integer port) {
        ChannelSftp channelSftp = null;

        String dstFilePath; // 目标文件名(带路径)，如： D:\\file\\file.doc,这个路径应该是远程目标服务器下要保存的路径
        OutputStream outputStream = null;
        try {
            // 一、 获取channelSftp对象
            channelSftp = getChannel(username, password, ip, port);
            channelSftp.setFilenameEncoding("UTF-8");
            // 二、 判断远程路径dstDirPath是否存在(通道配置的路径)
            try {
                Vector dir = channelSftp.ls(dstDirPath);
                // 如果路径不存在，则创建 调用方法 对上传目录进行递归创建
                if (dir == null) {
                    String[] dirs = dstDirPath.split("/");
                    String tempPath = "";
                    int index = 0;
                    mkdirDir(channelSftp, dirs, tempPath, dirs.length, index);
                }
            } catch (SftpException e) { // 如果dstDirPath不存在，则会报错，此时捕获异常并创建dstDirPath路径
                // 此时创建路径如果再报错，即创建失败，则抛出异常
                String[] dirs = dstDirPath.split("/");
                String tempPath = "";
                int index = 0;
                mkdirDir(channelSftp, dirs, tempPath, dirs.length, index);
            }
            // 三、 推送文件
            if (dstDirPath.endsWith("/")) {
                dstFilePath = dstDirPath + scriptName;
            } else {
                dstFilePath = dstDirPath + "/" + scriptName;
            }
            // 推送: dstFilePath——传送过去的文件路径(全路径),采用默认的覆盖式推送 jsch触发推送操作
            outputStream = channelSftp.put(dstFilePath,ChannelSftp.OVERWRITE);
            channelSftp.chmod(Integer.parseInt("777", 8),dstFilePath);
            outputStream.write(uploadFileContent.getBytes("UTF-8"));
            logger.info("···········upload dstFilePath:{} Integer.parseInt(\"777\",8):{}",dstFilePath,Integer.parseInt("777",10));
            return dstFilePath;
        } catch (Exception e) {
            logger.error("upload file to sftp exception", e);
            e.printStackTrace();
        } finally {
            try {
                if (null != outputStream) {
                    outputStream.flush();
                    outputStream.close();
                }
                closeChannel();
            } catch (Exception e) {
                logger.error("close stream exception ", e);
            }
            if (channelSftp != null) {
                channelSftp.quit();
            }
        }
        return null;
    }


    public String execScriptBySftp(String ip, String username, String password, String command) {
        ChannelSftp channelSftp = null;
        try {
            // 一、 获取channelSftp对象
            channelSftp = getChannel(username, password, ip, 22);
            channelSftp.setFilenameEncoding("UTF-8");
            // 二、 判断远程路径dstDirPath是否存在(通道配置的路径)
            channel = session.openChannel("exec");
            ChannelExec execChannel = (ChannelExec) channel;
            execChannel.setCommand(command);
            channel.connect();

            BufferedReader bi = new BufferedReader(new InputStreamReader(channel.getInputStream(), "UTF-8"));
            StringBuffer sb = new StringBuffer();
            String s = null;

            while((s = bi.readLine()) != null) {
                sb.append(s);
            }
            s = sb.toString();
            bi.close();

            logger.info("输出结果是：{}",s);
            return "输出结果是：" + sb.toString();
        } catch (Exception e) {
            logger.error("get connect exception", e);
            e.printStackTrace();
        } finally {
            if (channelSftp != null) {
                channelSftp.quit();
            }
        }
        return null;
    }

}
