@echo off
set CLASSPATH=.;target/classes;lib\*;lib\easyexcel-3.1.0.jar;lib\poi-4.1.2.jar;lib\poi-ooxml-4.1.2.jar

javac -encoding UTF-8 -d target/classes -cp "%CLASSPATH%" dgp-smart-product-expression\src\main\java\com\gwmfc\util\ExcelUtil.java dgp-smart-product-expression\src\main\java\com\gwmfc\util\ExcelUtilTest.java

if %ERRORLEVEL% EQU 0 (
    echo Compilation successful. Running test...
    java -cp "%CLASSPATH%" com.gwmfc.util.ExcelUtilTest
) else (
    echo Compilation failed.
)
