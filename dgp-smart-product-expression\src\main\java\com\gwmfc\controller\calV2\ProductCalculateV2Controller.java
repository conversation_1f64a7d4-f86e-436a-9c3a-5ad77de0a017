package com.gwmfc.controller.calV2;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.*;
import com.gwmfc.entity.ProductCalBasicInfoEntity;
import com.gwmfc.service.calV2.ProductCalculateV2Service;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Classname ProductCalculateController
 * @Description 计算产品相关指标
 * @Date 2023/9/14 9:12
 */
@Api(tags = "模板测算v2")
@RestController
@RequestMapping("/product/calculate/v2")
@Slf4j
public class ProductCalculateV2Controller {

    @Autowired
    private ProductCalculateV2Service productCalculateV2Service;
    /**
     * 测算所有参数
     * @return
     */
    @ApiOperation("指标测算")
    @PostMapping("/calAllParam")
    public Result calAllParam(@RequestBody ProductCalculateDto productCalculateDto){
        Result result = productCalculateV2Service.calculateAllUnionParam(productCalculateDto);
        return result;
    }

    @ApiOperation("计算提前结清概率，手续费比例，佣金扣返比例")
    @PostMapping("/calEarlySquareProbabilityAndRatio")
    public Result calEarlySquareProbabilityAndRatio(@RequestBody ProductCalculateDto productCalculateDto,@RequestParam String source){
        Result result = productCalculateV2Service.calEarlySquareProbabilityAndRatio(productCalculateDto,source);
        return result;
    }

    @ApiOperation("计算提前结清指标")
    @PostMapping("/calculateEarlySquare")
    public Result calculateEarlySquare(@RequestBody ProductCalculateDto productCalculateDto){
        Result result = productCalculateV2Service.calculateEarlySquare(productCalculateDto);
        return result;
    }

    @ApiOperation("获取提前结清参数附加")
    @GetMapping("/getEarlySquarePamSub")
    public Result getEarlySquarePamSub(@RequestParam Integer bussType, @RequestParam Integer repaymentMethod){
        Result result = productCalculateV2Service.getEarlySquarePamSub(bussType,repaymentMethod);
        return result;
    }
}
