package com.gwmfc.controller;

import com.gwmfc.service.ExcelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("/excel")
public class ExcelController {

    @Autowired
    private ExcelService excelService;

    @PostMapping("/upload")
    public ResponseEntity<String> uploadExcel(@RequestParam("file") MultipartFile file) {
        try {
            // excelService.parseAndPrintExcel(file);
            return ResponseEntity.ok("Excel parsed and printed successfully.");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body("Failed to parse Excel: " + e.getMessage());
        }
    }

    @PostMapping("/generateSample")
    public void generateSampleExcel(@RequestParam String title,
                                    @RequestParam String otherParty,
                                    @RequestParam List<String> otherPartyRate,
                                    @RequestParam List<String> tgwRate,
                                    @RequestParam List<String> otherPartyRewardType,
                                    @RequestParam List<String> tgwRewardType,
                                    @RequestParam List<String> otherPartyCustomRating,
                                    @RequestParam List<String> tgwCustomRating,
                                    HttpServletResponse response) {
        try {
            // 调用服务生成Excel文件
            byte[] excelData = excelService.generateSampleExcel(title, otherParty, otherPartyRate, tgwRate,
                    otherPartyRewardType, tgwRewardType, otherPartyCustomRating, tgwCustomRating);

            // 设置响应头
            String fileName = "样例导出_" + System.currentTimeMillis() + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");

            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName + ";" + "filename*=utf-8''" + encodedFileName);
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

            // 写入响应
            response.getOutputStream().write(excelData);
            response.getOutputStream().flush();

        } catch (IOException e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}
