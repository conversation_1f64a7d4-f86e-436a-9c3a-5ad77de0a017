package com.gwmfc.constant;

/**
 * <AUTHOR>
 * @Classname ProductCalBussType
 * @Description 业务类型
 * @Date 2023/9/22 14:26
 */
public enum GroupFileTypeEnum {
    /**
     * 月度乘用车
     */
    MONTH_PASSENGER("1"),

    /**
     * 月度皮卡
     */
    MONTH_PICKUP("2"),

    /**
     * 周度乘用车
     */
    WEEK_PASSENGER("3"),

    /**
     * 周度皮卡
     */
    WEEK_PICKUP("4");



    private final String type;

    GroupFileTypeEnum(String type) {
        this.type = type;
    }

    public String type() {
        return type;
    }

    public static GroupFileTypeEnum getGroupFileTypeEnumByType(String type) {
        for (GroupFileTypeEnum groupFileTypeEnum : GroupFileTypeEnum.values()) {
            if (groupFileTypeEnum.type().equals(type)) {
                return groupFileTypeEnum;
            }
        }
        return null;
    }
}
