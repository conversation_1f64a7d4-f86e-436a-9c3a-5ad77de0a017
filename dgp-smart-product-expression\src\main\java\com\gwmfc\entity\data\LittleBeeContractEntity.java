package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 *小蜜蜂合同数据
 *
 * 数据源：邮箱
 *
 * 数据格式：excel
 *
 * 更新频率：每天
 *
 * 获取方式：自动
 */
@Data
@TableName("little_bee_contract")
@ExcelIgnoreUnannotated
public class LittleBeeContractEntity extends BaseEntity {

  @ExcelIgnore
  @TableId(type = IdType.AUTO)
  private Long id;

  @ExcelProperty("资方放款成功状态更新时间") @TableFieldEnumMapping(dateEnum = true)
  @TableFieldMapping(value = "data_date", comment = "资方放款成功状态更新时间", queryItem = true)
  private String dataDate;

  @ExcelProperty("提交申请时间")
  @TableFieldMapping(value = "submit_date", comment = "提交申请时间")
  private String submitDate;

  @ExcelProperty("资方状态")
  @TableFieldMapping(value = "capital_status", comment = "资方状态")
  private String capitalStatus;

  @ExcelProperty("产品名称")
  @TableFieldMapping(value = "product_name", comment = "产品名称", queryItem = true)
  private String productName;

  @ExcelProperty("品牌")
  @TableFieldMapping(value = "brand", comment = "品牌", queryItem = true)
  private String brand;

  @ExcelProperty("车型")
  @TableFieldMapping(value = "vehicle_model", comment = "车型", queryItem = true)
  private String vehicleModel;

  @ExcelProperty("款式")
  @TableFieldMapping(value = "style", comment = "款式")
  private String style;

  @ExcelProperty("VIN")
  @TableFieldMapping(value = "vin", comment = "VIN")
  private String vin;

  @ExcelProperty("资方名称")
  @TableFieldMapping(value = "sponsor_name", comment = "资方名称", queryItem = true)
  private String sponsorName;

  @ExcelProperty("资方标签")
  @TableFieldMapping(value = "sponsor_label", comment = "资方标签")
  private String sponsorLabel;

  @ExcelProperty("经销商名称")
  @TableFieldMapping(value = "dealer_name", comment = "经销商名称")
  private String dealerName;

  @ExcelProperty("经销商省市")
  @TableFieldMapping(value = "franchiser_province", comment = "经销商省市")
  private String franchiserProvince;

  @ExcelProperty("期数_总和")
  @TableFieldMapping(value = "periods_sum", comment = "期数_总和")
  private String periodsSum;

  @ExcelProperty("客户利率_总和")
  @TableFieldMapping(value = "customer_interest_rate_sum", comment = "客户利率_总和")
  private String customerInterestRateSum;

  @ExcelProperty("资产金额_总和")
  @TableFieldMapping(value = "assets_sum", comment = "资产金额_总和")
  private String assetsSum;

  @ExcelProperty("贷款额_总和")
  @TableFieldMapping(value = "loan_amount_sum", comment = "贷款额_总和")
  private String loanAmountSum;

  @ExcelProperty("利息总金额_总和")
  @TableFieldMapping(value = "interest_amount_sum", comment = "利息总金额_总和")
  private String interestAmountSum;

  @ExcelProperty("贴息金额_总和")
  @TableFieldMapping(value = "discount_amount_sum", comment = "贴息金额_总和")
  private String discountAmountSum;

  @ExcelProperty("实际放款额_总和")
  @TableFieldMapping(value = "actual_loan_amount_sum", comment = "实际放款额_总和")
  private String actualLoanAmountSum;
}
