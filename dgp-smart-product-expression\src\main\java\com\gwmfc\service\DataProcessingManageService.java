package com.gwmfc.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.gwmfc.dao.DataProcessingManageDao;
import com.gwmfc.dto.DataProcessingManageDto;
import com.gwmfc.entity.DataProcessingManageEntity;
import com.gwmfc.entity.DataProcessingManageMapEntity;
import com.gwmfc.util.GsonUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-11-20 16:58
 */
@Slf4j
@Service
public class DataProcessingManageService {
    @Resource
    DataProcessingManageDao dataProcessingManageDao;

    /**
     * 分页查询
     */
    public IPage<DataProcessingManageEntity> page(DataProcessingManageEntity dataProcessingManageEntity, Integer current, Integer size) {
        IPage<DataProcessingManageEntity> page = new Page<>(current, size);
        QueryWrapper<DataProcessingManageEntity> wrapper = new QueryWrapper<>(dataProcessingManageEntity);
        wrapper.orderByDesc("id");
        if (dataProcessingManageEntity.getKeyword() != null && StringUtils.hasLength(dataProcessingManageEntity.getKeyword())) {
            String keyword = dataProcessingManageEntity.getKeyword();
            dataProcessingManageEntity.setKeyword(null);
            wrapper.like("keyword", keyword);
        }
        return dataProcessingManageDao.selectPage(page, wrapper);
    }

    /**
     * 保存
     */
    public void save(DataProcessingManageDto dataProcessingManageDto) {
        dataProcessingManageDto.getDataProcessingManageMapEntityList().forEach(dataProcessingManageMapEntity -> {
            DataProcessingManageEntity dataProcessingManageEntity = new DataProcessingManageEntity();
            BeanUtils.copyProperties(dataProcessingManageDto, dataProcessingManageEntity);
            dataProcessingManageEntity.setKeyword(dataProcessingManageMapEntity.getKeyword());
            dataProcessingManageEntity.setMappingLabel(dataProcessingManageMapEntity.getMappingLabel());
            dataProcessingManageDao.insert(dataProcessingManageEntity);
        });
    }

    /**
     * 更新
     */
    public Integer update(DataProcessingManageDto dataProcessingManageDto) {
        DataProcessingManageEntity dataProcessingManageEntity = new DataProcessingManageEntity();
        BeanUtils.copyProperties(dataProcessingManageDto, dataProcessingManageEntity);
        if (dataProcessingManageDto.getDataProcessingManageMapEntityList() != null && !dataProcessingManageDto.getDataProcessingManageMapEntityList().isEmpty()) {
            DataProcessingManageMapEntity dataProcessingManageMapEntity = dataProcessingManageDto.getDataProcessingManageMapEntityList().get(0);
            dataProcessingManageEntity.setKeyword(dataProcessingManageMapEntity.getKeyword());
            dataProcessingManageEntity.setMappingLabel(dataProcessingManageMapEntity.getMappingLabel());
        }
        return dataProcessingManageDao.updateById(dataProcessingManageEntity);
    }


    /**
     * 删除
     *
     * @param id
     */
    public Integer remove(Long id) {
        return dataProcessingManageDao.deleteById(id);
    }

    /**
     * 批量删除
     *
     * @param idList
     */
    public Integer batchRemove(List<Long> idList) {
        return dataProcessingManageDao.deleteBatchIds(idList);
    }

    /**
     * 查询单个数据
     */
    public DataProcessingManageDto getOne(Long id) {
        DataProcessingManageEntity dataProcessingManageEntity = dataProcessingManageDao.selectById(id);
        if (null != dataProcessingManageEntity) {
            DataProcessingManageEntity dataProcessingManageEntityQuery = new DataProcessingManageEntity();
            dataProcessingManageEntityQuery.setDataName(dataProcessingManageEntity.getDataName());
            LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<>(dataProcessingManageEntityQuery);
            DataProcessingManageDto dataProcessingManageDto = new DataProcessingManageDto();
            BeanUtils.copyProperties(dataProcessingManageEntity, dataProcessingManageDto);
            List<DataProcessingManageEntity> dataProcessingManageEntityList = dataProcessingManageDao.selectList(lambdaQueryWrapper);
            List<DataProcessingManageMapEntity> dataProcessingManageMapEntityList = new ArrayList<>();
            dataProcessingManageEntityList.forEach(dataProcessingManageEntity1 -> {
                DataProcessingManageMapEntity dataProcessingManageMapEntity = new DataProcessingManageMapEntity();
                dataProcessingManageMapEntity.setRealId(dataProcessingManageEntity1.getId());
                dataProcessingManageMapEntity.setKeyword(dataProcessingManageEntity1.getKeyword());
                dataProcessingManageMapEntity.setMappingLabel(dataProcessingManageEntity1.getMappingLabel());
                dataProcessingManageMapEntityList.add(dataProcessingManageMapEntity);
            });
            dataProcessingManageDto.setDataProcessingManageMapEntityList(dataProcessingManageMapEntityList);
            return dataProcessingManageDto;
        }
        return null;
    }
}
