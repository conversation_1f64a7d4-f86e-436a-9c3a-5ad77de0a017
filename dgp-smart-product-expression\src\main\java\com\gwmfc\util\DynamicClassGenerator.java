package com.gwmfc.util;

import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtField;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname DynamicClassGenerator
 * @Description TODO
 * @Date 2025/4/24 10:21
 */
public class DynamicClassGenerator {
    /**
     * 根据 columnNames 动态生成 Class
     *
     * @param columnNames 列名列表
     * @return 生成的 Class
     * @throws Exception 如果生成过程中发生错误
     */
    public static Class<?> generateClass(List<String> columnNames) throws Exception {
        // 创建 ClassPool
        ClassPool classPool = ClassPool.getDefault();

        // 定义新的类
        String className = "DynamicGeneratedClass";
        CtClass ctClass = classPool.makeClass(className);

        // 为每个列名添加字段
        for (String columnName : columnNames) {
            // 将列名转换为驼峰命名法（可选）
            String fieldName = toCamelCase(columnName);
            // 添加字段（这里假设字段类型为 String）
            CtField ctField = new CtField(classPool.get("java.lang.String"), fieldName, ctClass);
            ctClass.addField(ctField);
        }

        // 返回生成的 Class
        return ctClass.toClass();
    }

    /**
     * 将下划线命名法转换为驼峰命名法
     *
     * @param input 下划线命名法的字符串
     * @return 驼峰命名法的字符串
     */
    private static String toCamelCase(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;
        for (char c : input.toCharArray()) {
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    result.append(c);
                }
            }
        }
        return result.toString();
    }
}
