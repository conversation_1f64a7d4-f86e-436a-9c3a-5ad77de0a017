package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.service.CarIndustryIndexService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Classname CarIndustryIndexController
 * @Description TODO
 * @Date 2024/11/5 16:15
 */
@Api(tags = "汽车行业指数接口")
@RestController
@RefreshScope
@RequestMapping("/carIndustry")
public class CarIndustryIndexController {

    @Autowired
    private CarIndustryIndexService carIndustryIndexService;

    @ApiOperation("经销商库存预警指数获取")
    @GetMapping("/dealerInventoryWarningIndex/capture")
    public Result dealerInventoryWarningIndexCapture(@RequestParam String date,@CurrentUser User user){
        return carIndustryIndexService.dealerInventoryWarningIndexCapture(date,user);
    }
}
