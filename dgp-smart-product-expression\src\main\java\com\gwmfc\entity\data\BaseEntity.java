package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

@Data
public abstract class BaseEntity {

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER,pattern = "yyyy-MM-dd HH:mm:ss")
    @TableFieldMapping(value = "create_time", comment = "数据时间戳",queryItem = true)
    @ExcelIgnore
    private Long createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private String updateTime;

}
