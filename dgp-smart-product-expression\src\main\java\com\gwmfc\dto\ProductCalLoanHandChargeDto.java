package com.gwmfc.dto;

import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalLoanHandChargeDto
 * @Description TODO
 * @Date 2024/1/30 16:56
 */
@Data
@ApiModel(value = "放款手续费详情")
public class ProductCalLoanHandChargeDto {
    @ApiModelProperty("银行")
    private String bank;

    @ApiModelProperty("放款概率")
    private String loanProbability;

    @ApiModelProperty("金额")
    private String moneyRange;

    @ApiModelProperty("手续费")
    private String handCharge;

    @ApiModelProperty("截止日期")
    private String deadlineDate;
}
