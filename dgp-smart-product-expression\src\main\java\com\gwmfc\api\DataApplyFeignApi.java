package com.gwmfc.api;

import com.gwmfc.dto.DepartmentUserDto;
import com.gwmfc.dto.UserAndDepartmentDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年09月26日 8:44
 */
@FeignClient("data-apply-service")
public interface DataApplyFeignApi {
    /**
     * 获取用户及部门相关信息
     * @param userId 钉钉号
     * @param smartDepartmentId 使用部门id(本品部门dept_id)
     * @return UserAndDepartmentDto
     */
    @GetMapping("/dingding/getUserAndDepartmentInfoForSmart")
    UserAndDepartmentDto getUserAndDepartmentInfoForSmart(@RequestParam String userId, @RequestParam String smartDepartmentId);

    /**
     * 获取部门下用户列表
     * @param accessToken 可为null
     * @param departmentId 部门id
     * @return List
     */
    @GetMapping("/dingding/getUsersByDepartmentId")
    List<DepartmentUserDto> getUsersByDepartmentId(@RequestParam String accessToken, @RequestParam String departmentId);
}
