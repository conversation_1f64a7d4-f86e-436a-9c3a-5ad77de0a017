package com.gwmfc.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwmfc.dto.MenuDto;
import com.gwmfc.entity.MenuDO;
import com.gwmfc.dao.MenuDao;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname MenuService
 * @Date 2021/8/5 11:15
 */
@Service
public class MenuService {

    @Resource
    private MenuDao menuDao;


    public List<MenuDO> listMenuTree(Long userId){
        return menuDao.listMenuByUserId(userId);
    }

    public IPage<MenuDO> page(MenuDO menuDO,Integer current,Integer size){
        QueryWrapper wrapper = new QueryWrapper(menuDO);
        if (StringUtils.hasText(menuDO.getName())) {
            wrapper.like("name", menuDO.getName());
            menuDO.setName(null);
        }
        IPage page = new Page(current,size);
        wrapper.orderByDesc("menu_id");
        return menuDao.selectPage(page,wrapper);
    }

    public void save(MenuDto menuDto){
        MenuDO menuDO = new MenuDO();
        BeanUtils.copyProperties(menuDto,menuDO);
        menuDao.insert(menuDO);
    }
    public MenuDO getById(Long id){
        return menuDao.selectById(id);
    }

    public void update(MenuDto menuDto){
        MenuDO menuDO = new MenuDO();
        BeanUtils.copyProperties(menuDto,menuDO);
        menuDao.updateById(menuDO);
    }

    public void remove(Long id){
        menuDao.deleteRoleMenuByMenuId(id);
        menuDao.deleteById(id);
    }


    public List<Long> getTree(Long roleId) {
        return menuDao.listMenuIdByRoleId(roleId);
    }
}
