<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.ProductBusinessLineDao">

    <select id="getBussTypes" resultType="String">
        select distinct buss_type from product_business_line
    </select>

    <select id="getTrackByBussType" resultType="String">
        select distinct track from product_business_line where buss_type = #{bussType}
    </select>

    <select id="getSubdivisionByBussTypeAndTrack" resultType="String">
        select distinct subdivision from product_business_line where buss_type = #{bussType} and track = #{track}
    </select>


</mapper>