package com.gwmfc.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gwmfc.entity.ProductProposalApprovalStatusEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年10月18日 13:36
 */
@Mapper
public interface ProductProposalApprovalDao extends BaseMapper<ProductProposalApprovalStatusEntity> {
    List<ProductProposalApprovalStatusEntity> queryApprovalStatus(Long productProposalId, Integer approveType, String proposalStep, Integer status);

    void deleteProductProposalApproveHistory(Long productProposalId);

    List<ProductProposalApprovalStatusEntity> approvalHistory(Long productProposalId);

    void dealAllTagForProductProposal(Long proposalId);

    ProductProposalApprovalStatusEntity selectLatestCostAccountApprove(Long productProposalId);

}
