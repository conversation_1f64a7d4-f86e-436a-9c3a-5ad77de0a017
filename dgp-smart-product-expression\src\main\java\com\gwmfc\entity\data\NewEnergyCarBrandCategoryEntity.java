package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname NewEnergyCarBrandCategoryEntity
 * @Description TODO
 * @Date 2025/1/18 14:36
 */
@Data
@TableName("new_energy_car_brand_category")
@ExcelIgnoreUnannotated
public class NewEnergyCarBrandCategoryEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期")@TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("品牌定位")
    @TableFieldMapping(value = "brand_positioning", comment = "品牌定位")
    private String brandPositioning;

    @ExcelProperty("技术类型")
    @TableFieldMapping(value = "technology_type", comment = "技术类型")
    private String technologyType;

    @ExcelProperty("本月销量")
    @TableFieldMapping(value = "sales_volume_current_month", comment = "本月销量")
    private String salesVolumeCurrentMonth;

    @ExcelProperty("上月销量")
    @TableFieldMapping(value = "sales_volume_last_month", comment = "上月销量")
    private String salesVolumeLastMonth;

    @ExcelProperty("图片地址")
    @TableFieldMapping(value = "picture_url", comment = "图片地址")
    private String pictureUrl;
}
