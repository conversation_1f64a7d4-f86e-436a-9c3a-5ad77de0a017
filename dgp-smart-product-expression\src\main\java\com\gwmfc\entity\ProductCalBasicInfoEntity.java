package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductCalBasicInfoEntity
 * @Description ProductCalBasicInfoEntity
 * @Date 2023/9/26 14:09
 */
@Data
@TableName("product_cal_basic_info")
public class ProductCalBasicInfoEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "template_name", comment = "模板名称")
    private String templateName;

    @TableFieldMapping(value = "version", comment = "版本号")
    private Integer version;

    @TableFieldMapping(value = "business_type", comment = "业务类型")
    private Integer businessType;

    @TableFieldMapping(value = "repayment_method", comment = "还款方式")
    private Integer repaymentMethod;

    @TableFieldMapping(value = "repayment_method_instruction", comment = "还款方式说明")
    private String repaymentMethodInstruction;

    @TableFieldMapping(value = "state", comment = "状态")
    private String state;

    @TableFieldMapping(value = "effective_date", comment = "生效日期")
    private String effectiveDate;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @TableFieldMapping(value = "expiry_date", comment = "失效日期")
    private String expiryDate;

    @TableFieldMapping(value = "consider_early_square", comment = "是否考虑提前结清 1是 0否")
    private Integer considerEarlySquare;

    @TableFieldMapping(value = "create_time", comment = "创建日期")
    private Date createTime;

    @TableFieldMapping(value = "create_user", comment = "创建人")
    private String createUser;

    @TableFieldMapping(value = "update_time", comment = "修改日期")
    private Date updateTime;

    @TableFieldMapping(value = "update_user", comment = "修改人")
    private String updateUser;
}
