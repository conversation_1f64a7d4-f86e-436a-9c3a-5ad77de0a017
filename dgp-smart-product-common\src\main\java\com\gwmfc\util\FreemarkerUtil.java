package com.gwmfc.util;

import freemarker.template.Configuration;
import freemarker.template.DefaultObjectWrapper;
import freemarker.template.Template;

import java.io.*;
import java.util.Map;

public class FreemarkerUtil {
    public static File createFileByTemplate(String fileName, Map root) throws IOException {
        FileWriter fw = null;
        BufferedWriter bw = null;
        try {
            Configuration cfg = new Configuration(Configuration.VERSION_2_3_23);
            cfg.setClassForTemplateLoading(FreemarkerUtil.class, "/ftl/");
            cfg.setObjectWrapper(new DefaultObjectWrapper(Configuration.VERSION_2_3_23));
            Template temp = cfg.getTemplate("dagTemplate.ftl");
            String localPath = System.getProperty("user.dir") + "/" + fileName + ".py";
            File file = new File(localPath);
            File dir = file.getParentFile();
            if (!dir.exists()) {
                dir.mkdirs();
            }
            fw = new FileWriter(file);
            bw = new BufferedWriter(fw);
            temp.process(root, bw);
            return file;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != bw) {
                bw.flush();
            }
            if (null != fw) {
                fw.close();
            }
        }
        return null;
    }

    public static String createStringByTemplate(Map root, String ftlTemplateName) {
        try {
            Configuration cfg = new Configuration(Configuration.VERSION_2_3_23);
            cfg.setClassForTemplateLoading(FreemarkerUtil.class, "/ftl/");
            cfg.setObjectWrapper(new DefaultObjectWrapper(Configuration.VERSION_2_3_23));
            Template temp = cfg.getTemplate(ftlTemplateName, "UTF-8");
            StringWriter stringWriter = new StringWriter();
            BufferedWriter writer = new BufferedWriter(stringWriter);
            temp.process(root, writer);

            writer.flush();
            writer.close();
            return stringWriter.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
