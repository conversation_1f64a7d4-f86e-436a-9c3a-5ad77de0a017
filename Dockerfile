ARG DOCKERHUB
FROM $DOCKERHUB:8083/openjdk:8u201-jre-alpine
VOLUME /tmp
ARG APP_NAME
ARG SOURCE_FILE
ARG JAR_FILE
ARG http_proxy
ARG https_proxy
ARG JVM_MEM
COPY $SOURCE_FILE $JAR_FILE
ENV app_name $APP_NAME
ENV jar_name $JAR_FILE
ENV http_proxy=$http_proxy
ENV https_proxy=$https_proxy
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && apk add --update ttf-dejavu fontconfig && rm -rf /var/cache/apk/* && mkfontscale && mkfontdir && fc-cache
ENV http_proxy=""
ENV https_proxy=""
ENV JVM_MEM $JVM_MEM
ENTRYPOINT java -Xms$JVM_MEM -Xmx$JVM_MEM -XX:+PrintGCDetails -XX:+PrintGCDateStamps -Xloggc:/opt/$app_name/logs/gc.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/opt/$app_name/oom -Djava.net.preferIPv4Stack=true -Duser.timezone=GMT+08 -jar /$jar_name