package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductCalUnionLoanParamEntity
 * @Description 联合贷参数
 * @Date 2023/9/19 11:33
 */
@Data
@TableName("product_cal_union_loan_param")
public class ProductCalUnionLoanParamEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "basic_info_id", comment = "基本参数id")
    private Long basicInfoId;

    @TableFieldMapping(value = "item", comment = "条目")
    private String item;

    @TableFieldMapping(value = "item_desc", comment = "描述")
    private String itemDesc;

    @TableFieldMapping(value = "join_cal", comment = "是否参与计算  0-否 1-是")
    private String joinCal;

    @TableFieldMapping(value = "value", comment = "值")
    private String value;

    @TableFieldMapping(value = "is_formula", comment = "是否公式")
    private String isFormula;

    @TableFieldMapping(value = "create_time", comment = "创建日期")
    private Date createTime;

    @TableFieldMapping(value = "create_user", comment = "创建人")
    private String createUser;

    @TableFieldMapping(value = "update_time", comment = "修改日期")
    private Date updateTime;

    @TableFieldMapping(value = "update_user", comment = "修改人")
    private String updateUser;

}
