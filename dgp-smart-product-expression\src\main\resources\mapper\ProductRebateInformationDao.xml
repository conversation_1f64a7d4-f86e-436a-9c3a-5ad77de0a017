<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.ProductRebateInformationDao">

    <delete id="updateBatchIds">
        update `product_rebate_information` a set `status` = 3 where a.id in
        <foreach collection="delIdList" index="index" item="delId" open="(" separator="," close=")">
            #{delId}
        </foreach>
    </delete>
    <delete id="updateStatus">
        update
            `product_rebate_information`
        set
            `status` = 3
        where
            id = #{delId}
    </delete>
</mapper>