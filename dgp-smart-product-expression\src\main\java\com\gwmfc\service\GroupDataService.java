package com.gwmfc.service;

import com.gwmfc.constant.GroupFileTypeEnum;
import com.gwmfc.util.FtpUtil;
import com.gwmfc.util.Result;
import com.gwmfc.util.SftpUtils;
import com.jcraft.jsch.ChannelSftp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.ByteArrayInputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Vector;

import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @Classname GroupDataService
 * @Description 处理集团数据
 * @Date 2023/10/19 13:57
 */
@Service
@Slf4j
@RefreshScope
public class GroupDataService {

    @Value("${group-file-ftp.address}")
    private String address;
    @Value("${group-file-ftp.port}")
    private int port;
    @Value("${group-file-ftp.username}")
    private String username;
    @Value("${group-file-ftp.password}")
    private String password;

    @Value("${group-file-ftp.month-passenger-path}")
    private String monthPassengerPath;
    @Value("${group-file-ftp.month-pickup-path}")
    private String monthPickupPath;
    @Value("${group-file-ftp.week-passenger-path}")
    private String weekPassengerPath;
    @Value("${group-file-ftp.week-pickup-path}")
    private String weekPickupPath;

    @Value("${group-file-ftp.month-passenger-fileNameRoot}")
    private String monthPassengerFileNameRoot;
    @Value("${group-file-ftp.month-pickup-fileNameRoot}")
    private String monthPickupFileNameRoot;
    @Value("${group-file-ftp.week-passenger-fileNameRoot}")
    private String weekPassengerFileNameRoot;
    @Value("${group-file-ftp.week-pickup-fileNameRoot}")
    private String weekPickupFileNameRoot;

    private static final String PREFIX_FILE="smart_";


    public Integer updateGroupFileName(String fileType,String date){
        Integer result;
        try{
            String filePath;
            String fileNameRoot;
            ChannelSftp channelSftp = SftpUtils.getChannel(username, password, address, port);
            if(GroupFileTypeEnum.MONTH_PASSENGER.type().equals(fileType)){
                filePath=monthPassengerPath;
                fileNameRoot=monthPassengerFileNameRoot;
            }else if(GroupFileTypeEnum.MONTH_PICKUP.type().equals(fileType)){
                filePath=monthPickupPath;
                fileNameRoot=monthPickupFileNameRoot;
            }else if(GroupFileTypeEnum.WEEK_PASSENGER.type().equals(fileType)){
                filePath=weekPassengerPath;
                fileNameRoot=weekPassengerFileNameRoot;
            }else{
                filePath=weekPickupPath;
                fileNameRoot=weekPickupFileNameRoot;
            }
            channelSftp.cd(filePath);
            Vector<ChannelSftp.LsEntry> entries = channelSftp.ls(".");

            String replaceRxg = PREFIX_FILE+fileNameRoot+date+"_";
            boolean isExist = false;
            for(ChannelSftp.LsEntry entry:entries){
                String fileNameOld = entry.getFilename();
                if(fileNameOld.startsWith(fileNameRoot)){
                    isExist = true;
                    String newFileName=fileNameOld.replace(fileNameRoot,replaceRxg);
                    channelSftp.rename(filePath+fileNameOld,filePath+newFileName);
                }
            }

            /**
             * 如果不存在以fileNameRoot开头的文件则创建空文件，以确保datax自定义任务可以正常通过，空跑不报错
             */
            if(isExist == false){
                channelSftp.put(new ByteArrayInputStream(new byte[0]), filePath+replaceRxg+"empty.csv");
            }

            SftpUtils.closeChannel();
            result = 200;
        }catch(Exception e){
            log.error("updateGroupFileName error",e);
            SftpUtils.closeChannel();
            result = 500;
        }
        return result;
    }
}
