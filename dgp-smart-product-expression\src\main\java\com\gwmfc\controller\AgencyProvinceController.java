package com.gwmfc.controller;

import com.gwmfc.bo.ProvinceCityBo;
import com.gwmfc.service.AgencyProvinceService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @date 2023年09月07日 15:25
 */
@Api(tags = "经销商省市")
@RestController
@RequestMapping("/agency/province")
public class AgencyProvinceController {
    @Resource
    private AgencyProvinceService agencyProvinceService;

    /**
     * 返回所有市
     *
     * @return
     */
    @ApiOperation("返回所有经销商")
    @GetMapping("/catchAllAgency")
    public Result catchAllAgency(@RequestParam String province, @RequestParam String city) {
        Result result = new Result<>();
        List<String> cityList = agencyProvinceService.catchAllAgency(province, city);
        result.setData(cityList);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    /**
     * 返回所有省
     *
     * @param
     * @return
     */
    @ApiOperation(value = "返回所有省", produces = "application/json")
    @GetMapping("/detail")
    public Result<List<String>> catchAllProvince() {
        Result result = new Result<>();
        List<ProvinceCityBo> provinceCityList = agencyProvinceService.catchAllProvince();
        if (null != provinceCityList && !provinceCityList.isEmpty()) {
            Map<String, List<String>> provinceCity = provinceCityList.stream()
                    .collect(Collectors.groupingBy(o -> o.getProvince(),
                            Collectors.mapping(o -> o.getCity(),
                                    Collectors.collectingAndThen(Collectors.toList(),
                                            list -> new ArrayList<>(new HashSet<>(list))))));
            result.setData(provinceCity);
        }
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }
}
