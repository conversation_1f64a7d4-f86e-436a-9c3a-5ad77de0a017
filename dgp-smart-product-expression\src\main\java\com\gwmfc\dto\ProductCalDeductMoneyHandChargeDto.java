package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalDeductMoneyHandChargeDto
 * @Description TODO
 * @Date 2024/1/31 8:39
 */
@Data
@ApiModel(value = "扣款手续费详情")
public class ProductCalDeductMoneyHandChargeDto {
    @ApiModelProperty("银行")
    private String bank;

    @ApiModelProperty("扣款概率")
    private String deductProbability;

    @ApiModelProperty("金额")
    private String moneyRange;

    @ApiModelProperty("手续费")
    private String handCharge;

    @ApiModelProperty("截止日期")
    private String deadlineDate;
}
