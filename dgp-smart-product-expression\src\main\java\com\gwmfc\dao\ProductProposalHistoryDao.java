package com.gwmfc.dao;

import com.gwmfc.bo.ProductProposalHistoryQueryBo;
import com.gwmfc.dto.ProductProposalHistoryQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductProposalHistoryDao
 * @Description TODO
 * @Date 2023/11/7 13:59
 */
@Mapper
public interface ProductProposalHistoryDao {
    ProductProposalHistoryQueryBo getHistoryData(@Param("productProposalHistoryQueryDto") ProductProposalHistoryQueryDto productProposalHistoryQueryDto);

    List<Integer> getQueryCount(@Param("productProposalHistoryQueryDto") ProductProposalHistoryQueryDto productProposalHistoryQueryDto);

    Integer getBussTypeContractNum(String bussType);
}
