package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname MonthPassengerCarEntity
 * @Description TODO
 * @Date 2023/8/1 17:26
 */
@Data
@TableName("month_pickup_car")
@ExcelIgnoreUnannotated
public class MonthPickupCarEntity extends BaseEntity {
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("单双排")
    @TableFieldMapping(value = "single_and_double_row", comment = "单双排")
    private String singleAndDoubleRow;

    @ExcelProperty("底盘型号")
    @TableFieldMapping(value = "chassis_model", comment = "底盘型号")
    private String chassisModel;

    @ExcelProperty("年")
    @TableFieldMapping(value = "date_year", comment = "年")
    private String dateYear;

    @ExcelProperty("货厢宽")
    @TableFieldMapping(value = "cargo_compartment_width", comment = "货厢宽")
    private String cargoCompartmentWidth;

    @ExcelProperty("市")
    @TableFieldMapping(value = "city", comment = "市")
    private String city;

    @ExcelProperty("使用性质")
    @TableFieldMapping(value = "use_nature", comment = "使用性质")
    private String useNature;

    @ExcelProperty("车辆型号")
    @TableFieldMapping(value = "vehicle_model", comment = "车辆型号", queryItem = true)
    private String vehicleModel;

    @ExcelProperty("燃料种类")
    @TableFieldMapping(value = "fuel_types", comment = "燃料种类", queryItem = true)
    private String fuelTypes;

    @ExcelProperty("额定载质量")
    @TableFieldMapping(value = "rated_load_quality", comment = "额定载质量")
    private String ratedLoadQuality;

    @ExcelProperty("发动机型号")
    @TableFieldMapping(value = "engine_model", comment = "发动机型号")
    private String engineModel;

    @ExcelProperty("外廓高")
    @TableFieldMapping(value = "outer_profile_height", comment = "外廓高")
    private String outerProfileHeight;

    @ExcelProperty("油耗")
    @TableFieldMapping(value = "oil_consumption", comment = "油耗")
    private String oilConsumption;

    @ExcelProperty("记录数")
    @TableFieldMapping(value = "number", comment = "记录数")
    private String number;

    @ExcelProperty("最后更新人")
    @TableFieldMapping(value = "last_updated_by", comment = "最后更新人")
    private String lastUpdatedBy;

    @ExcelProperty("变速器")
    @TableFieldMapping(value = "transmission", comment = "变速器")
    private String transmission;

    @ExcelProperty("省")
    @TableFieldMapping(value = "province", comment = "省")
    private String province;

    @ExcelProperty("月")
    @TableFieldMapping(value = "data_month", comment = "月")
    private String dataMonth;

    @ExcelProperty("排量")
    @TableFieldMapping(value = "displacement", comment = "排量")
    private String displacement;

    @ExcelProperty("整备质量")
    @TableFieldMapping(value = "curb_weight", comment = "整备质量")
    private String curbWeight;

    @ExcelProperty("功率")
    @TableFieldMapping(value = "power", comment = "功率")
    private String power;

    @ExcelProperty("外廓宽")
    @TableFieldMapping(value = "outer_profile_width", comment = "外廓宽")
    private String outerProfileWidth;

    @ExcelProperty("品牌")
    @TableFieldMapping(value = "brand", comment = "品牌", queryItem = true)
    private String brand;

    @ExcelProperty("座位数")
    @TableFieldMapping(value = "number_of_seats", comment = "座位数")
    private String numberOfSeats;

    @ExcelProperty("最后更新日期")
    @TableFieldMapping(value = "last_update_date", comment = "最后更新日期")
    private String lastUpdateDate;

    @ExcelProperty("区")
    @TableFieldMapping(value = "area", comment = "区")
    private String area;

    @ExcelProperty("企业名称")
    @TableFieldMapping(value = "business_name", comment = "企业名称")
    private String businessName;

    @ExcelProperty("驱动形式")
    @TableFieldMapping(value = "driving_form", comment = "驱动形式")
    private String drivingForm;

    @ExcelProperty("月")
    @TableFieldMapping(value = "date_month", comment = "月")
    private String dateMonth;

    @ExcelProperty("进气方式")
    @TableFieldMapping(value = "intake_mode", comment = "进气方式")
    private String intakeMode;

    @ExcelProperty("外廓长")
    @TableFieldMapping(value = "outer_profile_length", comment = "外廓长")
    private String outerProfileLength;

    @ExcelProperty("集团简称")
    @TableFieldMapping(value = "group_abbreviation", comment = "集团简称", queryItem = true)
    private String groupAbbreviation;

    @ExcelProperty("排放标准")
    @TableFieldMapping(value = "emission_standard", comment = "排放标准")
    private String emissionStandard;

    @ExcelProperty("车辆名称")
    @TableFieldMapping(value = "name_of_vehicle", comment = "车辆名称")
    private String nameOfVehicle;

    @ExcelProperty("发动机企业")
    @TableFieldMapping(value = "engine_enterprise", comment = "发动机企业")
    private String engineEnterprise;

    @ExcelProperty("车型")
    @TableFieldMapping(value = "vehicle_type", comment = "车型", queryItem = true)
    private String vehicleType;

    @ExcelProperty("创建日期")
    @TableFieldMapping(value = "creation_date", comment = "创建日期")
    private String creationDate;

    @ExcelProperty("创建人")
    @TableFieldMapping(value = "created_by", comment = "创建人")
    private String createdBy;

    @ExcelProperty("底盘企业")
    @TableFieldMapping(value = "chassis_enterprise", comment = "底盘企业")
    private String chassisEnterprise;

    @ExcelProperty("货厢长")
    @TableFieldMapping(value = "cargo_compartment_length", comment = "货厢长")
    private String cargoCompartmentLength;

    @ExcelProperty("轴距")
    @TableFieldMapping(value = "wheelbase", comment = "轴距")
    private String wheelbase;

    @ExcelProperty("所有权")
    @TableFieldMapping(value = "ownership", comment = "所有权")
    private String ownership;

    @ExcelProperty("货厢高")
    @TableFieldMapping(value = "cargo_compartment_height", comment = "货厢高")
    private String cargoCompartmentHeight;

    @ExcelProperty("区域")
    @TableFieldMapping(value = "region", comment = "区域")
    private String region;

    @ExcelProperty("城市级别")
    @TableFieldMapping(value = "urban_level", comment = "城市级别", queryItem = true)
    private String urbanLevel;
}