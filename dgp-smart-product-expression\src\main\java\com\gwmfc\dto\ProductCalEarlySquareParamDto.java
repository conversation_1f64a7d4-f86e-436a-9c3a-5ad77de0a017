package com.gwmfc.dto;

import com.gwmfc.entity.ProductCalEarlySquareProbabilityEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname EarlySquareParamDto
 * @Description 提前结清参数
 * @Date 2023/9/19 14:07
 */
@Data
@ApiModel(value = "提前结清参数")
public class ProductCalEarlySquareParamDto {
    @ApiModelProperty("基础佣金扣返利率起点(%)")
    private Double basicCommissionRebateInterestRateStart;

    @ApiModelProperty("基础佣金扣返比例(%)")
    private Double basicCommissionRebateRatio;

    @ApiModelProperty("N期内结清扣返基础佣金")
    private Double numSquareRebateBasicCommission;

    @ApiModelProperty("提前结清概率")
    private Double earlySquareProbability;

    @ApiModelProperty("提前结清手续费比例（%）")
    private Double earlySquareHandChargeRatio;

    @ApiModelProperty("N期内结清收手续费")
    private Double numSquareHandCharge;
}
