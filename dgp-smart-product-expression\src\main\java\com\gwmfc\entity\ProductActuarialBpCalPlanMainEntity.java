package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductActuarialBpCalPlanMainEntity
 * @Description TODO
 * @Date 2025/4/21 13:54
 */
@Data
@TableName("product_actuarial_bp_cal_plan_main")
public class ProductActuarialBpCalPlanMainEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "template_id")
    @ApiModelProperty("模板id")
    private Long templateId;

    @TableField(value = "plan_name")
    @ApiModelProperty("方案名称")
    private String planName;

    @TableField(value = "plan_num")
    @ApiModelProperty("方案编号")
    private String planNum;

    @TableField(value = "process")
    @ApiModelProperty("过程 coefficientSupplement-系数补录、earlySquare-提前结清、index-指标测算、result-结果测算")
    private String process;

    @TableField(value = "contract_amount",updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("合同数量")
    private Integer contractAmount;

    @TableField(value = "rebate_coefficient",updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("返利系数")
    private Double rebateCoefficient;

    @TableField(value = "total_profit",updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("总利润")
    private Double totalProfit;

    @TableField(value = "per_profit",updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("单笔利润")
    private Double perProfit;

    @TableField(value = "max_column_num")
    @ApiModelProperty("最大列数")
    private Integer maxColumnNum;

    @TableField(value = "max_row_num")
    @ApiModelProperty("最大行数")
    private Integer maxRowNum;

    @TableField(value = "status")
    @ApiModelProperty("状态")
    private Integer status;

    @TableField(value = "effective_time")
    @ApiModelProperty("生效时间")
    private Date effectiveTime;

    @TableField(value = "end_time")
    @ApiModelProperty("结束时间")
    private Date endTime;

    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField(value = "create_user")
    @ApiModelProperty("创建人")
    private String createUser;

    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @TableField(value = "update_user")
    @ApiModelProperty("更新人")
    private String updateUser;
}
