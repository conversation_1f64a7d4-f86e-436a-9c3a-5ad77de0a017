package com.gwmfc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.*;
import com.gwmfc.entity.IrrCalResultEntity;
import com.gwmfc.entity.ProductCalBasicInfoEntity;
import com.gwmfc.entity.ProductProposalEntity;
import com.gwmfc.service.ProductCalculateService;
import com.gwmfc.service.ProductProposalService;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Classname ProductCalculateController
 * @Description 计算产品相关指标
 * @Date 2023/9/14 9:12
 */
@Api(tags = "模板测算")
@RestController
@RequestMapping("/product/calculate")
@Slf4j
public class ProductCalculateController {

    @Autowired
    private ProductCalculateService productCalculateService;
    @Resource
    private ProductProposalService productProposalService;
    /**
     * 测算所有参数
     * @return
     */
    @ApiOperation("指标测算")
    @PostMapping("/calAllParam")
    public Result calAllParam(@RequestBody ProductCalculateDto productCalculateDto){
        Result result = productCalculateService.calculateAllUnionParam(productCalculateDto);
        long timeAfter = System.currentTimeMillis();
        return result;
    }

    /**
     * 保存模板信息
     * @param productCalculateAppDto
     * @return
     */
    @ApiOperation("保存")
    @PostMapping("/save")
    public Result save(@RequestBody ProductCalculateAppDto productCalculateAppDto, @CurrentUser User user){
        return productCalculateService.save(productCalculateAppDto,user);
    }

    /**
     * 删除模板信息
     * @param id
     * @return
     */
    @ApiOperation("删除")
    @GetMapping("/delete")
    public Result delete(@RequestParam(value = "id") Integer id){
        return productCalculateService.delete(id);
    }

    @ApiOperation("更新")
    @PostMapping("/update")
    public Result update(@RequestBody ProductCalBasicInfoEntity productCalBasicInfoEntity,@CurrentUser User user){
        return productCalculateService.update(productCalBasicInfoEntity,user);
    }

    @ApiOperation("获取所有模板")
    @PostMapping("/getAllTemplate")
    public Result getAllTemplate(@RequestBody PageForm<ProductCalBasicInfoDto> pageFormReq){
        return productCalculateService.getAllTemplate(pageFormReq.getParam(), pageFormReq.getCurrent(), pageFormReq.getSize());
    }

    @ApiOperation("模板详细信息")
    @GetMapping("/getTemplateDetail")
    public Result getTemplateDetail(Integer id){
        return productCalculateService.getTemplateDetail(id);
    }

    @ApiOperation("获取固定成本信息")
    @GetMapping("/getFixCostInfo")
    public Result getFixCostInfo(){
        return productCalculateService.getFixCostInfo();
    }

    @ApiOperation("获取资金成本信息")
    @GetMapping("/getCapitalCostInfo")
    public Result getCapitalCostInfo(){
        return productCalculateService.getCapitalCostInfo();
    }

    @ApiOperation("获取公式描述")
    @GetMapping("/getFormulaDes")
    public Result getFormulaDes(String bussType, String repaymentMethod, String item){
        return productCalculateService.getFormulaDes(bussType,repaymentMethod,item);
    }

    @ApiOperation("模板对比")
    @GetMapping("/templateCompare")
    public Result templateCompare(@RequestParam String idList){
        return productCalculateService.templateCompare(idList);
    }

    @ApiOperation("根据单量计算利润")
    @PostMapping("/calulateProfitByAmount")
    public Result calulateProfitByAmount(@RequestBody ProductCalProfitDto productCalProfitDto){
        Result result = productCalculateService.calulateProfitByAmount(productCalProfitDto);
        return result;
    }

    @ApiOperation("计算提前结清指标")
    @PostMapping("/calculateEarlySquare")
    public Result calculateEarlySquare(@RequestBody ProductCalculateDto productCalculateDto){
        Result result = productCalculateService.calculateEarlySquare(productCalculateDto);
        return result;
    }

    @ApiOperation("获取提前结清概率信息")
    @GetMapping("/getEarlySquareProbabilityInfo")
    public Result getEarlySquareProbabilityInfo(@RequestParam(value = "businessType") Integer businessType, @RequestParam(value = "customerInterestRate") Double customerInterestRate, @RequestParam(value = "actualInterestRate") Double actualInterestRate, @RequestParam(value = "timeLimit") Integer timeLimit){
        return productCalculateService.getEarlySquareProbabilityInfo(businessType,customerInterestRate,actualInterestRate,timeLimit);
    }

    @ApiOperation("获取放款手续费信息")
    @GetMapping("/getLoanHandChargeDetail")
    public Result getLoanHandCharge(){
        return productCalculateService.getLoanHandChargeDetail();
    }

    @ApiOperation("获取扣款手续费信息")
    @GetMapping("/getDeductHandChargeDetail")
    public Result getDeductHandCharge(){
        return productCalculateService.getDeductHandChargeDetail();
    }

    @ApiOperation("导出数据")
    @GetMapping("/exportCalData")
    public void exportCalData(@RequestParam String exportCode, @RequestParam String exportDataJson, HttpServletResponse response){
        productCalculateService.exportCalData(exportCode,exportDataJson,response);
    }

    @ApiOperation("irr计算记录查询")
    @PostMapping("/getIrrCalResult")
    public Result getIrrCalResult(@RequestBody PageForm<IrrCalResultDto> irrCalResultDtoPageForm){
        Result result = new Result<>();
        IPage<IrrCalResultEntity> irrCalResultEntityIPage = productCalculateService.getIrrCalResultByPage(irrCalResultDtoPageForm);
        result.setTotal(irrCalResultEntityIPage.getTotal());
        List<IrrCalResultDto> irrCalResultDtos = new ArrayList<>((int) irrCalResultEntityIPage.getTotal());
        irrCalResultEntityIPage.getRecords().forEach(irrCalResultEntity -> {
            IrrCalResultDto irrCalResultDto = new IrrCalResultDto();
            BeanUtils.copyProperties(irrCalResultEntity, irrCalResultDto);
            irrCalResultDto.setProductCalRepaymentMethodParamDto(GsonUtil.gsonToBean(irrCalResultEntity.getProductCalRepaymentMethodParamDto(),ProductCalRepaymentMethodParamDto.class));
            if (StringUtils.hasText(irrCalResultEntity.getProductProposalGroupUuid())) {
                ProductProposalEntity productProposalEntity = productProposalService.selectById(irrCalResultEntity.getProductProposalId());
                if (StringUtils.hasText(productProposalEntity.getProductProposalTemplateGroupJson())) {
                    ProductProposalTemplateGroupDto productProposalTemplateGroupDto = GsonUtil.jsonToList(productProposalEntity.getProductProposalTemplateGroupJson(), ProductProposalTemplateGroupDto.class).stream().filter(productProposalTemplateGroupDtoFilter -> productProposalTemplateGroupDtoFilter.getUuid().equals(irrCalResultEntity.getProductProposalGroupUuid())).collect(Collectors.toList()).get(0);
                    irrCalResultDto.setGroupName(productProposalTemplateGroupDto.getGroupName());
                }
            }
            irrCalResultDtos.add(irrCalResultDto);
        });
        result.setData(irrCalResultDtos);
        result.setTotal(irrCalResultEntityIPage.getTotal());
        return result;
    }
}
