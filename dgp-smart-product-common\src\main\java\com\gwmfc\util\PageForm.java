package com.gwmfc.util;


import lombok.Data;

/**
 * @Classname Page
 * @Description 分页查询工具类
 * @Date 2021/3/31 16:46
 * <AUTHOR> y<PERSON><PERSON><PERSON>
 */
@Data
public class PageForm<T> {

    /**当前页**/

    private Integer current;
    /**每页条数**/

    private Integer size;
    /**条件对象**/
    private T param;

    private String orderType;

    private String orderBy;

    public PageForm() {
    }

    public PageForm(Integer current, Integer size) {
        this.current = current;
        this.size = size;
    }
}
