package com.gwmfc.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * 用户及部门详情
 * @date 2023年09月25日 13:27
 */
@Data
public class UserAndDepartmentDto {
    /**
     * 用户姓名
    */
    private String name;
    /**
     * 角色名
     */
    private String roleName;
    /**
     * 工号
     */
    private String staffNo;
    /**
     * 部门id
     */
    private String deptId;
    /**
     * 钉钉账号
     */
    private String userId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 部门领导
     */
    private List<String> deptLeader;
    /**
     * 所在部门的上级部门
     */
    private String deptParentId;
}