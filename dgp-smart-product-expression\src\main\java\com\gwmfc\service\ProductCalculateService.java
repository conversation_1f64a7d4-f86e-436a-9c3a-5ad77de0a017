package com.gwmfc.service;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.dozermapper.core.DozerBeanMapperBuilder;
import com.github.dozermapper.core.Mapper;
import com.google.common.base.CaseFormat;
import com.gwmfc.constant.ProductCalRepaymentMethodEnum;
import com.gwmfc.dao.*;
import com.gwmfc.domain.User;
import com.gwmfc.dto.*;
import com.gwmfc.entity.*;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.util.AviatorUtils;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;
import static java.lang.Double.NaN;

/**
 * <AUTHOR>
 * @Classname ProductCalculateService
 * @Description 计算产品相关指标
 * @Date 2023/9/14 9:14
 */
@Service
@Slf4j
@RefreshScope
public class ProductCalculateService {

    @Autowired
    private ProductCalBaseTemplateDao productCalBaseTemplateDao;

    @Autowired
    private ProductCalFixCostDao productCalFixCostDao;

    @Autowired
    private ProductCalCapitalCostDao productCalCapitalCostDao;

    @Autowired
    private ProductCalDao productCalDao;

    @Autowired
    private ProductCalBasicInfoDao productCalBasicInfoDao;

    @Autowired
    private ProductCalBasicParamDao productCalBasicParamDao;

    @Autowired
    private ProductCalCashStreamDao productCalCashStreamDao;

    @Autowired
    private ProductCalEarlySquareParamDao productCalEarlySquareParamDao;

    @Autowired
    private ProductCalRepaymentMethodParamDao productCalRepaymentMethodParamDao;

    @Autowired
    private ProductCalResultInfoDao productCalResultInfoDao;

    @Autowired
    private ProductCalTaxFeeParamDao productCalTaxFeeParamDao;

    @Autowired
    private ProductCalUnionLoanParamDao productCalUnionLoanParamDao;

    @Autowired
    private ProductCalEarlySquareProbabilityDao productCalEarlySquareProbabilityDao;

    @Autowired
    private ProductCalDataExportDao productCalDataExportDao;

    @Resource
    private IrrCalResultDao irrCalResultDao;

    @Autowired
    private ProductCalEarlySquareParamSubDao productCalEarlySquareParamSubDao;

    /**
     * 综合考虑联合贷计算IRR
     * @param productCalculateDto
     * @return
     */
    public Result calculateAllUnionParam(ProductCalculateDto productCalculateDto){
        if(productCalculateDto.getProductCalUnionLoanParamDto()!=null && productCalculateDto.getProductCalUnionLoanParamDto().getBankLoanRatio()!=null && productCalculateDto.getProductCalUnionLoanParamDto().getBankSettleRate()!=null && productCalculateDto.getProductCalUnionLoanParamDto().getIntegrativeIrrOursFactor()!=null){
            Mapper mapper = DozerBeanMapperBuilder.buildDefault();
            ProductCalculateDto productCalculateDtoOurs = mapper.map(productCalculateDto,ProductCalculateDto.class);
            Result result = calculateAllParam(productCalculateDto);
            /**
             * 如果返回结果不是200，直接返回失败
             */
            if(!SUCCESS.getCode().equals(result.getCode())){
                return result;
            }

            productCalculateDtoOurs.setProductCalUnionLoanParamDto(null);
            Result ourAllLoanIrr = calculateAllParam(productCalculateDtoOurs);
            if(!SUCCESS.getCode().equals(ourAllLoanIrr.getCode())){
                return ourAllLoanIrr;
            }

            ProductCalculateDto productCalculateDtoUnion = (ProductCalculateDto)result.getData();
            ProductCalculateDto productCalculateDtoOurAllLoan = (ProductCalculateDto)ourAllLoanIrr.getData();

            productCalculateDtoUnion.getProductCalResultInfoDto().setResultOurFullLoanIrr(productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultIrr());

            /**
             * 查询综合IRR计算公式
             */
            QueryWrapper<ProductCalBaseTemplateEntity> queryWrapper = new QueryWrapper();
            queryWrapper.eq("buss_type",7777).eq("item","integrative_irr");
            ProductCalBaseTemplateEntity productCalBaseTemplateEntity = productCalBaseTemplateDao.selectList(queryWrapper).get(0);
            String formula = productCalBaseTemplateEntity.getFormula();
            formula = formula.replace("@ourFullLoanIrr@",productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultIrr().toString());
            formula = formula.replace("@integrativeIrrOursFactor@",productCalculateDto.getProductCalUnionLoanParamDto().getIntegrativeIrrOursFactor().toString());
            formula = formula.replace("@resultIrr@",productCalculateDtoUnion.getProductCalResultInfoDto().getResultIrr().toString());

            Map map = new HashMap();
            Double paramValue = (Double) AviatorUtils.calculateValue(map, formula);
            Double paramValueNew = dealScale(productCalBaseTemplateEntity.getScaleValue(), paramValue);
            productCalculateDtoUnion.getProductCalResultInfoDto().setResultIntegrativeIrr(paramValueNew);
            result.setData(productCalculateDtoUnion);
            return result;
        }else{
            return calculateAllParam(productCalculateDto);
        }
    }

    /**
     * 指标测算
     * @param productCalculateDto
     * @return
     */
    public Result calculateAllParam(ProductCalculateDto productCalculateDto) {
        log.info("calculateAllParam start");
        long startTime = System.currentTimeMillis();
        Result result = new Result();
        /**
         * 查询对应的基础模板信息
         */
        ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
        List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities = getCalTemplates(productCalBasicInfoDto);

        /**
         * 首先校验所有必传参数是否为null，如果为null，返回错误提示
         */
        result = checkMustParam(productCalculateDto,productCalBaseTemplateEntities);
        if(result.getCode() != 200){
            return result;
        }


        /**
         * 解析参数
         */

        Map<String,Object> paramMap = new HashMap();
        ProductCalBasicParamDto productCalBasicParamDto = productCalculateDto.getProductCalBasicParamDto();
        ProductCalTaxFeeParamDto productCalTaxFeeParamDto = productCalculateDto.getProductCalTaxFeeParamDto();
        ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto = productCalculateDto.getProductCalRepaymentMethodParamDto();
        ProductCalEarlySquareParamDto productCalEarlySquareParamDto = productCalculateDto.getProductCalEarlySquareParamDto();
        ProductCalculateEarlySquareDto  productCalculateEarlySquareDto = productCalculateDto.getProductCalculateEarlySquareDto();
        ProductCalUnionLoanParamDto productCalUnionLoanParamDto = productCalculateDto.getProductCalUnionLoanParamDto();
        ProductCalProfitDetailDto productCalProfitDetailDto = productCalculateDto.getProductCalProfitDetailDto();

        boolean parseFlag = parseParams(paramMap,productCalBasicParamDto,productCalTaxFeeParamDto,productCalRepaymentMethodParamDto,productCalEarlySquareParamDto,productCalculateEarlySquareDto,productCalProfitDetailDto,productCalUnionLoanParamDto,productCalBasicInfoDto);

        if(parseFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("解析参数失败");
            return result;
        }

        /**
         * 计算前置方法
         */
        Result beforeCalResult = beforeCalDeal(productCalBaseTemplateEntities,paramMap,productCalculateDto);
        if(beforeCalResult.getCode() != 200){
            return result;
        }

        /**
         * 计算基本参数
         */
        boolean basicFlag = calulateBasicParam(paramMap,productCalBaseTemplateEntities,productCalBasicParamDto,productCalBasicInfoDto);

        Double actualLoanMoneyValue = productCalBasicParamDto.getActualLoanMoney();

        if(basicFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("计算基本参数失败");
            return result;
        }


        /**
         * 计算税费参数
         */
        boolean taxFeeFlag = calulateTaxFeeParam(paramMap,productCalBaseTemplateEntities,productCalTaxFeeParamDto,productCalBasicInfoDto);

        if(taxFeeFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("计算税费参数失败");
            return result;
        }

        if(productCalRepaymentMethodParamDto != null){
            /**
             * 计算还款方式参数
             */
            boolean repaymentMethodFlag = calulateRepaymentMethodParam(paramMap,productCalBaseTemplateEntities,productCalRepaymentMethodParamDto,productCalBasicInfoDto);

            if(repaymentMethodFlag == false){
                result.setCode(FAIL.getCode());
                result.setMessage("计算还款方式参数失败");
                return result;
            }
        }

        /**
         * 计算提前结清参数
         */
        if(productCalEarlySquareParamDto != null){
            boolean earlySquareFlag = calulateEarlySquareParam(productCalBasicInfoDto,productCalBasicParamDto,productCalEarlySquareParamDto);

            if(earlySquareFlag == false){
                result.setCode(FAIL.getCode());
                result.setMessage("计算提前结清参数失败");
                return result;
            }
        }

        /**
         * 计算联合贷参数
         */
        boolean isUnionFlag = false;
        if(productCalUnionLoanParamDto != null && productCalUnionLoanParamDto.getBankLoanRatio() != null && productCalUnionLoanParamDto.getBankSettleRate() != null && productCalUnionLoanParamDto.getIntegrativeIrrOursFactor() != null){
            isUnionFlag = true;
            boolean unionLoanFlag = calulateUnionLoanParam(paramMap,productCalBaseTemplateEntities,productCalUnionLoanParamDto,productCalBasicInfoDto);
            if(unionLoanFlag == false){
                result.setCode(FAIL.getCode());
                result.setMessage("计算联合贷参数失败");
                return result;
            }
        }

        /**
         * 计算现金流
         */
        boolean cashStremFlag = calulateCashStrem(paramMap,productCalBaseTemplateEntities,productCalculateDto,actualLoanMoneyValue,isUnionFlag);

        if(cashStremFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("计算现金流失败");
            return result;
        }

        /**
         * 计算后置方法
         */
        Result afterCalResult = afterCalDeal(paramMap,productCalBaseTemplateEntities,productCalculateDto,isUnionFlag);

        if(afterCalResult.getCode() != 200){
            return result;
        }

        /**
         * 计算返回结果
         */
        boolean resultInfoFlag = calulateResultInfo(paramMap,productCalBaseTemplateEntities,productCalculateDto,isUnionFlag);

        if(resultInfoFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("计算返回结果失败");
            return result;
        }

        /**
         * 处理小数百分比
         */
        boolean scalePercentFlag = dealScalePercent(productCalculateDto,productCalBaseTemplateEntities);

        if(scalePercentFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("处理百分比小数失败");
            return result;
        }

        result.setData(productCalculateDto);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());

        long endTime = System.currentTimeMillis();
        log.info("calculateAllParam cost time: "+(endTime - startTime)+"ms");
        log.info("calculateAllParam end");
        return result;
    }

    public List<ProductCalBaseTemplateEntity> getCalTemplates(ProductCalBasicInfoDto productCalBasicInfoDto){
        List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities = productCalBaseTemplateDao.getProductCalBaseTemplateEntityList(productCalBasicInfoDto);
        productCalBaseTemplateEntities = productCalBaseTemplateEntities.stream().map(productCalBaseTemplateEntity -> {
            productCalBaseTemplateEntity.setItem(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalBaseTemplateEntity.getItem()));
            return productCalBaseTemplateEntity;
        }).collect(Collectors.toList());
        return productCalBaseTemplateEntities;
    }

    /**
     * 校验必输项
     * @param productCalculateDto
     * @param productCalBaseTemplateEntities
     * @return
     */
    public Result checkMustParam(ProductCalculateDto productCalculateDto,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities){
        Result result = new Result();
        log.info("checkMustParam start");
        long startTime = System.currentTimeMillis();
        /**
         * 校验基本参数
         */
        ProductCalBasicParamDto productCalBasicParamDto = productCalculateDto.getProductCalBasicParamDto();
        Map<Object, Object> basicParamMap = GsonUtil.jsonToMap(GsonUtil.toJson(productCalBasicParamDto));
        List<ProductCalBaseTemplateEntity> basicParamBaseTemplateList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
            return "0".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getMust());
        }).collect(Collectors.toList());
        result = checkExist(basicParamMap,basicParamBaseTemplateList);
        if(result.getCode() != 200){
            return result;
        }

        /**
         * 校验税费参数
         */
        ProductCalTaxFeeParamDto productCalTaxFeeParamDto = productCalculateDto.getProductCalTaxFeeParamDto();
        Map<Object, Object> taxFeeParamMap = GsonUtil.jsonToMap(GsonUtil.toJson(productCalTaxFeeParamDto));
        List<ProductCalBaseTemplateEntity> taxFeeParamBaseTemplateList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
            return "1".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getMust());
        }).collect(Collectors.toList());
        result = checkExist(taxFeeParamMap,taxFeeParamBaseTemplateList);
        if(result.getCode() != 200){
            return result;
        }
        /**
         * 校验还款方式参数
         */
        ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto = productCalculateDto.getProductCalRepaymentMethodParamDto();
        Map<Object, Object> repaymentMethodParamMap = GsonUtil.jsonToMap(GsonUtil.toJson(productCalRepaymentMethodParamDto));
        List<ProductCalBaseTemplateEntity> repaymentMethodParamBaseTemplateList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
            return "2".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getMust());
        }).collect(Collectors.toList());
        result = checkExist(repaymentMethodParamMap,repaymentMethodParamBaseTemplateList);
        if(result.getCode() != 200){
            return result;
        }

        Double actualLoanMoney = productCalBasicParamDto.getActualLoanMoney();
        Double packageCarPrice = productCalRepaymentMethodParamDto.getPackageCarPrice();
        Double downpaymentsRatio = productCalRepaymentMethodParamDto.getDownpaymentsRatio();

        if(actualLoanMoney == null && (packageCarPrice == null || downpaymentsRatio == null)){
            result.setCode(FAIL.getCode());
            result.setMessage("(实际放款额)和(打包车价&首付比例)选一必填");
            return result;
        }

        /**
         * 校验提前结清参数
         */
        ProductCalEarlySquareParamDto productCalEarlySquareParamDto = productCalculateDto.getProductCalEarlySquareParamDto();
        Map<Object, Object> earlySquareParamMap = GsonUtil.jsonToMap(GsonUtil.toJson(productCalEarlySquareParamDto));
        List<ProductCalBaseTemplateEntity> earlySquareParamBaseTemplateList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
            return "3".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getMust());
        }).collect(Collectors.toList());
        result = checkExist(earlySquareParamMap,earlySquareParamBaseTemplateList);
        if(result.getCode() != 200){
            return result;
        }

        if(productCalculateDto.getProductCalculateEarlySquareDto()!=null){
            if(productCalEarlySquareParamDto == null){
                result.setCode(FAIL.getCode());
                result.setMessage("提前结清irr测算模板提前结清参数不可为空");
                return result;
            }
        }

        /**
         * 校验联合贷参数
         */
        ProductCalUnionLoanParamDto productCalUnionLoanParamDto = productCalculateDto.getProductCalUnionLoanParamDto();
        Map<Object, Object> unionLoanParamMap = GsonUtil.jsonToMap(GsonUtil.toJson(productCalUnionLoanParamDto));
        List<ProductCalBaseTemplateEntity> unionLoanParamBaseTemplateList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
            return "6".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getMust());
        }).collect(Collectors.toList());
        result = checkExist(unionLoanParamMap,unionLoanParamBaseTemplateList);
        if(result.getCode() != 200){
            return result;
        }

        if(productCalUnionLoanParamDto != null && productCalUnionLoanParamDto.getBankLoanRatio() != null && productCalUnionLoanParamDto.getBankSettleRate() != null && productCalUnionLoanParamDto.getIntegrativeIrrOursFactor() != null){
            QueryWrapper<ProductCalBaseTemplateEntity> queryWrapper = new QueryWrapper();
            queryWrapper.eq("buss_type",7777);
            queryWrapper.eq("repayment_method",productCalculateDto.getProductCalBasicInfoDto().getRepaymentMethod());
            List<ProductCalBaseTemplateEntity> unionLoanBaseTemplateEntities = productCalBaseTemplateDao.selectList(queryWrapper);
            if(unionLoanBaseTemplateEntities == null || unionLoanBaseTemplateEntities.size() == 0){
                result.setCode(FAIL.getCode());
                result.setMessage("该还款方式暂不支持联合贷");
                return result;
            }
        }

        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        long endTime = System.currentTimeMillis();
        log.info("checkMustParam cost time: "+(endTime - startTime)+"ms");
        log.info("checkMustParam end");
        return result;

    }

    /**
     * 校验参数是否存在
     * @param map
     * @param productCalBaseTemplateEntityList
     * @return
     */
    public Result checkExist(Map<Object, Object> map, List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntityList){
        Result result = new Result();
        for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : productCalBaseTemplateEntityList) {
            String item = productCalBaseTemplateEntity.getItem();
            if(map.get(item) == null){
                result.setCode(FAIL.getCode());
                result.setMessage("参数："+productCalBaseTemplateEntity.getItemDesc()+"为必传，请核查");
                result.setData(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, item));
                return result;
            }
        }
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    public boolean parseParams(Map<String,Object> paramMap,ProductCalBasicParamDto productCalBasicParamDto,ProductCalTaxFeeParamDto productCalTaxFeeParamDto,ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto,ProductCalEarlySquareParamDto productCalEarlySquareParamDto,ProductCalculateEarlySquareDto productCalculateEarlySquareDto,ProductCalProfitDetailDto productCalProfitDetailDto,ProductCalUnionLoanParamDto productCalUnionLoanParamDto,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            log.info("parseParams start");
            /**
             * 阶梯贷修改还款方式参数中月供上升比例为对应得到相反数
             */

            if(ProductCalRepaymentMethodEnum.JTD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                productCalRepaymentMethodParamDto.setDecreaseInMonthlyPaymentRatio(-1*productCalRepaymentMethodParamDto.getDecreaseInMonthlyPaymentRatio());
            }

            paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalBasicParamDto)));
            paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalTaxFeeParamDto)));
            if(productCalRepaymentMethodParamDto != null){
                paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalRepaymentMethodParamDto)));
            }
            if(productCalEarlySquareParamDto != null){
                paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalEarlySquareParamDto)));
            }
            if(productCalculateEarlySquareDto != null){
                paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalculateEarlySquareDto)));
                paramMap.put("earlySquareTimeLimit",productCalculateEarlySquareDto.getEarlySquareTimeLimit());
            }
            paramMap.put("timeLimit",productCalBasicParamDto.getTimeLimit());

            /**
             * 利润测算参数添加
             */

            if(productCalProfitDetailDto != null){
                paramMap.put("startMonth",productCalProfitDetailDto.getStartMonth());
            }
            /**
             * 联合贷参数
             */
            if(productCalUnionLoanParamDto != null){
                paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalUnionLoanParamDto)));
            }
            log.info("parseParams end");
            return true;
        }catch(Exception e){
            log.error("parseParams error",e);
            return false;
        }
    }

    public Result beforeCalDeal(List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,Map<String,Object> paramMap,ProductCalculateDto productCalculateDto){
        Result result = new Result();
        try{
            log.info("beforeCalDeal start");
            long startTime = System.currentTimeMillis();
            /**
             * 提前结清公式初次模板替换
             */
            ProductCalculateEarlySquareDto productCalculateEarlySquareDto = productCalculateDto.getProductCalculateEarlySquareDto();
            ProductCalBasicParamDto productCalBasicParamDto = productCalculateDto.getProductCalBasicParamDto();
            ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
            if(productCalculateEarlySquareDto!=null){
                productCalBasicParamDto.setTimeLimit(productCalculateEarlySquareDto.getTimeLimit());
                boolean dealEarlySquareflag = dealEarlySquareForm(productCalBaseTemplateEntities, productCalBasicInfoDto);
                if(dealEarlySquareflag == false){
                    result.setCode(FAIL.getCode());
                    result.setMessage("提前结清公式初次模板替换失败");
                    return result;
                }
            }


            /**
             * 计算现值总和
             */
            if (ProductCalRepaymentMethodEnum.DEBJ.type().equals(productCalBasicInfoDto.getRepaymentMethod()) || ProductCalRepaymentMethodEnum.JTD.type().equals(productCalBasicInfoDto.getRepaymentMethod())) {
                boolean flag = false;

                flag = calPresentValueSum(paramMap, productCalBaseTemplateEntities, productCalculateDto, productCalBasicInfoDto.getRepaymentMethod());


                if (flag == false) {
                    result.setCode(FAIL.getCode());
                    result.setMessage("计算现值总和失败");
                    return result;
                }
            }

            /**
             * 处理动态分段贷特殊公式：还款期次、还款比例、还款额、月供、贴息、现金流收入、首月现金流收入、现金流还款计划、首月现金流还款计划、现金流应收客户利息、首月现金流应收客户利息
             */
            ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto = productCalculateDto.getProductCalRepaymentMethodParamDto();
            if(ProductCalRepaymentMethodEnum.FDD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                boolean dealFddFormulaFlag = dealFddFormula(paramMap,productCalBaseTemplateEntities,productCalRepaymentMethodParamDto);
                if(dealFddFormulaFlag == false){
                    result.setCode(FAIL.getCode());
                    result.setMessage("处理分段贷失败");
                    return result;
                }
            }

            /**
             * 处理Roa参数
             */
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityRoa = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamRoaParam".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            Double capitalCost = (Double)paramMap.get("capitalCost");
            productCalBaseTemplateEntityRoa.setFormula(capitalCost.toString());
            paramMap.put("cashStreamRoaParam",capitalCost);

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            long endTime = System.currentTimeMillis();
            log.info("beforeCalDeal cost time: "+(endTime - startTime)+"ms");
            log.info("beforeCalDeal end");
        }catch(Exception e){
            log.error("beforeCalDeal error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("计算前置方法处理失败");
        }
        return result;
    }

    public void dealEarlyFormula(ProductCalBaseTemplateEntity productCalBaseTemplateEntity,Integer subsectionLendNum){
        StringBuffer stringBufferFir = new StringBuffer("");
        StringBuffer stringBufferSec = new StringBuffer("");
        StringBuffer stringBufferkh = new StringBuffer("");
        String formula = productCalBaseTemplateEntity.getFormula();
        String[] split = formula.split("~");
        for (int i = 1; i <= subsectionLendNum; i++) {
            if("0".equals(productCalBaseTemplateEntity.getPamType())){
                String replaFir = split[1];
                replaFir = replaFir.replaceAll("Numtime",String.valueOf(i));
                replaFir = replaFir.replaceAll("Numj1",String.valueOf(i-1));
                if(i != subsectionLendNum){
                    stringBufferFir.append(replaFir+",");
                }else{
                    stringBufferFir.append(replaFir);
                }
                String replaSec = split[3];
                replaSec = replaSec.replaceAll("Numtime",String.valueOf(i));
                replaSec = replaSec.replaceAll("Numj1",String.valueOf(i-1));
                if(i != subsectionLendNum){
                    stringBufferSec.append(replaSec+",");
                }else{
                    stringBufferSec.append(replaSec);
                }
            }else{
                String strRepl = split[1];
                strRepl = strRepl.replaceAll("Numtime",String.valueOf(i));
                stringBufferFir.append(strRepl+",");
                stringBufferkh.append(")");
            }
        }
        if("0".equals(productCalBaseTemplateEntity.getPamType())){
            formula = formula.replace("~"+split[1]+"~",stringBufferFir.toString()).replace("~"+split[3]+"~",stringBufferSec.toString());
        }else{
            formula = formula.replace("~"+split[1]+"~",stringBufferFir.toString());
            formula = formula+stringBufferkh.toString();
        }
        productCalBaseTemplateEntity.setFormula(formula);
    }

    public boolean dealEarlySquareForm(List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalBasicInfoDto productCalBasicInfoDto){
        /**
         * 替换提前结清中非现金流计算参数
         */
        try{
            QueryWrapper<ProductCalBaseTemplateEntity> earlySquareQueryWrapper = new QueryWrapper();
            earlySquareQueryWrapper.eq("buss_type",9999).ne("pam_type","4").eq("repayment_method",productCalBasicInfoDto.getRepaymentMethod());
            List<ProductCalBaseTemplateEntity> productCalBaseTemplateEarlySquareEntityList = productCalBaseTemplateDao.selectList(earlySquareQueryWrapper);
            productCalBaseTemplateEarlySquareEntityList = productCalBaseTemplateEarlySquareEntityList.stream().map(productCalBaseTemplateEntity -> {
                productCalBaseTemplateEntity.setItem(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalBaseTemplateEntity.getItem()));
                return productCalBaseTemplateEntity;
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateEntity productCalBaseTemplateEarlySquareEntity : productCalBaseTemplateEarlySquareEntityList) {
                boolean flag = true;
                for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : productCalBaseTemplateEntities) {
                    String item = productCalBaseTemplateEarlySquareEntity.getItem();
                    if(productCalBaseTemplateEntity.getItem().equals(item)){
                        productCalBaseTemplateEntity.setFormula(productCalBaseTemplateEarlySquareEntity.getFormula());
                        flag = false;
                    }
                }
                if(flag){
                    productCalBaseTemplateEntities.add(productCalBaseTemplateEarlySquareEntity);
                }
            }
            return true;
        }catch(Exception e){
            log.error("dealEarlySquareForm error",e);
            return false;
        }
    }

    private boolean calPresentValueSum(Map<String, Object> paramMap, List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities, ProductCalculateDto productCalculateDto, Integer repaymentMethod) {
        Integer timeLimit = (Integer)paramMap.get("timeLimit");
        Mapper mapper = DozerBeanMapperBuilder.buildDefault();
        List<ProductCalBaseTemplateEntity> cashStreamTemplateEntitiesBak = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
            return "4".equals(productCalBaseTemplateEntity.getPamType());
        }).map(productCalBaseTemplateEntity -> {
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityBak = mapper.map(productCalBaseTemplateEntity,ProductCalBaseTemplateEntity.class);
            return productCalBaseTemplateEntityBak;
        }).collect(Collectors.toList());
        List<Double> presentValueList = new ArrayList<>();
        List<Double> postFloatingDiscountRateList = new ArrayList<>();

        if (ProductCalRepaymentMethodEnum.JTD.type().equals(repaymentMethod)) {
            for (int i = 0; i <= timeLimit; i++) {
                /**
                 * 对于现金流计算，每次都要重新对现金流公式赋值
                 */
                Integer month = i;

                ProductCalCashStreamDto productCalCashStreamDto = new ProductCalCashStreamDto();
                productCalCashStreamDto.setMonth(month);

                paramMap.put("month", month);

                Map<String, Object> map = new HashMap<>();
                ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
                /**
                 * 计算首月信息
                 */
                if (month == 1) {

                    ProductCalBaseTemplateEntity productCalBaseTemplateEntityDiscountRate = cashStreamTemplateEntitiesBak.stream().filter(productCalBaseTemplateEntity -> {
                        return "discountRate".equals(productCalBaseTemplateEntity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto, productCalBaseTemplateEntities, productCalBaseTemplateEntityDiscountRate, paramMap, map, month, productCalBasicInfoDto);

                    ProductCalBaseTemplateEntity productCalBaseTemplateEntityPostFloatingDiscountRate = cashStreamTemplateEntitiesBak.stream().filter(productCalBaseTemplateEntity -> {
                        return "postFloatingDiscountRate".equals(productCalBaseTemplateEntity.getItem());
                    }).findFirst().get();
                    postFloatingDiscountRateList.add(dealFormula(productCalCashStreamDto, productCalBaseTemplateEntities, productCalBaseTemplateEntityPostFloatingDiscountRate, paramMap, map, month, productCalBasicInfoDto));
                }

                if (month >= 2) {
                    List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntitiesCashStream = productCalBaseTemplateEntities.stream().map(productCalBaseTemplateEntity -> {
                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityBak = mapper.map(productCalBaseTemplateEntity, ProductCalBaseTemplateEntity.class);
                        return productCalBaseTemplateEntityBak;
                    }).collect(Collectors.toList());

                    ProductCalBaseTemplateEntity productCalBaseTemplateEntityDiscountRate = productCalBaseTemplateEntitiesCashStream.stream().filter(productCalBaseTemplateEntity -> {
                        return "discountRate".equals(productCalBaseTemplateEntity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto, productCalBaseTemplateEntities, productCalBaseTemplateEntityDiscountRate, paramMap, map, month, productCalBasicInfoDto);

                    ProductCalBaseTemplateEntity productCalBaseTemplateEntityPostFloatingDiscountRate = productCalBaseTemplateEntitiesCashStream.stream().filter(productCalBaseTemplateEntity -> {
                        return "postFloatingDiscountRate".equals(productCalBaseTemplateEntity.getItem());
                    }).findFirst().get();
                    postFloatingDiscountRateList.add(dealFormula(productCalCashStreamDto, productCalBaseTemplateEntities, productCalBaseTemplateEntityPostFloatingDiscountRate, paramMap, map, month, productCalBasicInfoDto));
                }
            }
            paramMap.put("postFloatingDiscountRateSum", postFloatingDiscountRateList.stream().mapToDouble(Double::doubleValue).sum());
        }

        for(int i=0;i<=timeLimit;i++){
            /**
             * 对于现金流计算，每次都要重新对现金流公式赋值
             */
            Integer month = i;

            ProductCalCashStreamDto productCalCashStreamDto = new ProductCalCashStreamDto();
            productCalCashStreamDto.setMonth(month);

            paramMap.put("month",month);

            Map<String,Object> map = new HashMap<>();
            ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
            /**
             * 计算首月信息
             */
            if(month == 1){
                /**
                 * 计算首月利息支出
                 */
                ProductCalBaseTemplateEntity productCalBaseTemplateEntityPresentValueFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "presentValueFirst".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                presentValueList.add(dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityPresentValueFirst,paramMap,map,month,productCalBasicInfoDto));
            }

            if(month >= 2){
                //重新赋值一遍公式 不然会一直是数字
                List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntitiesCashStream = productCalBaseTemplateEntities.stream().map(productCalBaseTemplateEntity -> {
                    ProductCalBaseTemplateEntity productCalBaseTemplateEntityBak = mapper.map(productCalBaseTemplateEntity,ProductCalBaseTemplateEntity.class);
                    return productCalBaseTemplateEntityBak;
                }).collect(Collectors.toList());

                ProductCalBaseTemplateEntity productCalBaseTemplateEntityPresentValue = productCalBaseTemplateEntitiesCashStream.stream().filter(productCalBaseTemplateEntity -> {
                    return "presentValue".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                presentValueList.add(dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityPresentValue,paramMap,map,month,productCalBasicInfoDto));
            }
        }
        paramMap.put("presentValueSum",presentValueList.stream().mapToDouble(Double::doubleValue).sum());
        return true;
    }

    public boolean dealFddFormula(Map<String, Object> paramMap,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto){
        try{
            /**
             * 动态分段贷笔数
             */
            List<ProductCalSubsectionLendDto> productCalSubsectionLendDtoList = productCalRepaymentMethodParamDto.getProductCalSubsectionLendDtoList();
            Integer subsectionLendNum = productCalSubsectionLendDtoList.size();
            paramMap.put("subRepaymentRatio0",0);
            paramMap.put("subRepaymentTimeLimit0",0);
            paramMap.put("subRepaymentMoney0",0);
            for (ProductCalSubsectionLendDto productCalSubsectionLendDto : productCalSubsectionLendDtoList) {
                /**
                 * 动态分段贷第几笔
                 */
                Integer subRepaymentCount = productCalSubsectionLendDto.getSubRepaymentCount();
                /**
                 * 动态分段贷期次
                 */
                Integer subRepaymentTimeLimit = productCalSubsectionLendDto.getSubRepaymentTimeLimit();
                /**
                 * 动态分段贷比例
                 */
                Double subRepaymentRatio = productCalSubsectionLendDto.getSubRepaymentRatio();

                paramMap.put("subRepaymentTimeLimit"+subRepaymentCount,subRepaymentTimeLimit);
                paramMap.put("subRepaymentRatio"+subRepaymentCount,subRepaymentRatio);

                ProductCalBaseTemplateEntity subRepaymentMoneyBaseTemplateEntity = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "subRepaymentMoneyNumtime".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                String formula = subRepaymentMoneyBaseTemplateEntity.getFormula();
                formula = formula.replace("Numtime",String.valueOf(subRepaymentCount));

                formula = formula.replaceAll("@packageCarPrice@",String.valueOf(paramMap.get("packageCarPrice")));
                formula = formula.replaceAll("@subRepaymentRatio"+subRepaymentCount+"@",String.valueOf(paramMap.get("subRepaymentRatio"+subRepaymentCount)));

                Map<String,Object> mapRepl = new HashMap<>();
                productCalSubsectionLendDto.setSubRepaymentMoney((Double)AviatorUtils.calculateValue(mapRepl,formula));
            }
            /**
             * 处理月供、贴息、现金流收入、首月现金流收入、现金流还款计划、首月现金流还款计划、现金流应收客户利息、首月现金流应收客户利息公式
             */
            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : productCalBaseTemplateEntities) {
                if("monthPayment".equals(productCalBaseTemplateEntity.getItem())||"subInterest".equals(productCalBaseTemplateEntity.getItem())||"cashStreamIncom".equals(productCalBaseTemplateEntity.getItem())||"cashStreamIncomFirst".equals(productCalBaseTemplateEntity.getItem())
                        ||"cashStreamRepaymentPlan".equals(productCalBaseTemplateEntity.getItem())||"cashStreamRepaymentPlanFirst".equals(productCalBaseTemplateEntity.getItem())||"cashStreamReceivableCustomerInterest".equals(productCalBaseTemplateEntity.getItem())||"cashStreamReceivableCustomerInterestFirst".equals(productCalBaseTemplateEntity.getItem())){
                    dealEarlyFormula(productCalBaseTemplateEntity,subsectionLendNum);
                }
            }
            return true;
        }catch(Exception e){
            log.error("dealFddFormula error",e);
            return false;
        }
    }

    /**
     * 计算基本参数
     * @param paramMap
     * @param productCalBaseTemplateEntities
     * @param productCalBasicParamDto
     * @return
     */
    public boolean calulateBasicParam(Map<String,Object> paramMap,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalBasicParamDto productCalBasicParamDto,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            log.info("calulateBasicParam start");
            long startTime = System.currentTimeMillis();
            Map<String,Object> map = new HashMap<>();

            Double actualLoanMoneyValue = productCalBasicParamDto.getActualLoanMoney();
            /**
             * 实际放款额特殊需要先计算
             */
            if(!ProductCalRepaymentMethodEnum.DEBJ.type().equals(productCalBasicInfoDto.getRepaymentMethod())) {
                ProductCalBaseTemplateEntity actualLoanMoneyBaseTemplateEntity = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "actualLoanMoney".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                if (actualLoanMoneyValue == null) {
                    dealFormula(productCalBasicParamDto, productCalBaseTemplateEntities, actualLoanMoneyBaseTemplateEntity, paramMap, map, null, productCalBasicInfoDto);
                    paramMap.put("actualLoanMoney", actualLoanMoneyBaseTemplateEntity.getFormula());
                    actualLoanMoneyValue = productCalBasicParamDto.getActualLoanMoney();
                }
            }


            /**
             * 计算基本公式参数
             */
            List<ProductCalBaseTemplateEntity> basicFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "0".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : basicFormularList) {
                dealFormula(productCalBasicParamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalBasicInfoDto);
            }

            /**
             * 计算公式套公式参数
             */
            List<ProductCalBaseTemplateEntity> formularAndFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "0".equals(productCalBaseTemplateEntity.getPamType()) && "2".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : formularAndFormularList) {
                if("actualLoanMoney".equals(productCalBaseTemplateEntity.getItem())){
                    continue;
                }
                dealFormula(productCalBasicParamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalBasicInfoDto);
            }
            long endTime = System.currentTimeMillis();
            log.info("calulateBasicParam cost time: "+(endTime - startTime)+"ms");
            log.info("calulateBasicParam end");
        }catch(Exception e){
            log.error("calulateBasicParam error",e);
            return false;
        }

        return true;
    }

    /**
     * 计算税费参数
     * @param paramMap
     * @param productCalBaseTemplateEntities
     * @param productCalTaxFeeParamDto
     * @return
     */
    public boolean calulateTaxFeeParam(Map<String,Object> paramMap,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalTaxFeeParamDto productCalTaxFeeParamDto,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            log.info("calulateTaxFeeParam start");
            long startTime = System.currentTimeMillis();
            Map<String,Object> map = new HashMap<>();
            /**
             * 计算基本公式参数
             */
            List<ProductCalBaseTemplateEntity> basicFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "1".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : basicFormularList) {
                dealFormula(productCalTaxFeeParamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalBasicInfoDto);
            }

            /**
             * 计算公式套公式参数
             */
            List<ProductCalBaseTemplateEntity> formularAndFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "1".equals(productCalBaseTemplateEntity.getPamType()) && "2".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : formularAndFormularList) {
                dealFormula(productCalTaxFeeParamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalBasicInfoDto);
            }
            paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalTaxFeeParamDto)));

            long endTime = System.currentTimeMillis();
            log.info("calulateTaxFeeParam cost time: "+(endTime - startTime)+"ms");
            log.info("calulateTaxFeeParam end");
        }catch(Exception e){
            log.error("calulateBasicParam error",e);
            return false;
        }

        return true;
    }

    /**
     * 计算还款方式参数
     * @param paramMap
     * @param productCalBaseTemplateEntities
     * @param productCalRepaymentMethodParamDto
     * @return
     */
    public boolean calulateRepaymentMethodParam(Map<String,Object> paramMap,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            log.info("calulateRepaymentMethodParam start");
            long startTime = System.currentTimeMillis();

            Map<String,Object> map = new HashMap<>();
            /**
             * 计算基本公式参数
             */
            List<ProductCalBaseTemplateEntity> basicFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "2".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : basicFormularList) {
                dealFormula(productCalRepaymentMethodParamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalBasicInfoDto);
            }
            paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalRepaymentMethodParamDto)));
            long endTime = System.currentTimeMillis();
            log.info("calulateRepaymentMethodParam cost time: "+(endTime - startTime)+"ms");
            log.info("calulateRepaymentMethodParam end");
        }catch(Exception e){
            log.error("calulateRepaymentMethodParam error",e);
            return false;
        }
        return true;
    }

    /**
     * 计算提前结清参数
     * @param productCalEarlySquareParamDto
     * @return
     */
    public boolean calulateEarlySquareParam(ProductCalBasicInfoDto productCalBasicInfoDto,ProductCalBasicParamDto productCalBasicParamDto,ProductCalEarlySquareParamDto productCalEarlySquareParamDto){
        /**
         * 计算提前结清概率
         */
        try{
            log.info("calulateEarlySquareParam start");
            Result earlySquareProbabilityInfo = getEarlySquareProbabilityInfo(productCalBasicInfoDto.getBusinessType(), productCalBasicParamDto.getCustomerInterestRate(), productCalBasicParamDto.getActualInterestRate(), productCalBasicParamDto.getTimeLimit());
            List<ProductCalEarlySquareProbabilityEntity> earlySquareProbabilityEntityList = (List<ProductCalEarlySquareProbabilityEntity>)earlySquareProbabilityInfo.getData();
            if(earlySquareProbabilityEntityList != null && earlySquareProbabilityEntityList.size()>0){
                Double earlySquareProbability = earlySquareProbabilityEntityList.stream().filter(productCalEarlySquareProbabilityEntity -> {
                    return !productCalEarlySquareProbabilityEntity.getPayoutRentalId().equals(earlySquareProbabilityEntityList.size());
                }).mapToDouble(ProductCalEarlySquareProbabilityEntity::getPayoutProbability).sum();
                productCalEarlySquareParamDto.setEarlySquareProbability(earlySquareProbability);
            }
            log.info("calulateEarlySquareParam end");
        }catch(Exception e){
            log.error("calulateEarlySquareParam error",e);
            return false;
        }
        return true;
    }

    /**
     * 计算联合贷参数
     * @param paramMap
     * @param productCalBaseTemplateEntities
     * @param productCalUnionLoanParamDto
     * @param productCalBasicInfoDto
     * @return
     */
    public boolean calulateUnionLoanParam(Map<String,Object> paramMap,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalUnionLoanParamDto productCalUnionLoanParamDto,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            log.info("calulateUnionLoanParam start");
            long startTime = System.currentTimeMillis();

            Map<String,Object> map = new HashMap<>();
            /**
             * 计算基本公式参数
             */
            List<ProductCalBaseTemplateEntity> basicFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "6".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : basicFormularList) {
                dealFormula(productCalUnionLoanParamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalBasicInfoDto);
            }
            paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalUnionLoanParamDto)));
            long endTime = System.currentTimeMillis();
            log.info("calulateUnionLoanParam cost time: "+(endTime - startTime)+"ms");
            log.info("calulateUnionLoanParam end");
        }catch(Exception e){
            log.error("calulateUnionLoanParam error",e);
            return false;
        }
        return true;
    }

    /**
     * 计算现金流
     * @param paramMap
     * @param productCalBaseTemplateEntities
     * @param productCalculateDto
     * @return
     */
    public boolean calulateCashStrem(Map<String,Object> paramMap,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities, ProductCalculateDto productCalculateDto,Double actualLoanMoneyValue,boolean isUnionFlag){
        try{
            log.info("calulateCashStrem start");
            long startTime = System.currentTimeMillis();

            ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
            /**
             * 将现金流模板公式保存下来，以后不用每次都重新查库，直接用模板中的内容就可以，增大计算效率
             */

            Mapper mapper = DozerBeanMapperBuilder.buildDefault();
            List<ProductCalBaseTemplateEntity> cashStreamTemplateEntitiesBak = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "4".equals(productCalBaseTemplateEntity.getPamType());
            }).map(productCalBaseTemplateEntity -> {
                ProductCalBaseTemplateEntity productCalBaseTemplateEntityBak = mapper.map(productCalBaseTemplateEntity,ProductCalBaseTemplateEntity.class);
                return productCalBaseTemplateEntityBak;
            }).collect(Collectors.toList());

            /**
             * 如果拨备率和管理费分摊系数不为空的时候计算利润
             */
            List<ProductCalBaseTemplateEntity> productCalBaseTemplateProfitCalEntityListBak = new ArrayList<>();
            if(productCalculateDto.getProductCalBasicParamDto().getProvisionRate() != null && productCalculateDto.getProductCalBasicParamDto().getManageFeeShareCoefficient() != null){
                QueryWrapper<ProductCalBaseTemplateEntity> queryWrapper = new QueryWrapper();
                queryWrapper.eq("buss_type",8888).eq("repayment_method",productCalBasicInfoDto.getRepaymentMethod());
                productCalBaseTemplateProfitCalEntityListBak = productCalBaseTemplateDao.selectList(queryWrapper);
                productCalBaseTemplateProfitCalEntityListBak = productCalBaseTemplateProfitCalEntityListBak.stream().map(productCalBaseTemplateEntity -> {
                    productCalBaseTemplateEntity.setItem(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalBaseTemplateEntity.getItem()));
                    return productCalBaseTemplateEntity;
                }).collect(Collectors.toList());
                boolean dealProfitTemplateFlag = dealProfitTemplate(productCalBaseTemplateProfitCalEntityListBak,productCalBaseTemplateEntities,mapper);
                if(dealProfitTemplateFlag == false){
                    log.info("处理利润公式失败");
                    return false;
                }
            }
            Integer timeLimit = (Integer)paramMap.get("timeLimit");
            /**
             * 如果联合贷参数不为空，需要处理联合贷参数
             */
            List<ProductCalBaseTemplateEntity> productCalBaseTemplateUnionLoanEntityListBak = new ArrayList<>();
            if(isUnionFlag){
                QueryWrapper<ProductCalBaseTemplateEntity> queryWrapper = new QueryWrapper();
                queryWrapper.eq("buss_type",7777).eq("repayment_method",productCalBasicInfoDto.getRepaymentMethod()).eq("pam_type","4");
                productCalBaseTemplateUnionLoanEntityListBak = productCalBaseTemplateDao.selectList(queryWrapper);
                productCalBaseTemplateUnionLoanEntityListBak = productCalBaseTemplateUnionLoanEntityListBak.stream().map(productCalBaseTemplateEntity -> {
                    productCalBaseTemplateEntity.setItem(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalBaseTemplateEntity.getItem()));
                    return productCalBaseTemplateEntity;
                }).collect(Collectors.toList());
                boolean dealUnionLoanTemplateFlag = dealUnionLoanTemplate(productCalBaseTemplateUnionLoanEntityListBak,productCalBaseTemplateEntities,mapper);
                if(dealUnionLoanTemplateFlag == false){
                    log.info("处理联合贷公式失败");
                    return false;
                }
                timeLimit = timeLimit+1;
            }

            List<ProductCalCashStreamDto> productCalCashStreamDtoList = new ArrayList<>();

            for(int i=0;i<=timeLimit;i++){
                /**
                 * 对于现金流计算，每次都要重新对现金流公式赋值
                 */
                Integer month = i;
                Map<String,Object> map = new HashMap<>();
                ProductCalCashStreamDto productCalCashStreamDto = new ProductCalCashStreamDto();
                productCalCashStreamDto.setMonth(month);
                paramMap.put("month",month);

                if(month > 2){
                    productCalBaseTemplateEntities = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                        return !"4".equals(productCalBaseTemplateEntity.getPamType());
                    }).collect(Collectors.toList());
                    List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntitiesCashStream = cashStreamTemplateEntitiesBak.stream().map(productCalBaseTemplateEntity -> {
                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityBak = mapper.map(productCalBaseTemplateEntity,ProductCalBaseTemplateEntity.class);
                        return productCalBaseTemplateEntityBak;
                    }).collect(Collectors.toList());
                    productCalBaseTemplateEntities.addAll(productCalBaseTemplateEntitiesCashStream);

                    if(productCalculateDto.getProductCalBasicParamDto().getProvisionRate() != null && productCalculateDto.getProductCalBasicParamDto().getManageFeeShareCoefficient() != null){
                        List<ProductCalBaseTemplateEntity> productCalBaseTemplateProfitCalEntityList = productCalBaseTemplateProfitCalEntityListBak.stream().map(productCalBaseTemplateEntity -> {
                            ProductCalBaseTemplateEntity productCalBaseTemplateEntityBak = mapper.map(productCalBaseTemplateEntity,ProductCalBaseTemplateEntity.class);
                            return productCalBaseTemplateEntityBak;
                        }).collect(Collectors.toList());
                        productCalBaseTemplateEntities.addAll(productCalBaseTemplateProfitCalEntityList);
                    }

                    if(isUnionFlag){
                        dealUnionLoanTemplate(productCalBaseTemplateUnionLoanEntityListBak,productCalBaseTemplateEntities,mapper);
                    }
                }

                /**
                 * 现金流计算，首先判断是否是提前结清，并且已经达到提前结清期次，如果是则查询数据库获取提前结清公式，替换模板中的公式
                 */
                if(productCalculateDto.getProductCalculateEarlySquareDto()!=null && month.equals(productCalculateDto.getProductCalculateEarlySquareDto().getEarlySquareTimeLimit())){
                    /**
                     * 提前结清公式替换
                     */
                    boolean replaceEarlySquareFlag = replaceEarlySquareFormula(productCalBasicInfoDto,productCalBaseTemplateEntities);
                    if(replaceEarlySquareFlag == false){
                        log.info("替换提前结清公式失败");
                        return false;
                    }
                }

                /**
                 * 处理现金流日期
                 */
                boolean dealDateFlag = dealCashStreamDate(month,paramMap,productCalCashStreamDto);
                if(dealDateFlag == false){
                    log.info("处理现金流日期失败");
                    return false;
                }

                /**
                 * 计算通用公式
                 */
                boolean calNormalFormulaFlag = calNormalFormula(month,productCalBaseTemplateEntities,paramMap,map,productCalCashStreamDto,productCalBasicInfoDto);
                if(calNormalFormulaFlag == false){
                    log.info("计算现金流通用公式失败");
                    return false;
                }

                /**
                 * 计算零月信息
                 */
                if(month == 0){
                    boolean zeroFlag = calZeroSpecialFormula(month,actualLoanMoneyValue,productCalBaseTemplateEntities,productCalCashStreamDto,paramMap,map,productCalBasicInfoDto);
                    if(zeroFlag == false){
                        log.info("计算现金流0月特殊公式失败");
                        return false;
                    }
                }

                /**
                 * 计算首月信息
                 */
                if(month == 1){
                    boolean firstFlag = calFirstSpecialFormula(month,actualLoanMoneyValue,productCalBaseTemplateEntities,productCalCashStreamDto,paramMap,map,productCalBasicInfoDto,productCalculateDto.getProductCalBasicParamDto(),isUnionFlag);
                    if(firstFlag == false){
                        log.info("计算现金流首月特殊公式失败");
                        return false;
                    }
                }

                /**
                 * 计算2月及之后信息
                 */
                if(month >= 2){
                    boolean firstFlag = calSecondAndMoreFormula(month,productCalBaseTemplateEntities,productCalCashStreamDto,paramMap,map,productCalBasicInfoDto,productCalculateDto.getProductCalBasicParamDto(),cashStreamTemplateEntitiesBak,productCalBaseTemplateProfitCalEntityListBak,mapper,isUnionFlag);
                    if(firstFlag == false){
                        log.info("计算现金流大于等于2月公式失败");
                        return false;
                    }
                }

                productCalCashStreamDtoList.add(productCalCashStreamDto);
                productCalculateDto.setProductCalCashStreamDtoList(productCalCashStreamDtoList);

                /**
                 * 如果是提前结清，并且月数等于提前结清月数，则跳出循环
                 */
                if(productCalculateDto.getProductCalculateEarlySquareDto()!=null && month.equals(productCalculateDto.getProductCalculateEarlySquareDto().getEarlySquareTimeLimit())){
                    break;
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("calulateCashStream end");
        }catch(Exception e){
            log.error("calulateCashStream error：{}",e);
            return false;
        }
        return true;

    }

    public boolean dealProfitTemplate(List<ProductCalBaseTemplateEntity> productCalBaseTemplateProfitCalEntityListBak,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,Mapper mapper){
        try{
            List<ProductCalBaseTemplateEntity> productCalBaseTemplateProfitCalEntityList = productCalBaseTemplateProfitCalEntityListBak.stream().map(productCalBaseTemplateEntity -> {
                ProductCalBaseTemplateEntity productCalBaseTemplateEntityBak = mapper.map(productCalBaseTemplateEntity,ProductCalBaseTemplateEntity.class);
                return productCalBaseTemplateEntityBak;
            }).collect(Collectors.toList());
            productCalBaseTemplateEntities.addAll(productCalBaseTemplateProfitCalEntityList);
            return true;
        }catch(Exception e){
            log.error("dealProfitTemplate error",e);
            return false;
        }
    }

    public boolean dealUnionLoanTemplate(List<ProductCalBaseTemplateEntity> productCalBaseTemplateUnionLoanEntityListBak,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,Mapper mapper){
        try{
            List<ProductCalBaseTemplateEntity> productCalBaseTemplateUnionLoanEntityList = productCalBaseTemplateUnionLoanEntityListBak.stream().map(productCalBaseTemplateEntity -> {
                ProductCalBaseTemplateEntity productCalBaseTemplateEntityBak = mapper.map(productCalBaseTemplateEntity,ProductCalBaseTemplateEntity.class);
                return productCalBaseTemplateEntityBak;
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateEntity productCalBaseTemplateEarlySquareEntity : productCalBaseTemplateUnionLoanEntityList) {
                boolean flag = true;
                String item = productCalBaseTemplateEarlySquareEntity.getItem();
                for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : productCalBaseTemplateEntities) {
                    if(productCalBaseTemplateEntity.getItem().equals(item)){
                        productCalBaseTemplateEntity.setFormula(productCalBaseTemplateEarlySquareEntity.getFormula());
                        flag = false;
                    }
                }
                if(flag){
                    productCalBaseTemplateEntities.add(productCalBaseTemplateEarlySquareEntity);
                }
            }

//            Iterator<ProductCalBaseTemplateEntity> iterator = productCalBaseTemplateEntities.iterator();
//            while(iterator.hasNext()){
//                ProductCalBaseTemplateEntity productCalBaseTemplateEntity = iterator.next();
//                if("cashStreamActualPrincipalFirst".equals(productCalBaseTemplateEntity.getItem()) || "cashStreamActualPrincipal".equals(productCalBaseTemplateEntity.getItem()) || "cashStreamActualInterestFirst".equals(productCalBaseTemplateEntity.getItem()) || "cashStreamActualInterest".equals(productCalBaseTemplateEntity.getItem())){
//                    iterator.remove();
//                }
//            }
            return true;
        }catch(Exception e){
            log.error("dealUnionLoanTemplate error",e);
            return false;
        }
    }

    public boolean replaceEarlySquareFormula(ProductCalBasicInfoDto productCalBasicInfoDto,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities){
        try{
            /**
             * 查询提前结清公式
             */
            QueryWrapper<ProductCalBaseTemplateEntity> queryWrapper = new QueryWrapper();
            queryWrapper.eq("buss_type",9999).eq("pam_type","4").eq("repayment_method",productCalBasicInfoDto.getRepaymentMethod());
            List<ProductCalBaseTemplateEntity> productCalBaseTemplateEarlySquareEntityList = productCalBaseTemplateDao.selectList(queryWrapper);
            productCalBaseTemplateEarlySquareEntityList = productCalBaseTemplateEarlySquareEntityList.stream().map(productCalBaseTemplateEntity -> {
                productCalBaseTemplateEntity.setItem(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalBaseTemplateEntity.getItem()));
                return productCalBaseTemplateEntity;
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateEntity productCalBaseTemplateEarlySquareEntity : productCalBaseTemplateEarlySquareEntityList) {
                boolean flag = true;
                String item = productCalBaseTemplateEarlySquareEntity.getItem();
                for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : productCalBaseTemplateEntities) {
                    if(productCalBaseTemplateEntity.getItem().equals(item)){
                        productCalBaseTemplateEntity.setFormula(productCalBaseTemplateEarlySquareEntity.getFormula());
                        flag = false;
                    }
                }
                if(flag){
                    productCalBaseTemplateEntities.add(productCalBaseTemplateEarlySquareEntity);
                }
            }
            return true;
        }catch(Exception e){
            log.error("replaceEarlySquareFormula error",e);
            return false;
        }
    }

    public boolean dealCashStreamDate(Integer month,Map<String,Object> paramMap,ProductCalCashStreamDto productCalCashStreamDto){
        try{
            /**
             * 获取日期  2017-10-15 格式
             */
            String activationDate = "";
            if(paramMap.get("startMonth")!=null){
                String startMonth = (String)paramMap.get("startMonth");
                activationDate = startMonth+"-15";
            }else{
                activationDate = LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), 15).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            paramMap.put("activationDate",activationDate);
            productCalCashStreamDto.setCashStreamDate(LocalDate.parse(activationDate).plusMonths(month).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

            paramMap.put(month+"dueDate",productCalCashStreamDto.getCashStreamDate());
            return true;
        }catch(Exception e){
            log.error("dealDate error",e);
            return false;
        }
    }

    public boolean calNormalFormula(Integer month,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,Map<String,Object> paramMap,Map<String,Object> map,ProductCalCashStreamDto productCalCashStreamDto,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            /**
             * 计算基本公式参数
             */
            List<ProductCalBaseTemplateEntity> basicFormularList;
            if(month == 0){
                basicFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "4".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getDataType()) && productCalBaseTemplateEntity.getItem().endsWith("Zero");
                }).collect(Collectors.toList());
            }else if(month == 1){
                basicFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "4".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getDataType()) && productCalBaseTemplateEntity.getItem().endsWith("First");
                }).collect(Collectors.toList());
            }else{
                basicFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "4".equals(productCalBaseTemplateEntity.getPamType()) && "1".equals(productCalBaseTemplateEntity.getDataType()) && !productCalBaseTemplateEntity.getItem().endsWith("Zero") && !productCalBaseTemplateEntity.getItem().endsWith("First");
                }).collect(Collectors.toList());
            }
            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : basicFormularList) {
                dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,month,productCalBasicInfoDto);
            }

            /**
             * 计算公式套公式
             */
            List<ProductCalBaseTemplateEntity> formularAndFormularList = new ArrayList<>();
            if(month == 0){
                formularAndFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "4".equals(productCalBaseTemplateEntity.getPamType()) && "2".equals(productCalBaseTemplateEntity.getDataType()) && productCalBaseTemplateEntity.getItem().endsWith("Zero");
                }).collect(Collectors.toList());
            }else if(month == 1){
                formularAndFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "4".equals(productCalBaseTemplateEntity.getPamType()) && "2".equals(productCalBaseTemplateEntity.getDataType()) && productCalBaseTemplateEntity.getItem().endsWith("First");
                }).collect(Collectors.toList());
            }else{
                if(!ProductCalRepaymentMethodEnum.WKD.equals(productCalBasicInfoDto.getRepaymentMethod())){
                    formularAndFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                        return "4".equals(productCalBaseTemplateEntity.getPamType()) && "2".equals(productCalBaseTemplateEntity.getDataType()) && !productCalBaseTemplateEntity.getItem().endsWith("Zero") && !productCalBaseTemplateEntity.getItem().endsWith("First");
                    }).collect(Collectors.toList());
                }
            }
            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : formularAndFormularList) {
                dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,month,productCalBasicInfoDto);
            }
            return true;
        }catch(Exception e){
            log.error("calNormalFormula",e);
            return false;
        }
    }

    public boolean calZeroSpecialFormula(Integer month,Double actualLoanMoneyValue,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalCashStreamDto productCalCashStreamDto,Map<String,Object> paramMap,Map<String,Object> map,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            Double fixedCost = (Double) paramMap.get("fixedCost");
            productCalCashStreamDto.setCashStreamOtherChangeCost(fixedCost);
            productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamOtherChangeCostZero".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get().setFormula(fixedCost.toString());
            /**
             * 查询中台放款手续费信息
             */
            List<ProductCalLoanHandChargeEntity> productCalLoanHandChargeEntityList = productCalDao.getLoanHandCharge(actualLoanMoneyValue);
            Double loanHandCharge = productCalLoanHandChargeEntityList.stream().mapToDouble(productCalLoanHandChargeEntity -> {
                return productCalLoanHandChargeEntity.getHandCharge() * productCalLoanHandChargeEntity.getLoanProbability();
            }).sum();
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityLoanHandChargeZero = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamLoanHandChargeZero".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            Double loanHandChargeNew = dealScale(productCalBaseTemplateEntityLoanHandChargeZero.getScaleValue(),loanHandCharge);
            productCalCashStreamDto.setCashStreamLoanHandCharge(Double.valueOf("-"+loanHandChargeNew));
            productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamLoanHandChargeZero".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get().setFormula("-"+loanHandChargeNew);
            paramMap.put(month+"CashStreamLoanHandCharge","-"+loanHandChargeNew);

            /**
             * 计算零月现金流支出
             */
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityExpendZero = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamExpendZero".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityExpendZero,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 计算零月现金流
             */
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityCashStreamZero = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamZero".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityCashStreamZero,paramMap,map,month,productCalBasicInfoDto);
            return true;
        }catch(Exception e){
            log.error("calZeroSpecialFormula error",e);
            return false;
        }
    }

    public boolean calFirstSpecialFormula(Integer month,Double actualLoanMoneyValue,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalCashStreamDto productCalCashStreamDto,Map<String,Object> paramMap,Map<String,Object> map,ProductCalBasicInfoDto productCalBasicInfoDto,ProductCalBasicParamDto productCalBasicParamDto,boolean isUnionFlag){
        try{
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityRemainPrincipalZero = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamRemainPrincipalZero".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            paramMap.put("cashStreamRemainPrincipalUp",productCalBaseTemplateEntityRemainPrincipalZero.getFormula());
            /**
             * 计算首月扣款手续费
             */
            List<ProductCalDeductMoneyHandChargeEntity> productCalDeductMoneyHandChargeEntityList = productCalDao.getDeductMoneyHandCharge(actualLoanMoneyValue);
            Double deductHandCharge = productCalDeductMoneyHandChargeEntityList.stream().mapToDouble(productCalDeductMoneyHandChargeEntity -> {
                return productCalDeductMoneyHandChargeEntity.getHandCharge() * productCalDeductMoneyHandChargeEntity.getDeductProbability() * productCalDeductMoneyHandChargeEntity.getProportion();
            }).sum();
            paramMap.put("deductHandCharge",deductHandCharge);

            ProductCalBaseTemplateEntity productCalBaseTemplateEntityDeductMoneyFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamDeductMoneyHandChargeFirst".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityDeductMoneyFirst,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 计算首月现金流支出
             */
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityExpendFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamExpendFirst".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityExpendFirst,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 计算首月现金流
             */
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityCashStreamFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamFirst".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityCashStreamFirst,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 计算首月剩余本金
             */
            Double remainPrincipal = 0.0;

            ProductCalBaseTemplateEntity productCalBaseTemplateEntityRemainPrincipalFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamRemainPrincipalFirst".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            Double paramValueRemainPrincipal = dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityRemainPrincipalFirst,paramMap,map,month,productCalBasicInfoDto);
            remainPrincipal = paramValueRemainPrincipal;


            /**
             * 对于分段贷  首月需要计算实际剩余本金
             */
            Double actualRemainPrincipal = 0.0;
            if(ProductCalRepaymentMethodEnum.FDD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                ProductCalBaseTemplateEntity productCalBaseTemplateEntityActualRemainPrincipalZero = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "cashStreamActualRemainPrincipalZero".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                paramMap.put("cashStreamActualRemainPrincipalUp",productCalBaseTemplateEntityActualRemainPrincipalZero.getFormula());

                ProductCalBaseTemplateEntity productCalBaseTemplateEntityActualRemainPrincipalFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "cashStreamActualRemainPrincipalFirst".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                Double paramValueActualRemainPrincipal = dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityActualRemainPrincipalFirst,paramMap,map,month,productCalBasicInfoDto);
                actualRemainPrincipal = paramValueActualRemainPrincipal;
            }

            /**
             * 计算首月实际利息
             */
            if(!isUnionFlag && !ProductCalRepaymentMethodEnum.DEBJ.type().equals(productCalBasicInfoDto.getRepaymentMethod()) && !ProductCalRepaymentMethodEnum.JTD.type().equals(productCalBasicInfoDto.getRepaymentMethod())) {
                ProductCalBaseTemplateEntity productCalBaseTemplateEntityActualInterestFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "cashStreamActualInterestFirst".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto, productCalBaseTemplateEntities, productCalBaseTemplateEntityActualInterestFirst, paramMap, map, month, productCalBasicInfoDto);
            }
            paramMap.put("cashStreamRemainPrincipalUp",remainPrincipal);

            if(ProductCalRepaymentMethodEnum.FDD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                paramMap.put("cashStreamActualRemainPrincipalUp",actualRemainPrincipal);
            }
            /**
             * 计算首月剩余金额
             */
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityRemainMoneyZero = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamRemainMoneyZero".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            paramMap.put("cashStreamRemainMoneyUp",productCalBaseTemplateEntityRemainMoneyZero.getFormula());

            ProductCalBaseTemplateEntity productCalBaseTemplateEntityRemainMoneyFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamRemainMoneyFirst".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            Double paramValueRemainMoney = dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityRemainMoneyFirst,paramMap,map,month,productCalBasicInfoDto);
            paramMap.put("cashStreamRemainMoneyUp",paramValueRemainMoney);
            /**
             * 计算首月实际本金
             */
            if(!isUnionFlag && !ProductCalRepaymentMethodEnum.DEBJ.type().equals(productCalBasicInfoDto.getRepaymentMethod()) && !ProductCalRepaymentMethodEnum.JTD.type().equals(productCalBasicInfoDto.getRepaymentMethod())) {
                ProductCalBaseTemplateEntity productCalBaseTemplateEntityActualPrincipalFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "cashStreamActualPrincipalFirst".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto, productCalBaseTemplateEntities, productCalBaseTemplateEntityActualPrincipalFirst, paramMap, map, month, productCalBasicInfoDto);
            }
            /**
             * 计算首月利息支出
             */
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityInterestExpendFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamInterestExpendFirst".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityInterestExpendFirst,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 对于利润计算，增值税和教育附加特殊处理
             */
//            if(productCalBasicParamDto.getProvisionRate() != null && productCalBasicParamDto.getManageFeeShareCoefficient() != null){
//                ProductCalBaseTemplateEntity productCalBaseTemplateEntityInterestIncomZero = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
//                    return "cashStreamInterestIncomZero".equals(productCalBaseTemplateEntity.getItem());
//                }).findFirst().get();
//                paramMap.put("cashStreamInterestIncomUp",productCalBaseTemplateEntityInterestIncomZero.getFormula());
//
//                ProductCalBaseTemplateEntity productCalBaseTemplateEntityValueAddedTaxFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
//                    return "cashStreamValueAddedTaxFirst".equals(productCalBaseTemplateEntity.getItem());
//                }).findFirst().get();
//                dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityValueAddedTaxFirst,paramMap,map,month,productCalBasicInfoDto);
//
//                ProductCalBaseTemplateEntity productCalBaseTemplateEntityEducationFeeAppendFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
//                    return "cashStreamEducationFeeAppendFirst".equals(productCalBaseTemplateEntity.getItem());
//                }).findFirst().get();
//                dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityEducationFeeAppendFirst,paramMap,map,month,productCalBasicInfoDto);
//
//                ProductCalBaseTemplateEntity productCalBaseTemplateEntityInterestIncomFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
//                    return "cashStreamInterestIncomFirst".equals(productCalBaseTemplateEntity.getItem());
//                }).findFirst().get();
//                paramMap.put("cashStreamInterestIncomUp",productCalBaseTemplateEntityInterestIncomFirst.getFormula());
//
//                ProductCalBaseTemplateEntity productCalBaseTemplateEntityManageFeeShareFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
//                    return "cashStreamManageFeeShareFirst".equals(productCalBaseTemplateEntity.getItem());
//                }).findFirst().get();
//                dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityManageFeeShareFirst,paramMap,map,month,productCalBasicInfoDto);
//
//            }
            return true;
        }catch(Exception e){
            log.error("calFirstSpecialFormula",e);
            return false;
        }
    }

    public boolean calSecondAndMoreFormula(Integer month,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalCashStreamDto productCalCashStreamDto,Map<String,Object> paramMap,Map<String,Object> map,ProductCalBasicInfoDto productCalBasicInfoDto,ProductCalBasicParamDto productCalBasicParamDto,List<ProductCalBaseTemplateEntity> cashStreamTemplateEntitiesBak,List<ProductCalBaseTemplateEntity> productCalBaseTemplateProfitCalEntityListBak,Mapper mapper,boolean isUnionFlag){
        try{
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityDeductMoney = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamDeductMoneyHandCharge".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityDeductMoney,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 对于尾款贷需要单独处理当期利息 并且在计算出当期利息后计算剩余的  现金流信息
             */
            if(ProductCalRepaymentMethodEnum.WKD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                ProductCalBaseTemplateEntity productCalBaseTemplateEntityReceivableCustomerInterest = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "cashStreamReceivableCustomerInterest".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityReceivableCustomerInterest,paramMap,map,month,productCalBasicInfoDto);

                ProductCalBaseTemplateEntity productCalBaseTemplateEntityPrincipal = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "cashStreamPrincipal".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityPrincipal,paramMap,map,month,productCalBasicInfoDto);

                List<ProductCalBaseTemplateEntity> formularAndFormularList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "4".equals(productCalBaseTemplateEntity.getPamType()) && "2".equals(productCalBaseTemplateEntity.getDataType()) && !productCalBaseTemplateEntity.getItem().endsWith("Zero") && !productCalBaseTemplateEntity.getItem().endsWith("First");
                }).collect(Collectors.toList());
                for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : formularAndFormularList) {
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,month,productCalBasicInfoDto);
                }
            }

            /**
             * 计算现金流支出
             */
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityExpend = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamExpend".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityExpend,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 计算首月现金流
             */
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityCashStream = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStream".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityCashStream,paramMap,map,month,productCalBasicInfoDto);


            /**
             * 计算剩余本金
             */
            Double remainPrincipal = 0.0;
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityRemainPrincipal = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamRemainPrincipal".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            Double paramValueRemainPrincipal = dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityRemainPrincipal,paramMap,map,month,productCalBasicInfoDto);
            remainPrincipal = paramValueRemainPrincipal;

            /**
             * 分段贷需要单独计算实际剩余本金
             */
            Double actualRemainPrincipal = 0.0;
            if(ProductCalRepaymentMethodEnum.FDD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                ProductCalBaseTemplateEntity productCalBaseTemplateEntityActualRemainPrincipal = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "cashStreamActualRemainPrincipal".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                Double paramValueActualRemainPrincipal = dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityActualRemainPrincipal,paramMap,map,month,productCalBasicInfoDto);
                actualRemainPrincipal = paramValueActualRemainPrincipal;
            }

            /**
             * 计算首月实际利息
             */
            if(!isUnionFlag && !ProductCalRepaymentMethodEnum.DEBJ.type().equals(productCalBasicInfoDto.getRepaymentMethod()) && !ProductCalRepaymentMethodEnum.JTD.type().equals(productCalBasicInfoDto.getRepaymentMethod())) {
                ProductCalBaseTemplateEntity productCalBaseTemplateEntityActualInterest = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "cashStreamActualInterest".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto, productCalBaseTemplateEntities, productCalBaseTemplateEntityActualInterest, paramMap, map, month, productCalBasicInfoDto);
            }
            paramMap.put("cashStreamRemainPrincipalUp",remainPrincipal);
            if(ProductCalRepaymentMethodEnum.FDD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                paramMap.put("cashStreamActualRemainPrincipalUp",actualRemainPrincipal);
            }

            /**
             * 计算首月剩余金额
             */

            ProductCalBaseTemplateEntity productCalBaseTemplateEntityRemainMoney = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamRemainMoney".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            Double paramValueRemainMoney = dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityRemainMoney,paramMap,map,month,productCalBasicInfoDto);
            paramMap.put("cashStreamRemainMoneyUp",paramValueRemainMoney);

            /**
             * 计算首月实际本金
             */
            if(!isUnionFlag && !ProductCalRepaymentMethodEnum.DEBJ.type().equals(productCalBasicInfoDto.getRepaymentMethod()) && !ProductCalRepaymentMethodEnum.JTD.type().equals(productCalBasicInfoDto.getRepaymentMethod())) {
                ProductCalBaseTemplateEntity productCalBaseTemplateEntityActualPrincipal = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                    return "cashStreamActualPrincipal".equals(productCalBaseTemplateEntity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto, productCalBaseTemplateEntities, productCalBaseTemplateEntityActualPrincipal, paramMap, map, month, productCalBasicInfoDto);
            }

            /**
             * 计算首月利息支出
             */
            ProductCalBaseTemplateEntity productCalBaseTemplateEntityInterestExpend = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "cashStreamInterestExpend".equals(productCalBaseTemplateEntity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityInterestExpend,paramMap,map,month,productCalBasicInfoDto);

//            if(productCalBasicParamDto.getProvisionRate() != null && productCalBasicParamDto.getManageFeeShareCoefficient() != null){
//                ProductCalBaseTemplateEntity productCalBaseTemplateEntityValueAddedTax = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
//                    return "cashStreamValueAddedTax".equals(productCalBaseTemplateEntity.getItem());
//                }).findFirst().get();
//                dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityValueAddedTax,paramMap,map,month,productCalBasicInfoDto);
//
//                ProductCalBaseTemplateEntity productCalBaseTemplateEntityEducationFeeAppend = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
//                    return "cashStreamEducationFeeAppend".equals(productCalBaseTemplateEntity.getItem());
//                }).findFirst().get();
//                dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityEducationFeeAppend,paramMap,map,month,productCalBasicInfoDto);
//
//                ProductCalBaseTemplateEntity productCalBaseTemplateEntityInterestIncom = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
//                    return "cashStreamInterestIncom".equals(productCalBaseTemplateEntity.getItem());
//                }).findFirst().get();
//                paramMap.put("cashStreamInterestIncomUp",productCalBaseTemplateEntityInterestIncom.getFormula());
//
//                ProductCalBaseTemplateEntity productCalBaseTemplateEntityManageFeeShare = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
//                    return "cashStreamManageFeeShare".equals(productCalBaseTemplateEntity.getItem());
//                }).findFirst().get();
//                dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityManageFeeShare,paramMap,map,month,productCalBasicInfoDto);
//            }
            return true;
        }catch(Exception e){
            log.error("calNormalFormula",e);
            return false;
        }
    }

    public Result afterCalDeal(Map<String,Object> paramMap,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalculateDto productCalculateDto,boolean isUnionFlag){
        Result result = new Result();
        try{
            log.info("afterCalDeal start");
            long startTime = System.currentTimeMillis();
            Map<String,Object> map = new HashMap<>();

            boolean basicPamFlag = basicPamAfterCal(productCalBaseTemplateEntities,productCalculateDto,paramMap,map,isUnionFlag);
            if(basicPamFlag == false){
                result.setCode(FAIL.getCode());
                result.setMessage("基本参数后置方法处理失败");
                return result;
            }

            boolean cashStreamFlag = cashStreamAfterCal(productCalBaseTemplateEntities,productCalculateDto,paramMap,map);
            if(cashStreamFlag == false){
                result.setCode(FAIL.getCode());
                result.setMessage("现金流后置方法处理失败");
                return result;
            }

            boolean taxFeePamFlag = taxFeePamAfterCal(productCalBaseTemplateEntities,productCalculateDto,paramMap,map,isUnionFlag);
            if(taxFeePamFlag == false){
                result.setCode(FAIL.getCode());
                result.setMessage("税费参数后置方法处理失败");
                return result;
            }

            if(isUnionFlag){
                boolean unionFamFlag = unionLoanPamAfterCal(productCalBaseTemplateEntities,productCalculateDto,paramMap,map);
                if(unionFamFlag == false){
                    result.setCode(FAIL.getCode());
                    result.setMessage("联合贷参数后置方法处理失败");
                    return result;
                }
            }

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            long endTime = System.currentTimeMillis();
            log.info("calulateResultInfo cost time: "+(endTime - startTime)+"ms");
            log.info("afterCalDeal end");
        }catch(Exception e){
            log.error("afterCalDeal error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("计算后置方法失败");
        }
        return result;
    }

    public boolean basicPamAfterCal(List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalculateDto productCalculateDto,Map<String,Object> paramMap,Map<String,Object> map, boolean isUnionLoanFlag){
        try{
            /**
             * 计算基本参数剩余参数
             */
            List<ProductCalBaseTemplateEntity> basicRemainBaseTemplateEntityList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "0".equals(productCalBaseTemplateEntity.getPamType()) && "5".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());
            ProductCalBasicParamDto productCalBasicParamDto = productCalculateDto.getProductCalBasicParamDto();
            ProductCalCashStreamDto productCalCashStreamDtoZero = productCalculateDto.getProductCalCashStreamDtoList().stream().filter(x -> {
                return x.getMonth() == 0;
            }).findFirst().get();

            ProductCalCashStreamDto productCalCashStreamDtoFirst = productCalculateDto.getProductCalCashStreamDtoList().stream().filter(x -> {
                return x.getMonth() == 1;
            }).findFirst().get();
            productCalBasicParamDto.setLoanHandCharge(productCalCashStreamDtoZero.getCashStreamLoanHandCharge());
            productCalBasicParamDto.setDeductMoneyHandCharge(productCalCashStreamDtoFirst.getCashStreamDeductMoneyHandCharge());

            /**
             * 计算总利息
             */
            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : basicRemainBaseTemplateEntityList) {

                String formula = productCalBaseTemplateEntity.getFormula();
                String[] split = formula.split("~");
                List<String> arrOrMapList = Arrays.stream(split).filter(x -> {
                    return x.startsWith("cashStream");
                }).collect(Collectors.toList());

                int count = 1;
                for (String arrorMapStr : arrOrMapList){
                    formula = dealArrayOrMap(count,formula,arrorMapStr,paramMap,map,productCalBaseTemplateEntity,productCalBaseTemplateEntities,isUnionLoanFlag);
                    count++;
                }
                productCalBaseTemplateEntity.setFormula(formula);
                dealFormula(productCalBasicParamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalculateDto.getProductCalBasicInfoDto());
            }
            return true;
        }catch(Exception e){
            log.error("basicPamAfterCal error",e);
            return false;
        }
    }

    public boolean cashStreamAfterCal(List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalculateDto productCalculateDto,Map<String,Object> paramMap,Map<String,Object> map){
        try{
            /**
             * 计算现金流中的利润
             */
            if(productCalculateDto.getProductCalBasicParamDto().getProvisionRate() != null && productCalculateDto.getProductCalBasicParamDto().getManageFeeShareCoefficient() != null){
                List<ProductCalCashStreamDto> productCalCashStreamDtoList = productCalculateDto.getProductCalCashStreamDtoList();

                for (int i = 0; i < productCalCashStreamDtoList.size(); i++) {
                    paramMap.put("month",i);
                    ProductCalCashStreamDto productCalCashStreamDto = productCalCashStreamDtoList.get(i);
                    if(productCalCashStreamDto.getMonth() == 0){
                        if(productCalCashStreamDtoList.size()>2){
                            ProductCalCashStreamDto productCalCashStreamDtoFirst = productCalCashStreamDtoList.get(1);
                            paramMap.put("cashStreamReceivableCustomerInterestDown",productCalCashStreamDtoFirst.getCashStreamReceivableCustomerInterest());
                        }else{
                            paramMap.put("cashStreamReceivableCustomerInterestDown",0);
                        }

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityInterestIncomZero = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamInterestIncomZero".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityInterestIncomZero,paramMap,map,productCalCashStreamDto.getMonth(),productCalculateDto.getProductCalBasicInfoDto());

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityCommissionShareZero = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamCommissionShareZero".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityCommissionShareZero,paramMap,map,productCalCashStreamDto.getMonth(),productCalculateDto.getProductCalBasicInfoDto());

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityProfitZero = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamProfitZero".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityProfitZero,paramMap,map,productCalCashStreamDto.getMonth(),productCalculateDto.getProductCalBasicInfoDto());
                    }else if(productCalCashStreamDto.getMonth() == 1){
                        if(productCalCashStreamDtoList.size()>=3){
                            ProductCalCashStreamDto productCalCashStreamDtoSec = productCalCashStreamDtoList.get(2);
                            paramMap.put("cashStreamReceivableCustomerInterestDown",productCalCashStreamDtoSec.getCashStreamReceivableCustomerInterest());
                        }else{
                            paramMap.put("cashStreamReceivableCustomerInterestDown",0);
                        }

                        paramMap.put("cashStreamReceivableCustomerInterest",productCalCashStreamDto.getCashStreamReceivableCustomerInterest());
                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityInterestIncomFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamInterestIncomFirst".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityInterestIncomFirst,paramMap,map,productCalCashStreamDto.getMonth(),productCalculateDto.getProductCalBasicInfoDto());

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityInterestIncomZero = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamInterestIncomZero".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        paramMap.put("cashStreamInterestIncomUp",productCalBaseTemplateEntityInterestIncomZero.getFormula());

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityValueAddedTaxFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamValueAddedTaxFirst".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityValueAddedTaxFirst,paramMap,map,1,productCalculateDto.getProductCalBasicInfoDto());

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityEducationFeeAppendFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamEducationFeeAppendFirst".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityEducationFeeAppendFirst,paramMap,map,1,productCalculateDto.getProductCalBasicInfoDto());

//                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityInterestIncomFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
//                            return "cashStreamInterestIncomFirst".equals(productCalBaseTemplateEntity.getItem());
//                        }).findFirst().get();
                        paramMap.put("cashStreamInterestIncomUp",productCalBaseTemplateEntityInterestIncomFirst.getFormula());

                        paramMap.put("cashStreamRemainPrincipalFirst",paramMap.get(i+"cashStreamRemainPrincipal"));
                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityManageFeeShareFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamManageFeeShareFirst".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityManageFeeShareFirst,paramMap,map,1,productCalculateDto.getProductCalBasicInfoDto());

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityCommissionShareFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamCommissionShareFirst".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityCommissionShareFirst,paramMap,map,productCalCashStreamDto.getMonth(),productCalculateDto.getProductCalBasicInfoDto());

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityProfitFirst = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamProfitFirst".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntityProfitFirst,paramMap,map,productCalCashStreamDto.getMonth(),productCalculateDto.getProductCalBasicInfoDto());
                    }else{

                        List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntitiesNew = productCalBaseTemplateEntities.stream().map(productCalBaseTemplateEntity -> {
                            ProductCalBaseTemplateEntity productCalBaseTemplateEntityNew  = new ProductCalBaseTemplateEntity();
                            BeanUtils.copyProperties(productCalBaseTemplateEntity,productCalBaseTemplateEntityNew);
                            return productCalBaseTemplateEntityNew;
                        }).collect(Collectors.toList());

                        if(productCalCashStreamDtoList.size()>=(i+2)){
                            ProductCalCashStreamDto productCalCashStreamDtoUp = productCalCashStreamDtoList.get(i+1);
                            paramMap.put("cashStreamReceivableCustomerInterestDown",productCalCashStreamDtoUp.getCashStreamReceivableCustomerInterest());
                        }else{
                            paramMap.put("cashStreamReceivableCustomerInterestDown",0);
                        }

                        paramMap.put("cashStreamReceivableCustomerInterest",productCalCashStreamDto.getCashStreamReceivableCustomerInterest());
                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityInterestIncom = productCalBaseTemplateEntitiesNew.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamInterestIncom".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntitiesNew,productCalBaseTemplateEntityInterestIncom,paramMap,map,productCalCashStreamDto.getMonth(),productCalculateDto.getProductCalBasicInfoDto());

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityValueAddedTax = productCalBaseTemplateEntitiesNew.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamValueAddedTax".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntitiesNew,productCalBaseTemplateEntityValueAddedTax,paramMap,map,i,productCalculateDto.getProductCalBasicInfoDto());

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityEducationFeeAppend = productCalBaseTemplateEntitiesNew.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamEducationFeeAppend".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntitiesNew,productCalBaseTemplateEntityEducationFeeAppend,paramMap,map,i,productCalculateDto.getProductCalBasicInfoDto());

//                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityInterestIncom = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
//                            return "cashStreamInterestIncom".equals(productCalBaseTemplateEntity.getItem());
//                        }).findFirst().get();
                        paramMap.put("cashStreamInterestIncomUp",productCalBaseTemplateEntityInterestIncom.getFormula());

                        paramMap.put("cashStreamRemainPrincipal",paramMap.get(i+"cashStreamRemainPrincipal"));
                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityManageFeeShare = productCalBaseTemplateEntitiesNew.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamManageFeeShare".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntitiesNew,productCalBaseTemplateEntityManageFeeShare,paramMap,map,i,productCalculateDto.getProductCalBasicInfoDto());

                        for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : productCalBaseTemplateEntitiesNew) {
                            if("cashStreamSubInterest".equals(productCalBaseTemplateEntity.getItem()) || "cashStreamInterestIncom".equals(productCalBaseTemplateEntity.getItem()) || "cashStreamInterestExpendShare".equals(productCalBaseTemplateEntity.getItem())
                                    || "cashStreamManageFeeShare".equals(productCalBaseTemplateEntity.getItem()) || "cashStreamProvision".equals(productCalBaseTemplateEntity.getItem())
                                    || "cashStreamValueAddedTax".equals(productCalBaseTemplateEntity.getItem()) || "cashStreamEducationFeeAppend".equals(productCalBaseTemplateEntity.getItem()) || "cashStreamDeductMoneyHandCharge".equals(productCalBaseTemplateEntity.getItem())
                                    || "cashStreamCollectionFeeTotal".equals(productCalBaseTemplateEntity.getItem())){
                                productCalBaseTemplateEntity.setFormula(String.valueOf(paramMap.get(productCalCashStreamDto.getMonth()+productCalBaseTemplateEntity.getItem())));
                            }
                        }
                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityCommissionShare = productCalBaseTemplateEntitiesNew.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamCommissionShare".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();
                        Mapper mapper = DozerBeanMapperBuilder.buildDefault();
                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityCommissionShareBak = mapper.map(productCalBaseTemplateEntityCommissionShare,ProductCalBaseTemplateEntity.class);
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntitiesNew,productCalBaseTemplateEntityCommissionShareBak,paramMap,map,productCalCashStreamDto.getMonth(),productCalculateDto.getProductCalBasicInfoDto());

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityProfit = productCalBaseTemplateEntitiesNew.stream().filter(productCalBaseTemplateEntity -> {
                            return "cashStreamProfit".equals(productCalBaseTemplateEntity.getItem());
                        }).findFirst().get();

                        ProductCalBaseTemplateEntity productCalBaseTemplateEntityProfitBak = mapper.map(productCalBaseTemplateEntityProfit,ProductCalBaseTemplateEntity.class);
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateEntitiesNew,productCalBaseTemplateEntityProfitBak,paramMap,map,productCalCashStreamDto.getMonth(),productCalculateDto.getProductCalBasicInfoDto());
                    }
                }
            }
            return true;
        }catch(Exception e){
            log.error("cashStreamAfterCal error",e);
            return false;
        }
    }

    public boolean taxFeePamAfterCal(List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalculateDto productCalculateDto,Map<String,Object> paramMap,Map<String,Object> map,boolean isUnionLoanFlag){
        try{
            /**
             * 计算税费参数剩余参数
             */
            ProductCalTaxFeeParamDto productCalTaxFeeParamDto = productCalculateDto.getProductCalTaxFeeParamDto();

            List<ProductCalBaseTemplateEntity> taxFeeRemainArrBaseTemplateEntityList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "1".equals(productCalBaseTemplateEntity.getPamType()) && "5".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : taxFeeRemainArrBaseTemplateEntityList) {

                String formula = productCalBaseTemplateEntity.getFormula();
                String[] split = formula.split("~");
                List<String> arrOrMapList = Arrays.stream(split).filter(x -> {
                    return x.startsWith("cashStream") || x.startsWith("repl");
                }).collect(Collectors.toList());

                int count = 1;
                for (String arrorMapStr : arrOrMapList){
                    formula = dealArrayOrMap(count,formula,arrorMapStr,paramMap,map,productCalBaseTemplateEntity,productCalBaseTemplateEntities,isUnionLoanFlag);
                    count++;
                }
                productCalBaseTemplateEntity.setFormula(formula);
                dealFormula(productCalTaxFeeParamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalculateDto.getProductCalBasicInfoDto());

            }

            List<ProductCalBaseTemplateEntity> taxFeeRemainBaseTemplateEntityList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "1".equals(productCalBaseTemplateEntity.getPamType()) && "4".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());


            /**
             * 先计算费额
             */

            List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntityMoneyList= taxFeeRemainBaseTemplateEntityList.stream().filter(productCalBaseTemplateEntity -> {
                return "valueAddedTax".equals(productCalBaseTemplateEntity.getItem()) || "educationFeeAppend".equals(productCalBaseTemplateEntity.getItem()) || "badness".equals(productCalBaseTemplateEntity.getItem()) || "totalCost".equals(productCalBaseTemplateEntity.getItem()) || "netProfitMoney".equals(productCalBaseTemplateEntity.getItem()) || "incomeTaxCost".equals(productCalBaseTemplateEntity.getItem());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : productCalBaseTemplateEntityMoneyList) {
                dealFormula(productCalTaxFeeParamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalculateDto.getProductCalBasicInfoDto());
            }

            /**
             * 计算费率相关参数
             */

            List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntityRateList= taxFeeRemainBaseTemplateEntityList.stream().filter(productCalBaseTemplateEntity -> {
                return "stampTaxFeeRate".equals(productCalBaseTemplateEntity.getItem()) || "otherChangeFeeRate".equals(productCalBaseTemplateEntity.getItem()) || "incomeTaxRate".equals(productCalBaseTemplateEntity.getItem()) || "interestExpendFeeRate".equals(productCalBaseTemplateEntity.getItem()) || "educationFeeAppendFeeRate".equals(productCalBaseTemplateEntity.getItem()) || "badnessFeeRate".equals(productCalBaseTemplateEntity.getItem()) || "serviceFeeRate".equals(productCalBaseTemplateEntity.getItem()) || "valueAddedTaxFeeRate".equals(productCalBaseTemplateEntity.getItem()) || "fixedExpendFeeRate".equals(productCalBaseTemplateEntity.getItem()) || "totalFeeRate".equals(productCalBaseTemplateEntity.getItem()) || "profitRate".equals(productCalBaseTemplateEntity.getItem());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : productCalBaseTemplateEntityRateList) {
                dealFormula(productCalTaxFeeParamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalculateDto.getProductCalBasicInfoDto());
            }
            return true;
        }catch(Exception e){
            log.error("taxFeePamAfterCal error",e);
            return false;
        }
    }

    public boolean unionLoanPamAfterCal(List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalculateDto productCalculateDto,Map<String,Object> paramMap,Map<String,Object> map){
        try{
            ProductCalUnionLoanParamDto productCalUnionLoanParamDto = productCalculateDto.getProductCalUnionLoanParamDto();
            List<ProductCalBaseTemplateEntity> unionLoanRemainArrBaseTemplateEntityList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "6".equals(productCalBaseTemplateEntity.getPamType()) && "5".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : unionLoanRemainArrBaseTemplateEntityList) {

                String formula = productCalBaseTemplateEntity.getFormula();
                String[] split = formula.split("~");
                List<String> arrOrMapList = Arrays.stream(split).filter(x -> {
                    return x.startsWith("cashStream") || x.startsWith("repl");
                }).collect(Collectors.toList());

                int count = 1;
                for (String arrorMapStr : arrOrMapList){
                    formula = dealArrayOrMap(count,formula,arrorMapStr,paramMap,map,productCalBaseTemplateEntity,productCalBaseTemplateEntities,true);
                    count++;
                }
                productCalBaseTemplateEntity.setFormula(formula);
                dealFormula(productCalUnionLoanParamDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalculateDto.getProductCalBasicInfoDto());
            }

            return true;
        }catch(Exception e){
            log.error("unionLoanPamAfterCal error",e);
            return false;
        }
    }

    /**
     * 计算结果信息
     * @param paramMap
     * @param productCalBaseTemplateEntities
     * @param productCalculateDto
     * @return
     */
    public boolean calulateResultInfo(Map<String,Object> paramMap,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalculateDto productCalculateDto,boolean isUnionLoanFlag){
        try{
            log.info("calulateResultInfo start");
            ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
            long startTime = System.currentTimeMillis();
            Map<String,Object> map = new HashMap<>();

            ProductCalResultInfoDto productCalResultInfoDto = new ProductCalResultInfoDto();
            /**
             * 计算结果信息
             */
            List<ProductCalBaseTemplateEntity> resultInfoBaseTemplateEntityList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return "5".equals(productCalBaseTemplateEntity.getPamType());
            }).collect(Collectors.toList());

            /**
             * 计算公式套公式
             */
            List<ProductCalBaseTemplateEntity> formulaAndForBaseTemplateEntityList = resultInfoBaseTemplateEntityList.stream().filter(productCalBaseTemplateEntity -> {
                return "2".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : formulaAndForBaseTemplateEntityList) {
                dealFormula(productCalResultInfoDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalBasicInfoDto);

            }

            /**
             * 计算数组公式
             */
            List<ProductCalBaseTemplateEntity> arrOrMapBaseTemplateEntityList = resultInfoBaseTemplateEntityList.stream().filter(productCalBaseTemplateEntity -> {
                return "5".equals(productCalBaseTemplateEntity.getDataType());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : arrOrMapBaseTemplateEntityList) {
                String formula = productCalBaseTemplateEntity.getFormula();
                String[] split = formula.split("~");
                List<String> arrOrMapList = Arrays.stream(split).filter(x -> {
                    return x.startsWith("cashStream") || x.startsWith("repl") || x.startsWith("dueDate");
                }).collect(Collectors.toList());

                int count = 1;
                for (String arrorMapStr : arrOrMapList){
                    formula = dealArrayOrMap(count,formula,arrorMapStr,paramMap,map,productCalBaseTemplateEntity,productCalBaseTemplateEntities,isUnionLoanFlag);
                    count++;
                }
                productCalBaseTemplateEntity.setFormula(formula);
                dealFormula(productCalResultInfoDto,productCalBaseTemplateEntities,productCalBaseTemplateEntity,paramMap,map,null,productCalBasicInfoDto);
            }
            productCalculateDto.setProductCalResultInfoDto(productCalResultInfoDto);

            if(ProductCalRepaymentMethodEnum.JTD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                productCalculateDto.getProductCalRepaymentMethodParamDto().setDecreaseInMonthlyPaymentRatio(-1*productCalculateDto.getProductCalRepaymentMethodParamDto().getDecreaseInMonthlyPaymentRatio());
            }
            long endTime = System.currentTimeMillis();
            log.info("calulateResultInfo cost time: "+(endTime - startTime)+"ms");
            log.info("calulateResultInfo end");
        }catch(Exception e){
            log.error("计算返回结果信息失败",e);
            return false;
        }
        return true;
    }

    public boolean dealScalePercent(ProductCalculateDto productCalculateDto,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities){
        try{
            log.info("dealScalePercent start");
            long startTime = System.currentTimeMillis();
            List<String> scalePercentList = productCalBaseTemplateEntities.stream().filter(productCalBaseTemplateEntity -> {
                return 4==productCalBaseTemplateEntity.getScaleValue();
            }).map(ProductCalBaseTemplateEntity::getItem).collect(Collectors.toList());

            ProductCalBasicParamDto productCalBasicParamDto = productCalculateDto.getProductCalBasicParamDto();

            if(productCalBasicParamDto != null){
                dealFieldValuePercent(productCalBasicParamDto,scalePercentList);
            }

            ProductCalTaxFeeParamDto productCalTaxFeeParamDto = productCalculateDto.getProductCalTaxFeeParamDto();

            if(productCalTaxFeeParamDto != null){
                dealFieldValuePercent(productCalTaxFeeParamDto,scalePercentList);
            }

            ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto = productCalculateDto.getProductCalRepaymentMethodParamDto();

            if(productCalRepaymentMethodParamDto != null){
                dealFieldValuePercent(productCalRepaymentMethodParamDto,scalePercentList);
            }

            ProductCalEarlySquareParamDto productCalEarlySquareParamDto = productCalculateDto.getProductCalEarlySquareParamDto();

//            dealFieldValuePercent(productCalEarlySquareParamDto,scalePercentList);

            ProductCalResultInfoDto productCalResultInfoDto = productCalculateDto.getProductCalResultInfoDto();

            if(productCalResultInfoDto != null){
                dealFieldValuePercent(productCalResultInfoDto,scalePercentList);
            }
            long endTime = System.currentTimeMillis();
            log.info("dealScalePercent cost time: "+(endTime - startTime)+"ms");
            log.info("dealScalePercent end");
        }catch(Exception e){
            log.error("dealScalePercent error",e);
            return false;
        }
        return true;
    }

    public void dealFieldValuePercent(Object object,List<String> scalePercentList) throws Exception {
        Field[] declaredFields = object.getClass().getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            if(scalePercentList.contains(declaredField.getName())){
                Double value = null;
                if(declaredField.get(object) != null){
                    value = BigDecimal.valueOf((Double)declaredField.get(object)).multiply(BigDecimal.valueOf(100)).doubleValue();
                }
                assgnFieldWithCalValue(object,declaredField.getName(),value);
            }
        }
    }

    /**
     * 处理数组或者map
     * @param count
     * @param formula
     * @param arrorMapStr
     * @param paramMap
     * @param map
     * @param productCalBaseTemplateEntity
     * @param productCalBaseTemplateEntities
     * @return
     */
    public String dealArrayOrMap(int count, String formula, String arrorMapStr, Map<String,Object> paramMap, Map<String,Object> map, ProductCalBaseTemplateEntity productCalBaseTemplateEntity, List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities, boolean isUnionLoanFlag){
        Integer timeLimit;
        if(paramMap.get("earlySquareTimeLimit")!=null){
            timeLimit = (Integer) paramMap.get("earlySquareTimeLimit");
        }else{
            timeLimit = (Integer) paramMap.get("timeLimit");
        }
        if(isUnionLoanFlag){
            timeLimit = timeLimit + 1;
        }

        if(formula.indexOf("array(~"+arrorMapStr+"~)") != -1){
            StringBuffer stringBuffer = new StringBuffer("");
            for(int i=0; i<=timeLimit; i++){
                if(paramMap.get(i+arrorMapStr) != null){
                    if(i!=timeLimit && paramMap.get((i+1)+arrorMapStr) != null){
                        stringBuffer.append(paramMap.get(i+arrorMapStr)+",");
                    }else {
                        stringBuffer.append(paramMap.get(i+arrorMapStr));
                    }

                }
            }
            formula = formula.replaceAll("array\\(~"+arrorMapStr+"~\\)",stringBuffer.toString());
        }else if(formula.indexOf("mapDouble(~"+arrorMapStr+"~)") != -1){
            formula = formula.replaceAll("mapDouble\\(~"+arrorMapStr+"~\\)",arrorMapStr);
            List<Double> valueList = new ArrayList<>();
            for(int i=0; i<=timeLimit; i++){
                if(paramMap.get(i+arrorMapStr) != null){
                    if(i == 0){
                        if(!"resultBeforeTaxIncome".equals(productCalBaseTemplateEntity.getItem())){
                            valueList.add((Double) paramMap.get(i+arrorMapStr));
                        }
                    }else{
                        valueList.add((Double) paramMap.get(i+arrorMapStr));
                    }
                }
            }
            double[] doubles = new double[valueList.size()];
            for (int i=0; i<valueList.size(); i++){
                doubles[i] = valueList.get(i);
            }
            map.put(arrorMapStr,doubles);
        }else if(formula.indexOf("mapString(~"+arrorMapStr+"~)") != -1){
            formula = formula.replaceAll("mapString\\(~"+arrorMapStr+"~\\)",arrorMapStr);
            List<String> valueList = new ArrayList<>();
            for(int i=0; i<=timeLimit; i++){
                if(paramMap.get(i+arrorMapStr) != null){
                    valueList.add((String)paramMap.get(i+arrorMapStr));
                }
            }
            String[] strings = new String[valueList.size()];
            for (int i=0; i<valueList.size(); i++){
                strings[i] = valueList.get(i);
            }
            map.put(arrorMapStr,strings);
        }else if(formula.indexOf("~"+arrorMapStr+"~") != -1){
            String formulaNew = arrorMapStr;
            arrorMapStr = arrorMapStr.replaceAll("\\(","\\\\(");
            arrorMapStr = arrorMapStr.replaceAll("\\)","\\\\)");
            formula = formula.replaceAll("~"+arrorMapStr+"~","value"+count);
            String formulaNoRepl = formulaNew.replace("repl","");
            while(true){
                formulaNoRepl = getFinalFormula(formulaNoRepl,productCalBaseTemplateEntities);
                if(formulaNoRepl.indexOf("#") == -1){
                    break;
                }
            }
            for (String param : paramMap.keySet()) {
                if(formulaNoRepl.indexOf(param) != -1){
                    formulaNoRepl = formulaNoRepl.replaceAll("@"+param+"@",String.valueOf(paramMap.get(param)));
                }
            }
            Map<String,Object> mapRepl = new HashMap<>();
            map.put("value"+count,(Double)AviatorUtils.calculateValue(mapRepl,formulaNoRepl));
        }

        return formula;
    }

    /**
     * 获取最终不带#公式
     * @param formula
     * @param productCalBaseTemplateEntityList
     * @return
     */
    public String getFinalFormula(String formula, List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntityList){
        for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : productCalBaseTemplateEntityList) {
            if(formula.indexOf("#"+productCalBaseTemplateEntity.getItem()+"#")!=-1){
                formula = formula.replaceAll("#"+productCalBaseTemplateEntity.getItem()+"#",productCalBaseTemplateEntity.getFormula());
            }
        }
        return formula;
    }

    /**
     * 使用反射方法，为对象的属性赋值
     * @param object
     * @param fieldName
     * @param value
     */
    public void assgnFieldWithCalValue(Object object,String fieldName,Double value) throws Exception {
        Field field = object.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(object,value);
    }

    /**
     * 处理小数部分
     * @param scaleValue
     * @param paramValue
     * @return
     */
    public Double dealScale(Integer scaleValue, Double paramValue){
        Double paramValueNew;
        if(scaleValue!=null){
            paramValueNew = BigDecimal.valueOf(paramValue).setScale(scaleValue, BigDecimal.ROUND_HALF_UP).doubleValue();
        }else{
            paramValueNew = BigDecimal.valueOf(paramValue).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        return paramValueNew;
    }

    /**
     * 计算公式
     * @param productCalBaseTemplateEntities
     * @param productCalBaseTemplateEntity
     * @param paramMap
     * @param map
     * @return
     */
    public Double dealFormula(Object object,List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntities,ProductCalBaseTemplateEntity productCalBaseTemplateEntity,Map<String,Object> paramMap,Map<String,Object> map,Integer month,ProductCalBasicInfoDto productCalBasicInfoDto) {
        Double paramValue = 0.0;
        String formula = productCalBaseTemplateEntity.getFormula();

        String formulaOld = "";
        while(true){
            formula = getFinalFormula(formula,productCalBaseTemplateEntities);
            if(formula.indexOf("#") == -1){
                break;
            }
            if(formulaOld.equals(formula)){
                break;
            }else{
                formulaOld = formula;
            }
        }

        if(formula.indexOf("#") != -1){
            log.error("条目："+productCalBaseTemplateEntity.getItem()+"的公式："+formula+"含有未替换完成公式#?#");
            throw new SystemRuntimeException("条目："+productCalBaseTemplateEntity.getItem()+"的公式："+formula+"含有未替换完成公式#?#");
        }

        /**
         * 第二部计算值
         */
        for (String param : paramMap.keySet()) {
            if(formula.indexOf(param) != -1){
                if("activationDate".equals(param)){
                    formula = formula.replaceAll("@"+param+"@","'"+String.valueOf(paramMap.get(param))+"'");
                }else{
                    formula = formula.replaceAll("@"+param+"@",String.valueOf(paramMap.get(param)));
                }
            }
        }

        if(formula.indexOf("@") == -1){
            productCalBaseTemplateEntity.setFormula(formula);
            try {
                paramValue = (Double) AviatorUtils.calculateValue(map, formula);
            } catch (Exception e) {
                log.error("报错条目：{}，报错公式：{}\n{}",productCalBaseTemplateEntity.getItem(),formula,e);
            }
            if (paramValue.equals(NaN)) {
                log.error("报错条目：{}，报错公式NaN：{}\n{}",productCalBaseTemplateEntity.getItem(),formula);
            }
            Double paramValueNew = dealScale(productCalBaseTemplateEntity.getScaleValue(), paramValue);
            String item = productCalBaseTemplateEntity.getItem();
            if(month != null){
                if(month == 0){
                    if(item.endsWith("Zero")){
                        item = item.substring(0,item.length()-4);
                    }
                }else if(month == 1){
                    if(item.endsWith("First")){
                        item = item.substring(0,item.length()-5);
                    }
                }
            }
            if(!(paramValueNew == 0 && ProductCalRepaymentMethodEnum.DEBX.type().equals(productCalBasicInfoDto.getRepaymentMethod()) && (ProductCalRepaymentMethodEnum.DEBJ.type().equals(productCalBasicInfoDto.getRepaymentMethod())) && "cashStreamRemainPrincipal".equals(item))){
                if(month != null){
                    paramMap.put(month+item,paramValue);
                }
                try {
                    assgnFieldWithCalValue(object,item,paramValueNew);
                } catch (Exception e) {
                    log.error("assgnFieldWithCalValue error:{}",e);
                }
            }
        } else {
            log.info("公式中的参数未替换完毕！"+formula);
        }
        return paramValue;
    }

    /**
     * 保存
     * @param productCalculateAppDto
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result save(ProductCalculateAppDto productCalculateAppDto, User user){
        log.info("save template info start");
        Result result = new Result();
        try{
            /**
             * 保存基本信息表
             */
            ProductCalBasicInfoEntity productCalBasicInfoEntity = productCalculateAppDto.getProductCalBasicInfoEntity();
            Date date = new Date();
            /**
             * 或者这个模板名称+业务类型+还款方式+最大版本号
             */
            QueryWrapper<ProductCalBasicInfoEntity> queryWrapper = new QueryWrapper();
            queryWrapper.eq("template_name",productCalBasicInfoEntity.getTemplateName());
            queryWrapper.eq("business_type",productCalBasicInfoEntity.getBusinessType());
            queryWrapper.eq("repayment_method",productCalBasicInfoEntity.getRepaymentMethod());
            queryWrapper.orderByDesc("version");
            List<ProductCalBasicInfoEntity> productCalBasicInfoEntities = productCalBasicInfoDao.selectList(queryWrapper);
            Integer version = 1;
            if(productCalBasicInfoEntities!=null&&productCalBasicInfoEntities.size()>0){
                Integer versionOri = productCalBasicInfoEntities.stream().findFirst().get().getVersion();
                version = versionOri+1;
            }
            productCalBasicInfoEntity.setState(productCalBasicInfoEntity.getState());
            productCalBasicInfoEntity.setEffectiveDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            productCalBasicInfoEntity.setVersion(version);
            productCalBasicInfoEntity.setCreateTime(date);
            productCalBasicInfoEntity.setCreateUser(user.getUserName());
            productCalBasicInfoDao.insert(productCalBasicInfoEntity);
            Long basicInfoId = productCalBasicInfoEntity.getId();

            /**
             * 保存基本参数表
             */
            List<ProductCalBasicParamEntity> productCalBasicParamEntityList = productCalculateAppDto.getProductCalBasicParamEntityList();
            if(productCalBasicParamEntityList != null && productCalBasicParamEntityList.size()>0){
                productCalBasicParamEntityList.stream().forEach(productCalBasicParamEntity -> {
                    productCalBasicParamEntity.setBasicInfoId(basicInfoId);
                    productCalBasicParamEntity.setCreateTime(date);
                    productCalBasicParamEntity.setCreateUser(user.getUserName());
                    productCalBasicParamDao.insert(productCalBasicParamEntity);
                });
            }

            /**
             * 保存现金流
             */
            List<ProductCalCashStreamEntity> productCalCashStreamEntityList = productCalculateAppDto.getProductCalCashStreamEntityList();
            if(productCalCashStreamEntityList != null && productCalCashStreamEntityList.size()>0){
                productCalCashStreamEntityList.stream().forEach(productCalCashStreamEntity -> {
                    productCalCashStreamEntity.setBasicInfoId(basicInfoId);
                    productCalCashStreamEntity.setCreateTime(date);
                    productCalCashStreamEntity.setCreateUser(user.getUserName());
                    productCalCashStreamDao.insert(productCalCashStreamEntity);
                });
            }

            /**
             * 保存提前还款参数表,根据是否考虑提前结清保存到不同的表
             */
            if(productCalBasicInfoEntity.getConsiderEarlySquare() == 1){
                ProductCalEarlySquareParamSubDto productCalEarlySquareParamSubDto = productCalculateAppDto.getProductCalEarlySquareParamSubDto();
                ProductCalEarlySquareParamSubEntity productCalEarlySquareParamSubEntity = new ProductCalEarlySquareParamSubEntity();
                BeanUtils.copyProperties(productCalEarlySquareParamSubDto,productCalEarlySquareParamSubEntity);

                productCalEarlySquareParamSubEntity.setProbabilityPam(GsonUtil.toJson(productCalEarlySquareParamSubDto.getProductCalEarlySquareProbabilityPamDto()));
                productCalEarlySquareParamSubEntity.setHandChargeAndCommissionRebatePam(GsonUtil.toJson(productCalEarlySquareParamSubDto.getProductCalEarlySquareHandChargeAndCommissionRebatePamDto()));

                productCalEarlySquareParamSubEntity.setEarlySquareProbability(GsonUtil.toJson(productCalEarlySquareParamSubDto.getProductCalEarlySquareProbabilityDtoList()));
                productCalEarlySquareParamSubEntity.setEarlySquareHandChargeRatio(GsonUtil.toJson(productCalEarlySquareParamSubDto.getProductCalEarlySquareHandChargeRatioDtoList()));
                productCalEarlySquareParamSubEntity.setEarlySquareBasicCommissionRebateRatio(GsonUtil.toJson(productCalEarlySquareParamSubDto.getProductCalEarlySquareBasicCommissionRebateRatioDtoList()));
                productCalEarlySquareParamSubEntity.setBasicInfoId(basicInfoId);
                productCalEarlySquareParamSubEntity.setCreateTime(date);
                productCalEarlySquareParamSubEntity.setCreateUser(user.getUserName());
                productCalEarlySquareParamSubDao.insert(productCalEarlySquareParamSubEntity);
            }else{
                List<ProductCalEarlySquareParamEntity> productCalEarlySquareParamEntityList = productCalculateAppDto.getProductCalEarlySquareParamEntityList();
                if(productCalEarlySquareParamEntityList != null && productCalEarlySquareParamEntityList.size()>0){
                    productCalEarlySquareParamEntityList.stream().forEach(productCalEarlySquareParamEntity -> {
                        productCalEarlySquareParamEntity.setBasicInfoId(basicInfoId);
                        productCalEarlySquareParamEntity.setCreateTime(date);
                        productCalEarlySquareParamEntity.setCreateUser(user.getUserName());
                        productCalEarlySquareParamDao.insert(productCalEarlySquareParamEntity);
                    });
                }
            }

            /**
             * 保存还款方式参数
             */
            List<ProductCalRepaymentMethodParamEntity> productCalRepaymentMethodParamEntityList = productCalculateAppDto.getProductCalRepaymentMethodParamEntityList();
            if(productCalRepaymentMethodParamEntityList != null && productCalRepaymentMethodParamEntityList.size()>0){
                productCalRepaymentMethodParamEntityList.stream().forEach(productCalRepaymentMethodParamEntity -> {
                    productCalRepaymentMethodParamEntity.setBasicInfoId(basicInfoId);
                    productCalRepaymentMethodParamEntity.setCreateTime(date);
                    productCalRepaymentMethodParamEntity.setCreateUser(user.getUserName());
                    productCalRepaymentMethodParamDao.insert(productCalRepaymentMethodParamEntity);
                });
            }

            /**
             * 保存结果表
             */
            ProductCalResultInfoEntity productCalResultInfoEntity = productCalculateAppDto.getProductCalResultInfoEntity();
            if(productCalResultInfoEntity != null){
                productCalResultInfoEntity.setBasicInfoId(basicInfoId);
                productCalResultInfoEntity.setCreateTime(date);
                productCalResultInfoEntity.setCreateUser(user.getUserName());
                productCalResultInfoDao.insert(productCalResultInfoEntity);
            }

            /**
             * 保存税费参数表
             */
            List<ProductCalTaxFeeParamEntity> productCalTaxFeeParamEntityList = productCalculateAppDto.getProductCalTaxFeeParamEntityList();
            if(productCalTaxFeeParamEntityList != null && productCalTaxFeeParamEntityList.size()>0){
                productCalTaxFeeParamEntityList.stream().forEach(productCalTaxFeeParamEntity -> {
                    productCalTaxFeeParamEntity.setBasicInfoId(basicInfoId);
                    productCalTaxFeeParamEntity.setCreateTime(date);
                    productCalTaxFeeParamEntity.setCreateUser(user.getUserName());
                    productCalTaxFeeParamDao.insert(productCalTaxFeeParamEntity);
                });
            }

            /**
             * 保存联合贷参数
             */
            List<ProductCalUnionLoanParamEntity> productCalUnionLoanParamEntityList = productCalculateAppDto.getProductCalUnionLoanParamEntityList();
            if(productCalUnionLoanParamEntityList != null && productCalUnionLoanParamEntityList.size()>0){
                productCalUnionLoanParamEntityList.stream().forEach(productCalUnionLoanParamEntity -> {
                    productCalUnionLoanParamEntity.setBasicInfoId(basicInfoId);
                    productCalUnionLoanParamEntity.setCreateTime(date);
                    productCalUnionLoanParamEntity.setCreateUser(user.getUserName());
                    productCalUnionLoanParamDao.insert(productCalUnionLoanParamEntity);
                });
            }

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());

            log.info("save template info end");
        }catch(Exception e){
            log.error("save calculate data error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("save calculate data error");
        }
        return result;
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result delete(Integer id){
        log.info("delete template {} start",id);
        Result result = new Result();
        try{
            /**
             * 删除对应id的模板的7张表信息
             */
            /**
             * 删除基础信息表
             */
            productCalBasicInfoDao.deleteById(id);

            /**
             * 删除基本参数表
             */
            QueryWrapper queryWrapperBasicParam = new QueryWrapper();
            queryWrapperBasicParam.eq("basic_info_id",id);
            productCalBasicParamDao.delete(queryWrapperBasicParam);

            /**
             * 删除现金流表
             */
            QueryWrapper queryWrapperCashStream = new QueryWrapper();
            queryWrapperCashStream.eq("basic_info_id",id);
            productCalCashStreamDao.delete(queryWrapperCashStream);

            /**
             * 删除提前还款参数表
             */
            QueryWrapper queryWrapperEarlySquare = new QueryWrapper();
            queryWrapperEarlySquare.eq("basic_info_id",id);
            productCalEarlySquareParamDao.delete(queryWrapperEarlySquare);

            /**
             * 删除提前还款附加表
             */
            QueryWrapper queryWrapperEarlySquareSub = new QueryWrapper();
            queryWrapperEarlySquareSub.eq("basic_info_id",id);
            productCalEarlySquareParamSubDao.delete(queryWrapperEarlySquareSub);

            /**
             * 删除还款方式参数表
             */
            QueryWrapper queryWrapperRepaymentMethod = new QueryWrapper();
            queryWrapperRepaymentMethod.eq("basic_info_id",id);
            productCalRepaymentMethodParamDao.delete(queryWrapperRepaymentMethod);

            /**
             * 删除税费参数表
             */
            QueryWrapper queryWrapperTaxFee = new QueryWrapper();
            queryWrapperTaxFee.eq("basic_info_id",id);
            productCalTaxFeeParamDao.delete(queryWrapperTaxFee);

            /**
             * 删除结果信息表
             */
            QueryWrapper queryWrapperResultInfo = new QueryWrapper();
            queryWrapperResultInfo.eq("basic_info_id",id);
            productCalResultInfoDao.delete(queryWrapperResultInfo);

            /**
             * 删除联合贷参数表
             */
            QueryWrapper queryWrapperUnionLoan = new QueryWrapper();
            queryWrapperUnionLoan.eq("basic_info_id",id);
            productCalUnionLoanParamDao.delete(queryWrapperUnionLoan);

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());

            log.info("delete template {} end",id);
        }catch(Exception e){
            log.error("delete template data error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("delete template data error");
        }
        return result;
    }

    /**
     * 更新基础信息
     * @param productCalBasicInfoEntity
     * @param user
     * @return
     */
    public Result update(ProductCalBasicInfoEntity productCalBasicInfoEntity,User user){
        Result result = new Result();
        try{
            ProductCalBasicInfoEntity productCalBasicInfoEntityOld = productCalBasicInfoDao.selectById(productCalBasicInfoEntity.getId());
            productCalBasicInfoEntityOld.setTemplateName(productCalBasicInfoEntity.getTemplateName());
            productCalBasicInfoEntityOld.setRepaymentMethodInstruction(productCalBasicInfoEntity.getRepaymentMethodInstruction());
            /**
             * 判断如果状态发生变更，需要更新生效日期或者失效日期
             */
            if(!productCalBasicInfoEntityOld.getState().equals(productCalBasicInfoEntity.getState())){
                if("0".equals(productCalBasicInfoEntity.getState())){
                    productCalBasicInfoEntityOld.setState(productCalBasicInfoEntity.getState());
                    productCalBasicInfoEntityOld.setExpiryDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    productCalBasicInfoEntityOld.setUpdateTime(new Date());
                    productCalBasicInfoEntityOld.setUpdateUser(user.getUserName());
                }else{
                    productCalBasicInfoEntityOld.setState(productCalBasicInfoEntity.getState());
                    productCalBasicInfoEntityOld.setEffectiveDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    productCalBasicInfoEntityOld.setExpiryDate(null);
                    productCalBasicInfoEntityOld.setUpdateTime(new Date());
                    productCalBasicInfoEntityOld.setUpdateUser(user.getUserName());
                }
            }
            productCalBasicInfoDao.updateById(productCalBasicInfoEntityOld);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch(Exception e){
            log.error("update state error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("update state error");
        }
        return result;
    }

    /**
     * 获取所有模板
     * @return
     */
    public Result getAllTemplate(ProductCalBasicInfoDto productCalBasicInfoDto,Integer current, Integer size){
        log.info("get template list start");
        Result result = new Result();
        try{
            IPage<ProductCalBasicInfoEntity> page = new Page<>(current, size);
            ProductCalBasicInfoEntity productCalBasicInfoEntity = new ProductCalBasicInfoEntity();
            BeanUtils.copyProperties(productCalBasicInfoDto,productCalBasicInfoEntity);

            QueryWrapper queryWrapper = new QueryWrapper(productCalBasicInfoEntity);
            queryWrapper.orderByDesc("id");

            if(StringUtils.hasLength(productCalBasicInfoEntity.getTemplateName())){
                String name = productCalBasicInfoEntity.getTemplateName();
                productCalBasicInfoEntity.setTemplateName(null);
                queryWrapper.like("template_name", name);
            }
            if(StringUtils.hasLength(productCalBasicInfoEntity.getRepaymentMethodInstruction())){
                String instruction = productCalBasicInfoEntity.getRepaymentMethodInstruction();
                productCalBasicInfoEntity.setRepaymentMethodInstruction(null);
                queryWrapper.like("repayment_method_instruction", instruction);
            }

            IPage<ProductCalBasicInfoEntity> iPage = productCalBasicInfoDao.selectPage(page,queryWrapper);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(iPage.getRecords());
            result.setTotal(iPage.getTotal());
            log.info("get template list end");
        }catch(Exception e){
            log.error("get all template error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("get all template error");
        }
        return result;
    }

    /**
     * 获取模板详细信息
     * @param id
     * @return
     */
    public Result getTemplateDetail(Integer id){
        log.info("get template {} edtail start",id);
        Result result = new Result();
        try{
            ProductCalculateAppDto productCalculateAppDto = new ProductCalculateAppDto();

            /**
             * 查询基础信息
             */
            ProductCalBasicInfoEntity productCalBasicInfoEntity = productCalBasicInfoDao.selectById(id);
            productCalculateAppDto.setProductCalBasicInfoEntity(productCalBasicInfoEntity);

            /**
             * 查询基础参数
             */
            QueryWrapper queryWrapperBasicParam = new QueryWrapper();
            queryWrapperBasicParam.eq("basic_info_id",id);
            List<ProductCalBasicParamEntity> productCalBasicParamEntityList = productCalBasicParamDao.selectList(queryWrapperBasicParam);
            productCalculateAppDto.setProductCalBasicParamEntityList(productCalBasicParamEntityList);

            /**
             * 查询现金流
             */
            QueryWrapper queryWrapperCashStream = new QueryWrapper();
            queryWrapperCashStream.eq("basic_info_id",id);
            List<ProductCalCashStreamEntity> productCalCashStreamEntityList = productCalCashStreamDao.selectList(queryWrapperCashStream);
            productCalculateAppDto.setProductCalCashStreamEntityList(productCalCashStreamEntityList);

            /**
             * 查询税费
             */
            QueryWrapper queryWrapperTaxFee = new QueryWrapper();
            queryWrapperTaxFee.eq("basic_info_id",id);
            List<ProductCalTaxFeeParamEntity> productCalTaxFeeParamEntityList = productCalTaxFeeParamDao.selectList(queryWrapperTaxFee);
            productCalculateAppDto.setProductCalTaxFeeParamEntityList(productCalTaxFeeParamEntityList);

            /**
             * 查询还款方式
             */
            QueryWrapper queryWrapperRepaymentMethod = new QueryWrapper();
            queryWrapperRepaymentMethod.eq("basic_info_id",id);
            List<ProductCalRepaymentMethodParamEntity> productCalRepaymentMethodParamEntityList = productCalRepaymentMethodParamDao.selectList(queryWrapperRepaymentMethod);
            productCalculateAppDto.setProductCalRepaymentMethodParamEntityList(productCalRepaymentMethodParamEntityList);

            /**
             * 查询提前结清
             */
            QueryWrapper queryWrapperEarlySquare = new QueryWrapper();
            queryWrapperEarlySquare.eq("basic_info_id",id);
            List<ProductCalEarlySquareParamEntity> productCalEarlySquareParamEntityList = productCalEarlySquareParamDao.selectList(queryWrapperEarlySquare);
            productCalculateAppDto.setProductCalEarlySquareParamEntityList(productCalEarlySquareParamEntityList);

            /**
             * 查询提前结清附加表
             */
            QueryWrapper queryWrapperEarlySquareSub = new QueryWrapper();
            queryWrapperEarlySquareSub.eq("basic_info_id",id);
            ProductCalEarlySquareParamSubEntity productCalEarlySquareParamSubEntity = productCalEarlySquareParamSubDao.selectOne(queryWrapperEarlySquare);
            if(productCalEarlySquareParamSubEntity != null){
                ProductCalEarlySquareParamSubDto productCalEarlySquareParamSubDto = new ProductCalEarlySquareParamSubDto();
                BeanUtils.copyProperties(productCalEarlySquareParamSubEntity,productCalEarlySquareParamSubDto);

                productCalEarlySquareParamSubDto.setProductCalEarlySquareProbabilityPamDto(GsonUtil.gsonToBean(productCalEarlySquareParamSubEntity.getProbabilityPam(), ProductCalEarlySquareProbabilityPamDto.class));
                productCalEarlySquareParamSubDto.setProductCalEarlySquareHandChargeAndCommissionRebatePamDto(GsonUtil.gsonToBean(productCalEarlySquareParamSubEntity.getHandChargeAndCommissionRebatePam(), ProductCalEarlySquareHandChargeAndCommissionRebatePamDto.class));

                productCalEarlySquareParamSubDto.setProductCalEarlySquareProbabilityDtoList(GsonUtil.jsonToList(productCalEarlySquareParamSubEntity.getEarlySquareProbability(), ProductCalEarlySquareProbabilityDto.class));
                productCalEarlySquareParamSubDto.setProductCalEarlySquareBasicCommissionRebateRatioDtoList(GsonUtil.jsonToList(productCalEarlySquareParamSubEntity.getEarlySquareBasicCommissionRebateRatio(), ProductCalEarlySquareBasicCommissionRebateRatioDto.class));
                productCalEarlySquareParamSubDto.setProductCalEarlySquareHandChargeRatioDtoList(GsonUtil.jsonToList(productCalEarlySquareParamSubEntity.getEarlySquareHandChargeRatio(), ProductCalEarlySquareHandChargeRatioDto.class));
                productCalculateAppDto.setProductCalEarlySquareParamSubDto(productCalEarlySquareParamSubDto);
            }

            /**
             * 查询结果信息
             */
            QueryWrapper queryWrapperResultInfo = new QueryWrapper();
            queryWrapperResultInfo.eq("basic_info_id",id);
            ProductCalResultInfoEntity productCalResultInfoEntity = productCalResultInfoDao.selectOne(queryWrapperResultInfo);
            productCalculateAppDto.setProductCalResultInfoEntity(productCalResultInfoEntity);

            /**
             * 查询联合贷信息
             */
            QueryWrapper queryWrapperUnionLoan = new QueryWrapper();
            queryWrapperUnionLoan.eq("basic_info_id",id);
            List<ProductCalUnionLoanParamEntity> productCalUnionLoanParamEntityList = productCalUnionLoanParamDao.selectList(queryWrapperUnionLoan);
            productCalculateAppDto.setProductCalUnionLoanParamEntityList(productCalUnionLoanParamEntityList);

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productCalculateAppDto);

            log.info("get template {} edtail end",id);
        }catch(Exception e){
            log.error("get template detail error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("get template detail error");
        }
        return result;
    }

    /**
     * 获取固定成本
     * @return
     */
    public Result getFixCostInfo(){
        Result result = new Result();
        QueryWrapper queryWrapperFixCost = new QueryWrapper();
        queryWrapperFixCost.orderByDesc("year");
        List<ProductCalFixCostEntity> productCalFixCostEntities = productCalFixCostDao.selectList(queryWrapperFixCost);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        result.setData(productCalFixCostEntities);
        return result;
    }

    /**
     * 获取资金成本
     * @return
     */
    public Result getCapitalCostInfo(){
        Result result = new Result();
        QueryWrapper queryWrapperCapitalCost = new QueryWrapper();
        queryWrapperCapitalCost.orderByDesc("year");
        queryWrapperCapitalCost.orderByDesc("month");
        List<ProductCalCapitalCostEntity> productCalCapitalCostEntities = productCalCapitalCostDao.selectList(queryWrapperCapitalCost);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        result.setData(productCalCapitalCostEntities);
        return result;
    }

    /**
     * 获取公式描述
     * @param bussType
     * @param repaymentMethod
     * @param item
     * @return
     */
    public Result getFormulaDes(String bussType, String repaymentMethod, String item){
        Result result = new Result();
        /**
         * 对动态分段贷还款额公式特殊处理
         */
        if(item.startsWith("sub_repayment_money")){
            item = "sub_repayment_money_numtime";
        }

        String itemFinal = item;

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("buss_type",bussType);
        queryWrapper.or();
        queryWrapper.eq("repayment_method",repaymentMethod);
        List<ProductCalBaseTemplateEntity> productCalBaseTemplateEntityList = productCalBaseTemplateDao.selectList(queryWrapper);
        Optional<ProductCalBaseTemplateEntity> productCalBaseTemplateEntityOptional = productCalBaseTemplateEntityList.stream().filter(productCalBaseTemplateEntity -> {
            return itemFinal.equals(productCalBaseTemplateEntity.getItem());
        }).findFirst();

        if(productCalBaseTemplateEntityOptional == null || !productCalBaseTemplateEntityOptional.isPresent()){
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData("该条目没有对应公式");
            return result;
        }

        String formula = productCalBaseTemplateEntityOptional.get().getFormula();

        if(!StringUtils.hasText(formula)){
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData("该条目没有对应公式");
            return result;
        }

        productCalBaseTemplateEntityList = productCalBaseTemplateEntityList.stream().map(productCalBaseTemplateEntity -> {
            productCalBaseTemplateEntity.setItem(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalBaseTemplateEntity.getItem()));
            return productCalBaseTemplateEntity;
        }).collect(Collectors.toList());

        for (ProductCalBaseTemplateEntity productCalBaseTemplateEntity : productCalBaseTemplateEntityList) {
            if(formula.indexOf("#"+productCalBaseTemplateEntity.getItem()+"#")!=-1){
                formula = formula.replaceAll("#"+productCalBaseTemplateEntity.getItem()+"#",productCalBaseTemplateEntity.getItemDesc());
            }
            if(formula.indexOf("@"+productCalBaseTemplateEntity.getItem()+"@")!=-1){
                formula = formula.replaceAll("@"+productCalBaseTemplateEntity.getItem()+"@",productCalBaseTemplateEntity.getItemDesc());
            }
            if(formula.indexOf("@"+productCalBaseTemplateEntity.getItem()+"Up@")!=-1){
                formula = formula.replaceAll("@"+productCalBaseTemplateEntity.getItem()+"Up@",productCalBaseTemplateEntity.getItemDesc());
            }
            if(formula.indexOf("array(~"+productCalBaseTemplateEntity.getItem()+"~)")!=-1){
                formula = formula.replaceAll("array\\(~"+productCalBaseTemplateEntity.getItem()+"~\\)",productCalBaseTemplateEntity.getItemDesc());
            }
            if(formula.indexOf("mapDouble(~"+productCalBaseTemplateEntity.getItem()+"~)")!=-1){
                formula = formula.replaceAll("mapDouble\\(~"+productCalBaseTemplateEntity.getItem()+"~\\)",productCalBaseTemplateEntity.getItemDesc());
            }
            if(formula.indexOf("mapString(~"+productCalBaseTemplateEntity.getItem()+"~)")!=-1){
                formula = formula.replaceAll("mapString\\(~"+productCalBaseTemplateEntity.getItem()+"~\\)",productCalBaseTemplateEntity.getItemDesc());
            }
        }
        formula = formula.replaceAll("@month@","月份");
        formula = formula.replaceAll("mapString\\(~dueDate~\\)","还款日");
        formula = formula.replaceAll("~repl","");
        formula = formula.replaceAll("~","");
        formula = formula.replaceAll("/100","");
        formula = formula.replaceAll("@subRepaymentTimeLimitNumj1@","上一笔分段贷还款期次");
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        result.setData(formula);
        return result;
    }

    public Result templateCompare(String idList){
        List<String> list = Arrays.stream(idList.split(",")).collect(Collectors.toList());
        Result result = new Result();

        try{
            List<ProductCalculateAppDto> productCalculateAppDtoList = new ArrayList<>();
            if(list != null){
                list.stream().forEach(id -> {
                    Result resultDetail = getTemplateDetail(Integer.valueOf(id));
                    productCalculateAppDtoList.add((ProductCalculateAppDto)resultDetail.getData());
                });

            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productCalculateAppDtoList);
        }catch(Exception e){
            log.error("get template compare error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("get template compare error");
        }
        return result;
    }

    public Result calulateProfitByAmount(ProductCalProfitDto productCalProfitDto){
        Result result = new Result();
        try{
            log.info("根据销量计算利润开始");
            long startTime = System.currentTimeMillis();
            ProductCalculateDto productCalculateDto = productCalProfitDto.getProductCalculateDto();
            List<ProductCalProfitDetailDto> productCalProfitDetailDtoList = productCalProfitDto.getProductCalProfitDetailDtoList();
            int count = 1;
            List<ProductCalProfitCollectDto> productCalProfitCollectDtoList = new ArrayList<>();
            List<ProductCalProfitDetailDto> productCalProfitDetailDtoListReturn = new ArrayList<>();
            for (ProductCalProfitDetailDto productCalProfitDetailDto : productCalProfitDetailDtoList) {
                productCalculateDto.setProductCalProfitDetailDto(productCalProfitDetailDto);
                Result profitResult = calculateAllParam(productCalculateDto);
                if(profitResult.getCode()!=200){
                    result.setCode(profitResult.getCode());
                    result.setMessage(profitResult.getMessage());
                    return result;
                }
                ProductCalculateDto productCalculateDtoResult = (ProductCalculateDto)profitResult.getData();
                List<ProductCalCashStreamDto> productCalCashStreamDtoList = productCalculateDtoResult.getProductCalCashStreamDtoList();
                List<ProductCalProfitDetailDto> productCalProfitDetailDtoListResult = new ArrayList<>();
                for (ProductCalCashStreamDto productCalCashStreamDto : productCalCashStreamDtoList) {
                    ProductCalProfitDetailDto productCalProfitDetailDtoResult = new ProductCalProfitDetailDto();
                    productCalProfitDetailDtoResult.setAmount(productCalProfitDetailDto.getAmount());
                    productCalProfitDetailDtoResult.setPutMonth(count);
                    productCalProfitDetailDtoResult.setStartMonth(productCalProfitDetailDto.getStartMonth());
                    productCalProfitDetailDtoResult.setDueDate(productCalCashStreamDto.getCashStreamDate());
                    productCalProfitDetailDtoResult.setDueYear(String.valueOf(LocalDate.parse(productCalCashStreamDto.getCashStreamDate()).getYear()));
                    productCalProfitDetailDtoResult.setProfitMoney(dealScale(2,productCalCashStreamDto.getCashStreamProfit()*productCalProfitDetailDto.getAmount()));
                    productCalProfitDetailDtoListResult.add(productCalProfitDetailDtoResult);
                    productCalProfitDetailDtoListReturn.add(productCalProfitDetailDtoResult);
                }
                Map<String, List<ProductCalProfitDetailDto>> collect = productCalProfitDetailDtoListResult.stream().collect(Collectors.groupingBy(ProductCalProfitDetailDto::getDueYear));
                Set<String> dueYearSet = collect.keySet();
                for (String dueYear : dueYearSet) {
                    ProductCalProfitCollectDto productCalProfitCollectDto = new ProductCalProfitCollectDto();
                    productCalProfitCollectDto.setPutMonth(count);
                    productCalProfitCollectDto.setDueYear(dueYear);
                    productCalProfitCollectDto.setProfitMoney(dealScale(2,collect.get(dueYear).stream().mapToDouble(ProductCalProfitDetailDto::getProfitMoney).sum()));
                    productCalProfitCollectDtoList.add(productCalProfitCollectDto);
                }
                count++;
            }
            Map<String, List<ProductCalProfitCollectDto>> collect = productCalProfitCollectDtoList.stream().collect(Collectors.groupingBy(ProductCalProfitCollectDto::getDueYear));
            Set<String> dueYearSet = collect.keySet();
            for (String dueYear : dueYearSet) {
                ProductCalProfitCollectDto productCalProfitCollectDto = new ProductCalProfitCollectDto();
                productCalProfitCollectDto.setPutMonth(9999);
                productCalProfitCollectDto.setDueYear(dueYear);
                productCalProfitCollectDto.setProfitMoney(dealScale(2,collect.get(dueYear).stream().mapToDouble(ProductCalProfitCollectDto::getProfitMoney).sum()));
                productCalProfitCollectDtoList.add(productCalProfitCollectDto);
            }

            productCalProfitDto.setProductCalProfitCollectDtoList(productCalProfitCollectDtoList);
            productCalProfitDto.setProductCalProfitDetailDtoList(productCalProfitDetailDtoListReturn);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productCalProfitDto);
            log.info("根据销量计算利润结束");
            long endTime = System.currentTimeMillis();
            log.info("根据销量计算利润耗时：{}s",(endTime-startTime)/1000);
        }catch(Exception e){
            log.error("根据销量计算利润异常",e);
            result.setCode(FAIL.getCode());
            result.setMessage("根据销量计算利润异常");
        }
        return result;
    }

    /**
     * 计算提前结清irr相关指标
     * @param productCalculateDto
     * @return
     */
    public Result calculateEarlySquare(ProductCalculateDto productCalculateDto){
        log.info("计算提前结清irr开始");
        long startTime = System.currentTimeMillis();
        Result result = new Result();
        try{
            /**
             * 联合贷暂不支持提前结清，默认为非联合贷测算
             */
            productCalculateDto.setProductCalUnionLoanParamDto(null);
            Integer timeLimit = productCalculateDto.getProductCalBasicParamDto().getTimeLimit();
            List<ProductCalEarlySquareResultInfoDto> productCalEarlySquareResultInfoDtoList = new ArrayList<>();

            for (int i = 1; i <=timeLimit; i++) {
                ProductCalculateEarlySquareDto productCalculateEarlySquareDto = new ProductCalculateEarlySquareDto();
                productCalculateEarlySquareDto.setEarlySquareTimeLimit(i);
                productCalculateEarlySquareDto.setTimeLimit(timeLimit);
                productCalculateDto.setProductCalculateEarlySquareDto(productCalculateEarlySquareDto);
                Result earlyResult = calculateAllParam(productCalculateDto);
                if(earlyResult.getCode()!=200){
                    result.setCode(FAIL.getCode());
                    result.setMessage("计算提前结清期限："+i+"异常："+earlyResult.getMessage());
                    return result;
                }else{
                    ProductCalculateDto productCalculateDtoResult = (ProductCalculateDto)earlyResult.getData();
                    ProductCalEarlySquareResultInfoDto productCalEarlySquareResultInfoDto = new ProductCalEarlySquareResultInfoDto();
                    productCalEarlySquareResultInfoDto.setTime(i);
                    productCalEarlySquareResultInfoDto.setResultIrr(productCalculateDtoResult.getProductCalResultInfoDto().getResultIrr());
                    productCalEarlySquareResultInfoDto.setResultXirr(productCalculateDtoResult.getProductCalResultInfoDto().getResultXirr());
                    productCalEarlySquareResultInfoDto.setResultRoa(productCalculateDtoResult.getProductCalResultInfoDto().getResultRoa());
                    productCalEarlySquareResultInfoDto.setResultBeforeTaxIncome(productCalculateDtoResult.getProductCalResultInfoDto().getResultBeforeTaxIncome());
                    productCalEarlySquareResultInfoDto.setResultNetProfitMoney(productCalculateDtoResult.getProductCalResultInfoDto().getResultNetProfitMoney());
                    productCalEarlySquareResultInfoDto.setResultPerTenThousandIncome(productCalculateDtoResult.getProductCalResultInfoDto().getResultPerTenThousandIncome());
                    productCalEarlySquareResultInfoDto.setProductCalCashStreamDtoList(productCalculateDtoResult.getProductCalCashStreamDtoList());
                    productCalEarlySquareResultInfoDtoList.add(productCalEarlySquareResultInfoDto);
                }
            }

            /**
             * 查询提前结清概率信息,计算汇总结果信息
             */
            Result earlySquare = getEarlySquareProbabilityInfo(productCalculateDto.getProductCalBasicInfoDto().getBusinessType(),productCalculateDto.getProductCalBasicParamDto().getCustomerInterestRate(),productCalculateDto.getProductCalBasicParamDto().getActualInterestRate(),productCalculateDto.getProductCalBasicParamDto().getTimeLimit());
            List<ProductCalEarlySquareProbabilityEntity> productCalEarlySquareProbabilityEntityList = (List<ProductCalEarlySquareProbabilityEntity>)earlySquare.getData();
            ProductCalEarlySquareResultInfoDto productCalEarlySquareResultInfoDtoTotal = new ProductCalEarlySquareResultInfoDto();
            productCalEarlySquareResultInfoDtoTotal.setTime(9999);
            BigDecimal totalIrr = BigDecimal.valueOf(0.0);
            BigDecimal totalXirr = BigDecimal.valueOf(0.0);
            BigDecimal totalRoa = BigDecimal.valueOf(0.0);
            BigDecimal totalBeforeTaxIncome = BigDecimal.valueOf(0.0);
            BigDecimal totalNetProfitMoney = BigDecimal.valueOf(0.0);
            BigDecimal totalPerTenThousandIncome = BigDecimal.valueOf(0.0);

            if(productCalEarlySquareProbabilityEntityList!=null&&productCalEarlySquareProbabilityEntityList.size()>0){
                for (ProductCalEarlySquareProbabilityEntity productCalEarlySquareProbabilityEntity : productCalEarlySquareProbabilityEntityList) {
                    for (ProductCalEarlySquareResultInfoDto productCalEarlySquareResultInfoDto : productCalEarlySquareResultInfoDtoList) {
                        if(productCalEarlySquareResultInfoDto.getTime().equals(productCalEarlySquareProbabilityEntity.getPayoutRentalId())){
                            totalIrr = totalIrr.add((BigDecimal.valueOf(productCalEarlySquareResultInfoDto.getResultIrr())).multiply(BigDecimal.valueOf(productCalEarlySquareProbabilityEntity.getPayoutProbability())));
                            totalXirr = totalXirr.add((BigDecimal.valueOf(productCalEarlySquareResultInfoDto.getResultXirr())).multiply(BigDecimal.valueOf(productCalEarlySquareProbabilityEntity.getPayoutProbability())));
                            totalRoa = totalRoa.add((BigDecimal.valueOf(productCalEarlySquareResultInfoDto.getResultRoa())).multiply(BigDecimal.valueOf(productCalEarlySquareProbabilityEntity.getPayoutProbability())));
                            totalBeforeTaxIncome = totalBeforeTaxIncome.add((BigDecimal.valueOf(productCalEarlySquareResultInfoDto.getResultBeforeTaxIncome())).multiply(BigDecimal.valueOf(productCalEarlySquareProbabilityEntity.getPayoutProbability())));
                            totalNetProfitMoney = totalNetProfitMoney.add((BigDecimal.valueOf(productCalEarlySquareResultInfoDto.getResultNetProfitMoney())).multiply(BigDecimal.valueOf(productCalEarlySquareProbabilityEntity.getPayoutProbability())));
                            totalPerTenThousandIncome = totalPerTenThousandIncome.add((BigDecimal.valueOf(productCalEarlySquareResultInfoDto.getResultPerTenThousandIncome())).multiply(BigDecimal.valueOf(productCalEarlySquareProbabilityEntity.getPayoutProbability())));
                        }
                    }
                }
            }
            productCalEarlySquareResultInfoDtoTotal.setResultIrr(totalIrr.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            productCalEarlySquareResultInfoDtoTotal.setResultRoa(totalRoa.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            productCalEarlySquareResultInfoDtoTotal.setResultXirr(totalXirr.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            productCalEarlySquareResultInfoDtoTotal.setResultBeforeTaxIncome(totalBeforeTaxIncome.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            productCalEarlySquareResultInfoDtoTotal.setResultNetProfitMoney(totalNetProfitMoney.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            productCalEarlySquareResultInfoDtoTotal.setResultPerTenThousandIncome(totalPerTenThousandIncome.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            productCalEarlySquareResultInfoDtoList.add(productCalEarlySquareResultInfoDtoTotal);

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productCalEarlySquareResultInfoDtoList);
            log.info("计算提前结清irr结束,耗时：{}s",(System.currentTimeMillis()-startTime)/1000);
        }catch (Exception e){
            log.error("calculateEarlySquare error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("计算提前结清信息失败");
        }
        return result;
    }

    /**
     * 获取提前结清概率信息
     * @return
     */
    public Result getEarlySquareProbabilityInfo(Integer businessType,Double customerInterestRate,Double actualInterestRate,Integer timeLimit){
        Result result = new Result();
        QueryWrapper queryWrapperEarlySquareProbability = new QueryWrapper();
        String bussType = businessType == 0?"本品":(businessType == 1?"全品新车":"全品二手车");
        queryWrapperEarlySquareProbability.eq("business_type",bussType);

        String ifDisInsterst = customerInterestRate.equals(actualInterestRate)==true?"非贴息":"贴息";
        queryWrapperEarlySquareProbability.eq("if_dis_insterst",ifDisInsterst);

        queryWrapperEarlySquareProbability.eq("contract_trm",timeLimit);

        queryWrapperEarlySquareProbability.orderByAsc("payout_rental_id");

        List<ProductCalEarlySquareProbabilityEntity> productCalEarlySquareProbabilityEntityList = productCalEarlySquareProbabilityDao.selectList(queryWrapperEarlySquareProbability);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        result.setData(productCalEarlySquareProbabilityEntityList);
        return result;
    }

    /**
     * 获取放款手续费详情
     * @return
     */
    public Result getLoanHandChargeDetail(){
        Result result = new Result();
        try{
            List<ProductCalLoanHandChargeDto> productCalLoanHandChargeDtoList = new ArrayList<>();
            List<ProductCalLoanHandChargeEntity> loanHandChargeDetailList = productCalDao.getLoanHandChargeDetail();
            Map<Double, List<ProductCalLoanHandChargeEntity>> loanHandChargeDetailMap = loanHandChargeDetailList.stream().collect(Collectors.groupingBy(ProductCalLoanHandChargeEntity::getRangeLow,TreeMap::new,Collectors.toList()));

            Set<Double> rangeLowSet = loanHandChargeDetailMap.keySet();
            for (Double rangeLow : rangeLowSet) {
                String moneyRange = "";
                List<ProductCalLoanHandChargeEntity> productCalLoanHandChargeEntityList = loanHandChargeDetailMap.get(rangeLow);
                productCalLoanHandChargeEntityList = productCalLoanHandChargeEntityList.stream().sorted(Comparator.comparing(ProductCalLoanHandChargeEntity::getBank)).collect(Collectors.toList());

                StringBuffer stringBuffer = new StringBuffer("");
                String deadlineDate = "";
                for (int i = 0; i <productCalLoanHandChargeEntityList.size(); i++) {
                    ProductCalLoanHandChargeEntity productCalLoanHandChargeEntity = productCalLoanHandChargeEntityList.get(i);
                    ProductCalLoanHandChargeDto productCalLoanHandChargeDto = new ProductCalLoanHandChargeDto();
                    productCalLoanHandChargeDto.setBank(productCalLoanHandChargeEntity.getBank());
                    productCalLoanHandChargeDto.setLoanProbability(String.valueOf(productCalLoanHandChargeEntity.getLoanProbability()));
                    productCalLoanHandChargeDto.setMoneyRange(productCalLoanHandChargeEntity.getMoneyRange());
                    moneyRange = productCalLoanHandChargeEntity.getMoneyRange();
                    productCalLoanHandChargeDto.setHandCharge(String.valueOf(productCalLoanHandChargeEntity.getHandCharge()));
                    if(!StringUtils.hasText(deadlineDate)){
                        deadlineDate = productCalLoanHandChargeEntity.getYear()+"-12-31";
                    }
                    productCalLoanHandChargeDto.setDeadlineDate(deadlineDate);
                    productCalLoanHandChargeDtoList.add(productCalLoanHandChargeDto);

                    if(i == (productCalLoanHandChargeEntityList.size()-1)){
                        stringBuffer.append(productCalLoanHandChargeEntity.getHandCharge()+"*"+productCalLoanHandChargeEntity.getLoanProbability());
                    }else{
                        stringBuffer.append(productCalLoanHandChargeEntity.getHandCharge()+"*"+productCalLoanHandChargeEntity.getLoanProbability()+"+");
                    }
                }

                ProductCalLoanHandChargeDto productCalLoanHandChargeDto = new ProductCalLoanHandChargeDto();
                productCalLoanHandChargeDto.setBank("汇总");
                productCalLoanHandChargeDto.setMoneyRange(moneyRange);
                productCalLoanHandChargeDto.setHandCharge(stringBuffer.toString());
                productCalLoanHandChargeDto.setDeadlineDate(deadlineDate);
                productCalLoanHandChargeDtoList.add(productCalLoanHandChargeDto);
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productCalLoanHandChargeDtoList);
        }catch(Exception e){
            log.error("getLoanHandChargeDetail error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("获取放款手续费详情失败");
        }
        return result;
    }

    /**
     * 获取扣款手续费详情
     * @return
     */
    public Result getDeductHandChargeDetail(){
        Result result = new Result();
        try{
            List<ProductCalDeductMoneyHandChargeDto> productCalDeductMoneyHandChargeDtoList = new ArrayList<>();
            List<ProductCalDeductMoneyHandChargeEntity> deductMoneyHandChargeEntityList = productCalDao.getDeductMoneyHandChargeDetail();
            Map<Double, List<ProductCalDeductMoneyHandChargeEntity>> deductMoneyHandChargeDetailMap = deductMoneyHandChargeEntityList.stream().collect(Collectors.groupingBy(ProductCalDeductMoneyHandChargeEntity::getRangeLow,TreeMap::new,Collectors.toList()));

            Set<Double> rangeLowSet = deductMoneyHandChargeDetailMap.keySet();
            for (Double rangeLow : rangeLowSet) {
                String moneyRange = "";
                List<ProductCalDeductMoneyHandChargeEntity> productCalDeductMoneyHandChargeEntityList = deductMoneyHandChargeDetailMap.get(rangeLow);
                productCalDeductMoneyHandChargeEntityList = productCalDeductMoneyHandChargeEntityList.stream().sorted(Comparator.comparing(ProductCalDeductMoneyHandChargeEntity::getBank)).collect(Collectors.toList());

                StringBuffer stringBuffer = new StringBuffer("");
                String deadlineDate = "";
                for (int i = 0; i <productCalDeductMoneyHandChargeEntityList.size(); i++) {
                    ProductCalDeductMoneyHandChargeEntity productCalDeductMoneyHandChargeEntity = productCalDeductMoneyHandChargeEntityList.get(i);
                    ProductCalDeductMoneyHandChargeDto productCalDeductMoneyHandChargeDto = new ProductCalDeductMoneyHandChargeDto();
                    productCalDeductMoneyHandChargeDto.setBank(productCalDeductMoneyHandChargeEntity.getBank());
                    productCalDeductMoneyHandChargeDto.setDeductProbability(String.valueOf(productCalDeductMoneyHandChargeEntity.getDeductProbability()));
                    productCalDeductMoneyHandChargeDto.setMoneyRange(productCalDeductMoneyHandChargeEntity.getMoneyRange());
                    moneyRange = productCalDeductMoneyHandChargeEntity.getMoneyRange();
                    productCalDeductMoneyHandChargeDto.setHandCharge(String.valueOf(productCalDeductMoneyHandChargeEntity.getHandCharge()));
                    if(!StringUtils.hasText(deadlineDate)){
                        deadlineDate = productCalDeductMoneyHandChargeEntity.getYear()+"-12-31";
                    }
                    productCalDeductMoneyHandChargeDto.setDeadlineDate(deadlineDate);
                    productCalDeductMoneyHandChargeDtoList.add(productCalDeductMoneyHandChargeDto);

                    if(i == (productCalDeductMoneyHandChargeEntityList.size()-1)){
                        stringBuffer.append(productCalDeductMoneyHandChargeEntity.getHandCharge()+"*"+productCalDeductMoneyHandChargeEntity.getDeductProbability());
                    }else{
                        stringBuffer.append(productCalDeductMoneyHandChargeEntity.getHandCharge()+"*"+productCalDeductMoneyHandChargeEntity.getDeductProbability()+"+");
                    }
                }

                ProductCalDeductMoneyHandChargeDto productCalDeductMoneyHandChargeDto = new ProductCalDeductMoneyHandChargeDto();
                productCalDeductMoneyHandChargeDto.setBank("汇总");
                productCalDeductMoneyHandChargeDto.setMoneyRange(moneyRange);
                productCalDeductMoneyHandChargeDto.setHandCharge(stringBuffer.toString());
                productCalDeductMoneyHandChargeDto.setDeadlineDate(deadlineDate);
                productCalDeductMoneyHandChargeDtoList.add(productCalDeductMoneyHandChargeDto);
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productCalDeductMoneyHandChargeDtoList);
        }catch(Exception e){
            log.error("getDeductHandChargeDetail error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("获取扣款手续费详情失败");
        }
        return result;
    }

    public void exportCalData(String exportCode, String exportDataJson, HttpServletResponse response){
        try {
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("export_code",exportCode);

            ProductCalDataExportEntity productCalDataExportEntity = productCalDataExportDao.selectOne(queryWrapper);
            String calssStr = productCalDataExportEntity.getExportClass();
            String fileName = productCalDataExportEntity.getExportName();
            fileName = URLEncoder.encode(fileName, "UTF-8");
            fileName = fileName+".xlsx";

            Class classExport = Class.forName(calssStr);

            OutputStream outputStream = response.getOutputStream();
            ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(1, "Sheet1").head(classExport)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            excelWriter.write(GsonUtil.jsonToList(exportDataJson,classExport), writeSheet);


            response.setCharacterEncoding("utf-8");
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);

//            EasyExcel.write(writerOutputStream, ExportEarlySquareProbabilityDto.class).sheet("sheet1").doWrite(exportEarlySquareProbabilityDtos);
            excelWriter.finish();
            outputStream.flush();
        } catch (Exception e) {
            log.error("exportCalData error",e);
        }
    }

    public IPage<IrrCalResultEntity> getIrrCalResultByPage(PageForm<IrrCalResultDto> irrCalResultDtoPageForm) {
        IrrCalResultEntity irrCalResultEntity = new IrrCalResultEntity();
        IrrCalResultDto irrCalResultDto = irrCalResultDtoPageForm.getParam();
        BeanUtils.copyProperties(irrCalResultDto, irrCalResultEntity);
        LambdaQueryWrapper<IrrCalResultEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>(irrCalResultEntity);
        lambdaQueryWrapper.ge(irrCalResultDto.getLoanAmountMin() != null, IrrCalResultEntity::getLoanAmount,irrCalResultDto.getLoanAmountMin());
        lambdaQueryWrapper.le(irrCalResultDto.getLoanAmountMax() != null, IrrCalResultEntity::getLoanAmount,irrCalResultDto.getLoanAmountMax());

        lambdaQueryWrapper.ge(irrCalResultDto.getPaymentDaysMin() != null, IrrCalResultEntity::getPaymentDays,irrCalResultDto.getPaymentDaysMin());
        lambdaQueryWrapper.le(irrCalResultDto.getPaymentDaysMax() != null, IrrCalResultEntity::getPaymentDays,irrCalResultDto.getPaymentDaysMax());

        lambdaQueryWrapper.ge(irrCalResultDto.getCustomerInterestRateMin() != null, IrrCalResultEntity::getCustomerInterestRate,irrCalResultDto.getCustomerInterestRateMin());
        lambdaQueryWrapper.le(irrCalResultDto.getCustomerInterestRateMax() != null, IrrCalResultEntity::getCustomerInterestRate,irrCalResultDto.getCustomerInterestRateMax());

        lambdaQueryWrapper.ge(irrCalResultDto.getSettledRateMin() != null, IrrCalResultEntity::getSettledRate,irrCalResultDto.getSettledRateMin());
        lambdaQueryWrapper.le(irrCalResultDto.getSettledRateMax() != null, IrrCalResultEntity::getSettledRate, irrCalResultDto.getSettledRateMax());

        lambdaQueryWrapper.ge(irrCalResultDto.getRiskLossRateMin() != null, IrrCalResultEntity::getRiskLossRate,irrCalResultDto.getRiskLossRateMin());
        lambdaQueryWrapper.le(irrCalResultDto.getRiskLossRateMax() != null, IrrCalResultEntity::getRiskLossRate,irrCalResultDto.getRiskLossRateMax());

        lambdaQueryWrapper.ge(irrCalResultDto.getBussRebeatMin() != null, IrrCalResultEntity::getBussRebeat,irrCalResultDto.getBussRebeatMin());
        lambdaQueryWrapper.le(irrCalResultDto.getBussRebeatMax() != null, IrrCalResultEntity::getBussRebeat,irrCalResultDto.getBussRebeatMax());

        if (irrCalResultDto.getCalTimeStart() != null) {
            lambdaQueryWrapper.ge(irrCalResultDto.getCalTimeStart() != null, IrrCalResultEntity::getCalTime, irrCalResultDto.getCalTimeStart().toString().concat(" 00:00:00"));
        }
        if (irrCalResultDto.getCalTimeEnd() != null) {
            lambdaQueryWrapper.le(irrCalResultDto.getCalTimeEnd() != null, IrrCalResultEntity::getCalTime, irrCalResultDto.getCalTimeEnd().toString().concat(" 23:59:59"));
        }
        lambdaQueryWrapper.like(StringUtils.hasText(irrCalResultDto.getProductProposalName()), IrrCalResultEntity::getProductProposalName, irrCalResultDto.getProductProposalName());
        irrCalResultEntity.setProductProposalName(null);
        lambdaQueryWrapper.orderByDesc(IrrCalResultEntity::getCalTime);
        IPage page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page(irrCalResultDtoPageForm.getCurrent(), irrCalResultDtoPageForm.getSize());
        IPage<IrrCalResultEntity> irrCalResultEntityIPage = irrCalResultDao.selectPage(page, lambdaQueryWrapper);
        return irrCalResultEntityIPage;
    }
}