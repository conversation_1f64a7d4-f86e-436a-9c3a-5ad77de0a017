package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.bo.UploadFileBo;
import com.gwmfc.constant.ProductProposalApproveEnum;
import com.gwmfc.constant.ProductProposalEnum;
import com.gwmfc.dao.AdjustCalResultHistoryDao;
import com.gwmfc.dao.IrrCalResultDao;
import com.gwmfc.dao.ProductProposalDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductCalProfitCollectDto;
import com.gwmfc.dto.ProductProposalDto;
import com.gwmfc.dto.ProductProposalTemplateDto;
import com.gwmfc.dto.ProductProposalTemplateGroupDto;
import com.gwmfc.entity.AdjustCalResultHistoryEntity;
import com.gwmfc.entity.IrrCalResultEntity;
import com.gwmfc.entity.ProductProposalApprovalStatusEntity;
import com.gwmfc.entity.ProductProposalEntity;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.service.approve.ProductProposalApprovalStrategyContext;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.PageForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年10月17日 17:04
 */
@Service
@Slf4j
@RefreshScope
public class ProductProposalService extends ServiceImpl<ProductProposalDao, ProductProposalEntity> {
    @Resource
    private FtpService ftpService;

    @Resource
    private ProductProposalDao productProposalDao;
    @Resource
    private AdjustCalResultHistoryDao adjustCalResultHistoryDao;
    @Resource
    private IrrCalResultDao irrCalResultDao;
    @Resource
    private ProductProposalApprovalStrategyContext productProposalApprovalStrategyContext;
    /**
     * 文件目录
     */
    @Value("${replenish-excel-dir}")
    private String replenishExcelDir;

    @Value("${proposal-upload-file-type}")
    private List<String> proposalUploadFileTypeList;

    /**
     * 保存
     *
     * @param
     * @param addFileList
     * @param user
     */
    public Integer save(ProductProposalDto productProposalDto, List<MultipartFile> addFileList, User user) {
        ProductProposalEntity productProposalSel = new ProductProposalEntity();
        productProposalSel.setName(productProposalDto.getName());
        QueryWrapper queryWrapper = new QueryWrapper<>(productProposalSel);
        if (baseMapper.selectCount(queryWrapper) != 0) {
            log.info("该产品提案的名称已经存在！{}", productProposalDto.getName());
            throw new SystemRuntimeException("该产品提案的名称已经存在！");
        }

        /**
         * 文件类型校验
         */
        if (addFileList != null && !addFileList.isEmpty()) {
            if (!proposalUploadFileTypeList.containsAll(addFileList.stream().map(multipartFile -> multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1)).collect(Collectors.toList()))) {
                log.info("文件格式存在错误！");
                throw new SystemRuntimeException("文件格式存在错误！");
            }
        }

        ProductProposalEntity productProposalEntity = new ProductProposalEntity();
        BeanUtils.copyProperties(productProposalDto, productProposalEntity);
        if (null != addFileList && !addFileList.isEmpty()) {
            productProposalEntity.setMarketAnalysisAnnex(GsonUtil.toJson(ftpService.uploadFileToFtp(replenishExcelDir, addFileList, user.getName())));
        }
        productProposalEntity.setCurrentStep(ProductProposalEnum.RISK_BUSINESS_CHECK);
        productProposalDto.getProductProposalTemplateGroupDtoList().forEach(productProposalTemplateGroupDto -> productProposalTemplateGroupDto.setUuid(UUID.randomUUID().toString()));
        productProposalEntity.setProductProposalTemplateGroupJson(GsonUtil.toJson(productProposalDto.getProductProposalTemplateGroupDtoList()));
        productProposalEntity.setCreateTime(LocalDateTime.now());
        productProposalEntity.setCreateUser(user.getName());
        Integer res = baseMapper.insert(productProposalEntity);

        ProductProposalApprovalStatusEntity productProposalApprovalStatusEntity = new ProductProposalApprovalStatusEntity();
        productProposalApprovalStatusEntity.setProductProposalId(productProposalEntity.getId());
        productProposalApprovalStatusEntity.setCreateTime(LocalDateTime.now());
        productProposalApprovalStatusEntity.setCreateUser(user.getName());
        productProposalApprovalStatusEntity.setStep(ProductProposalEnum.PRODUCT_PROPOSAL_SUBMIT);//1
        productProposalApprovalStatusEntity.setReason("产品提案初始化");
        productProposalApprovalStatusEntity.setApproveStatus(ProductProposalApproveEnum.APPROVE);
        productProposalApprovalStatusEntity.setStatus(ProductProposalApproveEnum.APPROVE);
        productProposalApprovalStrategyContext.save(productProposalApprovalStatusEntity);
        return res;
    }

    public Integer update(ProductProposalDto productProposalDto, List<MultipartFile> addFileList, User user) {
        ProductProposalDto oldProductProposalDto = this.detail(productProposalDto.getId());
        if (oldProductProposalDto.equals(productProposalDto)) {
            throw new SystemRuntimeException("数据没有修改！");
        }
        if (addFileList != null && !addFileList.isEmpty()) {
            if (!proposalUploadFileTypeList.containsAll(addFileList.stream().map(multipartFile -> multipartFile.getOriginalFilename().substring(multipartFile.getOriginalFilename().lastIndexOf(".") + 1)).collect(Collectors.toList()))) {
                log.info("文件格式存在错误！");
                throw new SystemRuntimeException("文件格式存在错误！");
            }
        }

        ProductProposalEntity productProposalEntity = new ProductProposalEntity();
        BeanUtils.copyProperties(productProposalDto, productProposalEntity);
        if (oldProductProposalDto.getMarketAnalysisAnnex() != null && !productProposalDto.getMarketAnalysisAnnex().equals(oldProductProposalDto.getMarketAnalysisAnnex())) {
            List<UploadFileBo> uploadFileBoListOld = oldProductProposalDto.getMarketAnalysisAnnex();
            List<UploadFileBo> uploadFileBoList = productProposalDto.getMarketAnalysisAnnex();
            if (uploadFileBoListOld != null && uploadFileBoList != null) {
                List<UploadFileBo> delList = uploadFileBoListOld.stream().filter(m -> !uploadFileBoList.stream().map(d -> d.getFileName()).collect(Collectors.toList()).contains(m.getFileName())).collect(Collectors.toList());
                delList.forEach(uploadFileBo -> ftpService.deleteFile(uploadFileBo.getFilePath()));
                List<UploadFileBo> remainList = uploadFileBoListOld.stream().filter(m -> uploadFileBoList.stream().map(d -> d.getFileName()).collect(Collectors.toList()).contains(m.getFileName())).collect(Collectors.toList());
                if (null != addFileList && !addFileList.isEmpty()) {
                    remainList.addAll(ftpService.uploadFileToFtp(replenishExcelDir, addFileList, user.getName()));
                }
                productProposalEntity.setMarketAnalysisAnnex(GsonUtil.toJson(remainList));
            } else if (uploadFileBoListOld != null && uploadFileBoList == null) {
                uploadFileBoListOld.forEach(uploadFileBo -> ftpService.deleteFile(uploadFileBo.getFilePath()));
                productProposalEntity.setMarketAnalysisAnnex(null);
            }
        } else if (oldProductProposalDto.getMarketAnalysisAnnex() == null && productProposalDto.getMarketAnalysisAnnex() != null) {
            productProposalEntity.setMarketAnalysisAnnex(GsonUtil.toJson(ftpService.uploadFileToFtp(replenishExcelDir, addFileList, user.getName())));
        }
        // 过滤删除的组
        List<ProductProposalTemplateGroupDto> oldProductProposalTemplateGroupDtoList = oldProductProposalDto.getProductProposalTemplateGroupDtoList();

        List<ProductProposalTemplateGroupDto> deleteGroupList = oldProductProposalTemplateGroupDtoList.stream()
                .filter(p1 -> productProposalDto.getProductProposalTemplateGroupDtoList().stream().noneMatch(p2 -> p1.getUuid().equals(p2.getUuid())))
                .collect(Collectors.toList());
        deleteGroupList.forEach(deleteGroup -> {
            AdjustCalResultHistoryEntity adjustCalResultHistoryEntity = new AdjustCalResultHistoryEntity();
            adjustCalResultHistoryEntity.setProductProposalGroupUuid(deleteGroup.getUuid());
            QueryWrapper adjustCalResultHistoryQueryWrapper = new QueryWrapper<>(adjustCalResultHistoryEntity);
            adjustCalResultHistoryDao.delete(adjustCalResultHistoryQueryWrapper);

            IrrCalResultEntity irrCalResultEntity = new IrrCalResultEntity();
            irrCalResultEntity.setProductProposalGroupUuid(deleteGroup.getUuid());
            QueryWrapper irrCalResultQueryWrapper = new QueryWrapper<>(irrCalResultEntity);
            irrCalResultDao.delete(irrCalResultQueryWrapper);
        });

        productProposalDto.getProductProposalTemplateGroupDtoList().forEach(productProposalTemplateGroupDto -> {
            if (StringUtils.isEmpty(productProposalTemplateGroupDto.getUuid())) {
                productProposalTemplateGroupDto.setUuid(UUID.randomUUID().toString());
            }
        });
        productProposalEntity.setProductProposalTemplateGroupJson(GsonUtil.toJson(productProposalDto.getProductProposalTemplateGroupDtoList()));
        productProposalEntity.setCurrentStep(ProductProposalEnum.RISK_BUSINESS_CHECK);
        productProposalEntity.setUpdateTime(LocalDateTime.now());
        productProposalEntity.setUpdateUser(user.getName());
        Integer res = baseMapper.updateById(productProposalEntity);

        ProductProposalApprovalStatusEntity productProposalApprovalStatusEntity = new ProductProposalApprovalStatusEntity();
        productProposalApprovalStatusEntity.setProductProposalId(productProposalEntity.getId());
        productProposalApprovalStatusEntity.setCreateTime(LocalDateTime.now());
        productProposalApprovalStatusEntity.setCreateUser(user.getName());
        productProposalApprovalStatusEntity.setStep(ProductProposalEnum.PRODUCT_PROPOSAL_SUBMIT);//1
        productProposalApprovalStatusEntity.setReason("产品提案更新");
        productProposalApprovalStatusEntity.setApproveStatus(ProductProposalApproveEnum.APPROVE);
        productProposalApprovalStatusEntity.setStatus(ProductProposalApproveEnum.APPROVE);
        productProposalApprovalStrategyContext.save(productProposalApprovalStatusEntity);

        if (productProposalEntity.getResetId() != null) {
            //把该条历史审核 置位为已处理 打上历史标签
            productProposalApprovalStrategyContext.dealTag(productProposalEntity.getResetId());
        }
        return res;
    }

    public ProductProposalDto detail(Long id) {
        ProductProposalEntity productProposalEntity = baseMapper.selectById(id);
        ProductProposalDto productProposalDto = new ProductProposalDto();
        if (null != productProposalEntity) {
            BeanUtils.copyProperties(productProposalEntity, productProposalDto);
            if (StringUtils.isNotEmpty(productProposalEntity.getProductProposalTemplateGroupJson())) {
                productProposalDto.setProductProposalTemplateGroupDtoList(GsonUtil.jsonToList(productProposalEntity.getProductProposalTemplateGroupJson(), ProductProposalTemplateGroupDto.class));
            }
            if (StringUtils.isNotEmpty(productProposalEntity.getMarketAnalysisAnnex())) {
                productProposalDto.setMarketAnalysisAnnex(GsonUtil.jsonToList(productProposalEntity.getMarketAnalysisAnnex(), UploadFileBo.class));
            }
            if (StringUtils.isNotEmpty(productProposalEntity.getProductProposalAnnex())) {
                productProposalDto.setProductProposalAnnex(GsonUtil.jsonToList(productProposalEntity.getProductProposalAnnex(), UploadFileBo.class));
            }
            if (StringUtils.isNotEmpty(productProposalEntity.getProductCalProfitCollectDtoJson())) {
                productProposalDto.setProductCalProfitCollectDtoList(GsonUtil.jsonToList(productProposalEntity.getProductCalProfitCollectDtoJson(), ProductCalProfitCollectDto.class));
            }
        }
        return productProposalDto;
    }

    public IPage<ProductProposalEntity> queryProductPage(PageForm<ProductProposalDto> productProposalDtoPageForm) {
        IPage<ProductProposalEntity> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(productProposalDtoPageForm.getCurrent(), productProposalDtoPageForm.getSize());
        ProductProposalDto productProposalDto = productProposalDtoPageForm.getParam();
        ProductProposalEntity productProposalEntity = new ProductProposalEntity();
        BeanUtils.copyProperties(productProposalDto, productProposalEntity);
        QueryWrapper wrapper = new QueryWrapper<>(productProposalEntity);
        wrapper.orderByDesc("id");
        if (StringUtils.isNotEmpty(productProposalEntity.getName())) {
            String name = productProposalEntity.getName();
            productProposalEntity.setName(null);
            wrapper.like("name", name);
        }
        if (StringUtils.isNotEmpty(productProposalEntity.getCreateUser())) {
            String user = productProposalEntity.getCreateUser();
            productProposalEntity.setCreateUser(null);
            wrapper.like("create_user", user);
        }
        return productProposalDao.selectPage(page, wrapper);
    }

    public IPage<ProductProposalEntity> queryPage(PageForm<ProductProposalDto> productProposalDtoPageForm) {
        //entity中保存着step
        //status 为 已办 待办
        Page<ProductProposalEntity> pageFormParams = new Page<>(productProposalDtoPageForm.getCurrent(), productProposalDtoPageForm.getSize());
        Page<ProductProposalEntity> productProposalEntityPageForm = productProposalDao.selectEntityPage(pageFormParams, productProposalDtoPageForm.getParam());
        IPage<ProductProposalEntity> productProposalEntityIPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        productProposalEntityIPage.setTotal(productProposalEntityPageForm.getTotal());
        productProposalEntityIPage.setRecords(productProposalEntityPageForm.getRecords());
        return productProposalEntityIPage;
    }

    public void revocation(Long id, User user) {
        ProductProposalEntity productProposalEntity = baseMapper.selectById(id);
        productProposalEntity.setCurrentStep(ProductProposalEnum.PRODUCT_PROPOSAL_SUBMIT);
        baseMapper.updateById(productProposalEntity);

        ProductProposalApprovalStatusEntity productProposalApprovalStatusEntity = new ProductProposalApprovalStatusEntity();
        productProposalApprovalStatusEntity.setProductProposalId(productProposalEntity.getId());
        productProposalApprovalStatusEntity.setCreateTime(LocalDateTime.now());
        productProposalApprovalStatusEntity.setCreateUser(user.getName());
        productProposalApprovalStatusEntity.setStep(ProductProposalEnum.PRODUCT_PROPOSAL_SUBMIT);//1
        productProposalApprovalStatusEntity.setReason("产品提案撤回");
        productProposalApprovalStatusEntity.setApproveStatus(ProductProposalApproveEnum.REJECT);
        productProposalApprovalStatusEntity.setStatus(ProductProposalApproveEnum.REJECT);
        productProposalApprovalStrategyContext.save(productProposalApprovalStatusEntity);
        productProposalApprovalStrategyContext.dealAllTagForProductProposal(id);
    }

    public void delete(Long id) {
        ProductProposalDto oldProductProposalDto = this.detail(id);
        List<UploadFileBo> uploadFileBoList = oldProductProposalDto.getMarketAnalysisAnnex();
        if (null != uploadFileBoList && uploadFileBoList.size() != 0) {
            uploadFileBoList.forEach(uploadFileBo -> ftpService.deleteFile(uploadFileBo.getFilePath()));
        }

        AdjustCalResultHistoryEntity adjustCalResultHistoryEntity = new AdjustCalResultHistoryEntity();
        adjustCalResultHistoryEntity.setProductProposalId(id);
        QueryWrapper adjustCalResultHistoryQueryWrapper = new QueryWrapper<>(adjustCalResultHistoryEntity);
        adjustCalResultHistoryDao.delete(adjustCalResultHistoryQueryWrapper);

        IrrCalResultEntity irrCalResultEntity = new IrrCalResultEntity();
        irrCalResultEntity.setProductProposalId(id);
        QueryWrapper irrCalResultQueryWrapper = new QueryWrapper<>(irrCalResultEntity);
        irrCalResultDao.delete(irrCalResultQueryWrapper);

        productProposalApprovalStrategyContext.deleteProductProposalApproveHistory(id);
        baseMapper.deleteById(id);
    }

    public Integer updateProductProposal(ProductProposalDto productProposalDto, User user) {
        ProductProposalEntity productProposalEntity = new ProductProposalEntity();
        BeanUtils.copyProperties(productProposalDto, productProposalEntity);
        productProposalEntity.setProductProposalTemplateGroupJson(GsonUtil.toJson(productProposalDto.getProductProposalTemplateGroupDtoList()));
        if (null != productProposalDto.getProductCalProfitCollectDtoList()) {
            productProposalEntity.setProductCalProfitCollectDtoJson(GsonUtil.toJson(productProposalDto.getProductCalProfitCollectDtoList()));
        }
        productProposalEntity.setUpdateTime(LocalDateTime.now());
        productProposalEntity.setUpdateUser(user.getName());
        Integer res = baseMapper.updateById(productProposalEntity);
        return res;
    }

    public void productProposalAnnexSave(ProductProposalDto productProposalDto, List<MultipartFile> addFileList, User user) {
        ProductProposalDto oldProductProposalDto = this.detail(productProposalDto.getId());

        ProductProposalEntity productProposalEntity = new ProductProposalEntity();
        BeanUtils.copyProperties(productProposalDto, productProposalEntity);
        if (oldProductProposalDto.getProductProposalAnnex() != null && !productProposalDto.getProductProposalAnnex().equals(oldProductProposalDto.getProductProposalAnnex())) {
            List<UploadFileBo> uploadFileBoListOld = oldProductProposalDto.getProductProposalAnnex();
            List<UploadFileBo> uploadFileBoList = productProposalDto.getProductProposalAnnex();
            if (uploadFileBoListOld != null && uploadFileBoList != null) {
                List<UploadFileBo> delList = uploadFileBoListOld.stream().filter(m -> !uploadFileBoList.stream().map(d -> d.getFileName()).collect(Collectors.toList()).contains(m.getFileName())).collect(Collectors.toList());
                delList.forEach(uploadFileBo -> ftpService.deleteFile(uploadFileBo.getFilePath()));
                List<UploadFileBo> remainList = uploadFileBoListOld.stream().filter(m -> uploadFileBoList.stream().map(d -> d.getFileName()).collect(Collectors.toList()).contains(m.getFileName())).collect(Collectors.toList());
                if (null != addFileList && !addFileList.isEmpty()) {
                    remainList.addAll(ftpService.uploadFileToFtp(replenishExcelDir, addFileList, user.getName()));
                }
                productProposalEntity.setProductProposalAnnex(GsonUtil.toJson(remainList));
            } else if (uploadFileBoListOld != null && uploadFileBoList == null) {
                uploadFileBoListOld.forEach(uploadFileBo -> ftpService.deleteFile(uploadFileBo.getFilePath()));
                productProposalEntity.setProductProposalAnnex(null);
            }
        } else if (oldProductProposalDto.getProductProposalAnnex() == null && productProposalDto.getProductProposalAnnex() != null) {
            productProposalEntity.setProductProposalAnnex(GsonUtil.toJson(ftpService.uploadFileToFtp(replenishExcelDir, addFileList, user.getName())));
        }
        productProposalEntity.setUpdateTime(LocalDateTime.now());
        productProposalEntity.setUpdateUser(user.getName());
        Integer res = baseMapper.updateById(productProposalEntity);
    }

    public ProductProposalEntity selectById(Long id) {
        ProductProposalEntity productProposalEntity = baseMapper.selectById(id);
        return productProposalEntity;
    }
}
