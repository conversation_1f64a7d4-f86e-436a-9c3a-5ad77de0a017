package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import com.gwmfc.dto.ProductCalEarlySquareParamSubDto;
import com.gwmfc.dto.ProductCalUnionLoanParamDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年10月16日 14:43
 */
@Data
@TableName(value = "product_proposal_template")
public class ProductProposalTemplateEntity {
    @TableId(type = IdType.AUTO)
    private Double id;

    @TableFieldMapping(value = "name", comment = "模板名称")
    private String name;

    @TableFieldMapping(value = "business_type", comment = "业务类型(0-本品  1-全品新车  2-二手车)")
    private Integer businessType;

    @TableFieldMapping(value = "self_defined_parameter", comment = "自定义参数")
    private String selfDefinedParameter;
    @TableFieldMapping(value = "brand", comment = "品牌")
    private String brand;
    @TableFieldMapping(value = "vehicle_type", comment = "车辆类型")
    private String vehicleType;
    @TableFieldMapping(value = "loan_amount", comment = "贷额")
    private Double loanAmount;
    @TableFieldMapping(value = "payment_days", comment = "贷期")
    private Double paymentDays;

    @TableFieldMapping(value = "irr_template_id", comment = "irr模板ID")
    private Double irrTemplateId;

    @TableFieldMapping(value = "customer_interest_rate", comment = "客户利率（%）")
    private Double customerInterestRate;
    @TableFieldMapping(value = "settled_rate", comment = "结算利率（%）")
    private Double settledRate;
    @TableFieldMapping(value = "repayment_mode", comment = "还款方式(后期改枚举)")// TODO: 2023/10/16  后期改枚举
    private Integer repaymentMode;
    @TableFieldMapping(value = "repayment_mode_explain", comment = "还款方式说明")
    private String repaymentModeExplain;
    @TableFieldMapping(value = "contracts_proportion", comment = "合同占比（%）")
    private Double contractsProportion;

    //风险
    @TableFieldMapping(value = "risk_loss_rate", comment = "风险损失率（%）")
    private Double riskLossRate;

    @TableFieldMapping(value = "base_commission_ratio", comment = "基础服务费（%）")
    private Double baseCommissionRatio;
    @TableFieldMapping(value = "ladder_bonus_ratio", comment = "阶梯奖金比例（%）")
    private Double ladderBonusRatio;
    @TableFieldMapping(value = "promotion_bonus_proportion", comment = "促销奖金比例（%）")
    private Double promotionBonusProportion;

    @TableFieldMapping(value = "personal_reward", comment = "个人奖励")
    private String personalReward;

    //政策
    @TableFieldMapping(value = "irr", comment = "IRR")
    private Double irr;
    @TableFieldMapping(value = "roa", comment = "ROA")
    private Double roa;
    @TableFieldMapping(value = "net_margin", comment = "净利润")
    private Double netMargin;
    @TableFieldMapping(value = "thousand_income", comment = "万元收益")
    private Double thousandIncome;
    @TableFieldMapping(value = "weighted_irr", comment = "加权IRR")
    private Double weightedIrr;

    @ApiModelProperty("销量预测")
    private String salesForecast;

    @TableFieldMapping(value = "changed", comment = "是否发生变化")
    private Boolean changed;

    @TableFieldMapping(value = "product_cal_repayment_method_param_dto", comment = "还款方式")
    private String productCalRepaymentMethodParamDto;

    @TableFieldMapping(value = "consider_early_square", comment = "是否考虑提前结清")
    private Integer considerEarlySquare;

    @TableFieldMapping(value = "is_union_loan", comment = "是否联合贷")
    private Integer isUnionLoan;

    @TableFieldMapping(value = "is_farmer_loan", comment = "是否农户贷")
    private Integer isFarmerLoan;

    @TableFieldMapping(value = "product_classification", comment ="默认产品分类")
    private String productClassification;

    @TableFieldMapping(value = "financing_type", comment ="默认融资类型")
    private String financingType;

    @TableFieldMapping(value = "early_square_probability_classification", comment = "提前结清概率分类")
    private String earlySquareProbabilityClassification;

    @TableFieldMapping(value = "agent_type", comment = "代理商类型")
    private String agentType;

    @TableFieldMapping(value = "track", comment = "赛道")
    private String track;

    @TableFieldMapping(value = "is_corp_to_corp", comment = "是否总对总")
    private Integer isCorpToCorp;

    @TableFieldMapping(value="product_cal_early_square_param_sub_dto",comment="提前结清附加")
    private String productCalEarlySquareParamSubDto;

    @TableFieldMapping(value="product_cal_union_loan_param_dto",comment="联合贷参数")
    private String productCalUnionLoanParamDto;

    /**
     * 提前结清 使用 边际利润
     */
    @TableFieldMapping(value="marginal_profit",comment="边际利润")
    private Double marginalProfit;

    /**
     * 联合贷结果信息中增加综合信息，我司全款信息等
     */
    @TableFieldMapping(value="integrative_irr",comment="综合Irr")
    private Double integrativeIrr;

    @TableFieldMapping(value="integrative_net_profit_money",comment="综合净利润")
    private Double integrativeNetProfitMoney;

    @TableFieldMapping(value="integrative_marginal_profit",comment="综合边际利润")
    private Double integrativeMarginalProfit;

    @TableFieldMapping(value="integrative_roa",comment="综合ROA")
    private Double integrativeRoa;

    @TableFieldMapping(value="our_full_loan_irr",comment="我司全额贷款Irr")
    private Double ourFullLoanIrr;

    @TableFieldMapping(value="our_full_loan_net_profit_money",comment="我司全额贷款净利润")
    private Double ourFullLoanNetProfitMoney;

    @TableFieldMapping(value="our_full_loan_marginal_profit",comment="我司全额贷款边际利润")
    private Double ourFullLoanMarginalProfit;

    @TableFieldMapping(value="our_full_loan_roa",comment="我司全额贷款Roa")
    private Double ourFullLoanRoa;

    @TableFieldMapping(value = "create_time", comment = "创建日期")
    private LocalDateTime createTime;
    @TableFieldMapping(value = "create_user", comment = "创建人")
    private String createUser;
}
