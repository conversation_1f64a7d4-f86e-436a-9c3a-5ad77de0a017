package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductActuarialRewardTypeEntity
 * @Description TODO
 * @Date 2025/4/21 14:33
 */
@Data
@TableName("product_actuarial_bp_reward_type")
public class ProductActuarialBpRewardTypeEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "name")
    @ApiModelProperty("奖励名称")
    private String name;

    @TableField(value = "reward_descp")
    @ApiModelProperty("奖励描述")
    private String rewardDescp;

    @TableField(value = "status")
    @ApiModelProperty("状态")
    private Integer status;

    @TableField(value = "effective_time")
    @ApiModelProperty("生效时间")
    private Date effectiveTime;

    @TableField(value = "end_time")
    @ApiModelProperty("结束时间")
    private Date endTime;

    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField(value = "create_user")
    @ApiModelProperty("创建人")
    private String createUser;

    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @TableField(value = "update_user")
    @ApiModelProperty("更新人")
    private String updateUser;
}
