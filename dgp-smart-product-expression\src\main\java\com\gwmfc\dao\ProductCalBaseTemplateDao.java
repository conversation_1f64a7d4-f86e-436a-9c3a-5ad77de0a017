package com.gwmfc.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gwmfc.dto.ProductCalBasicInfoDto;
import com.gwmfc.entity.ProductCalBaseTemplateEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalOwnbrandAcpiTemplateDao
 * @Description TODO
 * @Date 2023/9/20 9:45
 */
@Mapper
public interface ProductCalBaseTemplateDao extends BaseMapper<ProductCalBaseTemplateEntity> {
    List<ProductCalBaseTemplateEntity> getProductCalBaseTemplateEntityList(ProductCalBasicInfoDto productCalBasicInfoDto);
}
