package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023年08月23日 15:01
 */
@Data
@TableName("afc")
public class AfcEntity extends BaseEntity {
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期")  @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("企业简称")
    @TableFieldMapping(value = "enterprise_short", comment = "企业简称", queryItem = true)
    private String enterpriseShort;

    @ExcelProperty("当月合同量")
    @TableFieldMapping(value = "retail_cur_contract_cnt_m", comment = "当月合同量")
    private long retailCurContractCntM;

    @ExcelProperty("新能源汽车贷款合同量")
    @TableFieldMapping(value = "retail_nev_contract_cnt", comment = "新能源汽车贷款合同量")
    private long retailnevContractCnt;

    @ExcelProperty("合同余额个数")
    @TableFieldMapping(value = "retail_stock_contract_cnt", comment = "合同余额个数")
    private long retailStockContractCnt;

    @ExcelProperty("合同剩余金额（亿元）")
    @TableFieldMapping(value = "retail_outstanding_principal_amt", comment = "合同剩余金额（亿元）")
    private double retailOutstandingPrincipalAmt;

    @ExcelProperty("渗透率－全国")
    @TableFieldMapping(value = "retail_permeate_rate", comment = "渗透率－全国")
    private double retailPermeateRate;

    @ExcelProperty("审批通过率")
    @TableFieldMapping(value = "retail_approval_rate", comment = "审批通过率")
    private double retailApprovalRate;

    @ExcelProperty("30天以上至120天的逾期率(保留两位小数)")
    @TableFieldMapping(value = "retail_od_rate", comment = "30天以上至120天的逾期率(保留两位小数)")
    private double retailOdRate;

    @ExcelProperty("城市数")
    @TableFieldMapping(value = "retail_city_cnt", comment = "城市数")
    private long retailCityCnt;

    @ExcelProperty("经销商数")
    @TableFieldMapping(value = "retail_dealer_cnt", comment = "经销商数")
    private long retailDealerCnt;

    @ExcelProperty("平均首付比例")
    @TableFieldMapping(value = "retail_avg_downpay_pct", comment = "平均首付比例")
    private double retailAvgDownpayPct;

    @ExcelProperty("其中：新能源汽车贷款合同平均首付比例")
    @TableFieldMapping(value = "retail_nev_avg_downpay_pct", comment = "其中：新能源汽车贷款合同平均首付比例")
    private double retailnevAvgDownpayPct;

    @ExcelProperty("平均贷款期限")
    @TableFieldMapping(value = "retail_avg_trm", comment = "平均贷款期限")
    private double retailAvgTrm;

    @ExcelProperty("其中：新能源汽车贷款合同平均贷款期限")
    @TableFieldMapping(value = "retail_nev_avg_trm", comment = "其中：新能源汽车贷款合同平均贷款期限")
    private double retailnevAvgTrm;

    @ExcelProperty("平均贷款金额（千）")
    @TableFieldMapping(value = "retail_avg_loan_amt", comment = "平均贷款金额（千）")
    private double retailAvgLoanAmt;

    @ExcelProperty("批发类贷款余额（亿元）")
    @TableFieldMapping(value = "whsle_outstanding_principal_amt", comment = "批发类贷款余额（亿元）")
    private double whsleOutstandingPrincipalAmt;

    @ExcelProperty("批发类城市数")
    @TableFieldMapping(value = "whsle_city_cnt", comment = "批发类城市数")
    private long whsleCityCnt;

    @ExcelProperty("批发类经销商数")
    @TableFieldMapping(value = "whsle_dealer_cnt", comment = "批发类经销商数")
    private long whsleDealerCnt;

    @ExcelProperty("批发类渗透率")
    @TableFieldMapping(value = "whsle_permeate_rate", comment = "批发类渗透率")
    private double whslePermeateRate;

    @ExcelProperty("贷款总余额（亿元）")
    @TableFieldMapping(value = "outstanding_principal_amt", comment = "贷款总余额（亿元）")
    private double outstandingPrincipalAmt;
}
