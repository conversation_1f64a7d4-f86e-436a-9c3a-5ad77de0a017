package com.gwmfc.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.gwmfc.entity.MenuDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname MenuDao
 * @Date 2021/8/5 11:41
 */
@Mapper
public interface MenuDao extends BaseMapper<MenuDO> {

    /**
     * listMenuByUserId
     *
     * @param userId 入参
     * @return List
     */
    List<MenuDO> listMenuByUserId(@Param("userId") Long userId);

    /**
     * deleteRoleMenuByMenuId
     *
     * @param menuId 入参
     */
    void deleteRoleMenuByMenuId(@Param("menuId") Long menuId);

    /**
     * listMenuIdByRoleId
     *
     * @param roleId 入参
     * @return List
     */
    List<Long> listMenuIdByRoleId(Long roleId);
}
