package com.gwmfc.exception;

import com.auth0.jwt.exceptions.TokenExpiredException;
import com.gwmfc.util.Result;
import com.gwmfc.util.StatusCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ValidationException;
import java.sql.SQLIntegrityConstraintViolationException;

/**
 * <AUTHOR> jay
 * @Classname GlobalExceptionHandler
 * @Description Cheetah接口
 * @Date 2021/4/12
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    private static final String EXCEPTION_LOG = "Exception: {}";

    /**
     * 请求参数校验失败全局处理类
     * @param exception
     * @return
     */
    @ExceptionHandler(value = {
            BindException.class,
            MethodArgumentNotValidException.class,
            ValidationException.class,
            IllegalStateException.class,
            IllegalArgumentException.class
    })
    public Result validExceptionHandler(Exception exception) {
        String message = exception.getMessage();
        if (exception instanceof BindException) {
            FieldError fieldError = ((BindException) exception).getBindingResult().getFieldError();
            if (fieldError != null) {
                message = fieldError.getDefaultMessage();
            }
        }
        if (StringUtils.isEmpty(message)) {
            log.error("[error] [advice valid]", exception);
        } else {
            log.error("[error] [advice valid] [{}]", message);
        }
        return Result.error(message);
    }

    /**
     * 数据库重复key异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = {SQLIntegrityConstraintViolationException.class})
    public Result exceptionHandler(HttpServletRequest req, SQLIntegrityConstraintViolationException e) {
        log.error("duplicate key in db: {}", e);
        return Result.error(StatusCodeEnum.DUPLICATE_KEY.getCode(), StatusCodeEnum.DUPLICATE_KEY.getDesc());
    }

    /**
     * 处理其他异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    public Result exceptionHandler(HttpServletRequest req, Exception e) {
        log.error(EXCEPTION_LOG, e);
        return Result.error(StatusCodeEnum.FAIL.getCode(), StatusCodeEnum.FAIL.getDesc());
    }

    @ExceptionHandler(value = TokenExpiredException.class)
    public Result exceptionHandler(HttpServletRequest req, TokenExpiredException e) {
        log.error(EXCEPTION_LOG, e);
        return Result.error(StatusCodeEnum.TOKEN_EXPIR.getCode(), StatusCodeEnum.TOKEN_EXPIR.getDesc());
    }

    @ExceptionHandler(value = SystemRuntimeException.class)
    public Result exceptionHandler(SystemRuntimeException e) {
        log.error(e.getMsg(), e);
        return Result.error(e.getCode(),e.getMsg());
    }
}
