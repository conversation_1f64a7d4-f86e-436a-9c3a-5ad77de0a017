package com.gwmfc.util;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.AbstractVariadicFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.Finance;
import org.apache.poi.ss.formula.functions.FinanceLib;
import org.decampo.xirr.Transaction;
import org.decampo.xirr.Xirr;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.IntFunction;

@Slf4j
@Component
@RefreshScope
public class AviatorUtils {

    public static Integer irrCalTimes;

    public static Double irrGuess;

    @Value("${irr-cal-times}")
    public void setIrrCalTimes(Integer irrCalTimesInput) {
        irrCalTimes = irrCalTimesInput;
    }

    @Value("${irr-cal-guess}")
    public void setIrrGuess(Double irrGuessInput) {
        irrGuess = irrGuessInput;
    }

    static {
        /**
         * 注册excel 使用公式
         */
        AviatorEvaluator.addFunction(new AvgFunction());
        AviatorEvaluator.addFunction(new SumFunction());
        AviatorEvaluator.addFunction(new PmtFunction());
        AviatorEvaluator.addFunction(new PpmtFunction());
        AviatorEvaluator.addFunction(new IpmtFunction());
        AviatorEvaluator.addFunction(new PvFunction());
        AviatorEvaluator.addFunction(new NpvFunction());
        AviatorEvaluator.addFunction(new IfFunction());
        AviatorEvaluator.addFunction(new RoundFunction());
        AviatorEvaluator.addFunction(new IrrFunction());
        AviatorEvaluator.addFunction(new XirrFunction());
        AviatorEvaluator.addFunction(new AndFunction());
        AviatorEvaluator.addFunction(new OrFunction());
        AviatorEvaluator.addFunction(new PowFunction());
        AviatorEvaluator.addFunction(new EomonthFunction());
        AviatorEvaluator.addFunction(new EdateFunction());
        AviatorEvaluator.addFunction(new DayFunction());
        AviatorEvaluator.addFunction(new DivFunction());
    }

    /**
     * 自定义 AVERAGE 函数
     */
    static class AvgFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env, AviatorObject... args) {
            Double sum = 0D;
            Integer count = 0;
            for(AviatorObject arg:args){
                Number number = FunctionUtils.getNumberValue(arg, env);
                sum+=number.doubleValue();
                count++;
            }
            if (count != 0) {
                return new AviatorDouble(sum / count);
            }
            return null;
        }

        @Override
        public String getName() {
            return "AVERAGE";
        }
    }

    /**
     * 自定义 SUM 函数
     */
    static class SumFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env,  AviatorObject... args) {
            Double sum = 0D;
            for(AviatorObject arg:args){
                Number number = FunctionUtils.getNumberValue(arg, env);
                sum+=number.doubleValue();
            }
            return new AviatorDouble(sum);
        }

        @Override
        public String getName() {
            return "SUM";
        }
    }

    static class PmtFunction extends AbstractVariadicFunction {
        @Override
        public AviatorObject variadicCall(Map<String, Object> env,  AviatorObject... args) {
            Double pmt = 0D;
            List<Double> list = new ArrayList<>();
            for(AviatorObject arg:args){
                Number number = FunctionUtils.getNumberValue(arg, env);
                list.add(number.doubleValue());
            }
            double rate = list.get(0);
            int nper = list.get(1).intValue();
            double pv = list.get(2);
            double fv = list.size()>3?list.get(3):0d;
            int type = list.size()>4?list.get(4).intValue():0;
            boolean typeBoolean = type == 0?false:true;
            pmt = FinanceLib.pmt(rate,nper,pv,fv,typeBoolean);
            return new AviatorDouble(pmt);
        }
        @Override
        public String getName() {
            return "PMT";
        }
    }

    static class IpmtFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env,  AviatorObject... args) {
            Double ipmt = 0D;
            List<Double> list = new ArrayList<>();
            for(AviatorObject arg:args){
                Number number = FunctionUtils.getNumberValue(arg, env);
                list.add(number.doubleValue());
            }
            double rate = list.get(0);
            int per = list.get(1).intValue();
            int nper = list.get(2).intValue();
            double pv = list.get(3);
            double fv = list.size()>4?list.get(4):0d;
            int type = list.size()>5?list.get(5).intValue():0;
            if(rate != 0){
                ipmt = Finance.ipmt(rate,per,nper,pv,fv,type);
            }else {
                ipmtFunction(rate,per,nper,pv,fv,type);
            }
            return new AviatorDouble(ipmt);
        }
        @Override
        public String getName() {
            return "IPMT";
        }
    }

    public static double ipmtFunction(double rate, int per, int nper, double pv, double fv, int type){
        boolean typeBoolean = type == 0?false:true;
        double ipmt = FinanceLib.fv(rate, per - 1, FinanceLib.pmt(rate, nper, pv, fv, typeBoolean), pv, typeBoolean) * rate;
        if (type == 1) {
            ipmt /= 1.0D + rate;
        }
        return ipmt;
    }


    static class PpmtFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env,  AviatorObject... args) {
            Double ppmt = 0D;
            List<Double> list = new ArrayList<>();
            for(AviatorObject arg:args){
                Number number = FunctionUtils.getNumberValue(arg, env);
                list.add(number.doubleValue());
            }
            double rate = list.get(0);
            int per = list.get(1).intValue();
            int nper = list.get(2).intValue();
            double pv = list.get(3);
            double fv = list.size()>4?list.get(4):0d;
            int type = list.size()>5?list.get(5).intValue():0;
            if(rate != 0){
                ppmt = Finance.ppmt(rate,per,nper,pv,fv,type);
            }else{
                boolean typeBoolean = type == 0?false:true;
                ppmt = FinanceLib.pmt(rate, nper, pv, fv, typeBoolean) - ipmtFunction(rate, per, nper, pv, fv, type);
            }
            return new AviatorDouble(ppmt);
        }
        @Override
        public String getName() {
            return "PPMT";
        }
    }


    static class NpvFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env,  AviatorObject... args) {
            double npv = 0d;
            List<Object> list = new ArrayList<>();
            for(AviatorObject arg:args){
                Object object;
                if(arg instanceof AviatorDouble){
                    object = FunctionUtils.getNumberValue(arg, env);
                }else{
                    object = FunctionUtils.getJavaObject(arg, env);
                }
                list.add(object);
            }
            double rate = (double)list.get(0);
            double[] valuesDouble = (double[])list.get(1);
            npv = FinanceLib.npv(rate,valuesDouble);
            return new AviatorDouble(npv);
        }
        @Override
        public String getName() {
            return "NPV";
        }
    }

    static class PvFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env,  AviatorObject... args) {
            Double pv = 0D;
            List<Double> list = new ArrayList<>();
            for(AviatorObject arg:args){
                Number number = FunctionUtils.getNumberValue(arg, env);
                list.add(number.doubleValue());
            }
            double rate = list.get(0);
            double nper = list.get(1);
            double pmt = list.get(2);
            double fv = list.size()>3?list.get(3):0d;
            boolean type = list.size()>4?list.get(4)==1?true:false:false;
            pv = FinanceLib.pv(rate,nper,pmt,fv,type);
            return new AviatorDouble(pv);
        }
        @Override
        public String getName() {
            return "PV";
        }
    }

    static class IrrFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env,  AviatorObject... args) {
            Double irr = 0D;
            List<Object> list = new ArrayList<>();
            for(AviatorObject arg:args){
                Object object = FunctionUtils.getJavaObject(arg, env);
                list.add(object);
            }
            double[] valuesDouble = (double[])list.get(0);
            double guess = list.size()>1?(Double)list.get(1):0;
            if(guess!=0){
                irr = irr(valuesDouble,guess);
            }else{
                irr = irr(valuesDouble);
            }
            return new AviatorDouble(irr);
        }
        @Override
        public String getName() {
            return "IRR";
        }
    }
    public static double irr(double[] income) {
        return irr(income, irrGuess);
    }

    public static Double irr(double[] values, double guess){
        double x0 = guess;

        for(int i = 0; i < irrCalTimes; ++i) {
            double factor = 1.0D + x0;
            int k = 0;
            double fValue = values[k];
            double fDerivative = 0.0D;
            double denominator = factor;

            while(true) {
                ++k;
                if (k >= values.length) {
                    double x1 = x0 - fValue / fDerivative;
                    if (Math.abs(x1 - x0) <= 1.0E-7D) {
                        return x1;
                    }

                    x0 = x1;
                    break;
                }

                double value = values[k];
                fValue += value / denominator;
                denominator *= factor;
                fDerivative -= (double)k * value / denominator;
            }
        }

        return 0.0D / 0.0;
    }
    static class XirrFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env,  AviatorObject... args) {
            Double xirr = 0D;
            List<Object> list = new ArrayList<>();
            for(AviatorObject arg:args){
                Object object = FunctionUtils.getJavaObject(arg, env);
                list.add(object);
            }
            double[] valuesDouble = (double[])list.get(0);
            String[] datesString = (String[])list.get(1);
            double guess = list.size()>2?(Double)list.get(2):0;

            if(valuesDouble.length!=datesString.length){
                return null;
            }else{
                List<Transaction> list1 = new ArrayList<>();
                for (int i=0; i<valuesDouble.length; i++){
                    Transaction transaction = new Transaction(valuesDouble[i],datesString[i]);
                    list1.add(transaction);
                }
                if(guess!=0){
                    xirr = Xirr.builder().withGuess(guess).withTransactions(list1).xirr();
                }else{
                    xirr = Xirr.builder().withTransactions(list1).xirr();
                }
            }
            return new AviatorDouble(xirr);
        }
        @Override
        public String getName() {
            return "XIRR";
        }
    }


    static class IfFunction extends AbstractFunction {

        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2, AviatorObject arg3) {
            Boolean ifResult = FunctionUtils.getBooleanValue(arg1, env);
            Number ifTrue = FunctionUtils.getNumberValue(arg2, env);
            Number ifFalse = FunctionUtils.getNumberValue(arg3, env);
            if (ifResult) {
                return new AviatorDouble(ifTrue.doubleValue());
            } else {
                return new AviatorDouble(ifFalse.doubleValue());
            }
        }

        @Override
        public String getName() {
            return "IF";
        }
    }

    static class AndFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env,  AviatorObject... args) {
            for(AviatorObject arg:args){
                Boolean andResult = FunctionUtils.getBooleanValue(arg, env);
                if(!andResult){
                    return AviatorBoolean.FALSE;
                }
            }
            return AviatorBoolean.TRUE;
        }

        @Override
        public String getName() {
            return "AND";
        }
    }

    static class OrFunction extends AbstractVariadicFunction {

        @Override
        public AviatorObject variadicCall(Map<String, Object> env,  AviatorObject... args) {
            for(AviatorObject arg:args){
                Boolean andResult = FunctionUtils.getBooleanValue(arg, env);
                if(andResult){
                    return AviatorBoolean.TRUE;
                }
            }
            return AviatorBoolean.FALSE;
        }

        @Override
        public String getName() {
            return "OR";
        }
    }

    static class DivFunction extends AbstractFunction {
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            double div1  = FunctionUtils.getNumberValue(arg1, env).doubleValue();
            double div2 = FunctionUtils.getNumberValue(arg2, env).doubleValue();
            BigDecimal divBig1 = new BigDecimal(div1);
            BigDecimal divBig2 = new BigDecimal(div2);
            return new AviatorDouble(divBig1.divide(divBig2, RoundingMode.DOWN).doubleValue());
        }
        @Override
        public String getName() {
            return "DIV";
        }
    }

    static class PowFunction extends AbstractFunction {
        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            Number a  = FunctionUtils.getNumberValue(arg1, env);
            Number b = FunctionUtils.getNumberValue(arg2, env);
            double pow = Math.pow(a.doubleValue(), b.doubleValue());
            return new AviatorDouble(pow);
        }

        @Override
        public String getName() {
            return "POW";
        }
    }

    static class RoundFunction extends AbstractFunction{
        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            Number value  = FunctionUtils.getNumberValue(arg1, env);
            Number res = FunctionUtils.getNumberValue(arg2, env);
            value.doubleValue();
            res.intValue();
            BigDecimal bigDecimalValue = BigDecimal.valueOf(value.doubleValue()).setScale(res.intValue(),BigDecimal.ROUND_HALF_UP);
            return new AviatorDouble(bigDecimalValue.doubleValue());
        }

        @Override
        public String getName() {
            return "ROUND";
        }
    }

    static class EomonthFunction extends AbstractFunction{
        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            /**
             * yyyy-MM-dd
             */
            String startDate = FunctionUtils.getStringValue(arg1,env);
            Number months = FunctionUtils.getNumberValue(arg2,env);

//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate date = LocalDate.parse(startDate);
            LocalDate dateMonth = date.plusMonths(months.intValue());
            String dateMonthLastDay = dateMonth.withDayOfMonth(dateMonth.lengthOfMonth()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return new AviatorString(dateMonthLastDay);
        }

        @Override
        public String getName() {
            return "EOMONTH";
        }
    }

    static class EdateFunction extends AbstractFunction{
        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            String startDate = FunctionUtils.getStringValue(arg1,env);
            Number months = FunctionUtils.getNumberValue(arg2,env);

//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate date = LocalDate.parse(startDate);
            String dateMonth = date.plusMonths(months.intValue()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return new AviatorString(dateMonth);
        }

        @Override
        public String getName() {
            return "EDATE";
        }
    }

    static class DayFunction extends AbstractFunction{
        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
            String startDate = FunctionUtils.getStringValue(arg1,env);
            LocalDate date = LocalDate.parse(startDate);
            int dayOfMonth = date.getDayOfMonth();

            return new AviatorDouble(dayOfMonth);
        }

        @Override
        public String getName() {
            return "DAY";
        }
    }

    public static Object calculateValue(Map<String, Object> map,String exp){
        Expression compiledExp = AviatorEvaluator.compile(exp,true);
        return compiledExp.execute(map);
    }


    public static void main(String[] args) {
        System.out.println();
        long start = System.currentTimeMillis();
//        String exp = "IPMT(0,1,24,50000)";
//        String exp = "-NPV(value1,value2)";
//        String exp = "IPMT(0.072/12,1,34,100000)";
//        String exp = "PPMT(0.072/12,1,34,100000)";
//        String exp = "IRR(values)";
//        double[] values = {-137135.03,-11075.66,3281.98,3283.32,3284.67,3286.04,3287.43,3288.84,3290.27,3291.72,3293.19,3294.69,3296.20,3297.73,3299.29,3300.87,3302.47,3304.09,3305.74,3307.41,3309.10,3310.82,3312.56,3314.32,3316.11,3317.93,3319.77,3321.64,3323.54,3325.46,3327.41,3329.38,3331.39,3333.42,3335.48,3337.57,3339.69,3341.84,3344.02,3346.23,3348.47,3350.75,3353.05,3355.39,3357.76,3360.17,3362.61,3365.08,3367.59,3370.14,3372.72,3375.34,3377.99,3380.68,3383.41,3386.18,3388.99,3391.84,3394.72,3397.65,3400.62};
//        String exp = "PV(0.1698/12,24,PMT(0.1698/12,24,170000))";
//        String exp = "IRR(values)*12";
//        String a = "(78962-78962*0.3-78962*0.4/math.pow((1+0.0599/12),12)-78962*0.3/math.pow((1+0.0599/12),24))/((1/(1+0.0599/12)-1/math.pow((1+0.0599/12),24)/(1+0.0599/12))/(1-1/(1+0.0599/12))-(1/math.pow((1+0.0599/12),12)+1/math.pow((1+0.0599/12),24)))";
//        String exp = "78962-78962*0.3-(PV(0.0899/12,11,-(78962-78962*0.3-78962*0.4/math.pow((1+0.0599/12),12)-78962*0.3/math.pow((1+0.0599/12),24))/((1/(1+0.0599/12)-1/math.pow((1+0.0599/12),24)/(1+0.0599/12))/(1-1/(1+0.0599/12))-(1/math.pow((1+0.0599/12),12)+1/math.pow((1+0.0599/12),24))))+PV(0.0899/12,11,-(78962-78962*0.3-78962*0.4/math.pow((1+0.0599/12),12)-78962*0.3/math.pow((1+0.0599/12),24))/((1/(1+0.0599/12)-1/math.pow((1+0.0599/12),24)/(1+0.0599/12))/(1-1/(1+0.0599/12))-(1/math.pow((1+0.0599/12),12)+1/math.pow((1+0.0599/12),24))))/math.pow((1+0.0899/12),12)+78962*0.4/math.pow((1+0.0899/12),12)+78962*0.3/math.pow((1+0.0899/12),24))";
//        double[] values1 = {-66042.67,366.87,2009.72,2010.32,2010.93,2011.54,2012.15,2012.76,2013.38,2014.00,2014.63,2015.26,2015.89,2016.53,2017.17,2017.81,2018.46,2019.11,2019.77,2020.43,2021.09,2021.76,2022.43,2023.10,2023.78,2024.46,2025.15,2025.84,2026.53,2027.23,2027.93,2028.64,2029.35,2030.06,2030.78,2031.50,2032.23};
//        String[] values2 = {"2017-10-15","2017-11-15","2017-12-15","2018-01-15","2018-02-15","2018-03-15","2018-04-15","2018-05-15","2018-06-15","2018-07-15","2018-08-15","2018-09-15","2018-10-15","2018-11-15","2018-12-15","2019-01-15","2019-02-15","2019-03-15","2019-04-15","2019-05-15","2019-06-15","2019-07-15","2019-08-15","2019-09-15","2019-10-15","2019-11-15","2019-12-15","2020-01-15","2020-02-15","2020-03-15","2020-04-15","2020-05-15","2020-06-15","2020-07-15","2020-08-15","2020-09-15","2020-10-15"};
//        double[] values2 = {179.06,178.32,177.59,176.86,176.13,175.39,174.66,173.93,173.20,172.46,171.73,171.00,64.40,63.67,62.94,62.20,61.47,60.74,60.01,59.27,58.54,57.81,57.08,56.34};
//        double[] values2 = {222.75,215.88,209.02,202.15,195.29,188.42,181.56,174.69,167.82,160.96,154.09,147.23,140.36,133.50,126.63,119.77,112.90,106.03,99.17,92.30,85.44,78.57,71.71,64.84,57.97,51.11,44.24,37.38,30.51,23.65,16.78,9.91,3.05};

        Map<String, Object> map = new HashMap<>();
//        map.put("values",values);
//        map.put("value1",0.08/100/12);
        double[] cashStream = {-169726.15,-18176.79,1492.03,1492.20,1492.38,1492.56,1492.75,1492.93,1493.11,1493.30,1493.49,1493.68,1493.87,1494.06,1494.25,1494.45,1494.64,1494.84,1495.04,1495.24,1495.44,1495.64,1495.85,1496.06,1496.26,1496.47,1496.69,1496.90,1497.11,1497.33,1497.55,1497.77,1497.99,1498.21,1498.43,1498.66,1498.89,1499.12,1499.35,1499.58,1499.82,1500.05,1500.29,1500.53,1500.77,1501.01,1501.26,1501.51,1501.76,1502.01,1502.26,1502.51,1502.77,1503.03,1503.29,1503.55,1503.82,1504.08,1504.35,1504.62,144866.53};
//        double[] cashStream = {-169726.15,-18163.35,1505.47,1505.65,1505.84,1506.03,1506.22,1506.41,1506.60,1506.79,1506.99,1507.19,1507.38,1507.58,1507.79,1507.99,1508.19,1508.40,1508.61,1508.81,1509.02,1509.24,1509.45,1509.67,1509.88,1510.10,1510.32,1510.54,1510.77,1510.99,1511.22,1511.45,1511.68,1511.91,1512.14,1512.38,1512.61,1512.85,1513.09,1513.34,1513.58,1513.83,1514.07,1514.32,1514.58,1514.83,1515.09,1515.34,1515.60,1515.86,1516.13,1516.39,1516.66,1516.93,1517.20,1517.47,1517.75,1518.03,1518.30,1518.59,143867.44};
        map.put("cashStream",cashStream);
//        map.put("value1",0.0405/12);
//        double[] value2 = {337.50,326.50,315.49,304.49,293.49,282.48,271.48,260.48,249.48,238.47,227.47,216.47,205.46,194.46,183.46,172.45,161.45,150.45,139.45,128.44,117.44,106.44,95.43,84.43,73.43,62.42,51.42,40.42,29.42,18.41,7.41};
//        map.put("value2",value2);
//        double[] values = {-100036.03,319.54,3221.55,3222.57,3223.58,3224.61,3225.64,3226.67,3227.71,3228.76,3229.82,3230.88,3231.95,3233.02,3234.10,3235.19,3236.28,3237.38,3238.49,3239.60,3240.72,3241.85,3242.98,3244.12,3245.27,3246.42,3247.58,3248.75,3249.92,3251.10,3252.29,3253.49,3254.69,3255.90,3257.12};
//        map.put("values",values);
//        String exp = "XIRR(values1,values2)";
//        Expression compiledExp = AviatorEvaluator.compile(exp,true);
//        double[] values1 = {-100036.03,319.54,3221.55,3222.57,3223.58,3224.61,3225.64,3226.67,3227.71,3228.76,3229.82,3230.88,3231.95,3233.02,3234.10,3235.19,3236.28,3237.38,3238.49,3239.60,3240.72,3241.85,3242.98,3244.12,3245.27,3246.42,3247.58,3248.75,3249.92,3251.10,3252.29,3253.49,3254.69,3255.90,3257.12};
//        String[] values2 = {"2017-10-15","2017-11-15","2017-12-15","2018-01-15","2018-02-15","2018-03-15","2018-04-15","2018-05-15","2018-06-15","2018-07-15","2018-08-15","2018-09-15","2018-10-15","2018-11-15","2018-12-15","2019-01-15","2019-02-15","2019-03-15","2019-04-15","2019-05-15","2019-06-15","2019-07-15","2019-08-15","2019-09-15","2019-10-15","2019-11-15","2019-12-15","2020-01-15","2020-02-15","2020-03-15","2020-04-15","2020-05-15","2020-06-15","2020-07-15","2020-08-15"};
//        map.put("values1",values1);
//        map.put("values2",values2);
//        String exp = "IF(14>34,0,3260.15)";
//        String exp = "AND(1==1,2==2,3==3)";
//        String exp = "33/3";
//        String exp = "IF((1)<(12.0)*(DIV(((1)-1),(12.0)))+1,0,(IF((1)<(36)+1,1/(POW((1+((3.0)/100/12)),(1))),0))*(1-POW(((3.0)/100),(DIV(((1)-1),(12.0))))))";
//        String exp = "SUM(-169726.14513207547,-16109.23342439977,3494.6493420819374,3558.341367446445,3529.300336339342,3543.6607782971164,5403.774533793047,4274.804651514791,4140.395125804055,4125.449693231224,4078.573694543994,4407.3752301566465,4123.425255839562,3885.5016341079413,3918.1667481692275,3990.835646545026,3895.2029546813283,3779.8024586922593,3800.8768217027505,3795.0591813297888,3963.980132402704,3908.518540688686,3917.6373126624258,3960.282527620812,3796.582261395573,3622.7972639296254,3620.3115829560534,3568.1286363025183,3617.7147885562067,3472.7606039453076,3527.609265532991,3540.67237901154,3508.026961744852,3442.9782736596926,3433.422695817372,3368.9566068252357,3462.4423499549084,3362.944410383028,3347.6689123889328,3191.170510374144,3187.376179460129,3319.4392425746223,3148.391875538204,3076.775937321285,3023.5231329164817,2986.763358408455,3000.778887386283,3117.155107741986,2948.876687218458,2832.465132056091,2812.165912756015,2820.6121836537127,2759.7856124691402,2705.809759861198,2490.812577163228,2389.9440476159157,2233.5096881580116,2273.055073883554,2094.1680211055304,2002.7002528280304,1936.6244204830077)";
//        String exp = "((78000.0)*(POW((1+(8.99)),((36)/(12.0))))/{(1-(3.0))[(POW((1+(8.99)),(((36)/(12.0))/2)))-1]/(8.99)+(POW((1+(8.99)),(((36)/(12.0))/2-1)))*[(POW((1+(8.99)),(((36)/(12.0))/2)))-1]})/(POW((1+(8.99)/100/12),(1)))";
//        String exp = "SUM(-445.2000000000001,-491.1675274950296,-481.6604250339178,-471.98744422639544,-462.39491787346896,-452.7677418753195,-438.1925244909237,-426.61911138055757,-415.4132757196764,-404.25180052749687,-393.2192567144827,-381.3157692628569,-370.1708409606303,-359.6648751538032,-349.0804592511745,-338.3067404788036,-327.791822975869,-317.5882422576166,-307.33260974256035,-297.09655301015596,-286.4149485930348,-275.885282703579,-265.3356073473579,-254.67670567957143,-244.45791129132166,-234.70591281926383,-224.96456344067846,-215.36616697083696,-205.63978674959483,-196.30333655774578,-186.82482950071915,-177.31556426944542,-167.89723850870143,-158.6560865456544,-149.4443395938412,-140.40816533502195,-131.12706017837897,-122.11482062533567,-113.14716910251884,-104.60001701916254,-96.06674906194718,-87.18572587786582,-78.76390236737787,-70.5364594986417,-62.45445638723165,-54.47394446723755,-46.45972955044852,-38.13932981434488,-30.27050445571879,-22.715054541063125,-15.217041904139846,-7.699891558438641,-0.3479313289618295,-0.0,-0.0,-0.0,-0.0,-0.0,-0.0,-0.0,-0.0)";
//        String exp = "(14705.156004553177-12396.838172075919-(168000.0)*0.69/100)*0.75";
        String exp = "IRR(cashStream)*12";
        Expression compiledExp = AviatorEvaluator.compile(exp,true);
//        Boolean result = (Boolean) compiledExp.execute(map);

//        Long result = (Long) compiledExp.execute(map);
//        result = BigDecimal.valueOf(result).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//        Double result =  (Double)compiledExp.execute(map);
//        Double result =  (Double)compiledExp.execute();
//        System.out.println(result);
//        System.out.println((System.currentTimeMillis()-start));
//        System.out.println("pppp"+calculateCumpRinc(80000,7.5/12,34,LocalDate.of(2024,1,17),LocalDate.of(2024,6,17)));
        Double result = (Double)compiledExp.execute(map);
        System.out.println(result);

    }

}
