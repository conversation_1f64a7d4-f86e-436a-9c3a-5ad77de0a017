package com.gwmfc.entity.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Classname UsedCarWeeklyTrading
 * @Description 二手车月交易量
 * @Date 2023/10/31
 * <AUTHOR> z<PERSON><PERSON>
 */
@Data
@ApiModel(value = "二手车月交易量")
@TableName("used_car_monthly_trading")
public class UsedCarMonthlyTradingEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("更新月份")
    private String updateMonth;

    @ApiModelProperty("数据日期")
    private String dataDate;

    @ApiModelProperty("类型（1：交易量，2.区域交易量，3.年限交易量，4.车型区域交易量，5.跨区域交易量）")
    private Integer type;

    @ApiModelProperty("区域")
    private String region;

    @ApiModelProperty("使用年限")
    private String usefulLife;

    @ApiModelProperty("车型类型")
    private String carType;

    @ApiModelProperty("车型")
    private String vehicleType;

    @ApiModelProperty("交易量")
    private String tradingVolume;

    @ApiModelProperty("交易量占比")
    private String tradingProportion;

    @ApiModelProperty("转籍率")
    private String transferRate;

    @ApiModelProperty("图片地址")
    private String imgUrl;

    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人")
    private String updateUser;
}
