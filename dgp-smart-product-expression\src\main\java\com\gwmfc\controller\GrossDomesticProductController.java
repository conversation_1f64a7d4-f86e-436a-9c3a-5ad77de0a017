package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.service.GrossDomesticProductService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024年03月07日 17:45
 */
@Api(tags = "国内生产总值")
@RestController
@RequestMapping("/gross/domestic/product")
public class GrossDomesticProductController {
    @Resource
    private GrossDomesticProductService grossDomesticProductService;

    /**
     * 国内生产总值数据获取
     *
     * @return
     */
    @ApiOperation("国内生产总值数据获取")
    @GetMapping("/catchGrossDomesticPriceIndexQuarter")
    public Result catchGrossDomesticPriceIndexQuarter(@RequestParam Integer frequency, @CurrentUser User user) {
        grossDomesticProductService.catchGrossDomesticProductQuarter(frequency, user.getUserName());
        return Result.ok();
    }

    /**
     * 国内生产总值数据获取
     *
     * @return
     */
    @ApiOperation("国民生产总值指数数据获取")
    @GetMapping("/catchGrossDomesticPriceIndexYear")
    public Result catchGrossDomesticPriceIndexYear(@RequestParam Integer frequency, @CurrentUser User user) {
        grossDomesticProductService.catchGrossDomesticPriceIndexYear(frequency, user.getUserName());
        return Result.ok();
    }
}
