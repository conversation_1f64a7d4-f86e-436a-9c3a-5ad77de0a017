package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname UserDto
 * @Date 2021/8/4 15:47
 */
@ApiModel(value = "UserDto")
@Data
public class UserDto {

    private Long userId;

    private String userName;

    private String password;

    private String name;

    private List<Long> roles;

    private Integer rank;

    private Integer status;

    private String email;

    private Long deptId;

    private String staffNo;
    /**
     * 手机号
     */
    private String mobile;
}
