package com.gwmfc.listener;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.common.base.CaseFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.util.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname IrrBatchCalTemplateListener
 * @Description TODO
 * @Date 2024/3/18 14:30
 */
@Slf4j
public class ProductCalImportListener extends AnalysisEventListener<Map<Integer, String>> {

    List list;
    Class classImport;

    public ProductCalImportListener(Class classImport,List list) {
        this.classImport = classImport;
        this.list = list;
    }

    //读取excel表头信息时执行
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        Field[] declaredFields = classImport.getDeclaredFields();
        if(headMap == null || headMap.entrySet().size() == 0){
            throw new RuntimeException("表头为空");
        }
        if(headMap.entrySet().size() != declaredFields.length){
            throw new RuntimeException("表头列数量不一致");
        }
        for (int i=0; i<declaredFields.length; i++){
            ExcelProperty annotation = declaredFields[i].getAnnotation(ExcelProperty.class);
            if(!headMap.get(i).equals(annotation.value()[0])){
                log.info("导入excel表头：{},模板excel表头：{}",headMap.get(i),annotation.value()[0]);
                throw new RuntimeException("表头不一致,导入excel表头第"+(i+1)+"个："+headMap.get(i)+",模板excel表头第"+(i+1)+"个："+annotation.value()[0]+"");
            }
        }
        log.info("导入excel表头信息："+headMap);
    }

    // 读取excel内容信息时执行
    // EasyExcel会会一行一行去读取excle内容，每解析excel文件中的一行数据，都会调用一次invoke方法
    @Override
    public void invoke(Map<Integer, String> map, AnalysisContext analysisContext) {
        try {
            Map<String,String> returnMap = new HashMap<>();
            Field[] declaredFields = classImport.getDeclaredFields();
            for (Map.Entry<Integer, String> entry : map.entrySet()) {
                if(entry.getKey()>=declaredFields.length){
                    break;
                }
                Field field = declaredFields[entry.getKey()];
                String value = entry.getValue() == null ? "0" : entry.getValue();
                String name = field.getName();
                /**
                 * 驼峰转下划线
                 */
                name = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, name);

                returnMap.put(name,value);
            }
            list.add(returnMap);
        }catch(Exception e){
            log.error("处理excel文件数据解析异常",e);
        }
    }

    //读取完成后执行
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
