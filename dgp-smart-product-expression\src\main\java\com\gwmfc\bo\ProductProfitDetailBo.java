package com.gwmfc.bo;

import com.gwmfc.dto.ProductCalProfitDetailDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年12月25日 10:43
 */
@Data
@ApiModel("预测算实体")
public class ProductProfitDetailBo {
    @ApiModelProperty("总月数")
    private Integer totalMonth;

    @ApiModelProperty("利润详情")
    private String startMonth;

    @ApiModelProperty("利润详情")
    private List<ProductCalProfitDetailDto> productCalProfitDetailDtoList;
}
