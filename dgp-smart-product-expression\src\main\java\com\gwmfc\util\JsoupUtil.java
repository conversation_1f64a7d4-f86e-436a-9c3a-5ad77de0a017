package com.gwmfc.util;

import com.gwmfc.config.ProxyConfig;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024年03月29日 17:26
 */
@Component
public class JsoupUtil {
    @Resource
    private ProxyConfig proxyConfig;

    public Document connect(String url) {
        try {
            if (proxyConfig.getEnabled()) {
                return Jsoup.connect(url).proxy(proxyConfig.getHost(), proxyConfig.getPort()).get();
            } else {
                return Jsoup.connect(url).get();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
