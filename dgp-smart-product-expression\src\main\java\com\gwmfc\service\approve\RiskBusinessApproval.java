package com.gwmfc.service.approve;

import com.gwmfc.constant.ProductProposalApproveEnum;
import com.gwmfc.constant.ProductProposalEnum;
import com.gwmfc.dao.ProductProposalApprovalDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductProposalApprovalStatusDto;
import com.gwmfc.dto.ProductProposalDto;
import com.gwmfc.dto.ProductProposalTemplateDto;
import com.gwmfc.dto.ProductProposalTemplateGroupDto;
import com.gwmfc.entity.ProductProposalApprovalStatusEntity;
import com.gwmfc.entity.ProductProposalEntity;
import com.gwmfc.service.ProductProposalService;
import com.gwmfc.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.gwmfc.constant.ProductProposalEnum.COST_ACCOUNTING;
import static com.gwmfc.constant.ProductProposalEnum.RISK_BUSINESS_CHECK;

/**
 * <AUTHOR>
 * @date 2024年04月07日 11:46
 */
@Slf4j
@Component("risk_business_check")
public class RiskBusinessApproval implements ProductProposalApprovalStrategy {
    @Resource
    private ProductProposalApprovalDao productProposalApprovalDao;
    @Resource
    private ProductProposalService productProposalService;

    public static <T> T mergeObjects(T obj1, T obj2) throws IllegalAccessException {
        Class<?> clazz = obj1.getClass();
        T mergedObj;
        try {
            mergedObj = (T) clazz.newInstance();
        } catch (InstantiationException e) {
            throw new RuntimeException("无法创建新对象： " + clazz.getName(), e);
        }

        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Object value1 = field.get(obj1);
            Object value2 = field.get(obj2);
            if (value1 != null) {
                field.set(mergedObj, value1);
            } else if (value2 != null) {
                field.set(mergedObj, value2);
            }
        }
        return mergedObj;
    }

    @Override
    public void doApproval(ProductProposalDto productProposalDto, ProductProposalApprovalStatusDto productProposalApprovalStatusDto, User user, List<MultipartFile> addFileList) {
        ProductProposalEntity oldProductProposalEntity = productProposalService.selectById(productProposalDto.getId());

        if (oldProductProposalEntity.getUpdateTime() == null || !oldProductProposalEntity.getUpdateTime().equals(productProposalDto.getUpdateTime())) {
            //防覆盖
            List<ProductProposalTemplateGroupDto> oldProductProposalTemplateGroupDtoList = GsonUtil.jsonToList(oldProductProposalEntity.getProductProposalTemplateGroupJson(), ProductProposalTemplateGroupDto.class);
            oldProductProposalTemplateGroupDtoList.forEach(oldProductProposalTemplateGroupDto -> {
                List<ProductProposalTemplateGroupDto> newCopyTmpGroupList = productProposalDto.getProductProposalTemplateGroupDtoList().stream().filter(productProposalTemplateGroupDto -> productProposalTemplateGroupDto.getUuid().equals(oldProductProposalTemplateGroupDto.getUuid())).collect(Collectors.toList());
                if (!newCopyTmpGroupList.isEmpty()) {
                    ProductProposalTemplateGroupDto newProductProposalTemplateGroupDto = newCopyTmpGroupList.get(0);
                    List<ProductProposalTemplateDto> oldProductProposalTemplateDtoList = oldProductProposalTemplateGroupDto.getProductProposalTemplateList();
                    List<ProductProposalTemplateDto> mergeProductProposalTemplateDtoList = new ArrayList<>();
                    oldProductProposalTemplateDtoList.forEach(oldProductProposalEntityTmp -> {
                        List<ProductProposalTemplateDto> copyTmpList = newProductProposalTemplateGroupDto.getProductProposalTemplateList().stream().filter(productProposalTemplateDto -> productProposalTemplateDto.getCurrentTimeSeqNo().equals(oldProductProposalEntityTmp.getCurrentTimeSeqNo())).collect(Collectors.toList());
                        if (copyTmpList.size() != 0) {
                            ProductProposalTemplateDto copyTmp = copyTmpList.get(0);
                            try {
                                mergeProductProposalTemplateDtoList.add(mergeObjects(oldProductProposalEntityTmp, copyTmp));
                            } catch (IllegalAccessException e) {
                                log.error("模板复制报错，{}", e);
                            }
                        }
                    });
                    oldProductProposalTemplateGroupDto.setProductProposalTemplateList(mergeProductProposalTemplateDtoList);
                }
            });
            productProposalDto.setProductProposalTemplateGroupDtoList(oldProductProposalTemplateGroupDtoList);
        }

        ProductProposalApprovalStatusEntity productProposalApprovalStatusEntity = new ProductProposalApprovalStatusEntity();
        BeanUtils.copyProperties(productProposalApprovalStatusDto, productProposalApprovalStatusEntity);
        productProposalApprovalStatusEntity.setProductProposalId(productProposalDto.getId());
        productProposalApprovalStatusEntity.setCreateTime(LocalDateTime.now());
        productProposalApprovalStatusEntity.setCreateUser(user.getName());
        productProposalApprovalStatusEntity.setApproveStatus(productProposalApprovalStatusEntity.getStatus());

        productProposalApprovalStatusEntity.setStep(productProposalDto.getCurrentStep());//2
        productProposalApprovalDao.insert(productProposalApprovalStatusEntity);

        if (productProposalApprovalStatusDto.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
            //校验另一方是否通过了
            if (productProposalApprovalStatusDto.getType().equals(ProductProposalApproveEnum.RISK_VERIFY)) {
                List<ProductProposalApprovalStatusEntity> historyProductProposalApprovalStatusEntityList = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), ProductProposalApproveEnum.BUSINESS_VERIFY, RISK_BUSINESS_CHECK, 1);
                if (!historyProductProposalApprovalStatusEntityList.isEmpty()) {
                    log.info("productProposalApproval校验另一方是否通过了RISK_VERIFY:{}", historyProductProposalApprovalStatusEntityList);
                    ProductProposalApprovalStatusEntity historyProductProposalApprovalStatusEntity = historyProductProposalApprovalStatusEntityList.get(0);
                    if (historyProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
                        productProposalDto.setCurrentStep(COST_ACCOUNTING);//3
                    }
                }
            } else if (productProposalApprovalStatusDto.getType().equals(ProductProposalApproveEnum.BUSINESS_VERIFY)) {
                List<ProductProposalApprovalStatusEntity> historyProductProposalApprovalStatusEntityList = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), ProductProposalApproveEnum.RISK_VERIFY, RISK_BUSINESS_CHECK, 1);
                if (!historyProductProposalApprovalStatusEntityList.isEmpty()) {
                    log.info("productProposalApproval BUSINESS_VERIFY:{}", historyProductProposalApprovalStatusEntityList);
                    ProductProposalApprovalStatusEntity historyProductProposalApprovalStatusEntity = historyProductProposalApprovalStatusEntityList.get(0);
                    if (historyProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
                        productProposalDto.setCurrentStep(COST_ACCOUNTING);
                    }
                }
            }
            List<ProductProposalApprovalStatusEntity> historyProductProposalApprovalStatusEntityList = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), 0, ProductProposalEnum.COST_ACCOUNTING, -1);
            if (!historyProductProposalApprovalStatusEntityList.isEmpty()) {
                log.info("riskFinalApproval:", historyProductProposalApprovalStatusEntityList.size());
                ProductProposalApprovalStatusEntity historyProductProposalApprovalStatusEntity = historyProductProposalApprovalStatusEntityList.get(0);
                if (historyProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.REJECT)) {
                    historyProductProposalApprovalStatusEntity.setDealTag(true);
                    productProposalApprovalDao.updateById(historyProductProposalApprovalStatusEntity);
                }
            }
        } else if (productProposalApprovalStatusDto.getStatus().equals(ProductProposalApproveEnum.REJECT)) {
            //校验另一方是否通过了
            if (productProposalApprovalStatusDto.getType().equals(ProductProposalApproveEnum.RISK_VERIFY)) {
                List<ProductProposalApprovalStatusEntity> historyProductProposalApprovalStatusEntityList = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), ProductProposalApproveEnum.BUSINESS_VERIFY, RISK_BUSINESS_CHECK, 1);
                if (!historyProductProposalApprovalStatusEntityList.isEmpty()) {
                    log.info("productProposalApproval RISK_VERIFY:{}", historyProductProposalApprovalStatusEntityList);
                    ProductProposalApprovalStatusEntity historyProductProposalApprovalStatusEntity = historyProductProposalApprovalStatusEntityList.get(0);
                    if (historyProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
                        historyProductProposalApprovalStatusEntity.setStatus(ProductProposalApproveEnum.REJECT);
                        historyProductProposalApprovalStatusEntity.setDealTag(true);
                        productProposalApprovalDao.updateById(historyProductProposalApprovalStatusEntity);

                    }
                } else {
                    ProductProposalApprovalStatusEntity productProposalApprovalStatusEntityOther = new ProductProposalApprovalStatusEntity();
                    productProposalApprovalStatusEntityOther.setType(ProductProposalApproveEnum.BUSINESS_VERIFY);
                    productProposalApprovalStatusEntityOther.setProductProposalId(productProposalDto.getId());
                    productProposalApprovalStatusEntityOther.setCreateTime(LocalDateTime.now());
                    productProposalApprovalStatusEntityOther.setCreateUser(user.getName());
                    productProposalApprovalStatusEntityOther.setStep(RISK_BUSINESS_CHECK);//2
                    productProposalApprovalStatusEntityOther.setReason("风险方退回，导致级联退回");
                    productProposalApprovalStatusEntityOther.setStatus(ProductProposalApproveEnum.REJECT);
                    productProposalApprovalStatusEntityOther.setDealTag(true);
                    productProposalApprovalDao.insert(productProposalApprovalStatusEntityOther);
                }
                productProposalDto.setCurrentStep(ProductProposalEnum.PRODUCT_PROPOSAL_SUBMIT);//1
            } else if (productProposalApprovalStatusDto.getType().equals(ProductProposalApproveEnum.BUSINESS_VERIFY)) {
                List<ProductProposalApprovalStatusEntity> historyProductProposalApprovalStatusEntityList = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), ProductProposalApproveEnum.RISK_VERIFY, RISK_BUSINESS_CHECK, 1);
                log.info("productProposalApproval BUSINESS_VERIFY:{}", historyProductProposalApprovalStatusEntityList);
                if (!historyProductProposalApprovalStatusEntityList.isEmpty()) {
                    ProductProposalApprovalStatusEntity historyProductProposalApprovalStatusEntity = historyProductProposalApprovalStatusEntityList.get(0);
                    if (historyProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
                        historyProductProposalApprovalStatusEntity.setStatus(ProductProposalApproveEnum.REJECT);
                        historyProductProposalApprovalStatusEntity.setDealTag(true);
                        productProposalApprovalDao.updateById(historyProductProposalApprovalStatusEntity);
                    }
                } else {
                    //为了不被待办列表查出而增加的数据 由于没有approvestatus字段 查询历史记录会过滤掉
                    ProductProposalApprovalStatusEntity productProposalApprovalStatusEntityOther = new ProductProposalApprovalStatusEntity();
                    productProposalApprovalStatusEntityOther.setType(ProductProposalApproveEnum.RISK_VERIFY);
                    productProposalApprovalStatusEntityOther.setProductProposalId(productProposalDto.getId());
                    productProposalApprovalStatusEntityOther.setCreateTime(LocalDateTime.now());
                    productProposalApprovalStatusEntityOther.setCreateUser(user.getName());
                    productProposalApprovalStatusEntityOther.setStep(RISK_BUSINESS_CHECK);//2
                    productProposalApprovalStatusEntityOther.setReason("商务方退回，导致级联退回");
                    productProposalApprovalStatusEntityOther.setStatus(ProductProposalApproveEnum.REJECT);
                    productProposalApprovalStatusEntityOther.setDealTag(true);
                    productProposalApprovalDao.insert(productProposalApprovalStatusEntityOther);
                }
                productProposalDto.setCurrentStep(ProductProposalEnum.PRODUCT_PROPOSAL_SUBMIT);//1
            }
        }
        productProposalDto.setResetId(productProposalApprovalStatusEntity.getId());
        productProposalService.updateProductProposal(productProposalDto, user);
    }
}

