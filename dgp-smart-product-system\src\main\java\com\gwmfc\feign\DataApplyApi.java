package com.gwmfc.feign;


import com.gwmfc.dto.DepartmentNode;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = "data-apply-service")
public interface DataApplyApi {

    /**
     * 获取公司组织架构
     * @param
     * @return Result
     */
    @GetMapping("/dingding/findDepartmentsAndStaffsInfo")
    List<DepartmentNode> findDepartmentsAndStaffsInfo();

}
