package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductBusinessLine
 * @Description 业务条线
 * @Date 2024/8/12 9:16
 */
@Data
@TableName("product_business_line")
public class ProductBusinessLineEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "buss_type", comment = "业务类型")
    private String bussType;

    @TableFieldMapping(value = "track", comment = "赛道")
    private String track;

    @TableFieldMapping(value = "subdivision", comment = "细分领域")
    private String subdivision;

    @TableFieldMapping(value = "description", comment = "描述")
    private String description;

    @TableFieldMapping(value = "state", comment = "状态")
    private Integer state;

    @TableFieldMapping(value = "effective_date", comment = "生效开始日期")
    private String effectiveDate;

    @TableFieldMapping(value = "expiry_date", comment = "有效截止日期")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String expiryDate;

    @TableFieldMapping(value = "create_user", comment = "创建人")
    private String createUser;

    @TableFieldMapping(value = "update_user", comment = "更新人")
    private String updateUser;

    @TableFieldMapping(value = "create_time", comment = "创建时间")
    private Date createTime;

    @TableFieldMapping(value = "update_time", comment = "更新时间")
    private Date updateTime;
}
