package com.gwmfc;

import com.gwmfc.domain.User;
import com.gwmfc.service.ExpressionService;
import com.gwmfc.service.GlobalFormBusinessService;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年07月31日 13:36
 */
@SpringBootTest
public class excelTest {
    @Resource
    private ExpressionService expressionService;
    @Resource
    private GlobalFormBusinessService globalFormBusinessService;

    @Test
    void importExcel() throws IOException {
        //生成File文件
        File file = new File("C:\\Users\\<USER>\\Desktop\\smart\\欧拉融租及小蜜蜂数据源\\小蜜蜂平台-修改合同明细-6月(1).xlsx");

        //File文件转MultipartFile
        FileInputStream input = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "text/plain", IOUtils.toByteArray(input));
        expressionService.importData(multipartFile, 11, new User());
    }

    @Test
    void importExcel1() throws IOException {
        //生成File文件
        File file = new File("C:\\Users\\<USER>\\Desktop\\smart\\欧拉融租及小蜜蜂数据源\\小蜜蜂平台修改后申请明细-7月(1).xlsx");

        //File文件转MultipartFile
        FileInputStream input = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "text/plain", IOUtils.toByteArray(input));
        expressionService.importData(multipartFile, 10, new User());
    }

    @Test
    void importExcel2() throws IOException {
        //生成File文件
        File file = new File("C:\\Users\\<USER>\\Desktop\\smart\\欧拉融租及小蜜蜂申请数据\\欧拉回租业务申请表(滨银)数据-7月.xlsx");

        //File文件转MultipartFile
        FileInputStream input = new FileInputStream(file);
        MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "text/plain", IOUtils.toByteArray(input));
        expressionService.importData(multipartFile, 2, new User());
    }

    @Test
    void deleteBatch() {
        List list = new ArrayList<>();
        list.add(7);
        list.add(8);
        globalFormBusinessService.deleteBatch("ola_lease_contract",list);
    }
}
