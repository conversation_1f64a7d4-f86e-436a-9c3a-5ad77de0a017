package com.gwmfc.service.approve;

import com.gwmfc.constant.ProductProposalApproveEnum;
import com.gwmfc.constant.ProductProposalEnum;
import com.gwmfc.dao.IrrCalResultDao;
import com.gwmfc.dao.ProductProposalApprovalDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductProposalApprovalStatusDto;
import com.gwmfc.dto.ProductProposalDto;
import com.gwmfc.dto.ProductProposalTemplateDto;
import com.gwmfc.entity.IrrCalResultEntity;
import com.gwmfc.entity.ProductProposalApprovalStatusEntity;
import com.gwmfc.service.ProductProposalService;
import com.gwmfc.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月07日 15:23
 */
@Slf4j
@Component("finanical_product_proposal_passed")
public class FinanicalFinalApproval implements ProductProposalApprovalStrategy {
    @Resource
    private ProductProposalApprovalDao productProposalApprovalDao;
    @Resource
    private ProductProposalService productProposalService;
    @Resource
    private IrrCalResultDao irrCalResultDao;

    @Override
    public void doApproval(ProductProposalDto productProposalDto, ProductProposalApprovalStatusDto productProposalApprovalStatusDto, User user, List<MultipartFile> addFileList) {
        ProductProposalApprovalStatusEntity productProposalApprovalStatusEntity = new ProductProposalApprovalStatusEntity();
        BeanUtils.copyProperties(productProposalApprovalStatusDto, productProposalApprovalStatusEntity);
        productProposalApprovalStatusEntity.setProductProposalId(productProposalDto.getId());
        productProposalApprovalStatusEntity.setCreateTime(LocalDateTime.now());
        productProposalApprovalStatusEntity.setCreateUser(user.getName());
        productProposalApprovalStatusEntity.setStep(ProductProposalEnum.FINANICAL_PRODUCT_PROPOSAL_PASSED);//5

        productProposalApprovalStatusEntity.setApproveStatus(productProposalApprovalStatusEntity.getStatus());

        productProposalApprovalDao.insert(productProposalApprovalStatusEntity);

        if (productProposalApprovalStatusDto.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
            productProposalDto.setCurrentStep(ProductProposalEnum.PRODUCT_PROPOSAL_PASSED);//6
            //记录IRR测算记录
            if (!productProposalDto.getProductProposalTemplateGroupDtoList().isEmpty()) {
                ProductProposalApprovalStatusEntity latestCostAccountApprove = productProposalApprovalDao.selectLatestCostAccountApprove(productProposalDto.getId());
                productProposalDto.getProductProposalTemplateGroupDtoList().forEach(productProposalTemplateGroupDto -> {
                    if (!productProposalTemplateGroupDto.getProductProposalTemplateList().isEmpty()) {
                        List<ProductProposalTemplateDto> productProposalTemplateList = productProposalTemplateGroupDto.getProductProposalTemplateList();
                        productProposalTemplateList.forEach(productProposalTemplateDto -> {
                            IrrCalResultEntity irrCalResultEntity = new IrrCalResultEntity();
                            BeanUtils.copyProperties(productProposalTemplateDto, irrCalResultEntity);
                            irrCalResultEntity.setProductProposalGroupUuid(productProposalTemplateGroupDto.getUuid());
                            irrCalResultEntity.setBusinessType(productProposalDto.getBusinessType());
                            irrCalResultEntity.setVehicleType(productProposalTemplateDto.getVehicleType());
                            irrCalResultEntity.setProductProposalName(productProposalDto.getName());
                            irrCalResultEntity.setProductProposalId(productProposalDto.getId());
                            irrCalResultEntity.setWeightedBetweenGroupsOrNot(productProposalDto.getWeightedBetweenGroupsOrNot());
                            irrCalResultEntity.setWeightedOrNot(productProposalTemplateGroupDto.getWeightedOrNot());
                            irrCalResultEntity.setCalTime(latestCostAccountApprove.getCreateTime());
                            irrCalResultEntity.setCalUser(latestCostAccountApprove.getCreateUser());
                            irrCalResultEntity.setProductCalRepaymentMethodParamDto(GsonUtil.toJson(productProposalTemplateDto.getProductCalRepaymentMethodParamDto()));
                            Double baseCommissionRatio = productProposalTemplateDto.getBaseCommissionRatio();
                            if (baseCommissionRatio == null) {
                                baseCommissionRatio = 0.0;
                            }
                            Double ladderBonusRatio = productProposalTemplateDto.getLadderBonusRatio();
                            if (ladderBonusRatio == null) {
                                ladderBonusRatio = 0.0;
                            }
                            Double promotionBonusProportion = productProposalTemplateDto.getPromotionBonusProportion();
                            if (promotionBonusProportion == null) {
                                promotionBonusProportion = 0.0;
                            }
                            BigDecimal baseCommissionRatioBigDecimal = new BigDecimal(Double.toString(baseCommissionRatio));
                            BigDecimal ladderBonusRatioBigDecimal = new BigDecimal(Double.toString(ladderBonusRatio));
                            BigDecimal promotionBonusProportionBigDecimal = new BigDecimal(Double.toString(promotionBonusProportion));
                            Double bussRebeat = baseCommissionRatioBigDecimal.add(ladderBonusRatioBigDecimal).add(promotionBonusProportionBigDecimal).doubleValue();
                            if (bussRebeat == 0.0) {
                                irrCalResultEntity.setBussRebeat(null);
                            } else {
                                irrCalResultEntity.setBussRebeat(bussRebeat);
                            }
                            irrCalResultDao.insert(irrCalResultEntity);
                        });
                    }
                });
            }
        } else if (productProposalApprovalStatusDto.getStatus().equals(ProductProposalApproveEnum.REJECT)) {
            productProposalDto.setCurrentStep(ProductProposalEnum.COST_ACCOUNTING);

            List<ProductProposalApprovalStatusEntity> historyProductProposalApprovalStatusEntityList = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), 0, ProductProposalEnum.COST_ACCOUNTING, 1);
            if (!historyProductProposalApprovalStatusEntityList.isEmpty()) {
                log.info("finanicalFinalApproval historyProductProposalApprovalStatusEntityList: {}", historyProductProposalApprovalStatusEntityList.size());
                ProductProposalApprovalStatusEntity historyProductProposalApprovalStatusEntity = historyProductProposalApprovalStatusEntityList.get(0);
                if (historyProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
                    historyProductProposalApprovalStatusEntity.setDealTag(true);
                    historyProductProposalApprovalStatusEntity.setStatus(ProductProposalApproveEnum.REJECT);
                    productProposalApprovalDao.updateById(historyProductProposalApprovalStatusEntity);

                }
            }
            List<ProductProposalApprovalStatusEntity> riskHistoryProductProposalApprovalStatusEntityList = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), ProductProposalApproveEnum.RISK_VERIFY, ProductProposalEnum.RISK_PRODUCT_PROPOSAL_APPROVE, 1);
            if (!riskHistoryProductProposalApprovalStatusEntityList.isEmpty()) {
                log.info("finanicalFinalApproval riskHistoryProductProposalApprovalStatusEntityList:{}", riskHistoryProductProposalApprovalStatusEntityList.size());
                ProductProposalApprovalStatusEntity riskHistoryProductProposalApprovalStatusEntity = riskHistoryProductProposalApprovalStatusEntityList.get(0);
                if (riskHistoryProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
                    riskHistoryProductProposalApprovalStatusEntity.setDealTag(true);
                    riskHistoryProductProposalApprovalStatusEntity.setStatus(ProductProposalApproveEnum.REJECT);
                    productProposalApprovalDao.updateById(riskHistoryProductProposalApprovalStatusEntity);

                }
            }
        }
        productProposalDto.setResetId(productProposalApprovalStatusEntity.getId());
        productProposalService.updateProductProposal(productProposalDto, user);
    }
}
