package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname MonthPassengerCarEntity
 * @Description TODO
 * @Date 2023/8/1 17:26
 */
@Data
@TableName("week_passenger_car")
@ExcelIgnoreUnannotated
public class WeekPassengerCarEntity extends BaseEntity {
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("周度")
    @TableFieldMapping(value = "week", comment = "周度")
    private String week;

    @ExcelProperty("使用城市")
    @TableFieldMapping(value = "city", comment = "使用城市")
    private String city;

    @ExcelProperty("年份")
    @TableFieldMapping(value = "year", comment = "年份")
    private String year;

    @ExcelProperty("车型类别")
    @TableFieldMapping(value = "car_model_type", comment = "车型类别")
    private String carModelType;

    @ExcelProperty("使用性质")
    @TableFieldMapping(value = "use_nature", comment = "使用性质")
    private String useNature;

    @ExcelProperty("燃料种类")
    @TableFieldMapping(value = "fuel_types", comment = "燃料种类", queryItem = true)
    private String fuelTypes;

    @ExcelProperty("新能源类型")
    @TableFieldMapping(value = "energy_type", comment = "新能源类型", queryItem = true)
    private String energyType;

    @ExcelProperty("日期")
    @TableFieldMapping(value = "cycle", comment = "日期")
    private String cycle;

    @ExcelProperty("企业简称")
    @TableFieldMapping(value = "business_abbreviation", comment = "企业简称")
    private String businessAbbreviation;

    @ExcelProperty("外廓高")
    @TableFieldMapping(value = "outer_profile_height", comment = "外廓高")
    private String outerProfileHeight;

    @ExcelProperty("车型系别")
    @TableFieldMapping(value = "car_type", comment = "车型系别")
    private String carType;

    @ExcelProperty("数量")
    @TableFieldMapping(value = "number", comment = "数量")
    private String number;

    @ExcelProperty("轴距")
    @TableFieldMapping(value = "axle_distance", comment = "轴距")
    private String axleDistance;

    @ExcelProperty("变速器类型")
    @TableFieldMapping(value = "transmission", comment = "变速器类型")
    private String transmission;

    @ExcelProperty("额定载客")
    @TableFieldMapping(value = "rated_concession", comment = "额定载客")
    private String ratedConcession;

    @ExcelProperty("使用省份")
    @TableFieldMapping(value = "province", comment = "使用省份")
    private String province;

    @ExcelProperty("排量l")
    @TableFieldMapping(value = "displacement_l", comment = "排量l")
    private String displacementL;

    @ExcelProperty("排放水平")
    @TableFieldMapping(value = "layout_level", comment = "排放水平")
    private String layoutLevel;

    @ExcelProperty("功率")
    @TableFieldMapping(value = "power", comment = "功率")
    private String power;

    @ExcelProperty("外廓宽")
    @TableFieldMapping(value = "outer_profile_width", comment = "外廓宽")
    private String outerProfileWidth;

    @ExcelProperty("品牌")
    @TableFieldMapping(value = "brand", comment = "品牌", queryItem = true)
    private String brand;

    @ExcelProperty("企业名称")
    @TableFieldMapping(value = "business_name", comment = "企业名称")
    private String businessName;

    @ExcelProperty("外廓长")
    @TableFieldMapping(value = "outer_profile_length", comment = "外廓长")
    private String outerProfileLength;

    @ExcelProperty("集团简称")
    @TableFieldMapping(value = "group_abbreviation", comment = "集团简称", queryItem = true)
    private String groupAbbreviation;

    @ExcelProperty("车型")
    @TableFieldMapping(value = "vehicle_type", comment = "车型", queryItem = true)
    private String vehicleType;

    @ExcelProperty("更新日期")
    @TableFieldMapping(value = "data_date_week", comment = "更新日期")
    private String dataDateWeek;

    @ExcelProperty("驱动形式")
    @TableFieldMapping(value = "drive_form", comment = "驱动形式")
    private String driveForm;

    @ExcelProperty("所有权")
    @TableFieldMapping(value = "ownership", comment = "所有权")
    private String ownership;

    @ExcelProperty("性别")
    @TableFieldMapping(value = "gender", comment = "性别")
    private String gender;

    @ExcelProperty("车型级别")
    @TableFieldMapping(value = "vehicle_class", comment = "车型级别")
    private String vehicleClass;

    @ExcelProperty("厢数")
    @TableFieldMapping(value = "number_car", comment = "厢数")
    private String numberCar;

    @ExcelProperty("细化车型")
    @TableFieldMapping(value = "detailed_vehicle_type", comment = "细化车型")
    private String detailedVehicleType;
}