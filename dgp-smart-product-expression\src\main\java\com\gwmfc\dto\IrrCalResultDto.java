package com.gwmfc.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024年02月05日 11:15
 */
@Data
@ApiModel(value = "irr结果保存")
public class IrrCalResultDto {
    @ApiModelProperty("主键id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("业务类型")
    private Integer businessType;
    @ApiModelProperty("产品提案名称")
    private String productProposalName;
    @ApiModelProperty("提案id")
    private Long productProposalId;

    @ApiModelProperty("是否组间加权计算 1-加权   0-不加权")
    private Integer weightedBetweenGroupsOrNot;
    @ApiModelProperty("是否组内加权计算")
    private Integer weightedOrNot;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("贷额")
    private Double loanAmount;
    @ApiModelProperty("贷额")
    private Double loanAmountMin;
    @ApiModelProperty("贷额")
    private Double loanAmountMax;

    @ApiModelProperty("贷期")
    private Double paymentDays;
    @ApiModelProperty("贷期")
    private Double paymentDaysMin;
    @ApiModelProperty("贷期")
    private Double paymentDaysMax;

    @ApiModelProperty("客户利率（%）")
    private Double customerInterestRate;
    @ApiModelProperty("客户利率（%）")
    private Double customerInterestRateMin;
    @ApiModelProperty("客户利率（%）")
    private Double customerInterestRateMax;

    @ApiModelProperty("结算利率（%）")
    private Double settledRate;
    @ApiModelProperty("结算利率（%）")
    private Double settledRateMin;
    @ApiModelProperty("结算利率（%）")
    private Double settledRateMax;

    @ApiModelProperty("商务返利（%）")
    private Double bussRebeatMin;
    @ApiModelProperty("商务返利（%）")
    private Double bussRebeatMax;

    @ApiModelProperty("还款方式")
    private Integer repaymentMode;
    @ApiModelProperty("还款方式说明")
    private String repaymentModeExplain;
    @ApiModelProperty("合同占比（%）")
    private Double contractsProportion;

    //风险
    @ApiModelProperty("风险损失率（%）")
    private Double riskLossRate;
    @ApiModelProperty("风险损失率（%）")
    private Double riskLossRateMin;
    @ApiModelProperty("风险损失率（%）")
    private Double riskLossRateMax;

    @ApiModelProperty("基础服务费（%）")
    private Double baseCommissionRatio;
    @ApiModelProperty("阶梯奖金比例（%）")
    private Double ladderBonusRatio;
    @ApiModelProperty("促销奖金比例（%）")
    private Double promotionBonusProportion;

    //政策
    @ApiModelProperty("IRR")
    private Double irr;
    @ApiModelProperty("ROA")
    private Double roa;
    @ApiModelProperty("净利润")
    private Double netMargin;
    @ApiModelProperty("万元收益")
    private Double thousandIncome;
    @ApiModelProperty("加权IRR")
    private Double weightedIrr;

    @ApiModelProperty("个人奖励")
    private String personalReward;
    @ApiModelProperty("irr模板ID")
    private Double irrTemplateId;


    @ApiModelProperty("测算时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime calTime;
    @ApiModelProperty("测算时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate calTimeStart;
    @ApiModelProperty("测算时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate calTimeEnd;

    @ApiModelProperty("测算人")
    private String calUser;

    @ApiModelProperty("还款方式")
    private ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto;
    @ApiModelProperty("车辆类型")
    private String vehicleType;

    @ApiModelProperty("商务返利")
    private Double bussRebeat;

    @ApiModelProperty("分组名称")
    private String groupName;

}
