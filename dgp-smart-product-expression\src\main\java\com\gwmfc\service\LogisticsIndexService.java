package com.gwmfc.service;

import com.alibaba.nacos.common.utils.JacksonUtils;
import com.gwmfc.entity.CpcaautoCallRecordEntity;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.util.JsoupUtil;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024年03月07日 17:50
 */
@Service
@Slf4j
@RefreshScope
public class LogisticsIndexService {
    @Resource
    private GlobalFormBusinessService globalFormBusinessService;
    @Resource
    private CpcaautoService cpcaautoService;
    @Resource
    private JsoupUtil jsoupUtil;

    public void catchLogisticsIndex(String tableName, String userName) {
        String title = "";
        String label = "";
        try {
            switch (tableName) {
                case "commerce_logistics_index":
                    title = "电商物流指数为";
                    label = "点";
                    break;
                case "logistics_industry_prosperity_index":
                    title = "物流业景气指数为";
                    label = "%";
                    break;
                case "storage_index":
                    title = "中国仓储指数为";
                    label = "%";
                    break;
            }
            catchLogisticsIndexPage(title, label, tableName, userName);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void catchLogisticsIndexPage(String title, String label, String tableName, String userName) throws IOException {
        String url = "http://www.clic.org.cn/search.jspx?q=".concat(title);

        Document pageNumDocument = jsoupUtil.connect(url);
        String pageNumStr = pageNumDocument.select(".fy").select("span").get(0).text();
        Integer pageNum = Integer.parseInt(pageNumStr.substring(pageNumStr.indexOf("/") + 1, pageNumStr.lastIndexOf("页")));

        Map resMap = new HashMap<>();
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (int i = 1; i <= pageNum; i++) {
            String selUrl = "http://www.clic.org.cn/search_" + i + ".jspx?q=".concat(title);
            Document documentListPage = jsoupUtil.connect(selUrl);
            Elements links = documentListPage.select(".lbbox").select("ul").select("li");
            for (Element link : links) {
                String textNode = link.select("p").select("a").first().attr("title");
                if (textNode.indexOf(title) != -1 && textNode.indexOf(label) != -1 && textNode.contains("年") && textNode.contains("月")) {
                    try {
                        String year = textNode.substring(0, textNode.indexOf("年"));
                        String month = textNode.substring(textNode.indexOf("年") + 1, textNode.indexOf("月"));

                        String value = textNode.substring(textNode.indexOf(title) + title.length(), textNode.indexOf(label))
                                .replace("【经济信息联播】","")
                                .replace("【第一时间】","")
                                .replace("【新闻联播】","")
                                .replace("【中国财经报道】","")
                                .replace("中国", "");
                        String date;
                        if (Integer.valueOf(month) < 10) {
                            date = year.concat("-").concat("0").concat(month);
                        } else {
                            date = year.concat("-").concat(month);
                        }
                        //判断该月在不在
                        if (globalFormBusinessService.selectRecordCountByDate(tableName, date, "data_date") != 0) {
                            continue;
                        }
                        resMap.put(date, value);
                    } catch (Exception e) {
                        log.error(title.concat("textNode解析异常，{}"), textNode);
                        log.error(title.concat("获取异常，{}"), e);
                    }
                }
            }
        }
        resMap.forEach((k, v) -> {
            Map map = new HashMap<>();
            map.put("data_date", k);
            map.put(tableName.concat("_val"), v);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            map.put("create_time", Long.parseLong(dateTimeFormatter.format(LocalDateTime.now())));
            map.put("create_user", userName);
            dataList.add(map);
        });

        Set<String> columnSet = new HashSet<>();
        columnSet.add("data_date");
        columnSet.add(tableName.concat("_val"));
        columnSet.add("create_time");
        columnSet.add("create_user");
        if (!dataList.isEmpty()) {
            globalFormBusinessService.batchAddData(tableName, columnSet, dataList);
        }
    }

    public void manufacturingPmiIndexDownload(String year, String month, String userName) {
        String url = "http://www.clic.org.cn/search.jspx?q=制造业PMI数据";

        Document pageNumDocument = jsoupUtil.connect(url);
        String pageNumStr = pageNumDocument.select(".fy").select("span").get(0).text();
        Integer pageNum = Integer.parseInt(pageNumStr.substring(pageNumStr.indexOf("/") + 1, pageNumStr.lastIndexOf("页")));

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        for (int i = 1; i <= pageNum; i++) {
            String selUrl = "http://www.clic.org.cn/search_" + i + ".jspx?q=制造业PMI数据";
            Document documentListPage = jsoupUtil.connect(selUrl);
            Elements links = documentListPage.select(".lbbox").select("ul").select("li");
            for (Element link : links) {
                String title = String.valueOf(link.select("h3").text()).replaceAll("([　]|\\s|\\u00A0)+", "");
                if (title.contains("制造业PMI数据") && title.contains("年") && title.contains("月")) {
                    try {
                        String titleMonth = title.substring(title.indexOf("年") + 1, title.indexOf("月"));
                        String titleYear = title.substring(0, title.indexOf("年"));

                        if (titleYear.equals(year) && titleMonth.equals(Integer.valueOf(month).toString())) {
                            String dateStr = titleYear.concat("-").concat(month);
                            //判断该月在不在
                            if (globalFormBusinessService.selectRecordCountByDate("manufacturing_pmi_index", dateStr, "data_date") != 0) {
                                throw new SystemRuntimeException("该月数据已存在！");
                            }
                            String childUrl = link.children().first().select("a").attr("href");
                            Document childDocument = jsoupUtil.connect(childUrl);
                            String picUrl = childDocument.select(".art-zw").select("img").attr("src");
                            CpcaautoCallRecordEntity cpcaautoCallRecordEntity = new CpcaautoCallRecordEntity();
                            cpcaautoCallRecordEntity.setPageTitle(String.valueOf(link.select("h3").text()).replaceAll("([　]|\\s|\\u00A0)+", ""));
                            cpcaautoCallRecordEntity.setCallTime(dateTimeFormatter.format(LocalDateTime.now()));
                            cpcaautoCallRecordEntity.setTriggerMethod(2);
                            List picUrlList = new ArrayList<>(1);
                            picUrlList.add("http://www.clic.org.cn/".concat(picUrl));
                            cpcaautoCallRecordEntity.setPictureUrlList(JacksonUtils.toJson(picUrlList));
                            cpcaautoCallRecordEntity.setCreateUser(userName);
                            cpcaautoCallRecordEntity.setSource("manufacturing_pmi_index");
                            cpcaautoService.save(cpcaautoCallRecordEntity);
                            cpcaautoService.analysisData("manufacturing_pmi_index", dateTimeFormatter.format(LocalDateTime.now()), userName, cpcaautoCallRecordEntity, "post");
                        }
                    } catch (Exception systemRuntimeException) {
                        log.error("manufacturingPmiIndexDownload error title :{}", title);
                        throw systemRuntimeException;
                    }
                }
            }
        }
    }

}
