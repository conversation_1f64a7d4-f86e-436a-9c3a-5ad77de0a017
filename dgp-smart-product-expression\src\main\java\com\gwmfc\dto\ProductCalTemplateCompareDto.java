package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalTemplateCompareDto
 * @Description TODO
 * @Date 2023/10/12 8:48
 */
@Data
@ApiModel(value = "模板对比dto")
public class ProductCalTemplateCompareDto {

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("版本号")
    private Integer version;

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("还款方式")
    private String repaymentMethod;

    @ApiModelProperty("还款方式参数")
    private String repaymentMethodInstruction;

    @ApiModelProperty("打包融资金额")
    private Double packageCarPrice;

    @ApiModelProperty("首付比例")
    private Double downpaymentsRatio;

    @ApiModelProperty("首付金额")
    private Double downpaymentsMoney;

    @ApiModelProperty("实际放款额")
    private Double actualLoanMoney;

    @ApiModelProperty("客户利率（%）")
    private Double customerInterestRate;

    @ApiModelProperty("IRR")
    private Double resultIrr;

    @ApiModelProperty("XIRR")
    private Double resultXirr;

    @ApiModelProperty("万元收益")
    private Double resultPerTenThousandIncome;

    @ApiModelProperty("税前收益")
    private Double resultBeforeTaxIncome;

    @ApiModelProperty("ROA")
    private Double resultRoa;
}
