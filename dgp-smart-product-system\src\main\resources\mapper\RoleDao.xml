<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.RoleDao">

    <select id="getMenuByRoleId" resultType="com.gwmfc.entity.MenuDO">
        select perms,url from sys_menu sm where menu_id  in (select menu_id from sys_role_menu srm where role_id =#{roleId})
	</select>

    <select id="getRolesByIds" resultType="com.gwmfc.entity.RoleDO">
        select
        `role_id`,`role_name`,`role_sign`,`remark`,`user_id_create`,`gmt_create`,`gmt_modified`
        from sys_role where role_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="getMenuByRoleIds" resultType="com.gwmfc.entity.MenuDO">
        select perms,url from sys_menu sm where menu_id  in (
            select menu_id from sys_role_menu srm where role_id in
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
	</select>

    <delete id="removeMenuByRoleId">
		DELETE FROM sys_role_menu WHERE role_id=#{roleId}
	</delete>

    <insert id="batchSave">
        INSERT INTO sys_role_menu(role_id, menu_id) values
        <foreach item="item" index="index" collection="list"
                 separator=",">
            (#{item.roleId},#{item.menuId})
        </foreach>
    </insert>

    <select id="getRolesByUserId" resultType="com.gwmfc.entity.RoleDO">
        select `role_id`,`role_name`,`role_sign`,`remark`,`user_id_create`,`gmt_create`,`gmt_modified` from sys_role where role_id in
        (select role_id from sys_user_role where user_id=#{userId})
    </select>

    <delete id="deleteRolesByUserId">
        DELETE from sys_user_role WHERE user_id=#{userId}
    </delete>

    <insert id="saveUserRole" >
        INSERT INTO sys_user_role(role_id, user_id) values (#{roleId},#{userId})
    </insert>


</mapper>