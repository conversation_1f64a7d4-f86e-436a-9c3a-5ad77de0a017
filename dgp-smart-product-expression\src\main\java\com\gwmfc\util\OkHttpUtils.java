package com.gwmfc.util;

import java.io.IOException;
import java.io.Serializable;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.concurrent.TimeUnit;

import okhttp3.*;

public class OkHttpUtils implements Serializable {

    public static  String getUrl(String url) throws IOException {
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(url).build();
        Response response = client.newCall(request).execute();
        if (response.isSuccessful()) {
            return response.body().string();
        } else {
            throw new IOException("Unexpected code " + response);
        }
    }
    public static String doPost(String url,String sendData,String headerVal,String proxy,long timeout) {
        try {
            MediaType header = MediaType.parse(headerVal);
            OkHttpClient client = new OkHttpClient();
            client = client.newBuilder().connectTimeout(timeout, TimeUnit.MINUTES).writeTimeout(timeout,TimeUnit.MINUTES).readTimeout(timeout,TimeUnit.MINUTES).build();
            if (proxy!=null&&proxy.length()>0){
                String[] proxys=proxy.split(":");
                client=client.newBuilder().proxy(new Proxy(Proxy.Type.HTTP,new InetSocketAddress(proxys[0],Integer.parseInt(proxys[1])))).build();
            }

            RequestBody requestBody = RequestBody.create(header, sendData);
            Request request = new Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .build();

            Response response = client.newCall(request).execute();
            return response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
