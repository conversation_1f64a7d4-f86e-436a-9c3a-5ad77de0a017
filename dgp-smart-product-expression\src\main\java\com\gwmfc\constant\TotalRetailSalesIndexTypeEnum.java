package com.gwmfc.constant;

/**
 * <AUTHOR>
 * @Classname EarlySquareRatioRulePamEnum
 * @Description TODO
 * @Date 2024/7/3 13:14
 */
public enum TotalRetailSalesIndexTypeEnum {
    TOTAL_RETAIL_SALES("A0701", "零售总额"),
    TOTAL_RETAIL_SALES_CONSUMPTION("A0703","零售总额-消费分类"),
    TOTAL_RETAIL_SALES_CAR("A07040F","零售总额-汽车类"),
    FIXED_ASSETS_INVESTMENT_GROWTH_INDUSTRY("A0403","固定资产投资增速-按行业分类");

    private final String type;

    private final String value;

    TotalRetailSalesIndexTypeEnum(String type, String value) {
        this.type = type;
        this.value = value;
    }

    public String type() {
        return type;
    }

    public String value() {
        return value;
    }

}
