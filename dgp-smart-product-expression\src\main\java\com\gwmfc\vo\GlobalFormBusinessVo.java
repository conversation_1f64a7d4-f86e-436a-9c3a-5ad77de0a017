package com.gwmfc.vo;

import com.gwmfc.entity.data.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 通用业务页面返回结果
 * <AUTHOR>
 * @date 2023/2/17
 */
@Data
@AllArgsConstructor
public class GlobalFormBusinessVo {

    /**
     * 表格头
     */
    private Map<String, TableFieldDetailVo> tableHeaders;

    /**
     * 表格内容
     */
    private List<?> tableData;

    private Integer formId;

    public GlobalFormBusinessVo() {
    }
}
