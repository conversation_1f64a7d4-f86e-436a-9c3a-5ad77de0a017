package com.gwmfc.exception;

import com.gwmfc.util.BusinessEnum;
import com.gwmfc.util.StatusCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023年10月24日 9:49
 */
@Data
@NoArgsConstructor
public class SystemRuntimeException extends RuntimeException {

    private int code;

    private String msg;

    //对该异常类的构造方法进行补充，不写的化会默认只有一个无参构造
    public SystemRuntimeException(String msg) {
        this.code = StatusCodeEnum.FAIL.getCode();
        this.msg = msg;
    }

    public SystemRuntimeException(int code, String msg) {
        this.msg = msg;
        this.code = code;
    }

    public SystemRuntimeException(String msg, Throwable e) {
        super(e);
        this.code = StatusCodeEnum.FAIL.getCode();
        this.msg = msg;
    }

    public SystemRuntimeException(int code, String msg, Throwable e) {
        super(e);
        this.msg = msg;
        this.code = code;
    }

    public SystemRuntimeException(BusinessEnum businessEnum) {
        this.msg = businessEnum.getMsg();
        this.code = businessEnum.getCode();
    }

    public SystemRuntimeException(BusinessEnum businessEnum, Throwable e) {
        super(e);
        this.msg = businessEnum.getMsg();
        this.code = businessEnum.getCode();
    }
}
