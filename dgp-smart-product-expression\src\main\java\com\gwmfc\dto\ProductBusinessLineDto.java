package com.gwmfc.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductBusinessLine
 * @Description 业务条线
 * @Date 2024/8/12 9:16
 */
@Data
@ApiModel(value = "业务条线")
public class ProductBusinessLineDto {
    @ApiModelProperty("业务类型")
    private String bussType;

    @ApiModelProperty("赛道")
    private String track;

    @ApiModelProperty("细分领域")
    private String subdivision;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("状态")
    private Integer state;

    @ApiModelProperty("生效开始日期")
    private String effectiveDate;

    @ApiModelProperty("有效截止日期")
    private String expiryDate;

    @ApiModelProperty("创建人")
    private String createUser;
}
