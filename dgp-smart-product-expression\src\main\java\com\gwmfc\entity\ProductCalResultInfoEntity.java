package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductCalResultInfoEntity
 * @Description 计算结果信息
 * @Date 2023/9/19 11:27
 */
@Data
@TableName(value = "product_cal_result_info")
public class ProductCalResultInfoEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "basic_info_id", comment = "基本参数id")
    private Long basicInfoId;

    @TableFieldMapping(value = "result_irr", comment = "IRR")
    private Double resultIrr;

    @TableFieldMapping(value = "result_xirr", comment = "XIRR")
    private Double resultXirr;

    @TableFieldMapping(value = "result_net_profit_money", comment = "净利润额")
    private Double resultNetProfitMoney;

    @TableFieldMapping(value = "result_before_tax_income", comment = "税前收益")
    private Double resultBeforeTaxIncome;

    @TableFieldMapping(value = "result_roa", comment = "ROA(%)")
    private Double resultRoa;

    @TableFieldMapping(value = "result_per_ten_thousand_income", comment = "万元收益")
    private Double resultPerTenThousandIncome;

    @TableFieldMapping(value = "result_marginal_profit", comment = "边际利润")
    private Double resultMarginalProfit;

    @TableFieldMapping(value = "result_our_full_loan_irr", comment = "我司全额贷款Irr")
    private Double resultOurFullLoanIrr;

    @TableFieldMapping(value = "result_our_full_loan_net_profit_money", comment = "我司全额贷款净利润")
    private Double resultOurFullLoanNetProfitMoney;

    @TableFieldMapping(value = "result_our_full_loan_marginal_profit", comment = "我司全额贷款边际利润")
    private Double resultOurFullLoanMarginalProfit;

    @TableFieldMapping(value = "result_our_full_loan_xirr", comment = "我司全额贷款xirr")
    private Double resultOurFullLoanXirr;

    @TableFieldMapping(value = "result_our_full_loan_roa", comment = "我司全额贷款roa")
    private Double resultOurFullLoanRoa;

    @TableFieldMapping(value = "result_integrative_irr", comment = "综合Irr")
    private Double resultIntegrativeIrr;

    @TableFieldMapping(value = "result_integrative_net_profit_money", comment = "综合净利润")
    private Double resultIntegrativeNetProfitMoney;

    @TableFieldMapping(value = "result_integrative_marginal_profit", comment = "综合边际利润")
    private Double resultIntegrativeMarginalProfit;

    @TableFieldMapping(value = "result_integrative_xirr", comment = "综合xirr")
    private Double resultIntegrativeXirr;

    @TableFieldMapping(value = "result_integrative_roa", comment = "综合roa")
    private Double resultIntegrativeRoa;

    @TableFieldMapping(value = "create_time", comment = "创建日期")
    private Date createTime;

    @TableFieldMapping(value = "create_user", comment = "创建人")
    private String createUser;

    @TableFieldMapping(value = "update_time", comment = "修改日期")
    private Date updateTime;

    @TableFieldMapping(value = "update_user", comment = "修改人")
    private String updateUser;
}
