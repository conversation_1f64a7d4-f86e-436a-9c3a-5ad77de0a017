package com.gwmfc.filter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gwmfc.util.Result;
import com.gwmfc.vo.TokenVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.netty.ByteBufFlux;

import static com.gwmfc.util.Const.TOKEN_AUTHORIZATION;
import static com.gwmfc.util.Const.TOKEN_BEARER;

@RefreshScope
@Component
@Slf4j
public class DingTokenFilter implements GatewayFilter, Ordered {


    @Autowired
    private WebClient.Builder webClientBuilder;

    public static String IGNORE_GLOBAL_FILTER = "ignore_global_filter";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        exchange.getAttributes().put(IGNORE_GLOBAL_FILTER, true);
        String token = exchange.getRequest().getHeaders().getFirst(TOKEN_AUTHORIZATION);
        if(!StringUtils.hasText(token)){
            token = exchange.getRequest().getQueryParams().getFirst("token");
        }
        if (StringUtils.hasLength(token) && token.startsWith(TOKEN_BEARER)) {
            token = token.split(" ")[1];
            TokenVo tokenVo = new TokenVo();
            tokenVo.setToken(token);
            return webClientBuilder.build().method(HttpMethod.POST).uri("http://auth-service/ding_auth/verify_token")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromValue(tokenVo))
                    .retrieve()
                    .bodyToMono(Result.class)
                    .flatMap(s -> {
                        if (HttpStatus.OK.value() == s.getCode()) {
                            return chain.filter(exchange);
                        } else {
                            ServerHttpResponse response = exchange.getResponse();
                            response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
                            return response.writeAndFlushWith(Flux.just(ByteBufFlux.just(response.bufferFactory().wrap(getWrapData(s)))));
                        }
                    });
        }
        log.info("no token found ");
        exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
        return exchange.getResponse().setComplete();
    }

    @Override
    public int getOrder() {
        return -200;
    }

    private byte[] getWrapData(Result result) {
        try {
            return new ObjectMapper().writeValueAsString(result).getBytes();
        } catch (JsonProcessingException e) {
            log.error("serializable result error {}", e);
        }
        return "".getBytes();
    }
}
