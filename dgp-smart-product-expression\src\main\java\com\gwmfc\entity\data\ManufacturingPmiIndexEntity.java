package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * 制造业PMI数据
 * <AUTHOR>
 * @date 2024年02月26日 17:26
 */
@Data
@TableName("manufacturing_pmi_index")
@ExcelIgnoreUnannotated
public class ManufacturingPmiIndexEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("生产经营活动预期")
    @TableFieldMapping(value = "production_business_activities_expectations", comment = "生产经营活动预期")
    private String productionBusinessActivitiesExpectations;
}
