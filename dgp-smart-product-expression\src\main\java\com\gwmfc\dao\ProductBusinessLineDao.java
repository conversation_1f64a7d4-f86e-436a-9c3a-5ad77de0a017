package com.gwmfc.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gwmfc.entity.ProductBusinessLineEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname ProductBusinessLineDao
 * @Description TODO
 * @Date 2024/8/12 9:42
 */
@Mapper
public interface ProductBusinessLineDao extends BaseMapper<ProductBusinessLineEntity> {
    List<String> getBussTypes();
    List<String> getTrackByBussType(String bussType);
    List<String> getSubdivisionByBussTypeAndTrack(String bussType,String track);
}
