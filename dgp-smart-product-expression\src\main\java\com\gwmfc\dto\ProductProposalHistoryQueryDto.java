package com.gwmfc.dto;

/**
 * <AUTHOR>
 * @Classname ProductProposalHistoryQueryDto
 * @Description 方案提案历史数据查询
 * @Date 2023/11/6 15:56
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 方案提案历史数据查询
 */
@Data
@ApiModel(value = "方案提案历史数据查询dto")
public class ProductProposalHistoryQueryDto {

    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("激活日期-low")
    private String activationTimeLow;

    @ApiModelProperty("激活日期-high")
    private String activationTimeHigh;

    @ApiModelProperty("品牌")
    private List<String> brandNameList;

    @ApiModelProperty("车型（映射后")
    private String vehiclemodel;

    @ApiModelProperty("区域")
    private List<String> areaList;

    @ApiModelProperty("经销商省份")
    private String dealerProvince;

    @ApiModelProperty("经销商评级")
    private List<String> dealerLevelList;

    @ApiModelProperty("集团")
    private String clique;

    @ApiModelProperty("投资人")
    private String investor;

    @ApiModelProperty("经销商名称")
    private String dealerName;

    @ApiModelProperty("产品类型")
    private List<String> productTypeList;

    @ApiModelProperty("产品名称")
    private String finProductNme;

    @ApiModelProperty("客户利率-low")
    private Double customerRteLow;

    @ApiModelProperty("客户利率-high")
    private Double customerRteHigh;

    @ApiModelProperty("贴息利率-low")
    private Double subsidyPctLow;

    @ApiModelProperty("贴息利率-high")
    private Double subsidyPctHigh;

    @ApiModelProperty("总利率-low")
    private Double totalRateLow;

    @ApiModelProperty("总利率-high")
    private Double totalRateHigh;

    @ApiModelProperty("贷期-low")
    private Integer loanTrmLow;

    @ApiModelProperty("贷期-high")
    private Integer loanTrmHigh;

    @ApiModelProperty("首付比例-low")
    private Double firstPayPctLow;

    @ApiModelProperty("首付比例-high")
    private Double firstPayPctHigh;

    @ApiModelProperty("贷额-low")
    private Double loanAmtLow;

    @ApiModelProperty("贷额-high")
    private Double loanAmtHigh;

    @ApiModelProperty("附加贷金额-low")
    private Double addCreAmtLow;

    @ApiModelProperty("附加贷金额-high")
    private Double addCreAmtHigh;

    @ApiModelProperty("车价-low")
    private Double vehiclePriceLow;

    @ApiModelProperty("车价-high")
    private Double vehiclePriceHigh;

    @ApiModelProperty("车辆类型")
    private List<String> vehicleTypeList;

    @ApiModelProperty("是否新能源")
    private List<Integer> isNewEnergyList;

    @ApiModelProperty("挂靠方式")
    private List<String> affiliatedTypeDscList;

    @ApiModelProperty("使用性质")
    private List<String> usetypeList;

    @ApiModelProperty("评分卡等级")
    private List<String> cardGradeList;

    @ApiModelProperty("Fico等级")
    private List<String> ficoLvList;

    @ApiModelProperty("性别")
    private List<String> sexList;

    @ApiModelProperty("年龄-low")
    private Integer ageLow;

    @ApiModelProperty("年龄-high")
    private Integer ageHigh;

    @ApiModelProperty("学历")
    private List<String> educationIdDscList;

    @ApiModelProperty("本人月收入-low")
    private Double incomeLow;

    @ApiModelProperty("本人月收入-high")
    private Double incomeHigh;

    @ApiModelProperty("细分车型")
    private List<String> vehicleTypeDetailList;

    @ApiModelProperty("产品分类1-利率")
    private List<String> productGroup1List;

    @ApiModelProperty("产品分类2-车型")
    private List<String> productGroup2List;

    @ApiModelProperty("产品分类3-细化")
    private List<String> xfcxNew2List;

    @ApiModelProperty("是否挂牌车")
    private List<Integer> ifGpcList;

    @ApiModelProperty("是否专用车")
    private List<Integer> ifZycList;

    @ApiModelProperty("是否平行进口车")
    private List<Integer> ifParImVehicleList;

    @ApiModelProperty("车辆价格段")
    private List<String> pricethirdpartyGroupList;

    @ApiModelProperty("是否精品车")
    private List<Integer> ifJpcList;

    @ApiModelProperty("车龄分组")
    private List<String> carAgeGroupList;

    @ApiModelProperty("车龄-low")
    private Double carAgeLow;

    @ApiModelProperty("车龄-high")
    private Double carAgeHigh;

    @ApiModelProperty("指导价-low")
    private Double invoicepriceLow;

    @ApiModelProperty("指导价-high")
    private Double invoicepriceHigh;

    @ApiModelProperty("合同量")
    private String contractNum;

    @ApiModelProperty("合同占比")
    private String contractProportion;

    @ApiModelProperty("平均车价")
    private String avgCarPrice;

    @ApiModelProperty("平均贷期")
    private String avgLoanTrm;

    @ApiModelProperty("平均总利率")
    private String avgTotalRate;

    @ApiModelProperty("平均客户利率")
    private String avgCustomerRate;

    @ApiModelProperty("平均贷额")
    private String avgLoanAmt;

    @ApiModelProperty("平均首付比例")
    private String avgPayPct;

    @ApiModelProperty("平均附加带金额")
    private String avgAddCreAmt;

    @ApiModelProperty("平均附加带比例")
    private String avgAddCreRatio;

    @ApiModelProperty("总贷额")
    private String totalLoanAmt;

    @ApiModelProperty("不良贷款率")
    private String badLoanRate;

    @ApiModelProperty("核销前近18期30+逾期率")
    private String beforeHx18Times30dOverDueRate;

    @ApiModelProperty("核销前近12期30+逾期率")
    private String beforeHx12Times30dOverDueRate;

    @ApiModelProperty("核销前30+逾期率")
    private String beforeHx30dOverDueRate;

    @ApiModelProperty("核销后近18期30+逾期率")
    private String afterHx18Times30dOverDueRate;

    @ApiModelProperty("核销后近12期30+逾期率")
    private String afterHx12Times30dOverDueRate;

    @ApiModelProperty("核销后30+逾期率")
    private String afterHx30dOverDueRate;

    @ApiModelProperty("风险损失率")
    private String avgLossRate;

    @ApiModelProperty("平均万元收益")
    private String avg10ThondsIncom;

    @ApiModelProperty("平均万元销售利润")
    private String avg10ThondsSalProfit;

    @ApiModelProperty("平均商务返利")
    private String avgBussRebeat;

    @ApiModelProperty("IRR")
    private String irr;
}
