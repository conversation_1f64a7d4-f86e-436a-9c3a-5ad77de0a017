package com.gwmfc.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.bo.ProductRebateInformationBo;
import com.gwmfc.bo.UploadFileBo;
import com.gwmfc.entity.LittleBeeProductRebateInformationEntity;
import com.gwmfc.entity.OtherInformationEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023年09月07日 11:19
 */
@Data
@ApiModel(value = "资方")
public class CapitalDto {
    @TableId(type = IdType.AUTO)
    private Long id;
    @ApiModelProperty("数据来源（1：非小蜜蜂；2：小蜜蜂）")
    private Integer source;
    @ApiModelProperty("资方名称")
    private String capitalName;
    @ApiModelProperty("资方省份")
    private String capitalProvince;
    @ApiModelProperty("资方城市")
    private String capitalCity;
    @ApiModelProperty("经销商集团")
    private String dealerGroup;
    @ApiModelProperty("产品返佣信息")
    private List<ProductRebateInformationBo> productRebateInformation;
    @ApiModelProperty("小蜜蜂产品返佣信息")
    private List<LittleBeeProductRebateInformationEntity> littleBeeProductRebateInformation;
//    @ApiModelProperty("单量信息")
//    private List<OrderNumberInformationEntity> orderNumberInformation;
    @ApiModelProperty("其他信息")
    private List<OtherInformationEntity> otherInformation;
    @ApiModelProperty("附件")
    private List<UploadFileBo> attachmentUploadFileBo;

    @ApiModelProperty("状态码")
    private Integer status;

    /**
     * 原始ID
     */
    @ApiModelProperty("原始ID")
    private Long originId;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateUser;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CapitalDto that = (CapitalDto) o;

        Boolean productRebateInformationEqual = Objects.equals(productRebateInformation, that.productRebateInformation);
        if (!productRebateInformationEqual) {
            if (productRebateInformation == null && that.productRebateInformation.size() == 0) {
                productRebateInformationEqual = true;
            } else if (that.productRebateInformation == null && productRebateInformation.size() == 0) {
                productRebateInformationEqual = true;
            }
        }
        Boolean littleBeeProductRebateInformationEqual = Objects.equals(littleBeeProductRebateInformation, that.littleBeeProductRebateInformation);
        if (!littleBeeProductRebateInformationEqual) {
            if (littleBeeProductRebateInformation == null && that.littleBeeProductRebateInformation != null && that.littleBeeProductRebateInformation.size() == 0) {
                littleBeeProductRebateInformationEqual = true;
            } else if (that.littleBeeProductRebateInformation == null && littleBeeProductRebateInformation != null && littleBeeProductRebateInformation.size() == 0) {
                littleBeeProductRebateInformationEqual = true;
            }
        }
        Boolean attachmentUploadFileBoEqual = Objects.equals(attachmentUploadFileBo, that.attachmentUploadFileBo);
        if (!attachmentUploadFileBoEqual) {
            if (attachmentUploadFileBo == null && that.attachmentUploadFileBo != null && that.attachmentUploadFileBo.size() == 0) {
                attachmentUploadFileBoEqual = true;
            } else if (that.attachmentUploadFileBo == null && attachmentUploadFileBo != null && attachmentUploadFileBo.size() == 0) {
                attachmentUploadFileBoEqual = true;
            }
        }
        Boolean otherInformationEqual = Objects.equals(otherInformation, that.otherInformation);
        if (!otherInformationEqual) {
            if (otherInformation == null && that.otherInformation != null && that.otherInformation.size() == 0) {
                otherInformationEqual = true;
            } else if (that.otherInformation == null && otherInformation != null && otherInformation.size() == 0) {
                otherInformationEqual = true;
            }
        }

        return Objects.equals(source, that.source) && Objects.equals(capitalName, that.capitalName) && Objects.equals(capitalProvince, that.capitalProvince) && Objects.equals(capitalCity, that.capitalCity) && Objects.equals(dealerGroup, that.dealerGroup) && productRebateInformationEqual &&
                littleBeeProductRebateInformationEqual && otherInformationEqual && attachmentUploadFileBoEqual &&
                Objects.equals(status, that.status) && Objects.equals(originId, that.originId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(source, capitalName, capitalProvince, capitalCity, dealerGroup, productRebateInformation,
                littleBeeProductRebateInformation, otherInformation, attachmentUploadFileBo,
                status, originId);
    }
}
