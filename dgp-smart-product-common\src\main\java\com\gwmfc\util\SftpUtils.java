package com.gwmfc.util;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Properties;
import java.util.Vector;

/**
 * SFTP工具类
 * 包含两个方法：
 * 一个创建一个sftp通道对象，并返回这个对象，通过这 个对象可以直接发送文件。
 * 另一个是用于关闭回话和通道的。
 *
 * <AUTHOR>
 */
@Slf4j
public class SftpUtils {

    public static  final Logger logger = LoggerFactory.getLogger(SftpUtils.class);

    static private Session session = null;
    static private Channel channel = null;
    //超时数,一分钟
    static private int timeout = 60000;

    /**
     * 传入一个通道对象
     *
     * @param username 远程要连接的服务器的用户名
     * @param password 远程要连接的服务器的密码
     * @param ip       远程服务器ip
     * @param port     远程服务器的ssh服务端口
     * @return ChannelSftp返回指向这个通道指定的地址的channel实例
     * @throws JSchException
     */
    public static ChannelSftp getChannel(String username, String password, String ip, int port) throws JSchException {
        // 创建JSch对象
        JSch jsch = new JSch();
        // 根据用户名，主机ip，端口获取一个Session对象
        session = jsch.getSession(username, ip, port);
        if (password != null) {
            // 设置密码
            session.setPassword(password);
        }
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        config.put("PreferredAuthentications", "publickey,keyboard-interactive,password");
        // 为Session对象设置properties
        session.setConfig(config);
        // 设置timeout时间
        session.setTimeout(timeout);
        session.connect(); // 通过Session建立链接
        // 打开SFTP通道
        channel = session.openChannel("sftp");
        channel.connect(); // 建立SFTP通道的连接

        return (ChannelSftp) channel;
    }

    /**
     * 关闭channel和session
     *
     * @throws Exception
     */
    public static void closeChannel() {
        if (channel != null) {
            channel.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
    }

    /**
     * 文件推送的测试方法。
     * destDirPath 远程服务器要保存的文件夹路径
     * file  本地要推送的文件对象
     * username 远程服务器的用户名
     * password  远程服务器的密码
     * ip  远程服务器ip
     * port 远程服务器ssh服务端口
     */
    public static String uploadBySftp(File uploadFile, String dstDirPath, String username, String password, String ip, Integer port) {

        ChannelSftp channelSftp = null;

        String dstFilePath; // 目标文件名(带路径)，如： D:\\file\\file.doc,这个路径应该是远程目标服务器下要保存的路径
        FileInputStream fileInputStream = null;
        try {
            // 一、 获取channelSftp对象
            channelSftp = getChannel(username, password, ip, port);
            channelSftp.setFilenameEncoding("UTF-8");
            // 二、 判断远程路径dstDirPath是否存在(通道配置的路径)
            try {
                // TODO: 2020/12/10 实现递归创建目录的功能
                Vector dir = channelSftp.ls(dstDirPath);
                // 如果路径不存在，则创建
                if (dir == null) {
                    channelSftp.mkdir(dstDirPath);
                }
            } catch (SftpException e) { // 如果dstDirPath不存在，则会报错，此时捕获异常并创建dstDirPath路径
                // 此时创建路径如果再报错，即创建失败，则抛出异常
                try {
                    channelSftp.mkdir(dstDirPath);
                } catch (SftpException e1) {
                    e1.printStackTrace();
                }
                logger.error("read sftp  file exception",e);
            }
            // 三、 推送文件
            dstFilePath = dstDirPath + uploadFile.getName();
            // 推送: dstFilePath——传送过去的文件路径(全路径),采用默认的覆盖式推送 jsch触发推送操作
            fileInputStream = new FileInputStream(uploadFile);
            channelSftp.put(fileInputStream, dstFilePath);
            return dstFilePath;
        } catch (Exception e) {
            logger.error("upload file to sftp exception",e);
            e.printStackTrace();
        } finally {
            if (channelSftp != null) {
                channelSftp.quit();
            }
            try {
                if (null != fileInputStream) {
                    fileInputStream.close();
                }
                closeChannel();
            } catch (Exception e) {
                logger.error("close stream exception ", e);
            }
        }
        return null;
    }

    /**
     * 文件推送的测试方法。
     * dstDirPath 远程服务器要保存的文件夹路径
     * uploadFileContent  本地要推送的文件内容
     * username 远程服务器的用户名
     * password  远程服务器的密码
     * ip  远程服务器ip
     * port 远程服务器ssh服务端口
     */
    public static String uploadContentBySftp(String uploadFileContent, String dstDirPath, String scriptName, String username, String password, String ip, Integer port) {
        ChannelSftp channelSftp = null;

        String dstFilePath; // 目标文件名(带路径)，如： D:\\file\\file.doc,这个路径应该是远程目标服务器下要保存的路径
        OutputStream outputStream = null;
        try {
            // 一、 获取channelSftp对象
            channelSftp = getChannel(username, password, ip, port);
            channelSftp.setFilenameEncoding("UTF-8");
            // 二、 判断远程路径dstDirPath是否存在(通道配置的路径)
            try {
                Vector dir = channelSftp.ls(dstDirPath);
                // 如果路径不存在，则创建 调用方法 对上传目录进行递归创建
                if (dir == null) {
                    String[] dirs = dstDirPath.split("/");
                    String tempPath = "";
                    int index = 0;
                    mkdirDir(channelSftp, dirs, tempPath, dirs.length, index);
                }
            } catch (SftpException e) { // 如果dstDirPath不存在，则会报错，此时捕获异常并创建dstDirPath路径
                // 此时创建路径如果再报错，即创建失败，则抛出异常
                String[] dirs = dstDirPath.split("/");
                String tempPath = "";
                int index = 0;
                mkdirDir(channelSftp, dirs, tempPath, dirs.length, index);
            }
            // 三、 推送文件
            if (dstDirPath.endsWith("/")) {
                dstFilePath = dstDirPath + scriptName;
            } else {
                dstFilePath = dstDirPath + "/" + scriptName;
            }
            // 推送: dstFilePath——传送过去的文件路径(全路径),采用默认的覆盖式推送 jsch触发推送操作
            outputStream = channelSftp.put(dstFilePath,ChannelSftp.OVERWRITE);
            channelSftp.chmod(Integer.parseInt("777", 8),dstFilePath);
            outputStream.write(uploadFileContent.getBytes("UTF-8"));
            logger.info("···········upload dstFilePath:{} Integer.parseInt(\"777\",8):{}",dstFilePath,Integer.parseInt("777",10));
            return dstFilePath;
        } catch (Exception e) {
            logger.error("upload file to sftp exception", e);
            e.printStackTrace();
        } finally {
            try {
                if (null != outputStream) {
                    outputStream.flush();
                    outputStream.close();
                }
                closeChannel();
            } catch (Exception e) {
                logger.error("close stream exception ", e);
            }
            if (channelSftp != null) {
                channelSftp.quit();
            }
        }
        return null;
    }

    /**
     * 递归根据路径创建文件夹
     *
     * @param dirs     根据 / 分隔后的数组文件夹名称
     * @param tempPath 拼接路径
     * @param length   文件夹的格式
     * @param index    数组下标
     * @return
     */
    public static void mkdirDir(ChannelSftp channelSftp, String[] dirs, String tempPath, int length, int index) {
        // 以"/a/b/c/d"为例按"/"分隔后,第0位是""
        index++;
        if (index < length) {
            // 目录不存在，则创建文件夹
            tempPath += "/" + dirs[index];
        }
        try {
            logger.info("检测目录[" + tempPath + "]");
            channelSftp.cd(tempPath);
            if (index < length) {
                mkdirDir(channelSftp, dirs, tempPath, length, index);
            }
        } catch (SftpException ex) {
            logger.warn("创建目录[" + tempPath + "]");
            try {
                channelSftp.mkdir(tempPath);
                channelSftp.cd(tempPath);
            } catch (SftpException e) {
                e.printStackTrace();
                logger.error("创建目录[" + tempPath + "]失败,异常信息[" + e.getMessage() + "]");

            }
            logger.info("进入目录[" + tempPath + "]");
            mkdirDir(channelSftp, dirs, tempPath, length, index);
        }
    }

    /**
     * 删除stfp文件
     *
     * @param directory：要删除文件所在目录
     */
    public static void deleteSFTP(String directory, String username, String password, String ip, Integer port) throws JSchException {
        ChannelSftp channelSftp = null;
        channelSftp = getChannel(username, password, ip, port);
        try {
            if (isDirExist(directory,username, password, ip, port)) {
                Vector<ChannelSftp.LsEntry> vector = channelSftp.ls(directory);
                // 文件，直接删除
                if (vector.size() == 1) {
                    channelSftp.rm(directory);
                } else if (vector.size() == 2) {
                    // 空文件夹，直接删除
                    channelSftp.rmdir(directory);
                } else {
                    String fileName = "";
                    // 删除文件夹下所有文件
                    for (ChannelSftp.LsEntry en : vector) {
                        fileName = en.getFilename();
                        if (".".equals(fileName) || "..".equals(fileName)) {
                            continue;
                        } else {
                            deleteSFTP(directory + "/" + fileName,username, password, ip, port);
                        }
                    }
                    // 删除文件夹
                    channelSftp.rmdir(directory);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 判断目录是否存在
     *
     * @param directory
     * @return
     */
    public static boolean isDirExist(String directory, String username, String password, String ip, Integer port) {
        ChannelSftp channelSftp = null;
        boolean flag =false;
        try {
            channelSftp = getChannel(username, password, ip, port);
            Vector<?> vector = channelSftp.ls(directory);
            if(vector != null){
                flag =true;
            }
        } catch (Exception e) {
            flag = false;
        }finally {
            if (null != channelSftp) {
                channelSftp.disconnect();
            }
        }
        return flag;
    }



    public static String lsBySftp(String dstDirPath, String username, String password, String ip, Integer port) {
        ChannelSftp channelSftp = null;
        try {
            // 一、 获取channelSftp对象
            channelSftp = getChannel(username, password, ip, port);
            channelSftp.setFilenameEncoding("UTF-8");
            // 二、 判断远程路径dstDirPath是否存在(通道配置的路径)
            try {
                if (isDirExist(dstDirPath,username, password, ip, port)) {
                    Vector dirVector = channelSftp.ls(dstDirPath);
                    dirVector.forEach(dir -> {
                        System.out.println(dir);
                    });
                }
            } catch (SftpException e) { // 如果dstDirPath不存在，则会报错，此时捕获异常并创建dstDirPath路径
                logger.error("read sftp directory exception",e);
            }
        } catch (Exception e) {
            logger.error("get connect exception",e);
            e.printStackTrace();
        } finally {
            if (channelSftp != null) {
                channelSftp.quit();
            }
        }
        return null;
    }

    public static String readFileBySftp(String fileDirPath, String username, String password, String ip, Integer port) {
        ChannelSftp channelSftp = null;
        try {
            // 一、 获取channelSftp对象
            channelSftp = getChannel(username, password, ip, port);
            channelSftp.setFilenameEncoding("UTF-8");
            // 二、 判断远程路径dstDirPath是否存在(通道配置的路径)
            try {
                StringBuffer stringBuffer = new StringBuffer();
                if (isDirExist(fileDirPath,username, password, ip, port)) {
                    InputStream inputStream = channelSftp.get(fileDirPath);
                    BufferedReader br = new BufferedReader(new InputStreamReader(inputStream));
                    String tmpStr;
                    while((tmpStr=br.readLine()).length()!=0){
                        stringBuffer.append(tmpStr);
                    }
                    return stringBuffer.toString();
                }
            } catch (SftpException e) { // 如果dstDirPath不存在，则会报错，此时捕获异常并创建dstDirPath路径
                logger.error("read sftp directory exception",e);
            }
        } catch (Exception e) {
            logger.error("get connect exception", e);
            e.printStackTrace();
        } finally {
            if (channelSftp != null) {
                channelSftp.quit();
            }
        }
        return null;
    }


    public static String execScriptBySftp(String ip, String username, String password, String command) {
        ChannelSftp channelSftp = null;
        try {
            // 一、 获取channelSftp对象
            channelSftp = getChannel(username, password, ip, 22);
            channelSftp.setFilenameEncoding("UTF-8");
            // 二、 判断远程路径dstDirPath是否存在(通道配置的路径)
            channel = session.openChannel("exec");
            ChannelExec execChannel = (ChannelExec) channel;
            execChannel.setCommand(command);
            channel.connect();

            BufferedReader bi = new BufferedReader(new InputStreamReader(channel.getInputStream(), "UTF-8"));
            StringBuffer sb = new StringBuffer();
            String s = null;

            while((s = bi.readLine()) != null) {
                sb.append(s);
            }
            s = sb.toString();
            bi.close();

            logger.info("输出结果是：{}",s);
            return "输出结果是：" + sb.toString();
        } catch (Exception e) {
            logger.error("get connect exception", e);
            e.printStackTrace();
        } finally {
            if (channelSftp != null) {
                channelSftp.quit();
            }
        }
        return null;
    }

}