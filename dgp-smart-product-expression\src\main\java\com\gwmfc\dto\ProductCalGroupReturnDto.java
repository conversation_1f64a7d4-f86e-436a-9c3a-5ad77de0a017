package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalGroupReturnDto
 * @Description TODO
 * @Date 2024/4/2 10:42
 */
@Data
@ApiModel(value = "组间加权IRR返回结果")
public class ProductCalGroupReturnDto {
    @ApiModelProperty("组间加权irr")
    private Double groupWeightingIrr;
    @ApiModelProperty("组内irr/组内加权irr")
    private List<ProductCalGroupWeightingIrrDto> productCalGroupWeightingIrrDtoList;
}
