package com.gwmfc.constant;

import com.gwmfc.exception.SystemRuntimeException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * @Date: 2023/11/30
 * @Author: zhang<PERSON>yu
 */

@Getter
@AllArgsConstructor
public enum UsedCarWeeklyTradingTypeEnum {

    TRADING_DATA(1, "交易数据", UsedCarWeeklyTradingTableEnum.TRADING_DATA),
    REGION_TRADING_DATA(2, "区域交易数据", UsedCarWeeklyTradingTableEnum.REGION_TRADING_DATA),
    ;

    /**
     * 二手车销量类型
     */
    private final Integer usedCarType;
    /**
     * 名称
     */
    private final String tradingName;
    /**
     * excel表头及查询字段枚举类
     */
    private final UsedCarWeeklyTradingTableEnum usedCarWeeklyTradingTableEnum;

    /**
     * 根据类型获取名称
     *
     * @param usedCarType
     * @return
     */
    public static String getTradingNameByType(Integer usedCarType) {
        for (UsedCarWeeklyTradingTypeEnum value : UsedCarWeeklyTradingTypeEnum.values()) {
            if (value.getUsedCarType().equals(usedCarType)) {
                return value.getTradingName();
            }
        }
        throw new SystemRuntimeException("类型不存在");
    }

    /**
     * 根据类型查询枚举类
     * @param headMap
     * @param usedCarType
     * @return
     */
    public static void getUsedCarWeeklyTradingTableEnumByType(Map<String, String> headMap, Integer usedCarType) {
        for (UsedCarWeeklyTradingTypeEnum value : UsedCarWeeklyTradingTypeEnum.values()) {
            if (value.getUsedCarType().equals(usedCarType)) {
                value.getUsedCarWeeklyTradingTableEnum().getHeader(headMap);
            }
        }
    }
}
