package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalculateRangeDto
 * @Description TODO
 * @Date 2023/10/17 11:47
 */
@Data
@ApiModel(value = "条件变动，范围值，计算IRR")
public class ProductCalculateRangeDto extends ProductCalculateDto{
    @ApiModelProperty("条目")
    private String item;
    @ApiModelProperty("条目类型 0-基本参数 1-税费参数 2-还款方式参数 3-提前结清参数")
    private String itemType;
    @ApiModelProperty("范围高值")
    private String valueHigh;
    @ApiModelProperty("范围低值")
    private String valueLow;
    @ApiModelProperty("合同占比")
    private Double contractProportion;
    @ApiModelProperty("测算来源")
    private String source;
    @ApiModelProperty("利润详情")
    private List<ProductCalProfitDetailDto> productCalProfitDetailDtoList;
}
