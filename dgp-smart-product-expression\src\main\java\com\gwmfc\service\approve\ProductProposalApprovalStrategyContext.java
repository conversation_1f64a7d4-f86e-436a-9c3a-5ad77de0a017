package com.gwmfc.service.approve;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.bo.DingNotification;
import com.gwmfc.constant.ProductProposalApproveEnum;
import com.gwmfc.constant.ProductProposalEnum;
import com.gwmfc.dao.ProductProposalApprovalDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.*;
import com.gwmfc.entity.ProductProposalApprovalStatusEntity;
import com.gwmfc.service.DingDingService;
import com.gwmfc.util.BeanListCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年04月07日 15:39
 */
@Service
@Slf4j
@RefreshScope
public class ProductProposalApprovalStrategyContext extends ServiceImpl<ProductProposalApprovalDao, ProductProposalApprovalStatusEntity> {
    @Resource
    private Map<String, ProductProposalApprovalStrategy> selectorMap;
    @Resource
    private ProductProposalApprovalDao productProposalApprovalDao;
    @Resource
    private DingDingService dingDingService;
    @Value("${dingding.smart.agentId}")
    private String smartAgentId;
    @Value("${dingding.smart.callUrl}")
    private String callUrl;

    public void executeStrategy(ProductProposalApprovalDto productProposalApprovalDto, User user) {
        String step = productProposalApprovalDto.getProductProposalDto().getCurrentStep();
        ProductProposalApprovalStrategy productProposalApprovalStrategy = selectorMap.get(step);
        productProposalApprovalStrategy.doApproval(productProposalApprovalDto.getProductProposalDto(), productProposalApprovalDto.getProductProposalApprovalStatusDto(), user, null);
        dingdingNotify(productProposalApprovalDto, step, user);
    }

    public void productProposalSave(ProductProposalApprovalDto productProposalApprovalDto, List<MultipartFile> addFileList, User user) {
        String step = productProposalApprovalDto.getProductProposalDto().getCurrentStep();
        ProductProposalApprovalStrategy productProposalApprovalStrategy = selectorMap.get(step);
        productProposalApprovalStrategy.doApproval(productProposalApprovalDto.getProductProposalDto(), productProposalApprovalDto.getProductProposalApprovalStatusDto(), user, addFileList);
        dingdingNotify(productProposalApprovalDto, step, user);
    }

    public void dingdingNotify(ProductProposalApprovalDto productProposalApprovalDto, String step, User user) {
        DingDingNotifyDto dingDingNotifyDto = productProposalApprovalDto.getDingDingNotifyDto();
        if (dingDingNotifyDto.getNotify_or_not()) {
            String stepName = transStep(step, productProposalApprovalDto.getProductProposalApprovalStatusDto().getType());
            String status = transStatus(productProposalApprovalDto.getProductProposalApprovalStatusDto().getStatus());
            List<UserDto> userDtoList = dingDingNotifyDto.getNotify_user();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(productProposalApprovalDto.getProductProposalDto().getName());
            String dealtime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            userDtoList.forEach(userDto -> {
                DingNotification dingNotification = DingNotification.builder()
                        .content("")
                        .name(stringBuilder.toString())
                        .applyUser(user.getName().concat("(").concat(status).concat(")"))
                        .applyTime(dealtime).userId(userDto.getDingNo()).status(stepName)
                        .url(callUrl)
                        .accessToken(dingDingService.acquireSmartAccessToken()).build();
                dingDingService.pushNotification(dingNotification, smartAgentId);
            });
        }
    }

    private String transStatus(Integer status) {
        switch (status) {
            case ProductProposalApproveEnum.REJECT:
                return "拒绝";
            case ProductProposalApproveEnum.APPROVE:
                return "通过";
            case ProductProposalApproveEnum.ADD:
                return "新增";
            case ProductProposalApproveEnum.UPDATE:
                return "修改";
        }
        return null;
    }

    private String transStep(String step, Integer type) {
        switch (step) {
            case ProductProposalEnum.PRODUCT_PROPOSAL_SUBMIT:
                return "产品要素提案提交";
            case ProductProposalEnum.RISK_BUSINESS_CHECK: {
                if (type.equals(ProductProposalApproveEnum.BUSINESS_VERIFY)) {
                    return "商务提案";
                } else if (type.equals(ProductProposalApproveEnum.RISK_VERIFY)) {
                    return "风控提案";
                }
            }
            case ProductProposalEnum.COST_ACCOUNTING:
                return "IRR成本核算";
            case ProductProposalEnum.RISK_PRODUCT_PROPOSAL_APPROVE:
                return "风险确认";
            case ProductProposalEnum.FINANICAL_PRODUCT_PROPOSAL_PASSED:
                return "财务确认";
            case ProductProposalEnum.PRODUCT_PROPOSAL_PASSED:
                return "产品提案文档";
            case ProductProposalEnum.PRODUCT_PROPOSAL_DOC_CREATE:
                return "产品提案文档已生成";
        }
        return "";
    }

    public List<ProductProposalApprovalStatusDto> approvalHistory(Long productProposalId) {
        List<ProductProposalApprovalStatusDto> productProposalApprovalStatusDtoList = BeanListCopyUtil.copyListProperties(productProposalApprovalDao.approvalHistory(productProposalId), ProductProposalApprovalStatusDto::new);
        return productProposalApprovalStatusDtoList;
    }

    public void dealTag(Long resetId) {
        ProductProposalApprovalStatusEntity productProposalApprovalStatusEntity = productProposalApprovalDao.selectById(resetId);

        if (productProposalApprovalStatusEntity != null) {
            productProposalApprovalStatusEntity.setDealTag(true);
            productProposalApprovalDao.updateById(productProposalApprovalStatusEntity);
        }
    }

    public void dealAllTagForProductProposal(Long proposalId) {
        productProposalApprovalDao.dealAllTagForProductProposal(proposalId);
    }

    public void deleteProductProposalApproveHistory(Long id) {
        productProposalApprovalDao.deleteProductProposalApproveHistory(id);
    }
}
