package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_dept")
public class DeptDO {


	@TableId(type = IdType.AUTO)
	private Long deptId;

	@TableField("parent_id")
	private Long parentId;

	@TableField("name")
	private String name;

	@TableField("order_num")
	private Integer orderNum;

	@TableField("del_flag")
	private Integer delFlag;


}
