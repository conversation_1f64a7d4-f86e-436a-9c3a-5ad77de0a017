package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023年09月07日 13:04
 */
@Data
@TableName("order_number_information")
public class OrderNumberInformationEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("年月")
    @TableField("`year_month`")
    private String yearMonth;
    @ApiModelProperty("合同单量")
    private String contractOrderNumber;
    @ApiModelProperty("公户单量")
    private String surnameOrderNumber;
    @ApiModelProperty("其他")
    private String other;
    @ApiModelProperty("状态")
    private Integer status;
}
