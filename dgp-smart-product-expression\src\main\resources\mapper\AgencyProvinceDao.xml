<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.AgencyProvinceDao">

    <select id="catchAllAgency" resultType="java.lang.String">
        select distinct clique from df_chl_sup_d_investor_manage where province = #{province} and city = #{city}  group by clique
    </select>

    <select id="catchAllProvince" resultType="com.gwmfc.bo.ProvinceCityBo">
        select province,city from df_chl_sup_d_investor_manage
    </select>

    <select id="getManagerStaffNo" resultType="java.lang.String">
        select
            region_account
        from
            df_chl_sup_d_investor_manage
        where
            province = #{province}
          and city = #{city}
        group by
            region_account
    </select>

</mapper>