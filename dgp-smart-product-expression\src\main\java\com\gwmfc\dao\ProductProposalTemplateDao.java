package com.gwmfc.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwmfc.entity.ProductProposalTemplateEntity;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年10月18日 16:07
 */
@Mapper
public interface ProductProposalTemplateDao extends BaseMapper<ProductProposalTemplateEntity> {
    List<ProductProposalTemplateEntity> selectTemplateName(Page<ProductProposalTemplateEntity> page, String name, Integer businessType, String createUser, LocalDateTime createTime);

    Integer selectTemplateCount(String name, Integer businessType, String createUser, LocalDateTime createTime);

    Integer selectTemplateCountByName(String name);

    List<ProductProposalTemplateEntity> selectTemplateDetail(String name);
}
