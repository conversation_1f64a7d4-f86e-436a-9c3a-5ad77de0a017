package com.gwmfc.util;

/**
 * <AUTHOR>
 * @Classname JwtUtil
 * @Description TODO
 * @Date 2021/5/19 13:21
 */

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.gwmfc.domain.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.util.*;



public class JwtUtil {
    /**
     *  默认过期时间
     */

    private static final long EXPIRE_TIME = 60 * 60 * 1000;




    /**
     * 盐
     */
    private static final String TOKEN_SECRET = "bigdata_salt";

    public static  final Logger logger = LoggerFactory.getLogger(JwtUtil.class);
    /**
     * @param **username**
     * @param **password**
     * @return
     */
    public static String signData(String data) throws Exception {

        Date date = new Date(System.currentTimeMillis() + EXPIRE_TIME);
        Algorithm algorithm = Algorithm.HMAC256(TOKEN_SECRET);
        Map<String, Object> header = new HashMap<>(2);
        header.put("Type", "Jwt");
        header.put("alg", "HS256");
            // 返回token字符串
        return JWT.create()
                .withHeader(header)
                .withClaim("data", data)
                .withExpiresAt(date)
                .sign(algorithm);
    }

    /**
     * 登录后生成 com.gwmfc.jwt token
     * @param userDto
     * @return
     * @throws Exception
     */

    public static String signUser(User userDto) throws Exception {
        return signUserWithExpireTime(userDto,EXPIRE_TIME);
    }



    public static String signUserWithExpireTime(User userDto, Long expireTime) throws Exception {
        Date date = new Date(System.currentTimeMillis() + expireTime);
        Algorithm algorithm = Algorithm.HMAC256(TOKEN_SECRET);
        Map<String, Object> header = new HashMap<>(2);
        header.put("Type", "Jwt");
        header.put("alg", "HS256");
        JWTCreator.Builder builder = construct(userDto,expireTime);
        return builder.withExpiresAt(date).sign(algorithm);
    }


    public static String signUserWithExpireTime(User userDto, Long expireTime,String source) throws Exception {
        Date date = new Date(System.currentTimeMillis() + expireTime);
        Algorithm algorithm = Algorithm.HMAC256(TOKEN_SECRET);
        JWTCreator.Builder builder = construct(userDto,expireTime);
        builder.withClaim("source",source);
        return builder.withExpiresAt(date).sign(algorithm);
    }
    
    public static JWTCreator.Builder construct(User userDto, Long expireTime){
        Map<String, Object> header = new HashMap<>(2);
        header.put("Type", "Jwt");
        header.put("alg", "HS256");

        JWTCreator.Builder builder = JWT.create().withHeader(header);
        if(userDto.getRank() != null){
            builder.withClaim("rank",userDto.getRank().toString());
        }
        if(userDto.getStaffNo()!=null){
            builder.withClaim("staffNo",userDto.getStaffNo());
        }
        if(userDto.getId()!=null){
            builder.withClaim("id",userDto.getId().toString());
        }
        if(userDto.getDeptId()!=null){
            builder.withClaim("deptId",userDto.getDeptId().toString());
        }
        if(userDto.getConfluencePassword()!=null){
            builder.withClaim("confluencePassword",userDto.getConfluencePassword());
        }
        builder.withClaim("name",userDto.getName());
        builder.withClaim("userName",userDto.getUserName());
        return  builder;
    }
    


    public static User decodeUser(String token) throws Exception {
        User user = new User();
        Algorithm algorithm = Algorithm.HMAC256(TOKEN_SECRET);
        JWTVerifier verifier = JWT.require(algorithm).build();
        DecodedJWT jwt = verifier.verify(token);
        if(!jwt.getClaim("rank").isNull()){
            user.setRank(Integer.parseInt(jwt.getClaim("rank").asString()));
        }
        if(!jwt.getClaim("staffNo").isNull()){
            user.setStaffNo(jwt.getClaim("staffNo").asString());
        }
        if(!jwt.getClaim("dingNo").isNull()){
            user.setDingNo(jwt.getClaim("dingNo").asString());
        }
        if(!jwt.getClaim("id").isNull()){
            user.setId(Long.parseLong(jwt.getClaim("id").asString()));
        }

        if(!jwt.getClaim("deptId").isNull()){
            user.setDeptId(Long.parseLong(jwt.getClaim("deptId").asString()));
        }
        if(!jwt.getClaim("confluencePassword").isNull()){
            user.setConfluencePassword(jwt.getClaim("confluencePassword").asString());
        }
        user.setName(jwt.getClaim("name").asString());
        user.setUserName(jwt.getClaim("userName").asString());

        return user;
    }


    /**
     * 检验token是否正确
     * @param **token**
     * @return
     */
    public static String verify(String token) throws Exception {
        Algorithm algorithm = Algorithm.HMAC256(TOKEN_SECRET);
        JWTVerifier verifier = JWT.require(algorithm).build();
        DecodedJWT jwt = verifier.verify(token);
        return jwt.getClaim("data").asString();

    }


    public static String decode(String token,String name) throws Exception {
        Algorithm algorithm = Algorithm.HMAC256(TOKEN_SECRET);
        JWTVerifier verifier = JWT.require(algorithm).build();
        DecodedJWT jwt = verifier.verify(token);
        return jwt.getClaim(name).asString();
    }

}