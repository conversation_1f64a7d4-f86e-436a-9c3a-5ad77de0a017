package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname ProductProposalResultWordDto
 * @Description 方案IRR结果word文档下载dto
 * @Date 2023/10/31 17:01
 */
@Data
@ApiModel(value = "方案IRR结果word文档下载dto")
public class ProductProposalResultWordDto {
    private String groupName;
    private String groupWeightingIrr;
    private String groupProportion;
    private String firstEleValue;
    private List<String> addElementValues;
    private String loanMoney;
    private String customerInterestRate;
    private String actualInterestRate;
    private String timeLimit;
    private String dealerBasicCommissionRatio;
    private String specialReward;
    private String lossRate;
    private String fixedCost;
    private String resultIrr;
    private String resultRoa;
    private String resultNetProfitMoney;
    private String contractProportion;
    private String weightingIrr;
    private Integer index;
    private Map<String,String> strMap = new HashMap<>();

    public ProductProposalResultWordDto() {
    }
}
