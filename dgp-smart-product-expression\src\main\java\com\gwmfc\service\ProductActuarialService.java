package com.gwmfc.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.dozermapper.core.DozerBeanMapperBuilder;
import com.github.dozermapper.core.Mapper;
import com.gwmfc.constant.EarlySquareRatioRulePamEnum;
import com.gwmfc.dao.*;
import com.gwmfc.domain.User;
import com.gwmfc.dto.*;
import com.gwmfc.entity.*;
import com.gwmfc.service.calV2.ProductCalculateV2Service;
import com.gwmfc.util.DynamicDataListener;
import com.gwmfc.util.MergeColumnCellWriteHandler;
import com.gwmfc.util.Result;
import com.gwmfc.util.RuleExecutorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @Classname ProductActuarialService
 * @Description TODO
 * @Date 2025/4/21 13:35
 */
@Service
@Slf4j
@RefreshScope
public class ProductActuarialService {

    @Resource(name = "prestoKafkaTemplate")
    private JdbcTemplate prestoTemplate;

    @Autowired
    private ProductActuarialBpRewardTypeDao productActuarialBpRewardTypeDao;

    @Autowired
    private ProductActuarialBpPolicyTemplateDao productActuarialBpPolicyTemplateDao;

    @Autowired
    private ProductActuarialBpPolicyTemplateAssembleDimensionDetailDao productActuarialBpPolicyTemplateAssembleDimensionDetailDao;

    @Autowired
    private ProductActuarialBpCalPlanAssembleDimensionDetailDao productActuarialBpCalPlanAssembleDimensionDetailDao;

    @Autowired
    private ProductActuarialBpCalPlanMainDao productActuarialBpCalPlanMainDao;

    @Autowired
    private ProductActuarialBpCalPlanIndexDao productActuarialBpCalPlanIndexDao;

    @Autowired
    private ProductProposqlCalDao productProposqlCalDao;

    @Autowired
    private ProductProposalCalService productProposalCalService;

    @Autowired
    private ProductCalculateV2Service productCalculateV2Service;

    @Autowired
    private ProductCalBasicInfoDao productCalBasicInfoDao;

    @Autowired
    private ProductCalEarlySquareParamSubDao productCalEarlySquareParamSubDao;

    @Value("${productActuarial.bp-earlySquare}")
    private String bpEarlySquareSql;

    @Value("${productActuarial.bp-avgLoanAmt}")
    private String bpAvgLoanAmt;

    @Value("${productActuarial.bp-avgCustomerRte}")
    private String bpAvgCustomerRte;

    @Value("${productActuarial.bp-avgRealRte}")
    private String bpAvgRealRte;

    @Value("${productActuarial.bp-avgLossRte}")
    private String bpAvgLossRte;

    @Value("${productActuarial.bp-templateColumn}")
    private List<String> bpTemplateColumn;

    @Value("#{${productActuarial.bp-overduerate}}")
    private Map<Integer, Double> bpOverdueRate;

    public Result getProductActuarialBasicConfig(Integer type) {
        /**
         * 根据类型分类，使用presto查询数仓dm.dm_sales_actuarial_bp表
         */
        Result result = new Result();
        String sql = "";
        SqlRowSet resultSet = null;
        switch (type) {
            case 1:
                // 获取产品精算基础配置-产品类型
                sql = "select distinct product_type,p_data_dt  from hive.dm.dm_sales_actuarial_bp";
                break;
            case 2:
                // 获取产品精算基础配置-产品类型细分
                sql = "select distinct product_type_sub,p_data_dt  from hive.dm.dm_sales_actuarial_bp";
                break;
            case 3:
                // 获取产品精算基础配置-贷期
                sql = "select distinct loan_trm,p_data_dt  from hive.dm.dm_sales_actuarial_bp order by loan_trm asc";
                break;
            case 4:
                // 获取产品精算基础配置-品牌
                sql = "select distinct brand_name,p_data_dt  from hive.dm.dm_sales_actuarial_bp";
                break;
            case 5:
                // 获取产品精算基础配置-车型
                sql = "select distinct map_vehicle_model,p_data_dt  from hive.dm.dm_sales_actuarial_bp";
                break;
            case 6:
                // 获取产品精算基础配置-投资人
                sql = "select distinct investor,p_data_dt  from hive.dm.dm_sales_actuarial_bp";
                break;
            default:
                result.setCode(FAIL.getCode());
                result.setMessage("type is not exist");
                return result;
        }

        resultSet = prestoTemplate.queryForRowSet(sql);

        List<Map<String, Object> > list = new ArrayList<>();

        if(resultSet != null){
            while (resultSet.next()){
                Map<String, Object> map = new HashMap<>();
                if(type == 1){
                    map.put("product_type",resultSet.getString("product_type"));
                }else if (type == 2){
                    map.put("product_type_sub",resultSet.getString("product_type_sub"));
                }else if (type == 3){
                    map.put("loan_trm",resultSet.getString("loan_trm"));
                }else if (type == 4){
                    map.put("brand_name",resultSet.getString("brand_name"));
                }else if (type == 5){
                    map.put("map_vehicle_model",resultSet.getString("map_vehicle_model"));
                }else if (type == 6){
                    map.put("investor",resultSet.getString("investor"));
                }
                map.put("p_data_dt",resultSet.getString("p_data_dt"));
                list.add(map);
            }
        }
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        result.setData(list);
        return result;
    }

    public Result getProductActuarialRewardTypeList(ProductActuarialBpRewardTypeEntity productActuarialBpRewardTypeEntity, Integer current, Integer size) {
        Result result = new Result();
        try{
            IPage<ProductActuarialBpRewardTypeEntity> page = new Page<>(current, size);

            QueryWrapper queryWrapper = new QueryWrapper(productActuarialBpRewardTypeEntity);
            queryWrapper.orderByDesc("id");

            IPage<ProductActuarialBpRewardTypeEntity> iPage = productActuarialBpRewardTypeDao.selectPage(page,queryWrapper);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(iPage.getRecords());
            result.setTotal(iPage.getTotal());
        }catch (Exception e){
            log.error("get productActuarialRewardTypeEntity list error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("get productActuarialRewardTypeEntity list error");
        }
        return result;
    }

    public Result saveProductActuarialRewardType(ProductActuarialBpRewardTypeEntity productActuarialBpRewardTypeEntity, User user){
        Result result = new Result();
        try{
            /**
             * 默认1-启用
             */
            Date now = new Date();
            productActuarialBpRewardTypeEntity.setStatus(1);
            productActuarialBpRewardTypeEntity.setCreateUser(user.getUserName());
            productActuarialBpRewardTypeEntity.setCreateTime(now);
            productActuarialBpRewardTypeEntity.setEffectiveTime(now);
            productActuarialBpRewardTypeDao.insert(productActuarialBpRewardTypeEntity);

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("save productActuarialRewardTypeEntity error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("save productActuarialRewardTypeEntity error");
        }
        return result;
    }

    public Result deleteProductActuarialRewardType(Long id){
        Result result = new Result();
        try{
            productActuarialBpRewardTypeDao.deleteById(id);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("delete productActuarialRewardTypeEntity error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("delete productActuarialRewardTypeEntity error");
        }
        return result;
    }

    public Result updateProductActuarialRewardType(ProductActuarialBpRewardTypeEntity productActuarialBpRewardTypeEntity, User user){
        Result result = new Result();
        try{
            productActuarialBpRewardTypeEntity.setUpdateUser(user.getUserName());
            productActuarialBpRewardTypeEntity.setUpdateTime(new Date());
            productActuarialBpRewardTypeDao.updateById(productActuarialBpRewardTypeEntity);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("update productActuarialRewardTypeEntity error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("update productActuarialRewardTypeEntity error");
        }
        return result;
    }

    public Result updateStatus(Integer status, Long id, User user){
        Result result = new Result();
        try{
            ProductActuarialBpRewardTypeEntity productActuarialBpRewardTypeEntity = productActuarialBpRewardTypeDao.selectById(id);
            if(productActuarialBpRewardTypeEntity != null){
                Date now = new Date();
                productActuarialBpRewardTypeEntity.setStatus(status);
                productActuarialBpRewardTypeEntity.setUpdateUser(user.getUserName());
                productActuarialBpRewardTypeEntity.setUpdateTime(now);
                if(status == 0){
                    productActuarialBpRewardTypeEntity.setEndTime(now);
                }else if(status == 1){
                    productActuarialBpRewardTypeEntity.setEffectiveTime(now);
                }
                productActuarialBpRewardTypeDao.updateById(productActuarialBpRewardTypeEntity);
                result.setCode(SUCCESS.getCode());
                result.setMessage(SUCCESS.getDesc());
            }
        }catch(Exception e){
            log.error("update productActuarialRewardTypeEntity error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("update productActuarialRewardTypeEntity status error");
        }
        return result;
    }

    public Result saveOrUpdateProductActuarialPolicyTemplate(ProductActuarialBpPolicyTemplateDto productActuarialBpPolicyTemplateDto, User user){
        Result result = new Result();
        try{
            Date now = new Date();
            ProductActuarialBpPolicyTemplateEntity productActuarialBpPolicyTemplateEntity = new ProductActuarialBpPolicyTemplateEntity();
            BeanUtils.copyProperties(productActuarialBpPolicyTemplateDto,productActuarialBpPolicyTemplateEntity);
            if(productActuarialBpPolicyTemplateDto.getFirstDimensionContentList() != null && productActuarialBpPolicyTemplateDto.getFirstDimensionContentList().size() > 0){
                productActuarialBpPolicyTemplateEntity.setFirstDimensionContent(StringUtils.join(productActuarialBpPolicyTemplateDto.getFirstDimensionContentList(),","));
            }
            if(productActuarialBpPolicyTemplateDto.getSecondDimensionContentList() != null && productActuarialBpPolicyTemplateDto.getSecondDimensionContentList().size() > 0){
                productActuarialBpPolicyTemplateEntity.setSecondDimensionContent(StringUtils.join(productActuarialBpPolicyTemplateDto.getSecondDimensionContentList(),","));
            }
            if(productActuarialBpPolicyTemplateDto.getThirdDimensionContentList() != null && productActuarialBpPolicyTemplateDto.getThirdDimensionContentList().size() > 0){
                productActuarialBpPolicyTemplateEntity.setThirdDimensionContent(StringUtils.join(productActuarialBpPolicyTemplateDto.getThirdDimensionContentList(),","));
            }
            if(productActuarialBpPolicyTemplateDto.getForthDimensionContentList() != null && productActuarialBpPolicyTemplateDto.getForthDimensionContentList().size() > 0){
                productActuarialBpPolicyTemplateEntity.setForthDimensionContent(StringUtils.join(productActuarialBpPolicyTemplateDto.getForthDimensionContentList(),","));
            }
            if(productActuarialBpPolicyTemplateDto.getFifthDimensionContentList() != null && productActuarialBpPolicyTemplateDto.getFifthDimensionContentList().size() > 0){
                productActuarialBpPolicyTemplateEntity.setFifthDimensionContent(StringUtils.join(productActuarialBpPolicyTemplateDto.getFifthDimensionContentList(),","));
            }
            if(productActuarialBpPolicyTemplateDto.getSixthDimensionContentList() != null && productActuarialBpPolicyTemplateDto.getSixthDimensionContentList().size() > 0){
                productActuarialBpPolicyTemplateEntity.setSixthDimensionContent(StringUtils.join(productActuarialBpPolicyTemplateDto.getSixthDimensionContentList(),","));
            }

            if(productActuarialBpPolicyTemplateEntity.getId() != null){
                log.error("update PolicyTemplate");
                productActuarialBpPolicyTemplateEntity.setUpdateUser(user.getUserName());
                productActuarialBpPolicyTemplateEntity.setUpdateTime(now);
                productActuarialBpPolicyTemplateDao.updateById(productActuarialBpPolicyTemplateEntity);
                /**
                 * 删除详情表信息
                 */
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("template_id",productActuarialBpPolicyTemplateEntity.getId());
                productActuarialBpPolicyTemplateAssembleDimensionDetailDao.delete(queryWrapper);
            }else{
                log.error("save PolicyTemplate");
                /**
                 * 默认生效
                 */
                productActuarialBpPolicyTemplateEntity.setStatus(1);
                productActuarialBpPolicyTemplateEntity.setEffectiveTime(now);
                productActuarialBpPolicyTemplateEntity.setCreateTime(now);
                productActuarialBpPolicyTemplateEntity.setCreateUser(user.getUserName());
                productActuarialBpPolicyTemplateEntity.setUpdateTime(null);
                productActuarialBpPolicyTemplateEntity.setUpdateUser(null);
                productActuarialBpPolicyTemplateDao.insert(productActuarialBpPolicyTemplateEntity);
            }

            List<ProductActuarialBpPolicyTemplateAssembleDimensionDetailEntity> productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList = productActuarialBpPolicyTemplateDto.getProductActuarialBpPolicyTemplateAssembleDimensionDetailEntityList();
            if(productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList != null && productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList.size() > 0){
                for (ProductActuarialBpPolicyTemplateAssembleDimensionDetailEntity productActuarialBpPolicyTemplateAssembleDimensionDetailEntity : productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList) {
                    productActuarialBpPolicyTemplateAssembleDimensionDetailEntity.setTemplateId(productActuarialBpPolicyTemplateEntity.getId());
                    productActuarialBpPolicyTemplateAssembleDimensionDetailEntity.setCreateTime(now);
                    productActuarialBpPolicyTemplateAssembleDimensionDetailEntity.setCreateUser(user.getUserName());
                    productActuarialBpPolicyTemplateAssembleDimensionDetailDao.insert(productActuarialBpPolicyTemplateAssembleDimensionDetailEntity);
                }
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("save productActuarialPolicyTemplateEntity error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("save productActuarialPolicyTemplateEntity error");
        }
        return result;
    }

    public Result getProductActuarialPolicyTemplateList(ProductActuarialBpPolicyTemplateEntity productActuarialBpPolicyTemplateEntity, Integer current, Integer size) {
        Result result = new Result<>();
        try {
            IPage<ProductActuarialBpPolicyTemplateEntity> page = new Page<>(current, size);

            QueryWrapper queryWrapper = new QueryWrapper(productActuarialBpPolicyTemplateEntity);
            queryWrapper.orderByDesc("id");

            IPage<ProductActuarialBpRewardTypeEntity> iPage = productActuarialBpPolicyTemplateDao.selectPage(page,queryWrapper);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(iPage.getRecords());
            result.setTotal(iPage.getTotal());
        }catch (Exception e){
            log.error("get productActuarialPolicyTemplateEntity error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("get productActuarialPolicyTemplateEntity error");
        }
        return result;
    }

    public Result getProductActuarialPolicyTemplateDetail(Long id){
        Result result = new Result<>();
        try {
            ProductActuarialBpPolicyTemplateDto productActuarialBpPolicyTemplateDto = new ProductActuarialBpPolicyTemplateDto();
            /**
             * 查询模板详情
             */
            ProductActuarialBpPolicyTemplateEntity productActuarialBpPolicyTemplateEntity = productActuarialBpPolicyTemplateDao.selectById(id);
            if(productActuarialBpPolicyTemplateEntity != null){
                BeanUtils.copyProperties(productActuarialBpPolicyTemplateEntity,productActuarialBpPolicyTemplateDto);
                if(StringUtils.isNotEmpty(productActuarialBpPolicyTemplateEntity.getFirstDimensionContent())){
                    productActuarialBpPolicyTemplateDto.setFirstDimensionContentList(Arrays.asList(productActuarialBpPolicyTemplateEntity.getFirstDimensionContent().split(",")));
                }
                if(StringUtils.isNotEmpty(productActuarialBpPolicyTemplateEntity.getSecondDimensionContent())){
                    productActuarialBpPolicyTemplateDto.setSecondDimensionContentList(Arrays.asList(productActuarialBpPolicyTemplateEntity.getSecondDimensionContent().split(",")));
                }
                if(StringUtils.isNotEmpty(productActuarialBpPolicyTemplateEntity.getThirdDimensionContent())){
                    productActuarialBpPolicyTemplateDto.setThirdDimensionContentList(Arrays.asList(productActuarialBpPolicyTemplateEntity.getThirdDimensionContent().split(",")));
                }
                if(StringUtils.isNotEmpty(productActuarialBpPolicyTemplateEntity.getForthDimensionContent())){
                    productActuarialBpPolicyTemplateDto.setForthDimensionContentList(Arrays.asList(productActuarialBpPolicyTemplateEntity.getForthDimensionContent().split(",")));
                }
                if(StringUtils.isNotEmpty(productActuarialBpPolicyTemplateEntity.getFifthDimensionContent())){
                    productActuarialBpPolicyTemplateDto.setFifthDimensionContentList(Arrays.asList(productActuarialBpPolicyTemplateEntity.getFifthDimensionContent().split(",")));
                }
                if(StringUtils.isNotEmpty(productActuarialBpPolicyTemplateEntity.getSixthDimensionContent())){
                    productActuarialBpPolicyTemplateDto.setSixthDimensionContentList(Arrays.asList(productActuarialBpPolicyTemplateEntity.getSixthDimensionContent().split(",")));
                }
            }

            /**
             * 查询组合维度详情
             */
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("template_id",id);
//            queryWrapper.eq("assemble_dimension_type","basic");
            List<ProductActuarialBpPolicyTemplateAssembleDimensionDetailEntity> productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList = productActuarialBpPolicyTemplateAssembleDimensionDetailDao.selectList(queryWrapper);
            productActuarialBpPolicyTemplateDto.setProductActuarialBpPolicyTemplateAssembleDimensionDetailEntityList(productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList);

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productActuarialBpPolicyTemplateDto);
        }catch (Exception e){
            log.error("get productActuarialPolicyTemplateEntity error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("get productActuarialPolicyTemplateEntity error");
        }
        return result;
    }

    public Result deleteProductActuarialPolicyTemplate(Long id){
        Result result = new Result<>();
        try {
            /**
             * 删除前需要确认该模板是否已经被引用，如果已经被引用则不允许删除
             */
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("template_id",id);
            Integer count = productActuarialBpCalPlanMainDao.selectCount(queryWrapper);
            if(count > 0){
                result.setCode(FAIL.getCode());
                result.setMessage("该模板已经被引用，不允许删除");
                return result;
            }

            productActuarialBpPolicyTemplateDao.deleteById(id);
            productActuarialBpPolicyTemplateAssembleDimensionDetailDao.delete(queryWrapper);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("delete productActuarialPolicyTemplateEntity error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("delete productActuarialPolicyTemplateEntity error");
        }
        return result;
    }

    public Result updatePolicyTemplateStatus(Integer status, Long id, User user){
        Result result = new Result<>();
        try {
            ProductActuarialBpPolicyTemplateEntity productActuarialBpPolicyTemplateEntity = productActuarialBpPolicyTemplateDao.selectById(id);
            if(productActuarialBpPolicyTemplateEntity != null){
                Date now = new Date();
                productActuarialBpPolicyTemplateEntity.setStatus(status);
                productActuarialBpPolicyTemplateEntity.setUpdateUser(user.getUserName());
                productActuarialBpPolicyTemplateEntity.setUpdateTime(now);
                if(status == 0){
                    productActuarialBpPolicyTemplateEntity.setEndTime(now);
                }else if(status == 1){
                    productActuarialBpPolicyTemplateEntity.setEffectiveTime(now);
                }
                productActuarialBpPolicyTemplateDao.updateById(productActuarialBpPolicyTemplateEntity);
                result.setCode(SUCCESS.getCode());
                result.setMessage(SUCCESS.getDesc());
            }
        }catch (Exception e){
            log.error("update productActuarialPolicyTemplateEntity error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("update productActuarialPolicyTemplateEntity error");
         }
        return result;
    }

    public void downloadPolicyTemplate(Long id, HttpServletResponse response){
        try {
            /**
             * 根据id查询组合模板详情信息
             */
            ProductActuarialBpPolicyTemplateEntity productActuarialBpPolicyTemplateEntity = productActuarialBpPolicyTemplateDao.selectById(id);

            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("template_id",id);
            queryWrapper.orderByAsc("assemble_dimension_num","assemble_dimension_grade");
            List<ProductActuarialBpPolicyTemplateAssembleDimensionDetailEntity> productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList = productActuarialBpPolicyTemplateAssembleDimensionDetailDao.selectList(queryWrapper);
            if(productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList != null && productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList.size()>0){
                /**
                 * productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList 按照assemble_dimension_num进行分组，生成新的集合
                 */
                Map<Integer, List<ProductActuarialBpPolicyTemplateAssembleDimensionDetailEntity>> numDataList = productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList.stream().collect(Collectors.groupingBy(ProductActuarialBpPolicyTemplateAssembleDimensionDetailEntity::getAssembleDimensionNum));
                List<String> columnNames = new ArrayList<>();
                List<List<Object>> dataListNew = new ArrayList<>();
                numDataList.keySet().forEach(num -> {
                    List<Object> data = new ArrayList<>();
                    List<ProductActuarialBpPolicyTemplateAssembleDimensionDetailEntity> assembleDimensionDetailEntityList = numDataList.get(num);
                    for (ProductActuarialBpPolicyTemplateAssembleDimensionDetailEntity assembleDimensionDetailEntity : assembleDimensionDetailEntityList) {
                        data.add(assembleDimensionDetailEntity.getAssembleDimensionContent());
                        if(num == 0){
                            columnNames.add(assembleDimensionDetailEntity.getAssembleDimensionName());
                        }
                    }
                    dataListNew.add(data);
                });

                log.info("dataListNew: {}", dataListNew);

                QueryWrapper queryWrapperBpRewardType = new QueryWrapper();
                queryWrapperBpRewardType.eq("status",1);
                List<String> basic = new ArrayList<>();
                basic.addAll(bpTemplateColumn);
                List<ProductActuarialBpRewardTypeEntity> productActuarialBpRewardTypeEntityList = productActuarialBpRewardTypeDao.selectList(queryWrapperBpRewardType);
                if(productActuarialBpRewardTypeEntityList != null && productActuarialBpRewardTypeEntityList.size()>0){
                    List<String> collect = productActuarialBpRewardTypeEntityList.stream().map(ProductActuarialBpRewardTypeEntity::getName).collect(Collectors.toList());
                    basic.addAll(collect);
                }
                columnNames.addAll(basic);
                log.info("columnNames: {}", columnNames);

                String fileName = productActuarialBpPolicyTemplateEntity.getPolicyTemplateName();
                fileName = URLEncoder.encode(fileName, "UTF-8");
                fileName = fileName+".xlsx";

                response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
                response.setHeader("Content-disposition", "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);
                response.setCharacterEncoding("utf-8");

                int[] mergeColumnIndexArray = new int[columnNames.size()];
                for (int i = 0; i < columnNames.size(); i++) {
                    mergeColumnIndexArray[i] = i;
                }

                OutputStream outputStream = response.getOutputStream();
                try {
                    EasyExcel.write(outputStream)
                            .head(columnNames.stream().map(header -> Arrays.asList(header)).collect(Collectors.toList()))
//                            .registerWriteHandler(new MergeColumnCellWriteHandler(mergeColumnIndexArray))
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .sheet("Sheet1")
                            .doWrite(dataListNew);
                    outputStream.flush();
                }catch (Exception e) {
                    // 处理异常
                    e.printStackTrace();
                    throw new RuntimeException("导出Excel失败", e);
                } finally {
                    // 确保资源关闭
                    try {
                        if (outputStream != null) {
                            outputStream.close();
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }catch (Exception e){
            log.error("downloadPolicyTemplate error",e);
        }
    }

    public Result policyCalBasicInfoSaveOrUpdate(ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity, User user){
        Result result = new Result();
        try{
            Date now = new Date();
            /**
             * 判断是否存在该方案，如果存在，则更新，如果不存在，则新增
             */
            if(productActuarialBpCalPlanMainEntity.getId() != null){
                ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntityOld = productActuarialBpCalPlanMainDao.selectById(productActuarialBpCalPlanMainEntity.getId());
                /**
                 * 判断政策模板是否发生变更，如果发生变更则清空测算组合维度详情表
                 */
                if(!productActuarialBpCalPlanMainEntityOld.getTemplateId().equals(productActuarialBpCalPlanMainEntity.getTemplateId())){
                    QueryWrapper queryWrapper = new QueryWrapper();
                    queryWrapper.eq("cal_id",productActuarialBpCalPlanMainEntity.getId());
                    productActuarialBpCalPlanAssembleDimensionDetailDao.delete(queryWrapper);

                    /**
                     * 指标表删除
                     */
                    QueryWrapper queryWrapperCalPlan = new QueryWrapper();
                    queryWrapperCalPlan.eq("cal_id",productActuarialBpCalPlanMainEntity.getId());
                    productActuarialBpCalPlanIndexDao.delete(queryWrapperCalPlan);

                    productActuarialBpCalPlanMainEntity.setPerProfit(null);
                    productActuarialBpCalPlanMainEntity.setContractAmount(null);
                    productActuarialBpCalPlanMainEntity.setRebateCoefficient(null);
                    productActuarialBpCalPlanMainEntity.setTotalProfit(null);
                    productActuarialBpCalPlanMainEntity.setStatus(2);
                }
                productActuarialBpCalPlanMainEntity.setUpdateTime(now);
                productActuarialBpCalPlanMainEntity.setUpdateUser(user.getName());
                productActuarialBpCalPlanMainDao.updateById(productActuarialBpCalPlanMainEntity);
            }else{
                productActuarialBpCalPlanMainEntity.setStatus(2);//草稿状态
                productActuarialBpCalPlanMainEntity.setCreateTime(now);
                productActuarialBpCalPlanMainEntity.setCreateUser(user.getName());
                productActuarialBpCalPlanMainEntity.setProcess("baseInfo");
                productActuarialBpCalPlanMainDao.insert(productActuarialBpCalPlanMainEntity);

                /**
                 * 拿到主键ID，生成方案编号 BP-ID
                 */
                productActuarialBpCalPlanMainEntity.setPlanNum("BP-"+productActuarialBpCalPlanMainEntity.getId());
                productActuarialBpCalPlanMainDao.updateById(productActuarialBpCalPlanMainEntity);

            }
            result.setCode(SUCCESS.getCode());
            result.setMessage("policyCalBasicInfoSaveOrUpdate success");
            result.setData(productActuarialBpCalPlanMainEntity);
        }catch (Exception e){
            log.error("policyCalBasicInfoSaveOrUpdate error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("policyCalBasicInfoSaveOrUpdate error");
        }
        return result;
    }

    public Result getPolicyCalBasicInfoList(ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity, Integer current, Integer size){
        Result result = new Result();
        try{
            IPage<ProductActuarialBpCalPlanMainEntity> page = new Page<>(current, size);
            QueryWrapper queryWrapper = new QueryWrapper(productActuarialBpCalPlanMainEntity);
            queryWrapper.orderByDesc("id");

            IPage<ProductActuarialBpCalPlanMainEntity> iPage = productActuarialBpCalPlanMainDao.selectPage(page,queryWrapper);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(iPage.getRecords());
            result.setTotal(iPage.getTotal());
        }catch (Exception e){
            log.error("getPolicyCalBasicInfoList error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("getPolicyCalBasicInfoList error");
        }
        return result;
    }

    public Result updatePolicyCalBasicInfoStatus(Integer status, Long id, User user){
        Result result = new Result();
        try{
            ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity = productActuarialBpCalPlanMainDao.selectById(id);
            if(productActuarialBpCalPlanMainEntity != null){
                Date now = new Date();
                productActuarialBpCalPlanMainEntity.setStatus(status);
                productActuarialBpCalPlanMainEntity.setUpdateUser(user.getUserName());
                productActuarialBpCalPlanMainEntity.setUpdateTime(now);
                if(status == 0){
                    productActuarialBpCalPlanMainEntity.setEndTime(now);
                }else if(status == 1){
                    productActuarialBpCalPlanMainEntity.setEffectiveTime(now);
                }
                productActuarialBpCalPlanMainDao.updateById(productActuarialBpCalPlanMainEntity);
                result.setCode(SUCCESS.getCode());
                result.setMessage(SUCCESS.getDesc());
            }
        }catch(Exception e){
            log.error("update productActuarialRewardTypeEntity error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("update productActuarialRewardTypeEntity status error");
        }
        return result;
    }

    public Result deletePolicyCal(Long id){
        Result result = new Result();
        try{
            /**
             * 先删除基础信息表
             */
            productActuarialBpCalPlanMainDao.deleteById(id);

            /**
             * 再删除详情表
             */
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("cal_id",id);
            productActuarialBpCalPlanAssembleDimensionDetailDao.delete(queryWrapper);

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch(Exception e){
            log.error("delete productActuarialRewardTypeEntity error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("delete productActuarialRewardTypeEntity error");
        }
        return result;
    }

    public Result policyCalCoefficientSupplement(Long templateId, MultipartFile file){
        Result result = new Result();
        try{
            /**
             * 系数补录，首先要生成测算主表，拿到主表的id
             */
            List<String> basicColumnList = new ArrayList<>();
            ProductActuarialBpPolicyTemplateEntity productActuarialBpPolicyTemplateEntity = productActuarialBpPolicyTemplateDao.selectById(templateId);
            if(productActuarialBpPolicyTemplateEntity != null){
                if(productActuarialBpPolicyTemplateEntity.getFirstDimensionName() != null){
                    basicColumnList.add(productActuarialBpPolicyTemplateEntity.getFirstDimensionName());
                }

                if(productActuarialBpPolicyTemplateEntity.getSecondDimensionName() != null){
                    basicColumnList.add(productActuarialBpPolicyTemplateEntity.getSecondDimensionName());
                }

                if(productActuarialBpPolicyTemplateEntity.getThirdDimensionName() != null){
                    basicColumnList.add(productActuarialBpPolicyTemplateEntity.getThirdDimensionName());
                }

                if(productActuarialBpPolicyTemplateEntity.getForthDimensionName() != null){
                    basicColumnList.add(productActuarialBpPolicyTemplateEntity.getForthDimensionName());
                }

                if(productActuarialBpPolicyTemplateEntity.getFifthDimensionName() != null){
                    basicColumnList.add(productActuarialBpPolicyTemplateEntity.getFifthDimensionName());
                }

                if(productActuarialBpPolicyTemplateEntity.getSixthDimensionName() != null){
                    basicColumnList.add(productActuarialBpPolicyTemplateEntity.getSixthDimensionName());
                }
            }

            /**
             * 解析导入的excel，拿到每一行的数据，去除模板固定维度数据后的数据保存到product_actuarial_bp_cal_assemble_dimension_detail表中
             */
            DynamicDataListener listener = new DynamicDataListener();
            InputStream inputStream = file.getInputStream();
            EasyExcel.read(inputStream, listener).sheet().doRead();

            List<Map<String, String>> dataList = listener.getDataList();
            List<String> columnNames = listener.getColumnNames();
            /**
             * 校验补录文档中的列和模板中的维度选择是否一致，如果不一致，则直接返回失败
             */
            if(!basicColumnList.equals(columnNames.subList(0, basicColumnList.size()))){
                result.setCode(FAIL.getCode());
                result.setMessage("补录文档基础模板维度和选择的模板不一致(列数或者列的顺序)，请确认！");
                return result;
            }

            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalAssembleDimensionDetailEntityList = new ArrayList<>();

            int num = 0;
            for (Map<String, String> data : dataList) {
                for (String columnName : columnNames) {
                    String value = data.get(columnName);
                    ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalAssembleDimensionDetailEntity = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                    productActuarialBpCalAssembleDimensionDetailEntity.setTemplateId(templateId);
                    productActuarialBpCalAssembleDimensionDetailEntity.setAssembleDimensionNum(num);
                    productActuarialBpCalAssembleDimensionDetailEntity.setAssembleDimensionGrade(columnNames.indexOf(columnName));
                    productActuarialBpCalAssembleDimensionDetailEntity.setAssembleDimensionName(columnName);
                    if(basicColumnList.contains(columnName)){
                        productActuarialBpCalAssembleDimensionDetailEntity.setAssembleDimensionType("basic");
                    }else{
                        productActuarialBpCalAssembleDimensionDetailEntity.setAssembleDimensionType("coefficientSupplement");
                    }
                    productActuarialBpCalAssembleDimensionDetailEntity.setAssembleDimensionContent(value);
                    productActuarialBpCalAssembleDimensionDetailEntityList.add(productActuarialBpCalAssembleDimensionDetailEntity);
                }
                num++;
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productActuarialBpCalAssembleDimensionDetailEntityList);
        }catch (Exception e){
            log.error("policyCalCoefficientSupplement error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("policyCalCoefficientSupplement error");
        }
        return result;
    }

    public Result policyCalCoefficientSupplementSave(ProductActuarialBpCalPlanAssembleDimensionDetailDto productActuarialBpCalPlanAssembleDimensionDetailDto, User user){
        Result result = new Result();
        try{
            if(productActuarialBpCalPlanAssembleDimensionDetailDto != null){
                /**
                 * 对于系数补录保存，需要将之前测算的详情表中的该calId的所有数据清空
                 */
                ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity = productActuarialBpCalPlanAssembleDimensionDetailDto.getProductActuarialBpCalPlanMainEntity();
                productActuarialBpCalPlanAssembleDimensionDetailDao.delete(new QueryWrapper<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>().eq("cal_id", productActuarialBpCalPlanMainEntity.getId()));
                productActuarialBpCalPlanIndexDao.delete(new QueryWrapper<ProductActuarialBpCalPlanIndexEntity>().eq("cal_id", productActuarialBpCalPlanMainEntity.getId()));
                List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDto.getProductActuarialBpCalPlanAssembleDimensionDetailEntityList();
                if(productActuarialBpCalPlanAssembleDimensionDetailEntityList != null && productActuarialBpCalPlanAssembleDimensionDetailEntityList.size()>0){
                    for (ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity : productActuarialBpCalPlanAssembleDimensionDetailEntityList) {
                        productActuarialBpCalPlanAssembleDimensionDetailEntity.setCalId(productActuarialBpCalPlanMainEntity.getId());
                        productActuarialBpCalPlanAssembleDimensionDetailEntity.setCreateTime(new Date());
                        productActuarialBpCalPlanAssembleDimensionDetailEntity.setCreateUser(user.getUserName());
                        productActuarialBpCalPlanAssembleDimensionDetailDao.insert(productActuarialBpCalPlanAssembleDimensionDetailEntity);
                    }
                }
                /**
                 * 更新主表process为系数补录coefficientSupplement
                 */
                productActuarialBpCalPlanMainEntity.setPerProfit(null);
                productActuarialBpCalPlanMainEntity.setContractAmount(null);
                productActuarialBpCalPlanMainEntity.setRebateCoefficient(null);
                productActuarialBpCalPlanMainEntity.setTotalProfit(null);
                productActuarialBpCalPlanMainEntity.setStatus(2);
                productActuarialBpCalPlanMainEntity.setProcess("coefficientSupplement");
                productActuarialBpCalPlanMainDao.updateById(productActuarialBpCalPlanMainEntity);
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("policyCalCoefficientSupplementSave error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("policyCalCoefficientSupplementSave error");
        }
        return result;
    }

    public Result getPolicyCalCoefficientSupplement(Long calId){
        Result result = new Result();
        try{
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("cal_id",calId);
            queryWrapper.in("assemble_dimension_type","basic","coefficientSupplement");
            queryWrapper.orderByAsc("assemble_dimension_num","assemble_dimension_grade");
            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapper);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productActuarialBpCalPlanAssembleDimensionDetailEntityList);
        }catch (Exception e){
            log.error("getPolicyCalCoefficientSupplement error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("getPolicyCalCoefficientSupplement error");
        }
        return result;
    }

    public Result calEarlySquare(Long calId){
        Result result = new Result();
        try{
            ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity = productActuarialBpCalPlanMainDao.selectById(calId);

            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("cal_id",calId);
            queryWrapper.eq("assemble_dimension_type","basic");
            queryWrapper.orderByAsc("assemble_dimension_num","assemble_dimension_grade");

            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapper);
            /**
             * 获取贷期信息，并得到最大的贷期
             */
            QueryWrapper queryWrapperMaxLoanTrm = new QueryWrapper();
            queryWrapperMaxLoanTrm.eq("cal_id",calId);
            queryWrapperMaxLoanTrm.eq("assemble_dimension_type","basic");
            queryWrapperMaxLoanTrm.eq("assemble_dimension_name","贷期");
            queryWrapperMaxLoanTrm.orderByDesc("assemble_dimension_content");
            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntityMaxLoanTrm = (ProductActuarialBpCalPlanAssembleDimensionDetailEntity)productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapperMaxLoanTrm).stream().findFirst().get();
            Integer maxLoanTrm = Integer.valueOf(productActuarialBpCalPlanAssembleDimensionDetailEntityMaxLoanTrm.getAssembleDimensionContent());

            /**
             * 集合按assemble_dimension_num分组
             */
            Map<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> map = productActuarialBpCalPlanAssembleDimensionDetailEntityList.stream().collect(Collectors.groupingBy(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionNum));


            ExecutorService executorService = Executors.newFixedThreadPool(5);
            CountDownLatch latch = new CountDownLatch(map.size());
            /**
             * 循环处理每一行集合数据
             */
            for (Map.Entry<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> entry : map.entrySet()){

                executorService.execute(()->{
                    try{
                        calPerEarlySquare(entry,productActuarialBpCalPlanAssembleDimensionDetailEntityList,productActuarialBpCalPlanMainEntity,maxLoanTrm);
                    }catch (Exception e){
                        log.error("calPerEarlySquare error：{}",map.entrySet(),e);
                    }finally  {
                        latch.countDown();
                    }
                });
            }
            executorService.shutdown();
            latch.await();
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productActuarialBpCalPlanAssembleDimensionDetailEntityList);
        }catch (Exception e){
            log.error("calEarlySquare error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("calEarlySquare error");
        }
        return result;
    }

    public void calPerEarlySquare(Map.Entry<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> entry,List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList,ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity,Integer maxLoanTrm){
        Integer key = entry.getKey();
        String sql = bpEarlySquareSql;
        StringBuilder str = new StringBuilder();
        List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> list = entry.getValue();
        for (ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity : list) {
            if("产品类型".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                str.append("AND m.product_type = '"+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append("' ");
            }else if ("产品类型细分".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                str.append("AND m.product_type_sub = '"+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append("' ");
            }else if ("贷期".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                str.append("AND m.loan_trm = "+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append(" ");
            }else if ("投资人".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                str.append("AND m.investor = '"+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append("' ");
            }else if ("品牌".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                str.append("AND m.brand_name = '"+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append("' ");
            }else if ("车型".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                str.append("AND COALESCE(m.map_vehicle_model,'') = '"+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append("' ");
            }
        }

        sql = sql.replaceAll("@param@",str.toString());
        log.info("计算提前结清sql:{}",sql);
        SqlRowSet resultSet = prestoTemplate.queryForRowSet(sql);
        Integer grade = productActuarialBpCalPlanMainEntity.getMaxColumnNum()+1;
        Integer etPaidTrm = 0;
        if(resultSet != null){
            while (resultSet.next()){
                ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionNum(key);
                productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionType("earlySquare");
                productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionGrade(grade);
                /**
                 * 提前结清概率保留6位小数
                 */
                String earlyPayoutRate = resultSet.getString("early_payout_rate");
                BigDecimal earlyPayoutRateBig = new BigDecimal(earlyPayoutRate);
                String earlyPayoutRateScale = earlyPayoutRateBig.setScale(6,BigDecimal.ROUND_HALF_UP).toPlainString();

                productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionContent(earlyPayoutRateScale);
                productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionName(resultSet.getString("et_paid_trm"));
                etPaidTrm = Integer.valueOf(resultSet.getString("et_paid_trm"));
                productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntity);
                grade++;
            }
        }
        /**
         * 如果贷期小于最大的贷期，则补足后续的列信息
         */
        if(etPaidTrm < maxLoanTrm){
            for (int i = etPaidTrm+1; i <= maxLoanTrm; i++){
                ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionNum(key);
                productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionType("earlySquare");
                productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionGrade(grade);
                productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionName(String.valueOf(i));
                productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntity);
                grade++;
            }
        }
    }

    public Result saveEarlySquare(ProductActuarialBpCalPlanAssembleDimensionDetailDto productActuarialBpCalPlanAssembleDimensionDetailDto, User user){
        Result result = new Result();
        try{
            /**
             * 保存前先清除已经存在的该cal_id下的所有的type类型的earlySquare的维度数据
             */
            if(productActuarialBpCalPlanAssembleDimensionDetailDto != null){
                /**
                 * 增加校验，校验每一行的提前结清概率求和是否为1 如果不为1则直接返回错误提示：第n行提前结清概率求和不为1
                 */
                List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDto.getProductActuarialBpCalPlanAssembleDimensionDetailEntityList().stream().filter(p -> p.getAssembleDimensionType().equals("earlySquare")).collect(Collectors.toList());
                if(productActuarialBpCalPlanAssembleDimensionDetailEntityList != null && productActuarialBpCalPlanAssembleDimensionDetailEntityList.size()>0){
                    Map<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> map = productActuarialBpCalPlanAssembleDimensionDetailEntityList.stream().collect(Collectors.groupingBy(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionNum));
                    int num = 1;
                    for (Map.Entry<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> entry : map.entrySet()){
                        List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> list = entry.getValue();
                        BigDecimal sum = new BigDecimal(0);
                        for (ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity : list) {
                            if(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent() != null){
                                sum = sum.add(BigDecimal.valueOf(Double.valueOf(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent())));
                            }
                        }

                        if(sum.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue() != 1.0){
                            return Result.error("第"+num+"行提前结清概率求和不为1");
                        }
                        num++;
                    }
                }


                ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity = productActuarialBpCalPlanAssembleDimensionDetailDto.getProductActuarialBpCalPlanMainEntity();
                productActuarialBpCalPlanAssembleDimensionDetailDao.delete(new QueryWrapper<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>().eq("cal_id",productActuarialBpCalPlanMainEntity.getId()).in("assemble_dimension_type","earlySquare","index","result"));
                productActuarialBpCalPlanIndexDao.delete(new QueryWrapper<ProductActuarialBpCalPlanIndexEntity>().eq("cal_id", productActuarialBpCalPlanMainEntity.getId()));
                if(productActuarialBpCalPlanAssembleDimensionDetailEntityList != null && productActuarialBpCalPlanAssembleDimensionDetailEntityList.size()>0){
                    for (ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity : productActuarialBpCalPlanAssembleDimensionDetailEntityList) {
                        if(!"basic".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionType())){
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setCalId(productActuarialBpCalPlanMainEntity.getId());
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setCreateTime(new Date());
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setCreateUser(user.getName());
                            productActuarialBpCalPlanAssembleDimensionDetailDao.insert(productActuarialBpCalPlanAssembleDimensionDetailEntity);
                        }
                    }
                }

                /**
                 * 更新主表process为系数补录coefficientSupplement
                 */
                productActuarialBpCalPlanMainEntity.setPerProfit(null);
                productActuarialBpCalPlanMainEntity.setContractAmount(null);
                productActuarialBpCalPlanMainEntity.setRebateCoefficient(null);
                productActuarialBpCalPlanMainEntity.setTotalProfit(null);
                productActuarialBpCalPlanMainEntity.setStatus(2);
                productActuarialBpCalPlanMainEntity.setProcess("earlySquare");
                productActuarialBpCalPlanMainDao.updateById(productActuarialBpCalPlanMainEntity);
                result.setCode(SUCCESS.getCode());
                result.setMessage(SUCCESS.getDesc());
            }
        }catch (Exception e){
            log.error("saveEarlySquare error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("saveEarlySquare error");
        }
        return result;
    }

    public Result getEarlySquare(Long calId){
        Result result = new Result();
        try{
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("cal_id",calId);
            queryWrapper.eq("assemble_dimension_type","earlySquare");
            queryWrapper.orderByAsc("assemble_dimension_num","assemble_dimension_grade");
            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapper);
            List<Integer> numList = productActuarialBpCalPlanAssembleDimensionDetailEntityList.stream().map(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionNum).distinct().collect(Collectors.toList());

            QueryWrapper queryWrapperBasic = new QueryWrapper();
            queryWrapperBasic.eq("cal_id",calId);
            queryWrapperBasic.eq("assemble_dimension_type","basic");
            queryWrapperBasic.in("assemble_dimension_num",numList);
            queryWrapperBasic.orderByAsc("assemble_dimension_num","assemble_dimension_grade");
            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityListBasic = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapperBasic);

//            productActuarialBpCalPlanAssembleDimensionDetailEntityList.addAll(productActuarialBpCalPlanAssembleDimensionDetailEntityListBasic);
            productActuarialBpCalPlanAssembleDimensionDetailEntityListBasic.addAll(productActuarialBpCalPlanAssembleDimensionDetailEntityList);

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productActuarialBpCalPlanAssembleDimensionDetailEntityListBasic);
        }catch (Exception e){
            log.error("getEarlySquare error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("getEarlySquare error");
        }
        return result;
    }

    public Result calParamIndex(ProductActuarialBpCalPlanAssembleDimensionDetailDto productActuarialBpCalPlanAssembleDimensionDetailDto){
        Result result = new Result();
        try{
            if(productActuarialBpCalPlanAssembleDimensionDetailDto != null){
                ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity = productActuarialBpCalPlanAssembleDimensionDetailDto.getProductActuarialBpCalPlanMainEntity();
                ProductActuarialBpCalPlanIndexEntity productActuarialBpCalPlanIndexEntity = productActuarialBpCalPlanAssembleDimensionDetailDto.getProductActuarialBpCalPlanIndexEntity();

                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("cal_id",productActuarialBpCalPlanMainEntity.getId());
                queryWrapper.eq("assemble_dimension_type","earlySquare");
                List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityListEarlySquare = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapper);
                List<Integer> numList = productActuarialBpCalPlanAssembleDimensionDetailEntityListEarlySquare.stream().map(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionNum).distinct().collect(Collectors.toList());

                QueryWrapper queryWrapperBasic = new QueryWrapper();
                queryWrapperBasic.eq("cal_id",productActuarialBpCalPlanMainEntity.getId());
                queryWrapperBasic.eq("assemble_dimension_type","basic");
                queryWrapperBasic.in("assemble_dimension_num",numList);
                queryWrapperBasic.orderByAsc("assemble_dimension_num","assemble_dimension_grade");

                List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapperBasic);
                /**
                 * 集合按assemble_dimension_num分组
                 */
                Map<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> map = productActuarialBpCalPlanAssembleDimensionDetailEntityList.stream().collect(Collectors.groupingBy(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionNum));

                /**
                 * 循环处理每一行集合数据
                 */
                for (Map.Entry<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> entry : map.entrySet()){
                    StringBuilder str = new StringBuilder();
                    Integer key = entry.getKey();
                    List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> list = entry.getValue();
                    for (ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity : list) {
                        if("产品类型".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                            str.append("AND blw.product_type = '"+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append("' ");
                        }else if ("产品类型细分".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                            str.append("AND blw.product_type_sub = '"+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append("' ");
                        }else if ("贷期".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                            str.append("AND blw.loan_trm = '"+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append("' ");
                        }else if ("投资人".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                            str.append("AND blw.investor = '"+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append("' ");
                        }else if ("品牌".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                            str.append("AND blw.brand_name = '"+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append("' ");
                        }else if ("车型".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName())){
                            str.append("AND blw.map_vehicle_model = '"+productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent()).append("' ");
                        }

                    }

                    /**
                     * 平均贷额
                     */
                    String sqlLoanAmt = bpAvgLoanAmt.replace("@start_date@","DATE_PARSE('"+productActuarialBpCalPlanIndexEntity.getAverageLoanMoneyStartDate()+"','%Y-%m')").replace("@end_date@","DATE_PARSE('"+productActuarialBpCalPlanIndexEntity.getAverageLoanMoneyEndDate()+"','%Y-%m')").replace("@param@",str.toString());
                    log.info("平均贷额sql:{}",sqlLoanAmt);
                    SqlRowSet resultSetAvgLoanAmt = prestoTemplate.queryForRowSet(sqlLoanAmt);
                    Integer grade = productActuarialBpCalPlanMainEntity.getMaxColumnNum()+1;
                    if(resultSetAvgLoanAmt != null){
                        while (resultSetAvgLoanAmt.next()){
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionType("index");
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionContent(resultSetAvgLoanAmt.getString("avg_loan_amt"));
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionName("平均贷额");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntity);
                            grade++;
                        }
                    }

                    /**
                     * 平均客户利率
                     */
                    String sqlAvgCustomerRte = bpAvgCustomerRte.replace("@start_date@","DATE_PARSE('"+productActuarialBpCalPlanIndexEntity.getCustomerRateStartDate()+"','%Y-%m')").replace("@end_date@","DATE_PARSE('"+productActuarialBpCalPlanIndexEntity.getCustomerRateEndDate()+"','%Y-%m')").replace("@param@",str.toString());
                    log.info("平均客户利率sql:{}",sqlAvgCustomerRte);
                    SqlRowSet resultSetAvgCustomerRte = prestoTemplate.queryForRowSet(sqlAvgCustomerRte);
                    if(resultSetAvgCustomerRte != null){
                        while (resultSetAvgCustomerRte.next()){
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionType("index");
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionContent(resultSetAvgCustomerRte.getString("avg_customer_rte"));
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionName("客户利率");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntity);
                            grade++;
                        }
                    }

                    /**
                     * 平均实际利率
                     */
                    String sqlAvgRealRte = bpAvgRealRte.replace("@start_date@","DATE_PARSE('"+productActuarialBpCalPlanIndexEntity.getActualRateStartDate()+"','%Y-%m')").replace("@end_date@","DATE_PARSE('"+productActuarialBpCalPlanIndexEntity.getActualRateEndDate()+"','%Y-%m')").replace("@param@",str.toString());
                    log.info("平均实际利率sql:{}",sqlAvgRealRte);
                    SqlRowSet resultSetAvgRealRte = prestoTemplate.queryForRowSet(sqlAvgRealRte);
                    if(resultSetAvgRealRte != null){
                        while (resultSetAvgRealRte.next()){
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionType("index");
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionContent(resultSetAvgRealRte.getString("avg_real_rte"));
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionName("实际利率");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntity);
                            grade++;
                        }
                    }

                    /**
                     * 平均损失率
                     */
                    String sqlAvgLossRte = bpAvgLossRte.replace("@start_date@","DATE_PARSE('"+productActuarialBpCalPlanIndexEntity.getLossRateStartDate()+"','%Y-%m')").replace("@end_date@","DATE_PARSE('"+productActuarialBpCalPlanIndexEntity.getLossRateEndDate()+"','%Y-%m')").replace("@param@",str.toString());
                    log.info("平均损失率sql:{}",sqlAvgLossRte);
                    SqlRowSet resultSetAvgLossRte = prestoTemplate.queryForRowSet(sqlAvgLossRte);
                    if(resultSetAvgLossRte != null){
                        while (resultSetAvgLossRte.next()){
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionType("index");
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionContent(resultSetAvgLossRte.getString("avg_loss_rte"));
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setAssembleDimensionName("风险损失");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntity);
                            grade++;
                        }
                    }
                }
                result.setCode(SUCCESS.getCode());
                result.setMessage(SUCCESS.getDesc());
                result.setData(productActuarialBpCalPlanAssembleDimensionDetailEntityList);
            }else{
                result.setCode(FAIL.getCode());
                result.setMessage("传参为空");
            }
        }catch (Exception e){
            log.error("calParamIndex error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("calParamIndex error");
        }
        return result;
    }

    public Result saveParamIndex(ProductActuarialBpCalPlanAssembleDimensionDetailDto productActuarialBpCalPlanAssembleDimensionDetailDto,User user){
        Result result = new Result<>();
        try{
            /**
             * 保存前先清除已经存在的该cal_id下的所有的type类型的index的维度数据
             */
            if(productActuarialBpCalPlanAssembleDimensionDetailDto != null){
                ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity = productActuarialBpCalPlanAssembleDimensionDetailDto.getProductActuarialBpCalPlanMainEntity();
                productActuarialBpCalPlanAssembleDimensionDetailDao.delete(new QueryWrapper<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>().eq("cal_id",productActuarialBpCalPlanMainEntity.getId()).in("assemble_dimension_type","index","result"));
                productActuarialBpCalPlanIndexDao.delete(new QueryWrapper<ProductActuarialBpCalPlanIndexEntity>().eq("cal_id", productActuarialBpCalPlanMainEntity.getId()));
                List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDto.getProductActuarialBpCalPlanAssembleDimensionDetailEntityList().stream().filter(p -> p.getAssembleDimensionType().equals("index")).collect(Collectors.toList());
                if(productActuarialBpCalPlanAssembleDimensionDetailEntityList != null && productActuarialBpCalPlanAssembleDimensionDetailEntityList.size() > 0){
                    for (ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity : productActuarialBpCalPlanAssembleDimensionDetailEntityList) {
                        if(!"basic".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionType())){
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setCalId(productActuarialBpCalPlanMainEntity.getId());
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setCreateTime(new Date());
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setCreateUser(user.getName());
                            productActuarialBpCalPlanAssembleDimensionDetailDao.insert(productActuarialBpCalPlanAssembleDimensionDetailEntity);
                        }
                    }
                }

                /**
                 * 保存指标表
                 */
                ProductActuarialBpCalPlanIndexEntity productActuarialBpCalPlanIndexEntity = productActuarialBpCalPlanAssembleDimensionDetailDto.getProductActuarialBpCalPlanIndexEntity();
                if(productActuarialBpCalPlanIndexEntity != null){
                    productActuarialBpCalPlanIndexEntity.setCalId(productActuarialBpCalPlanMainEntity.getId());
                    productActuarialBpCalPlanIndexEntity.setCreateTime(new Date());
                    productActuarialBpCalPlanIndexEntity.setCreateUser(user.getName());
                    productActuarialBpCalPlanIndexDao.insert(productActuarialBpCalPlanIndexEntity);
                }

                /**
                 * 更新主表process为系数补录coefficientSupplement
                 */
                productActuarialBpCalPlanMainEntity.setPerProfit(null);
                productActuarialBpCalPlanMainEntity.setContractAmount(null);
                productActuarialBpCalPlanMainEntity.setRebateCoefficient(null);
                productActuarialBpCalPlanMainEntity.setTotalProfit(null);
                productActuarialBpCalPlanMainEntity.setStatus(2);
                productActuarialBpCalPlanMainEntity.setProcess("index");
                productActuarialBpCalPlanMainDao.updateById(productActuarialBpCalPlanMainEntity);
                result.setCode(SUCCESS.getCode());
                result.setMessage(SUCCESS.getDesc());
            }
        }catch (Exception e){
            log.error("saveParamIndex error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("saveParamIndex error");
        }
        return result;
    }

    public Result getParamIndex(Long calId){
        Result result = new Result<>();
        try{
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("cal_id",calId);
            queryWrapper.eq("assemble_dimension_type","index");
            queryWrapper.orderByAsc("assemble_dimension_num","assemble_dimension_grade");
            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapper);
            List<Integer> numList = productActuarialBpCalPlanAssembleDimensionDetailEntityList.stream().map(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionNum).distinct().collect(Collectors.toList());


            QueryWrapper queryWrapperBasic = new QueryWrapper();
            queryWrapperBasic.eq("cal_id",calId);
            queryWrapperBasic.eq("assemble_dimension_type","basic");
            queryWrapperBasic.in("assemble_dimension_num",numList);
            queryWrapperBasic.orderByAsc("assemble_dimension_num","assemble_dimension_grade");
            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityListBasic = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapperBasic);

//            productActuarialBpCalPlanAssembleDimensionDetailEntityList.addAll(productActuarialBpCalPlanAssembleDimensionDetailEntityListBasic);
            productActuarialBpCalPlanAssembleDimensionDetailEntityListBasic.addAll(productActuarialBpCalPlanAssembleDimensionDetailEntityList);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productActuarialBpCalPlanAssembleDimensionDetailEntityListBasic);
        }catch (Exception e){
            log.error("getParamIndex error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("getParamIndex error");
        }
        return result;
    }

    public Result getParamIndexDate(Long calId){
        Result result = new Result<>();
        try{
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("cal_id",calId);
            ProductActuarialBpCalPlanIndexEntity productActuarialBpCalPlanIndexEntity = productActuarialBpCalPlanIndexDao.selectOne(queryWrapper);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productActuarialBpCalPlanIndexEntity);
        }catch(Exception e){
            log.error("getParamIndexDate error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("getParamIndexDate error");
        }
        return result;
    }

    public Result calResult(Long calId){
        Result result = new Result<>();
        try{
            /**
             * 先查询本品 等额本息   考虑提前结清 有效  按照创建日期倒叙  查询符合条件的最新的模板详情
             */

            ProductCalculateDto productCalculateDto = new ProductCalculateDto();

            ProductCalBasicInfoDto productCalBasicInfoDto = new ProductCalBasicInfoDto();
            productCalBasicInfoDto.setBusinessType(0);
            productCalBasicInfoDto.setRepaymentMethod(0);
            productCalBasicInfoDto.setConsiderEarlySquare(1);
            productCalculateDto.setProductCalBasicInfoDto(productCalBasicInfoDto);

            ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity = productActuarialBpCalPlanMainDao.selectById(calId);

            /**
             * 查询提前结清手续费比例和佣金扣返比例规则
             */
            QueryWrapper queryWrapperBasicInfo = new QueryWrapper();
            queryWrapperBasicInfo.eq("business_type",0);
            queryWrapperBasicInfo.eq("repayment_method",0);
            queryWrapperBasicInfo.eq("consider_early_square",1);
            queryWrapperBasicInfo.orderByDesc("create_time");
            ProductCalBasicInfoEntity productCalBasicInfoEntity = (ProductCalBasicInfoEntity)productCalBasicInfoDao.selectList(queryWrapperBasicInfo).get(0);
            QueryWrapper queryWrapperEarlySquare = new QueryWrapper();
            queryWrapperEarlySquare.eq("basic_info_id",productCalBasicInfoEntity.getId());
            ProductCalEarlySquareParamSubEntity productCalEarlySquareParamSubEntity = productCalEarlySquareParamSubDao.selectOne(queryWrapperEarlySquare);



            /**
             * 查询条件：calId下的所有维度数据，按照assemble_dimension_num和assemble_dimension_grade升序进行排序
             */
            QueryWrapper queryWrapperIndex = new QueryWrapper();
            queryWrapperIndex.eq("cal_id",calId);
            queryWrapperIndex.eq("assemble_dimension_type","index");
            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityListIndex = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapperIndex);
            List<Integer> numList = productActuarialBpCalPlanAssembleDimensionDetailEntityListIndex.stream().map(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionNum).distinct().collect(Collectors.toList());


            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("cal_id",calId);
            queryWrapper.in("assemble_dimension_num",numList);
            queryWrapper.orderByAsc("assemble_dimension_num","assemble_dimension_grade");
            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapper);
            if(productActuarialBpCalPlanAssembleDimensionDetailEntityList != null && productActuarialBpCalPlanAssembleDimensionDetailEntityList.size() > 0){
                /**
                 * 按照assemble_dimension_num进行分组
                 */
                Map<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> map = productActuarialBpCalPlanAssembleDimensionDetailEntityList.stream().collect(Collectors.groupingBy(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionNum));
                for (Map.Entry<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> entry : map.entrySet()) {
                    Integer key = entry.getKey();
                    try{
                        Integer grade = productActuarialBpCalPlanMainEntity.getMaxColumnNum()+1;
                        ProductCalBasicParamDto productCalBasicParamDto = new ProductCalBasicParamDto();
                        List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> list = entry.getValue();
                        List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> basic = list.stream().filter(item -> item.getAssembleDimensionType().equals("basic")).collect(Collectors.toList());
                        productCalBasicParamDto.setTimeLimit(Integer.valueOf(basic.stream().filter(item -> item.getAssembleDimensionName().equals("贷期")).findFirst().get().getAssembleDimensionContent()));
                        List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> coefficientSupplement = list.stream().filter(item -> item.getAssembleDimensionType().equals("coefficientSupplement")).collect(Collectors.toList());
                        double bonusTotal = coefficientSupplement.stream().filter(item -> !item.getAssembleDimensionName().equals("合同量")).mapToDouble(item -> Double.valueOf(item.getAssembleDimensionContent())).sum();
                        productCalBasicParamDto.setDealerBasicCommissionRatio(bonusTotal);
                        productCalBasicParamDto.setDealerLadderBonusRatio(0.0);
                        productCalBasicParamDto.setDealerSaleBonusRatio(0.0);
                        /**
                         * 为了使用产品助手中的批量计算，这里将这两个值写死
                         */
                        productCalBasicParamDto.setSpecialReward(0.0);
                        productCalBasicParamDto.setIsFarmerLoan(0);
                        List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> index = list.stream().filter(item -> item.getAssembleDimensionType().equals("index")).collect(Collectors.toList());
                        productCalBasicParamDto.setActualLoanMoney(Double.valueOf(index.stream().filter(item -> item.getAssembleDimensionName().equals("平均贷额")).findFirst().get().getAssembleDimensionContent()));
                        productCalBasicParamDto.setCustomerInterestRate(BigDecimal.valueOf(Double.valueOf(index.stream().filter(item -> item.getAssembleDimensionName().equals("客户利率")).findFirst().get().getAssembleDimensionContent())).multiply(BigDecimal.valueOf(100)).doubleValue());
                        productCalBasicParamDto.setActualInterestRate(BigDecimal.valueOf(Double.valueOf(index.stream().filter(item -> item.getAssembleDimensionName().equals("实际利率")).findFirst().get().getAssembleDimensionContent())).multiply(BigDecimal.valueOf(100)).doubleValue());
                        productCalBasicParamDto.setLossRate(BigDecimal.valueOf(Double.valueOf(index.stream().filter(item -> item.getAssembleDimensionName().equals("风险损失")).findFirst().get().getAssembleDimensionContent())).multiply(BigDecimal.valueOf(100)).doubleValue());
                        productCalculateDto.setProductCalBasicParamDto(productCalBasicParamDto);
                        List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> earlySquare = list.stream().filter(item -> item.getAssembleDimensionType().equals("earlySquare")).collect(Collectors.toList());

                        ProductCalEarlySquareParamSubDto productCalEarlySquareParamSubDto = new ProductCalEarlySquareParamSubDto();

                        String earlySquareHandChargeRatioRule = productCalEarlySquareParamSubEntity.getEarlySquareHandChargeRatioRule();
                        String basicCommissionRebateRatioRule = productCalEarlySquareParamSubEntity.getBasicCommissionRebateRatioRule();
                        productCalEarlySquareParamSubDto.setEarlySquareHandChargeRatioRule(earlySquareHandChargeRatioRule);
                        productCalEarlySquareParamSubDto.setBasicCommissionRebateRatioRule(basicCommissionRebateRatioRule);

                        ProductCalEarlySquareHandChargeAndCommissionRebatePamDto productCalEarlySquareHandChargeAndCommissionRebatePamDto = new ProductCalEarlySquareHandChargeAndCommissionRebatePamDto();
                        productCalEarlySquareHandChargeAndCommissionRebatePamDto.setBusinessType(0);
                        productCalEarlySquareHandChargeAndCommissionRebatePamDto.setBusinessTypeStr("本品");
                        productCalEarlySquareHandChargeAndCommissionRebatePamDto.setDealerBasicCommissionRatio(productCalBasicParamDto.getDealerBasicCommissionRatio());
                        productCalEarlySquareHandChargeAndCommissionRebatePamDto.setDealerLadderBonusRatio(productCalBasicParamDto.getDealerLadderBonusRatio());
                        productCalEarlySquareHandChargeAndCommissionRebatePamDto.setDealerSaleBonusRatio(productCalBasicParamDto.getDealerSaleBonusRatio());

                        Optional<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productType = basic.stream().filter(item -> item.getAssembleDimensionName().equals("产品类型")).findFirst();
                        if(productType.isPresent()){
                            String productTypeContent = productType.get().getAssembleDimensionContent();
                            if("新标息-法人贷".equals(productTypeContent)){
                                productTypeContent = "新标息";
                            }else if ("增益贷".equals(productTypeContent)){
                                productTypeContent = "低息-增益";
                            }else if ("增益贷-法人贷".equals(productTypeContent)){
                                productTypeContent = "低息-增益";
                            }
                            productCalEarlySquareHandChargeAndCommissionRebatePamDto.setProductClassification(productTypeContent);
                        }else{
                            productCalEarlySquareHandChargeAndCommissionRebatePamDto.setProductClassification("");
                        }
                        productCalEarlySquareHandChargeAndCommissionRebatePamDto.setTimeLimit(productCalBasicParamDto.getTimeLimit());
                        productCalEarlySquareParamSubDto.setProductCalEarlySquareHandChargeAndCommissionRebatePamDto(productCalEarlySquareHandChargeAndCommissionRebatePamDto);
                        productCalculateDto.setProductCalEarlySquareParamSubDto(productCalEarlySquareParamSubDto);
                        Result resultlEarlySquareRatio = calEarlySquareRatio(productCalculateDto);
                        if(resultlEarlySquareRatio.getCode() .equals(SUCCESS.getCode())){
                            productCalEarlySquareParamSubDto = (ProductCalEarlySquareParamSubDto) resultlEarlySquareRatio.getData();
                            List<ProductCalEarlySquareProbabilityDto> productCalEarlySquareProbabilityDtoList = new ArrayList<>();
                            for (ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity : earlySquare) {
                                if(StringUtils.isNotEmpty(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent())){
                                    ProductCalEarlySquareProbabilityDto productCalEarlySquareProbabilityDto = new ProductCalEarlySquareProbabilityDto();
                                    Integer payoutRentalId = Integer.valueOf(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionName());
                                    productCalEarlySquareProbabilityDto.setPayoutRentalId(payoutRentalId);
                                    Double payoutProbability = Double.valueOf(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionContent());
                                    productCalEarlySquareProbabilityDto.setPayoutProbability(payoutProbability);
                                    productCalEarlySquareProbabilityDtoList.add(productCalEarlySquareProbabilityDto);
                                }
                            }
                            productCalEarlySquareParamSubDto.setProductCalEarlySquareProbabilityDtoList(productCalEarlySquareProbabilityDtoList);
                            productCalculateDto.setProductCalEarlySquareParamSubDto(productCalEarlySquareParamSubDto);
                        }else{
                            log.error("计算{}行佣金扣返和手续费比例失败:{}",key,resultlEarlySquareRatio.getMessage());
                            continue;
                        }
                        /**
                         * 使用productCalculateDto，调用productProposalCalService 的replace方法  得到最终的测算参数
                         */
                        productProposalCalService.replaceProductCalculateDto(productCalculateDto,null);
                        Result resultInfo = productCalculateV2Service.calculateAllParam(productCalculateDto);
                        if(resultInfo.getCode().equals(SUCCESS.getCode())){
                            productCalculateDto = (ProductCalculateDto) resultInfo.getData();
                            log.info("calResult success:"+productCalculateDto);
                            /**
                             * 应收利息%
                             */
                            String interest = BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getTotalInterest()).divide(BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getActualLoanMoney()),4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntityInterest = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntityInterest.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityInterest.setAssembleDimensionType("result");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityInterest.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityInterest.setAssembleDimensionContent(interest);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityInterest.setAssembleDimensionName("应收利息%");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntityInterest);
                            grade++;

                            /**
                             * 总服务费系数
                             */
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntityTotalServiecFee = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntityTotalServiecFee.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityTotalServiecFee.setAssembleDimensionType("result");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityTotalServiecFee.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityTotalServiecFee.setAssembleDimensionContent(String.valueOf(productCalBasicParamDto.getDealerBasicCommissionRatio()));
                            productActuarialBpCalPlanAssembleDimensionDetailEntityTotalServiecFee.setAssembleDimensionName("总服务费%");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntityTotalServiecFee);
                            grade++;

                            /**
                             * 资金成本
                             */
                            /**
                             * 现金流中的利息支出求合/平均贷款金额
                             */
                            List<ProductCalCashStreamDto> productCalCashStreamDtoList = productCalculateDto.getProductCalCashStreamDtoList();
                            Double  interestExpend = productCalCashStreamDtoList.stream().mapToDouble(productCalCashStreamDto -> -productCalCashStreamDto.getCashStreamInterestExpend()).sum();
                            String capitalCost = BigDecimal.valueOf(interestExpend).divide(BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getActualLoanMoney()),4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toString();

                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntityCapitalCost = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntityCapitalCost.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityCapitalCost.setAssembleDimensionType("result");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityCapitalCost.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityCapitalCost.setAssembleDimensionContent(capitalCost);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityCapitalCost.setAssembleDimensionName("资金成本%");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntityCapitalCost);
                            grade++;

                            /**
                             * 运营成本：扣款手续费+放款手续费+变动费用+外包催收费+印花税+附加税
                             */
                            Double sum = productCalCashStreamDtoList.stream().mapToDouble(productCalCashStreamDto -> {
                                BigDecimal bigDecimalOri = BigDecimal.valueOf(0.0);
                                if(productCalCashStreamDto.getCashStreamDeductMoneyHandCharge() != null){
                                    bigDecimalOri = bigDecimalOri.add(BigDecimal.valueOf(productCalCashStreamDto.getCashStreamDeductMoneyHandCharge()));
                                }
                                if(productCalCashStreamDto.getCashStreamLoanHandCharge() != null){
                                    bigDecimalOri = bigDecimalOri.add(BigDecimal.valueOf(productCalCashStreamDto.getCashStreamLoanHandCharge()));
                                }
                                if(productCalCashStreamDto.getCashStreamOtherChangeCost() != null){
                                    bigDecimalOri = bigDecimalOri.add(BigDecimal.valueOf(productCalCashStreamDto.getCashStreamOtherChangeCost()));
                                }
                                if(productCalCashStreamDto.getCashStreamCollectionFeeTotal() != null){
                                    bigDecimalOri = bigDecimalOri.add(BigDecimal.valueOf(productCalCashStreamDto.getCashStreamCollectionFeeTotal()));
                                }
                                if(productCalCashStreamDto.getCashStreamTurnoverTax() != null){
                                    bigDecimalOri = bigDecimalOri.add(BigDecimal.valueOf(productCalCashStreamDto.getCashStreamTurnoverTax()));
                                }
                                if(productCalCashStreamDto.getCashStreamStampTax() != null){
                                    bigDecimalOri = bigDecimalOri.add(BigDecimal.valueOf(productCalCashStreamDto.getCashStreamStampTax()));
                                }
                                return  bigDecimalOri.doubleValue();
                            }).sum();
                            String operatingCost = BigDecimal.valueOf(-sum).divide(BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getActualLoanMoney()),4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntityOperatingCost = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntityOperatingCost.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityOperatingCost.setAssembleDimensionType("result");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityOperatingCost.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityOperatingCost.setAssembleDimensionContent(operatingCost);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityOperatingCost.setAssembleDimensionName("运营成本%");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntityOperatingCost);
                            grade++;

                            /**
                             * 提前还款利息损失：（无提前结清利息收入-实际利息收入）/平均贷额  无提前结清利息收入：月供*贷期-贷额
                             */
                            Double preInterestIncom = BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getMonthPayment()).multiply(BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getTimeLimit())).subtract(BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getActualLoanMoney())).doubleValue();
                            Double actualInterestIncom = productCalCashStreamDtoList.stream().mapToDouble(productCalCashStreamDto -> {
                                if(productCalCashStreamDto.getCashStreamInterestIncom() != null){
                                    return productCalCashStreamDto.getCashStreamInterestIncom();
                                }else{
                                    return 0.0;
                                }
                            }).sum();
                            String earlySquareInterestLoss = BigDecimal.valueOf(preInterestIncom - actualInterestIncom).divide(BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getActualLoanMoney()),4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareInterestLoss = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareInterestLoss.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareInterestLoss.setAssembleDimensionType("result");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareInterestLoss.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareInterestLoss.setAssembleDimensionContent(earlySquareInterestLoss);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareInterestLoss.setAssembleDimensionName("提前还款利息损失%");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareInterestLoss);
                            grade++;

                            /**
                             * 提前还款违约金：提前终止手续费/平均贷额
                             */
                            Double earlySquareHandCharge = productCalCashStreamDtoList.stream().mapToDouble(productCalCashStreamDto -> {
                                if(productCalCashStreamDto.getCashStreamEarlySquareHandCharge() != null){
                                    return productCalCashStreamDto.getCashStreamEarlySquareHandCharge();
                                }else{
                                    return 0.0;
                                }
                            }).sum();
                            String earlySquareHandChargeRatio = BigDecimal.valueOf(earlySquareHandCharge).divide(BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getActualLoanMoney()),4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareHandChargeRatio = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareHandChargeRatio.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareHandChargeRatio.setAssembleDimensionType("result");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareHandChargeRatio.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareHandChargeRatio.setAssembleDimensionContent(earlySquareHandChargeRatio);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareHandChargeRatio.setAssembleDimensionName("提前还款违约金%");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntityEarlySquareHandChargeRatio);
                            grade++;

                            /**
                             * 服务费扣回
                             */
                            log.info("bpOverdueRate:{}", bpOverdueRate);
//                        Integer timeLimit = productCalculateDto.getProductCalBasicParamDto().getTimeLimit();
//                        Double overDueRate = 0.0;
//                        if(timeLimit <= 12){
//                            overDueRate = bpOverdueRate.get(12);
//                        }else if (timeLimit > 12 && timeLimit <= 24){
//                            overDueRate = bpOverdueRate.get(24);
//                        }else if (timeLimit > 24 && timeLimit <= 36){
//                            overDueRate = bpOverdueRate.get(36);
//                        }else if (timeLimit > 36 && timeLimit <= 48){
//                            overDueRate = bpOverdueRate.get(48);
//                        }else if (timeLimit > 48 && timeLimit <= 60){
//                            overDueRate = bpOverdueRate.get(60);
//                        }else if (timeLimit > 60){
//                            overDueRate = bpOverdueRate.get(999);
//                        }
                            Double actualLoanMoney = productCalculateDto.getProductCalBasicParamDto().getActualLoanMoney();
                            Double serviceFee = productCalCashStreamDtoList.stream().mapToDouble(productCalCashStreamDto -> {
                                if(productCalCashStreamDto.getCashStreamServiceFee() != null){
                                    return productCalCashStreamDto.getCashStreamServiceFee();
                                }else{
                                    return 0.0;
                                }
                            }).sum();
                            String serviceFeeRebateRatio = BigDecimal.valueOf(actualLoanMoney).multiply(BigDecimal.valueOf(productCalBasicParamDto.getDealerBasicCommissionRatio())).divide(BigDecimal.valueOf(100)).add(BigDecimal.valueOf(serviceFee)).divide(BigDecimal.valueOf(actualLoanMoney),4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).toString();
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntityServiceFeeRebateRatio = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntityServiceFeeRebateRatio.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityServiceFeeRebateRatio.setAssembleDimensionType("result");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityServiceFeeRebateRatio.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityServiceFeeRebateRatio.setAssembleDimensionContent(serviceFeeRebateRatio);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityServiceFeeRebateRatio.setAssembleDimensionName("服务费扣回%");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntityServiceFeeRebateRatio);
                            grade++;

                            /**
                             * 利润空间: (应收利息-总服务费-资金成本-运营成本-风险损失-提前还款利息损失+提前还款违约金+服务费扣回-0.69%)*0.75
                             */
                            BigDecimal last = BigDecimal.valueOf(1).subtract(BigDecimal.valueOf(productCalculateDto.getProductCalTaxFeeParamDto().getIncomeTaxFeeRatio()).divide(BigDecimal.valueOf(100)).setScale(4, RoundingMode.HALF_UP));
                            String profitSpqce = BigDecimal.valueOf(Double.valueOf(interest)).subtract(BigDecimal.valueOf(productCalBasicParamDto.getDealerBasicCommissionRatio()))
                                    .subtract(BigDecimal.valueOf(Double.valueOf(capitalCost))).subtract(BigDecimal.valueOf(Double.valueOf(operatingCost)))
                                    .subtract(BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getLossRate())).subtract(BigDecimal.valueOf(Double.valueOf(earlySquareInterestLoss)))
                                    .add(BigDecimal.valueOf(Double.valueOf(earlySquareHandChargeRatio))).add(BigDecimal.valueOf(Double.valueOf(serviceFeeRebateRatio)))
                                    .subtract(BigDecimal.valueOf(productCalculateDto.getProductCalTaxFeeParamDto().getOtherCostRatio()))
                                    .multiply(last).toString();

                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntityProfitSpqce = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntityProfitSpqce.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityProfitSpqce.setAssembleDimensionType("result");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityProfitSpqce.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityProfitSpqce.setAssembleDimensionContent(profitSpqce);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityProfitSpqce.setAssembleDimensionName("利润空间");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntityProfitSpqce);
                            grade++;

                            /**
                             * 单台净利润
                             */
                            Double resultNetProfitMoney = productCalculateDto.getProductCalResultInfoDto().getResultNetProfitMoney();
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntitySingleProfitMoney = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntitySingleProfitMoney.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntitySingleProfitMoney.setAssembleDimensionType("result");
                            productActuarialBpCalPlanAssembleDimensionDetailEntitySingleProfitMoney.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntitySingleProfitMoney.setAssembleDimensionContent(resultNetProfitMoney.toString());
                            productActuarialBpCalPlanAssembleDimensionDetailEntitySingleProfitMoney.setAssembleDimensionName("单台净利润");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntitySingleProfitMoney);
                            grade++;

                            /**
                             * 整体净利润
                             */
                            String contractNum = coefficientSupplement.stream().filter(item -> item.getAssembleDimensionName().equals("合同量")).findFirst().get().getAssembleDimensionContent();
                            String totalNetProfitMoney = BigDecimal.valueOf(Double.valueOf(contractNum)).multiply(BigDecimal.valueOf(resultNetProfitMoney)).toString();
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntityTotalNetProfitMoney = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntityTotalNetProfitMoney.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityTotalNetProfitMoney.setAssembleDimensionType("result");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityTotalNetProfitMoney.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityTotalNetProfitMoney.setAssembleDimensionContent(totalNetProfitMoney);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityTotalNetProfitMoney.setAssembleDimensionName("整体净利润");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntityTotalNetProfitMoney);
                            grade++;

                            /**
                             * IRR
                             */
                            Double resultIrr = productCalculateDto.getProductCalResultInfoDto().getResultIrr();
                            ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntityIrr = new ProductActuarialBpCalPlanAssembleDimensionDetailEntity();
                            productActuarialBpCalPlanAssembleDimensionDetailEntityIrr.setAssembleDimensionNum(key);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityIrr.setAssembleDimensionType("result");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityIrr.setAssembleDimensionGrade(grade);
                            productActuarialBpCalPlanAssembleDimensionDetailEntityIrr.setAssembleDimensionContent(resultIrr.toString());
                            productActuarialBpCalPlanAssembleDimensionDetailEntityIrr.setAssembleDimensionName("IRR");
                            productActuarialBpCalPlanAssembleDimensionDetailEntityList.add(productActuarialBpCalPlanAssembleDimensionDetailEntityIrr);
                        }else{
                            log.error("计算{}行结果信息失败:{}",key,resultInfo.getMessage());
                            continue;
                        }
                    }catch(Exception e){
                        log.error("计算{}行结果信息失败",key,e);
                    }
                }
            }
            /**
             * 移除集合中所有提前结清数据
             */
            productActuarialBpCalPlanAssembleDimensionDetailEntityList.removeIf(item -> item.getAssembleDimensionType().equals("earlySquare"));
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productActuarialBpCalPlanAssembleDimensionDetailEntityList);
        }catch (Exception e){
            log.error("calResult error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("calResult error");
        }
        return result;
    }

    public Result calEarlySquareRatio(ProductCalculateDto productCalculateDto){
        Result result = new Result();
        try{

            Mapper mapper = DozerBeanMapperBuilder.buildDefault();
            ProductCalEarlySquareParamSubDto productCalEarlySquareParamSubDto = mapper.map(productCalculateDto.getProductCalEarlySquareParamSubDto(),ProductCalEarlySquareParamSubDto.class);

            /**
             * 处理手续费比例和佣金扣返比例
             */
            Map<String, Object> variables = new HashMap<>();
            ProductCalEarlySquareHandChargeAndCommissionRebatePamDto productCalEarlySquareHandChargeAndCommissionRebatePamDto = productCalEarlySquareParamSubDto.getProductCalEarlySquareHandChargeAndCommissionRebatePamDto();
            try{
                log.info("计算提前结清佣金扣返比例");
                if(productCalEarlySquareHandChargeAndCommissionRebatePamDto.getBusinessType() == 1){
                    String isCorpToCorpStr = productCalEarlySquareHandChargeAndCommissionRebatePamDto.getIsCorpToCorp() == 1?"是":"否";
                    productCalEarlySquareHandChargeAndCommissionRebatePamDto.setIsCorpToCorpStr(isCorpToCorpStr);
                }

                for (Field declaredField : productCalEarlySquareHandChargeAndCommissionRebatePamDto.getClass().getDeclaredFields()) {
                    declaredField.setAccessible(true);
                    variables.put(declaredField.getName(), declaredField.get(productCalEarlySquareHandChargeAndCommissionRebatePamDto));
                }

                variables.put("actualLoanMoney", productCalculateDto.getProductCalBasicParamDto().getActualLoanMoney());
                variables.put("dealerBasicCommissionRatio", BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getDealerBasicCommissionRatio()).divide(BigDecimal.valueOf(100)).doubleValue());
                variables.put("dealerLadderBonusRatio",BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getDealerLadderBonusRatio()).divide(BigDecimal.valueOf(100)).doubleValue());
                variables.put("dealerSaleBonusRatio",BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getDealerSaleBonusRatio()).divide(BigDecimal.valueOf(100)).doubleValue());

                /**
                 * 使用佣金扣返比例规则，计算每期的佣金扣返比例
                 */
                String basicCommissionRebateRatioRule = productCalEarlySquareParamSubDto.getBasicCommissionRebateRatioRule();
                for (EarlySquareRatioRulePamEnum pamEnum : EarlySquareRatioRulePamEnum.values()) {
                    basicCommissionRebateRatioRule = basicCommissionRebateRatioRule.replaceAll(pamEnum.type(), pamEnum.value());
                }

                basicCommissionRebateRatioRule = basicCommissionRebateRatioRule.replaceAll("\n"," ");

                List<ProductCalEarlySquareBasicCommissionRebateRatioDto> productCalEarlySquareBasicCommissionRebateRatioDtoList = new ArrayList<>();
                for (int i = 1; i <=productCalEarlySquareHandChargeAndCommissionRebatePamDto.getTimeLimit(); i++) {
                    variables.put("month",i);
                    Object ratio = RuleExecutorUtil.executeRule(basicCommissionRebateRatioRule,variables);
                    ProductCalEarlySquareBasicCommissionRebateRatioDto productCalEarlySquareBasicCommissionRebateRatioDto = new ProductCalEarlySquareBasicCommissionRebateRatioDto();
                    productCalEarlySquareBasicCommissionRebateRatioDto.setPayoutRentalId(i);

                    Double douRatio = 0.0;
                    if(ratio != null){
                        if(ratio instanceof BigDecimal){
                            douRatio = ((BigDecimal)ratio).doubleValue();
                        }else if(ratio instanceof Double){
                            douRatio = (Double)ratio;
                        }else if(ratio instanceof Integer){
                            douRatio = ((Integer)ratio).doubleValue();
                        }
                        if(douRatio != 0.0){
                            douRatio = dealScale(6,douRatio);
                        }
                        productCalEarlySquareBasicCommissionRebateRatioDto.setBasicCommissionRebateRatio(douRatio);
                        productCalEarlySquareBasicCommissionRebateRatioDtoList.add(productCalEarlySquareBasicCommissionRebateRatioDto);
                    }else{
                        break;
                    }
                }
                productCalEarlySquareParamSubDto.setProductCalEarlySquareBasicCommissionRebateRatioDtoList(productCalEarlySquareBasicCommissionRebateRatioDtoList);
            }catch(Exception e){
                log.error("计算提前结清佣金扣返比例失败",e);
                productCalEarlySquareParamSubDto.setProductCalEarlySquareBasicCommissionRebateRatioDtoList(null);
            }


            /**
             * 使用提前结清手续费比例规则，计算每期的提前结清手续费比例
             */
            try{
                log.info("计算提前结清手续费比例");
                String earlySquareHandChargeRatioRule = productCalEarlySquareParamSubDto.getEarlySquareHandChargeRatioRule();
                for (EarlySquareRatioRulePamEnum pamEnum : EarlySquareRatioRulePamEnum.values()) {
                    earlySquareHandChargeRatioRule = earlySquareHandChargeRatioRule.replaceAll(pamEnum.type(), pamEnum.value());
                }

                earlySquareHandChargeRatioRule = earlySquareHandChargeRatioRule.replaceAll("\n"," ");

                List<ProductCalEarlySquareHandChargeRatioDto> productCalEarlySquareHandChargeRatioDtoList = new ArrayList<>();
                for (int i = 1; i <=productCalEarlySquareHandChargeAndCommissionRebatePamDto.getTimeLimit(); i++) {
                    variables.put("month",i);
                    Object ratio = RuleExecutorUtil.executeRule(earlySquareHandChargeRatioRule,variables);
                    ProductCalEarlySquareHandChargeRatioDto productCalEarlySquareHandChargeRatioDto = new ProductCalEarlySquareHandChargeRatioDto();
                    productCalEarlySquareHandChargeRatioDto.setPayoutRentalId(i);

                    Double douRatio = 0.0;
                    if(ratio != null){
                        if(ratio instanceof BigDecimal){
                            douRatio = ((BigDecimal)ratio).doubleValue();
                        }else if(ratio instanceof Double){
                            douRatio = (Double)ratio;
                        }else if(ratio instanceof Integer){
                            douRatio = ((Integer)ratio).doubleValue();
                        }
                        if(douRatio != 0.0){
                            douRatio = dealScale(6,douRatio);
                        }
                        productCalEarlySquareHandChargeRatioDto.setEarlySquareHandChargeRatio(douRatio);
                        productCalEarlySquareHandChargeRatioDtoList.add(productCalEarlySquareHandChargeRatioDto);
                    }else{
                        break;
                    }
                }
                productCalEarlySquareParamSubDto.setProductCalEarlySquareHandChargeRatioDtoList(productCalEarlySquareHandChargeRatioDtoList);
            }catch(Exception e){
                log.error("计算手续费比例失败",e);
                productCalEarlySquareParamSubDto.setProductCalEarlySquareHandChargeRatioDtoList(null);
            }
            result.setCode(SUCCESS.getCode());
            result.setData(productCalEarlySquareParamSubDto);

        }catch(Exception e){
            log.error("calEarlySquareProbabilityAndRatio error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("计算提前结清参数失败");
        }
        return result;
    }

    public Result saveResult(ProductActuarialBpCalPlanAssembleDimensionDetailDto productActuarialBpCalPlanAssembleDimensionDetailDto,User user){
        Result result = new Result<>();
        try{
            /**
             * 保存前先清除已经存在的该cal_id下的所有的type类型的index的维度数据
             */
            if(productActuarialBpCalPlanAssembleDimensionDetailDto != null){
                List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDto.getProductActuarialBpCalPlanAssembleDimensionDetailEntityList();
                ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity = productActuarialBpCalPlanAssembleDimensionDetailDto.getProductActuarialBpCalPlanMainEntity();
                productActuarialBpCalPlanAssembleDimensionDetailDao.delete(new QueryWrapper<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>().eq("cal_id",productActuarialBpCalPlanMainEntity.getId()).in("assemble_dimension_type","result"));
                List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityListResult = productActuarialBpCalPlanAssembleDimensionDetailEntityList.stream().filter(p -> p.getAssembleDimensionType().equals("result")).collect(Collectors.toList());
                if(productActuarialBpCalPlanAssembleDimensionDetailEntityListResult != null && productActuarialBpCalPlanAssembleDimensionDetailEntityListResult.size() > 0){
                    for (ProductActuarialBpCalPlanAssembleDimensionDetailEntity productActuarialBpCalPlanAssembleDimensionDetailEntity : productActuarialBpCalPlanAssembleDimensionDetailEntityListResult) {
                        if(!"basic".equals(productActuarialBpCalPlanAssembleDimensionDetailEntity.getAssembleDimensionType())){
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setCalId(productActuarialBpCalPlanMainEntity.getId());
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setCreateTime(new Date());
                            productActuarialBpCalPlanAssembleDimensionDetailEntity.setCreateUser(user.getName());
                            productActuarialBpCalPlanAssembleDimensionDetailDao.insert(productActuarialBpCalPlanAssembleDimensionDetailEntity);
                        }
                    }
                }
                /**
                 * 更新主表process为系数补录coefficientSupplement
                 */
                productActuarialBpCalPlanMainEntity.setProcess("result");
                /**
                 * 合同量：所有的合同量求合
                 */
                Integer contractAmount = productActuarialBpCalPlanAssembleDimensionDetailEntityList.stream().filter(p -> p.getAssembleDimensionType().equals("coefficientSupplement") && p.getAssembleDimensionName().equals("合同量")).mapToInt(p -> Integer.valueOf(p.getAssembleDimensionContent())).sum();
                productActuarialBpCalPlanMainEntity.setContractAmount(contractAmount);

                /**
                 * 返利系数
                 */
                Map<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> map = productActuarialBpCalPlanAssembleDimensionDetailEntityList.stream().collect(Collectors.groupingBy(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionNum));
                BigDecimal totalAward = BigDecimal.valueOf(0.0);
                BigDecimal totalLoanMoney = BigDecimal.valueOf(0.0);
                for (Map.Entry<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> entry : map.entrySet()) {
                    List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList1 = entry.getValue();
                    String contractNum = productActuarialBpCalPlanAssembleDimensionDetailEntityList1.stream().filter(p -> p.getAssembleDimensionType().equals("coefficientSupplement") && p.getAssembleDimensionName().equals("合同量")).map(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionContent).findFirst().get();
                    Double bonusTotal = productActuarialBpCalPlanAssembleDimensionDetailEntityList1.stream().filter(p -> p.getAssembleDimensionType().equals("coefficientSupplement") && !p.getAssembleDimensionName().equals("合同量")).mapToDouble(item -> Double.valueOf(item.getAssembleDimensionContent())).sum();
                    String avgLoanAmt =productActuarialBpCalPlanAssembleDimensionDetailEntityList1.stream().filter(p -> p.getAssembleDimensionType().equals("index") && p.getAssembleDimensionName().equals("平均贷额")).map(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionContent).findFirst().get();

                    totalAward = totalAward.add(BigDecimal.valueOf(Integer.valueOf(contractNum)).multiply(BigDecimal.valueOf(bonusTotal)).divide(BigDecimal.valueOf(100)).multiply(BigDecimal.valueOf(Double.valueOf(avgLoanAmt))));
                    totalLoanMoney = totalLoanMoney.add(BigDecimal.valueOf(Integer.valueOf(contractNum)).multiply(BigDecimal.valueOf(Double.valueOf(avgLoanAmt))));
                }
                if(totalLoanMoney.doubleValue() != 0.0){
                    productActuarialBpCalPlanMainEntity.setRebateCoefficient(totalAward.divide(totalLoanMoney,4,BigDecimal.ROUND_HALF_UP).doubleValue());
                }

                /**
                 * 总利润
                 */
                Double totalProfit = productActuarialBpCalPlanAssembleDimensionDetailEntityListResult.stream().filter(p -> p.getAssembleDimensionName().equals("整体净利润")).mapToDouble(p -> Double.valueOf(p.getAssembleDimensionContent())).sum();
                productActuarialBpCalPlanMainEntity.setTotalProfit(totalProfit);

                /**
                 * 单台利润
                 */
                Double perProfit = BigDecimal.valueOf(totalProfit).divide(BigDecimal.valueOf(contractAmount), 4, BigDecimal.ROUND_HALF_UP).doubleValue();
                productActuarialBpCalPlanMainEntity.setPerProfit(perProfit);

                productActuarialBpCalPlanMainEntity.setStatus(1);

                productActuarialBpCalPlanMainDao.updateById(productActuarialBpCalPlanMainEntity);
                result.setCode(SUCCESS.getCode());
                result.setMessage(SUCCESS.getDesc());
            }
        }catch (Exception e){
            log.error("saveResult error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("saveResult error");
        }
        return result;
    }

    public void downloadResult(Long id, HttpServletResponse response){
        try {
            /**
             * 根据id查询组合模板详情信息
             */
            ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity = productActuarialBpCalPlanMainDao.selectById(id);

            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("cal_id",id);
            queryWrapper.in("assemble_dimension_type","basic","coefficientSupplement","index","result");
            queryWrapper.orderByAsc("assemble_dimension_num","assemble_dimension_grade");
            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapper);
            if(productActuarialBpCalPlanAssembleDimensionDetailEntityList != null && productActuarialBpCalPlanAssembleDimensionDetailEntityList.size()>0){
                /**
                 * productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList 按照assemble_dimension_num进行分组，生成新的集合
                 */
                Map<Integer, List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity>> numDataList = productActuarialBpCalPlanAssembleDimensionDetailEntityList.stream().collect(Collectors.groupingBy(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionNum));
                List<String> columnNames = new ArrayList<>();
                List<List<Object>> dataListNew = new ArrayList<>();
                numDataList.keySet().forEach(num -> {
                    List<Object> data = new ArrayList<>();
                    List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> assembleDimensionDetailEntityList = numDataList.get(num);
                    for (ProductActuarialBpCalPlanAssembleDimensionDetailEntity assembleDimensionDetailEntity : assembleDimensionDetailEntityList) {
                        data.add(assembleDimensionDetailEntity.getAssembleDimensionContent());
                        if(num == 0){
                            columnNames.add(assembleDimensionDetailEntity.getAssembleDimensionName());
                        }
                    }
                    dataListNew.add(data);
                });

                log.info("dataListNew: {}", dataListNew);

                QueryWrapper queryWrapperBpRewardType = new QueryWrapper();
                queryWrapperBpRewardType.eq("status",1);
                List<ProductActuarialBpRewardTypeEntity> productActuarialBpRewardTypeEntityList = productActuarialBpRewardTypeDao.selectList(queryWrapperBpRewardType);
                if(productActuarialBpRewardTypeEntityList != null && productActuarialBpRewardTypeEntityList.size()>0){
                    bpTemplateColumn.addAll(productActuarialBpRewardTypeEntityList.stream().map(ProductActuarialBpRewardTypeEntity::getName).collect(Collectors.toList()));
                }
                columnNames.addAll(bpTemplateColumn);

                log.info("columnNames: {}", columnNames);

                String fileName = productActuarialBpCalPlanMainEntity.getPlanName();
                fileName = URLEncoder.encode(fileName, "UTF-8");
                fileName = fileName+".xlsx";

                response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
                response.setHeader("Content-disposition", "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);
                response.setCharacterEncoding("utf-8");

                int[] mergeColumnIndexArray = new int[columnNames.size()];
                for (int i = 0; i < columnNames.size(); i++) {
                    mergeColumnIndexArray[i] = i;
                }

                OutputStream outputStream = response.getOutputStream();
                try {
                    EasyExcel.write(outputStream)
                            .head(columnNames.stream().map(header -> Arrays.asList(header)).collect(Collectors.toList()))
                            .registerWriteHandler(new MergeColumnCellWriteHandler(mergeColumnIndexArray))
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .sheet("Sheet1")
                            .doWrite(dataListNew);
                    outputStream.flush();
                }catch (Exception e) {
                    // 处理异常
                    e.printStackTrace();
                    throw new RuntimeException("导出Excel失败", e);
                } finally {
                    // 确保资源关闭
                    try {
                        if (outputStream != null) {
                            outputStream.close();
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }catch (Exception e){
            log.error("downloadResult error",e);
        }
    }

    public Result getResult(Long calId){
        Result result = new Result<>();
        try{
            QueryWrapper queryWrapperResult = new QueryWrapper();
            queryWrapperResult.eq("cal_id",calId);
            queryWrapperResult.eq("assemble_dimension_type","result");
            queryWrapperResult.orderByAsc("assemble_dimension_num","assemble_dimension_grade");
            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityListResult = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapperResult);
            List<Integer> numList = productActuarialBpCalPlanAssembleDimensionDetailEntityListResult.stream().map(ProductActuarialBpCalPlanAssembleDimensionDetailEntity::getAssembleDimensionNum).distinct().collect(Collectors.toList());

            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("cal_id",calId);
            queryWrapper.in("assemble_dimension_type","basic","coefficientSupplement","index");
            queryWrapper.in("assemble_dimension_num",numList);
            queryWrapper.orderByAsc("assemble_dimension_num","assemble_dimension_grade");
            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapper);
//            productActuarialBpCalPlanAssembleDimensionDetailEntityListResult.addAll(productActuarialBpCalPlanAssembleDimensionDetailEntityList);

            productActuarialBpCalPlanAssembleDimensionDetailEntityList.addAll(productActuarialBpCalPlanAssembleDimensionDetailEntityListResult);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productActuarialBpCalPlanAssembleDimensionDetailEntityList);
        }catch (Exception e){
            log.error("getResult error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("getResult error");
        }
        return result;
    }

    public Double dealScale(Integer scaleValue, Double paramValue){
        Double paramValueNew;
        if(scaleValue!=null){
            paramValueNew = BigDecimal.valueOf(paramValue).setScale(scaleValue, BigDecimal.ROUND_HALF_UP).doubleValue();
        }else{
            paramValueNew = BigDecimal.valueOf(paramValue).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        return paramValueNew;
    }

    public Result getPolicyCalDetail(Long calId){
        Result result = new Result();
        try{
            ProductActuarialBpCalPlanAssembleDimensionDetailDto productActuarialBpCalPlanAssembleDimensionDetailDto = new ProductActuarialBpCalPlanAssembleDimensionDetailDto();
            ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity = productActuarialBpCalPlanMainDao.selectById(calId);
            productActuarialBpCalPlanAssembleDimensionDetailDto.setProductActuarialBpCalPlanMainEntity(productActuarialBpCalPlanMainEntity);
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("cal_id",calId);
            ProductActuarialBpCalPlanIndexEntity productActuarialBpCalPlanIndexEntity = productActuarialBpCalPlanIndexDao.selectOne(queryWrapper);
            productActuarialBpCalPlanAssembleDimensionDetailDto.setProductActuarialBpCalPlanIndexEntity(productActuarialBpCalPlanIndexEntity);
            QueryWrapper queryWrapper2 = new QueryWrapper();
            queryWrapper2.eq("cal_id",calId);
            queryWrapper2.orderByAsc("assemble_dimension_num","assemble_dimension_grade");
            List<ProductActuarialBpCalPlanAssembleDimensionDetailEntity> productActuarialBpCalPlanAssembleDimensionDetailEntityList = productActuarialBpCalPlanAssembleDimensionDetailDao.selectList(queryWrapper2);
            productActuarialBpCalPlanAssembleDimensionDetailDto.setProductActuarialBpCalPlanAssembleDimensionDetailEntityList(productActuarialBpCalPlanAssembleDimensionDetailEntityList);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productActuarialBpCalPlanAssembleDimensionDetailDto);
        }catch (Exception e){
            log.error("getPolicyCalDetail error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("getPolicyCalDetail error");
        }
        return result;
    }
}
