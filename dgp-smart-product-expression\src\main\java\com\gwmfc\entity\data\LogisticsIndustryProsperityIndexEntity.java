package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * 物流业景气指数
 * <AUTHOR>
 * @date 2024年02月26日 17:26
 */
@Data
@TableName("logistics_industry_prosperity_index")
@ExcelIgnoreUnannotated
public class LogisticsIndustryProsperityIndexEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("物流业景气指数")
    @TableFieldMapping(value = "logistics_industry_prosperity_index_val", comment = "物流业景气指数")
    private String logisticsIndustryProsperityIndexVal;
}
