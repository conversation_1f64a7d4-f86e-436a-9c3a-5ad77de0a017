package com.gwmfc.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023年08月02日 14:41
 */
@Data
@ApiModel(value = "数据集市实体")
public class ProductDataMartDto {
    private Integer id;

    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类（ 1:宏观数据、2:同业数据、3:市场数据、4:集团数据、5:政策数据、6:业务数据）")
    private Integer dataCategory;

    /**
     * 表单名称
     */
    @ApiModelProperty("表单名称")
    private String formName;

    /**
     * 有效标识
     */
    @ApiModelProperty("有效标识(1:有效；0：无效)")
    private Integer valid;

    /**
     * 表名
     */
    @ApiModelProperty("表名")
    private String tableName;

    /**
     * 更新频率
     */
    @ApiModelProperty("更新频率（1：日频次；2：周频次；3：月频次；4：季频次；5：不定期频次）")
    private Integer updateFrequency;

    /**
     * 数据获取方式
     */
    @ApiModelProperty("数据获取方式（1：自动；2：手动）")
    private Integer dataAcquisitionMethod;

    /**
     * 数据源类型: 网站、邮箱、钉钉、其它
     */
    @ApiModelProperty("数据源类型(1:网站、2:邮箱、3:钉钉、4:其它)")
    private Integer dataSourceType;

    /**
     * 更新宽限期（天）
     */
    @ApiModelProperty("更新宽限期（天）")
    private Integer gracePeriod;

    /**
     * 最近更新时间
     */
    @ApiModelProperty("最近更新时间")
    private String latestUpdateTime;

    /**
     * 数据联系人
     */
    @ApiModelProperty("数据联系人")
    private String contactPerson;

    /**
     * 是否及时更新
     */
    @ApiModelProperty("是否及时更新")
    private Boolean updateInTime;

    /**
     * 单批导入多少条数据
     */
    @ApiModelProperty("单批导入多少条数据")
    private Long singleBatchCount;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateUser;
}
