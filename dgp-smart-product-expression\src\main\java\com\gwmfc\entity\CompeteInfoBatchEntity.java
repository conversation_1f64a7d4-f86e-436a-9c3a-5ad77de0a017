package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname CompeteInfoBatch
 * @Description TODO
 * @Date 2024/12/11 16:09
 */
@Data
@TableName("compete_info_batch")
public class CompeteInfoBatchEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "data_type")
    @ApiModelProperty("数据类型")
    private String dataType;

    @TableField(value = "attachment")
    @ApiModelProperty("附件信息")
    private String attachment;

    @TableField(value = "staff_no")
    @ApiModelProperty("工号")
    private String staffNo;

    @TableField("create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField("create_user")
    @ApiModelProperty("创建用户")
    private String createUser;

    @TableField("update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @TableField("update_user")
    @ApiModelProperty("更新用户")
    private String updateUser;
}
