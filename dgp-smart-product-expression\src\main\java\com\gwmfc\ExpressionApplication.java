package com.gwmfc;

import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @Description : 此模块为用户管理，权限管理，菜单管理等系统相关模块，钉钉审批流程模块
 * @Date 2020-08-04
 */
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "PT1H")
public class ExpressionApplication {
     public static void main(String[] args) {
        SpringApplication.run(ExpressionApplication.class, args);
    }


}
