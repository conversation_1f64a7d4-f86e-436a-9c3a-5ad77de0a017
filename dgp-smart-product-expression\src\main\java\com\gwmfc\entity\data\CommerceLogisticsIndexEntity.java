package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * 电商物流指数
 * <AUTHOR>
 * @date 2024年02月26日 16:56
 */
@Data
@TableName("commerce_logistics_index")
@ExcelIgnoreUnannotated
public class CommerceLogisticsIndexEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("电商物流指数")
    @TableFieldMapping(value = "commerce_logistics_index_val", comment = "电商物流指数")
    private String commerceLogisticsIndexVal;

}
