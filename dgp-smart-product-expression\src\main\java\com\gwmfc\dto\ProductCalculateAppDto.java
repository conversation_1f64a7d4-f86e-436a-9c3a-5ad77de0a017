package com.gwmfc.dto;

import com.gwmfc.entity.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalculateDto
 * @Description 产品测算dto
 * @Date 2023/9/19 14:19
 */
@Data
@ApiModel(value = "产品保存dto")
public class ProductCalculateAppDto {
    @ApiModelProperty("基本信息")
    private ProductCalBasicInfoEntity productCalBasicInfoEntity;

    @ApiModelProperty("结果信息")
    private ProductCalResultInfoEntity productCalResultInfoEntity;

    @ApiModelProperty("基本参数")
    private List<ProductCalBasicParamEntity> productCalBasicParamEntityList;

    @ApiModelProperty("税费参数")
    private List<ProductCalTaxFeeParamEntity> productCalTaxFeeParamEntityList;

    @ApiModelProperty("还款方式参数")
    private List<ProductCalRepaymentMethodParamEntity> productCalRepaymentMethodParamEntityList;

    @ApiModelProperty("提前结清参数")
    private List<ProductCalEarlySquareParamEntity> productCalEarlySquareParamEntityList;

    @ApiModelProperty("提前结清参数附加")
    private ProductCalEarlySquareParamSubDto productCalEarlySquareParamSubDto;

    @ApiModelProperty("现金流量表")
    private List<ProductCalCashStreamEntity> productCalCashStreamEntityList;

    @ApiModelProperty("联合贷参数")
    private List<ProductCalUnionLoanParamEntity> productCalUnionLoanParamEntityList;

}
