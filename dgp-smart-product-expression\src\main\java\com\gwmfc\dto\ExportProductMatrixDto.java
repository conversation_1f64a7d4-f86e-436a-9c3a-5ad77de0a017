package com.gwmfc.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ExportProductMatrixDto
 * @Description TODO
 * @Date 2024/9/3 15:48
 */
@Data
public class ExportProductMatrixDto {
    @ExcelProperty("编号")
    private Integer num;

    @ExcelProperty("业务类型")
    private String bussType;

    @ExcelProperty("产品分类")
    private String productClassification;

    @ExcelProperty("产品二级分类")
    private String productSecondaryClassification;

    @ExcelProperty("产品定位")
    private String productPosition;

    @ExcelProperty("产品定位描述")
    private String productPositionDescription;

    @ExcelProperty("产品方案描述")
    private String productProgramDescription;

    @ExcelProperty("适用赛道")
    private String track;

    @ExcelProperty("适用细分领域")
    private String subdivision;

    @ExcelProperty("创建人")
    private String createUser;

    @ExcelProperty("生效开始日期")
    private String effectiveDate;

    @ExcelProperty("有效截止日期")
    private String expiryDate;


}
