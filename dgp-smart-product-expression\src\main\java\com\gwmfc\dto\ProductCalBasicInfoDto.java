package com.gwmfc.dto;

import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname BasicInfoDto
 * @Description 基本信息
 * @Date 2023/9/19 14:12
 */
@Data
@ApiModel(value = "基本信息")
public class ProductCalBasicInfoDto {
    private Long id;

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("版本号")
    private Integer version;

    @ApiModelProperty("业务类型 0-本品  1-全品新车  2-二手车")
    private Integer businessType;

    @ApiModelProperty("还款方式 0-等额本息  1-分段贷  2-尾款贷 3-等额本金")
    private Integer repaymentMethod;

    @ApiModelProperty("还款方式说明")
    private String repaymentMethodInstruction;

    @ApiModelProperty("状态 0-失效 1-生效")
    private String state;

    @ApiModelProperty("生效日期")
    private String effectiveDate;

    @ApiModelProperty("失效日期")
    private String expiryDate;

    @ApiModelProperty("是否考虑提前结清")
    private Integer considerEarlySquare;
}
