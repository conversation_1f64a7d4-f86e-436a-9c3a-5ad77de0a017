package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname ProductProposalTemplateJsonDto
 * @Description TODO
 * @Date 2023/11/16 16:43
 */
@Data
@ApiModel("产品模板json对应dto")
public class ProductProposalTemplateJsonDto {
    private Map<String,String> selfDefinedParameter;
    private String brand;
    private String vehicleType;
    private Double loanAmount;
    private Double paymentDays;
    private Double customerInterestRate;
    private Double settledRate;
    private Double contractsProportion;
    private Double riskLossRate;
    private Double baseCommissionRatio;
    private Double ladderBonusRatio;
    private Double promotionBonusProportion;
    private Double irr;
    private Double roa;
    private Double netMargin;
    private Double thousandIncome;
    private Double weightedIrr;
    private Long irrTemplateId;
    private Double personalReward;
}
