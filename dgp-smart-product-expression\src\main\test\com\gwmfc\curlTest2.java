package com.gwmfc;

import com.gwmfc.service.DomesticPriceIndexService;
import com.gwmfc.service.LogisticsIndexService;
import com.gwmfc.service.MonthlyUsedCarTransactionVolumeService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024年04月02日 10:19
 */
@SpringBootTest
public class curlTest2 {
    @Resource
    private DomesticPriceIndexService domesticPriceIndexService;
    @Resource
    private LogisticsIndexService logisticsIndexService;

    @Resource
    private MonthlyUsedCarTransactionVolumeService monthlyUsedCarTransactionVolumeService;

    @Test
    public void monthDownload() {
        domesticPriceIndexService.catchMonthDomesticPriceIndex(200,"");
    }
    @Test
    public void logisticsIndexService() {
        logisticsIndexService.catchLogisticsIndex("cpcaautod_weekly_sales_volume","");

    }

    @Test
    public void MonthlyUsedCarTransactionVolumeService() {
        monthlyUsedCarTransactionVolumeService.catchMonthlyUsedCarTransactionVolume("2024","02","cpcaautod_weekly_sales_volume","");

    }
}
