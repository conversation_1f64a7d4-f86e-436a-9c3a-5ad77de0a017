package com.gwmfc.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.bo.UploadFileBo;
import com.gwmfc.dao.PolicyDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.PolicyDto;
import com.gwmfc.entity.data.PolicyEntity;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.PageForm;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023年08月15日 14:08
 */
@Service
@Slf4j
public class PolicyService extends ServiceImpl<PolicyDao, PolicyEntity> {

    @Resource
    private FtpService ftpService;

    /**
     * 文件目录
     */
    @Value("${replenish-excel-dir}")
    private String replenishExcelDir;

    /**
     * 保存
     * @param
     * @param addFileList
     * @param user
     */
    public Integer save(PolicyDto policyDto, List<MultipartFile> addFileList, User user) {
        PolicyEntity policyEntity = new PolicyEntity();
        BeanUtils.copyProperties(policyDto, policyEntity);
        if (null != addFileList && !addFileList.isEmpty()) {

            policyEntity.setFtpFileJson(GsonUtil.toJson(ftpService.uploadFileToFtp(replenishExcelDir, policyDto.getAddFileList(), addFileList, user)));
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        policyEntity.setCreateTime(Long.parseLong(dateTimeFormatter.format(LocalDateTime.now())));
        policyEntity.setCreateUser(user.getUserName());
        Integer res = baseMapper.insert(policyEntity);
        return res;
    }

    /**
     * 详情
     * @param
     * @return
     */
    public PolicyDto detail(Long id) {
        PolicyEntity policyEntity = baseMapper.selectById(id);
        PolicyDto policyDto = new PolicyDto();
        if (null != policyEntity) {
            BeanUtils.copyProperties(policyEntity, policyDto);
            if (StringUtils.hasText(policyEntity.getFtpFileJson())) {
                policyDto.setRemainFileList(GsonUtil.jsonToList(policyEntity.getFtpFileJson(), UploadFileBo.class));
            }
            return policyDto;
        }
        return null;
    }

    /**
     * 更新
     * @param policyDto
     * @param addFileList
     * @param user
     */
    public Integer update(PolicyDto policyDto, List<MultipartFile> addFileList, User user) {
        PolicyEntity policyEntity = new PolicyEntity();
        BeanUtils.copyProperties(policyDto, policyEntity);
        List<UploadFileBo> remainFileBoList = policyDto.getRemainFileList();
        if (null == remainFileBoList) {
            remainFileBoList = new ArrayList<>();
        }
        if (null != addFileList && !addFileList.isEmpty()) {
            remainFileBoList.addAll(ftpService.uploadFileToFtp(replenishExcelDir, policyDto.getAddFileList(), addFileList, user));
        }
        if (null != policyDto.getDelFileList() && !policyDto.getDelFileList().isEmpty()) {
            deleteFileFromFtp(policyDto.getDelFileList());
        }
        policyEntity.setFtpFileJson(GsonUtil.toJson(remainFileBoList));
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        policyEntity.setUpdateTime(dateTimeFormatter.format(LocalDateTime.now()));
        policyEntity.setUpdateUser(user.getUserName());
        return baseMapper.updateById(policyEntity);
    }

    /**
     * 删除
     * @param id
     */
    public Integer delete(Long id) {
        PolicyEntity policyEntity = baseMapper.selectById(id);
        if (StringUtils.hasText(policyEntity.getFtpFileJson())) {
            deleteFileFromFtp(GsonUtil.jsonToList(policyEntity.getFtpFileJson(), UploadFileBo.class));
        }
        return baseMapper.deleteById(id);
    }


    /**
     * 删除文件
     * @param delFileList
     */
    public void deleteFileFromFtp(List<UploadFileBo> delFileList) {
        delFileList.forEach(delFile -> ftpService.deleteFile(delFile.getFilePath()));
    }

    public void deleteBatch(List<Long> recordIdList) {
        recordIdList.forEach(recordId -> {
            delete(recordId);
        });
    }

    /**
     * 下载文件
     * @param uploadFileBo
     * @param response
     * @throws IOException
     */
    public void downloadFile(UploadFileBo uploadFileBo, HttpServletResponse response) throws IOException {
        String fileName = uploadFileBo.getFileName();
        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
        } catch (Exception e) {
            log.error("context", e);
        }
        String remoteFilePath = uploadFileBo.getFilePath();
        response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);
        ftpService.downloadFile(remoteFilePath, response.getOutputStream());
    }

    public IPage<PolicyEntity> queryPage(PageForm<PolicyDto> pageForm) {
        PolicyDto param = pageForm.getParam();
        PolicyEntity policyEntity = new PolicyEntity();
        BeanUtils.copyProperties(param, policyEntity);

        LambdaQueryWrapper<PolicyEntity> queryWrapper = new LambdaQueryWrapper<>(policyEntity);

        queryWrapper.orderByDesc(PolicyEntity::getCreateTime);
        queryWrapper.like(com.alibaba.nacos.common.utils.StringUtils.isNotEmpty(param.getDataDate()), PolicyEntity::getDataDate, param.getDataDate());
        queryWrapper.like(com.alibaba.nacos.common.utils.StringUtils.isNotEmpty(param.getPolicyOverview()), PolicyEntity::getPolicyOverview, param.getPolicyOverview());
        queryWrapper.like(com.alibaba.nacos.common.utils.StringUtils.isNotEmpty(param.getInitiatingDepartment()), PolicyEntity::getInitiatingDepartment, param.getInitiatingDepartment());

        IPage page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page(pageForm.getCurrent(), pageForm.getSize());
        IPage<PolicyEntity> policyEntityIPage = baseMapper.selectPage(page, queryWrapper);
        return policyEntityIPage;
    }
}
