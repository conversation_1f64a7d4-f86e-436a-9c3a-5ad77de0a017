package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.service.NationalStatisticsIndexService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Classname TotalRetailSalesController
 * @Description TODO
 * @Date 2024/11/13 13:41
 */
@Api(tags = "国家统计指标获取")
@RestController
@RefreshScope
@RequestMapping("/nationalStatisticsIndex")
public class NationalStatisticsIndexController {
    @Autowired
    private NationalStatisticsIndexService nationalStatisticsIndexService;

    /**
     * 零售总额获取
     * @return
     */
    @ApiOperation("国家统计指标获取")
    @GetMapping("/capture")
    public Result nationalStatisticsIndexCapture(@RequestParam String type, @RequestParam String date, @CurrentUser User user){
        return nationalStatisticsIndexService.nationalStatisticsIndexCapture(type,date,user);
    }
}
