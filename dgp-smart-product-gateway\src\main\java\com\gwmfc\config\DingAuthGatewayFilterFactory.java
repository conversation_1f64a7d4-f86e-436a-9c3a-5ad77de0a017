package com.gwmfc.config;

import com.gwmfc.filter.DingTokenFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @classname DingAuthGatewayFilterFactory
 * @description 外部服务api调用通过 dingding token 进行校验 此种方式支持nacos 配置
 * @date 2023/2/22 15:20
 */
@Slf4j
@Component
public class DingAuthGatewayFilterFactory extends AbstractGatewayFilterFactory<DingAuthGatewayFilterFactory.NameConfig> {

    public DingAuthGatewayFilterFactory(){
        super(NameConfig.class);
    }

    @Resource
    private DingTokenFilter dingTokenFilter;

    @Override
    public GatewayFilter apply(NameConfig config) {
        return dingTokenFilter;
    }

    public static class NameConfig {

        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

    }
}
