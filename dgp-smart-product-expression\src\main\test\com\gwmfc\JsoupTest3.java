package com.gwmfc;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023年08月14日 13:16
 */
@SpringBootTest
public class JsoupTest3 {
    public static final String header = "http://cpcaauto.com/";

    @Test
    public void downloadByYearMonth() {
        String year = "2023";
        String month = "7";
        String url = "http://cpcaauto.com/news.php?types=csjd&anid=129&nid=28";
        try {
            Document documentListPage = Jsoup.connect(url).get();
            Elements links = documentListPage.select(".list_d").select("li");
            for (Element link : links) {
//                if (link.select("span").text().contains(year) && link.select("span").text().contains(month)) {
                    TextNode textNode = (TextNode) link.select("a").get(0).childNodes().get(0);
                    pageContent(textNode.getWholeText(), header.concat(link.select("a").get(0).attributes().get("href")));
//                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void pageContent(String wholeText, String href) {
        try {
            Document document = Jsoup.connect(href).get();
            Elements links = document.select(".read_content").select("img");

            links.forEach(link -> {
                System.out.println(wholeText);
                System.out.println(link);
            });
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
