package com.gwmfc.service.approve;

import com.gwmfc.constant.ProductProposalApproveEnum;
import com.gwmfc.constant.ProductProposalEnum;
import com.gwmfc.dao.ProductProposalApprovalDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductProposalApprovalStatusDto;
import com.gwmfc.dto.ProductProposalDto;
import com.gwmfc.entity.ProductProposalApprovalStatusEntity;
import com.gwmfc.service.ProductProposalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月09日 15:21
 */
@Slf4j
@Component("product_proposal_passed")
public class ProductProposalSaveApproval implements ProductProposalApprovalStrategy {
    @Resource
    private ProductProposalApprovalDao productProposalApprovalDao;
    @Resource
    private ProductProposalService productProposalService;

    @Override
    public void doApproval(ProductProposalDto productProposalDto, ProductProposalApprovalStatusDto productProposalApprovalStatusDto, User user, List<MultipartFile> addFileList) {
        ProductProposalApprovalStatusEntity productProposalApprovalStatusEntity = new ProductProposalApprovalStatusEntity();
        BeanUtils.copyProperties(productProposalApprovalStatusDto, productProposalApprovalStatusEntity);
        productProposalApprovalStatusEntity.setProductProposalId(productProposalDto.getId());
        productProposalApprovalStatusEntity.setCreateTime(LocalDateTime.now());
        productProposalApprovalStatusEntity.setCreateUser(user.getName());
        productProposalApprovalStatusEntity.setStep(ProductProposalEnum.PRODUCT_PROPOSAL_PASSED);//6
        productProposalApprovalStatusEntity.setStatus(ProductProposalApproveEnum.APPROVE);
        productProposalApprovalStatusEntity.setApproveStatus(ProductProposalApproveEnum.APPROVE);
        productProposalApprovalDao.insert(productProposalApprovalStatusEntity);

        productProposalDto.setResetId(productProposalApprovalStatusEntity.getId());
        productProposalDto.setCurrentStep(ProductProposalEnum.PRODUCT_PROPOSAL_DOC_CREATE);//7
        productProposalService.productProposalAnnexSave(productProposalDto, addFileList, user);
    }
}
