package com.gwmfc.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname MenuDto
 * @Date 2021/8/6 14:48
 */
@ApiModel(value = "MenuDto")
@Data
public class MenuDto {

    private Long menuId;
    /**
     * 父菜单ID，一级菜单为0
     */
    private Long parentId;
    /**
     * 菜单名称
     */
    private String name;
    /**
     * 菜单地址
     */
    private String url;
    /**
     * 授权信息
     */
    private String perms;
    /**
     * 类型：菜单，页面，按钮
     */
    private Integer type;
    /**
     * 菜单图标
     */
    private String icon;
    /**
     * 菜单顺序
     */
    private Integer orderNum;

    /**
     * 路由名称
     */
    private String routeName;

    /**
     * 外链
     */
    private String externalLink;

    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
}
