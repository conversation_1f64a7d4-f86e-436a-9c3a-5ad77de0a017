package com.gwmfc.constant;

import com.gwmfc.exception.SystemRuntimeException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Date: 2023/11/30
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 */

@Getter
@AllArgsConstructor
public enum VehicleTableTypeEnum {

    VEHICLE_SALES(1, "commercial_vehicle_sales"),
    USED_CAR_WEEKLY_TRADING(2, "used_car_weekly_trading"),
    USED_CAR_MONTHLY_TRADING(3, "used_car_monthly_trading"),
    ;

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 数据库表名
     */
    private final String tableName;

    /**
     * 根据类型查询数据库表名
     *
     * @param type
     * @return
     */
    public static String getTableNameByType(int type) {
        for (VehicleTableTypeEnum value : VehicleTableTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getTableName();
            }
        }
        throw new SystemRuntimeException("表名不存在");
    }
}
