package com.gwmfc.dto;

import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalSubsectionLendDto
 * @Description TODO
 * @Date 2023/10/26 15:17
 */
@Data
@ApiModel(value = "还款方式参数-分段贷动态变化dto")
public class ProductCalSubsectionLendDto {
    @ApiModelProperty("还款期次")
    private Integer subRepaymentTimeLimit;
    @ApiModelProperty("还款比例（%）")
    private Double subRepaymentRatio;
    @ApiModelProperty("还款额")
    private Double subRepaymentMoney;
    @ApiModelProperty("还款笔数")
    private Integer subRepaymentCount;
//    @ApiModelProperty("条目")
//    private String item;
//
//    @ApiModelProperty("值")
//    private String value;
}
