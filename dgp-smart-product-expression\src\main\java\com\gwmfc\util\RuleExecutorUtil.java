package com.gwmfc.util;


import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Classname RuleExecutorUtil
 * @Description TODO
 * @Date 2024/7/3 11:02
 */
@Component
@Slf4j
public class RuleExecutorUtil {
    public static Object executeRule(String script, Map<String, Object> variables) {
        // 创建变量绑定
        Binding binding = new Binding(variables);

        GroovyShell groovyShell = new GroovyShell(binding);

        try {
            // 解析并执行脚本
            return groovyShell.evaluate(script);
        } catch (Exception e) {
            // 处理执行脚本时可能发生的异常
            log.error("执行脚本异常",e);
            return null;
        }
    }
}
