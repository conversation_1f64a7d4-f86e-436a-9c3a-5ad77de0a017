package com.gwmfc.service;

import com.gwmfc.bo.ProvinceCityBo;
import com.gwmfc.dao.AgencyProvinceDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年09月14日 13:15
 */
@Service
@Slf4j
public class AgencyProvinceService {
    @Resource
    private AgencyProvinceDao agencyProvinceDao;

    public List<String> catchAllAgency(String province, String city) {
        return agencyProvinceDao.catchAllAgency(province, city);
    }

    public List<ProvinceCityBo> catchAllProvince() {
        return agencyProvinceDao.catchAllProvince();
    }

    public List<String> getManagerStaffNo(String province, String city) {
        return agencyProvinceDao.getManagerStaffNo(province, city);
    }
}
