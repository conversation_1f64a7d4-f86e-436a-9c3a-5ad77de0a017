package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.CompeteInfoBatchDto;
import com.gwmfc.dto.CompeteInfoDto;
import com.gwmfc.dto.CompeteInfoQueryDto;
import com.gwmfc.service.CompeteInfoCollectionService;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname CompeteInfoCollectionController
 * @Description TODO
 * @Date 2024/12/11 14:48
 */
@Api(tags = "竞品信息收集")
@RestController
@RequestMapping("/competeInfo")
public class CompeteInfoCollectionController {

    @Autowired
    private CompeteInfoCollectionService competeInfoCollectionService;

    @ApiOperation("竞对信息保存")
    @PostMapping(value = "/save", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result saveCompeteInfo(@RequestPart("competeInfoDto") CompeteInfoDto competeInfoDto, @RequestPart(name = "attachmentList", required = false) List<MultipartFile> attachmentList, @RequestPart User user){
        return competeInfoCollectionService.saveCompeteInfo(competeInfoDto,attachmentList,user);
    }

    @ApiOperation("获取竞对信息")
    @PostMapping(value = "/getCompeteInfo")
    public Result getCompeteInfo(@RequestBody CompeteInfoQueryDto competeInfoQueryDto){
        return competeInfoCollectionService.getCompeteInfo(competeInfoQueryDto);
    }

    @ApiOperation("竞对信息批次修改")
    @PostMapping(value = "/update", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result updateCompeteInfo(@RequestPart("competeInfoDto") CompeteInfoDto competeInfoDto, @RequestPart(name = "attachmentList", required = false) List<MultipartFile> attachmentList, @RequestBody User user){
        return competeInfoCollectionService.updateCompeteInfo(competeInfoDto,attachmentList,user);
    }

    @ApiOperation("删除某批次竞对信息")
    @PostMapping("/delete}")
    public Result deleteCompeteInfo(@RequestBody List<Long> competeInfoIdList){
        return competeInfoCollectionService.deleteCompeteInfo(competeInfoIdList);
    }

    @ApiOperation("获取某批次下竞对信息详情")
    @PostMapping("/getCompeteInfoDetail}")
    public Result getCompeteInfoDetail(Long batchId){
        return competeInfoCollectionService.getCompeteInfoDetail(batchId);
    }

    @ApiOperation("获取所有竞对批次信息")
    @PostMapping("/getCompeteInfoBatchList")
    public Result getCompeteInfoBatchList(@RequestBody PageForm<CompeteInfoBatchDto> pageFormReq){
        return competeInfoCollectionService.getCompeteInfoBatchList(pageFormReq.getParam(), pageFormReq.getCurrent(), pageFormReq.getSize());
    }

    @ApiOperation("获取所有集团信息")
    @GetMapping("/getAllGroupInfoByUserInfo")
    public Result getAllGroupInfoByUserInfo(@RequestParam(value = "userName") String userName){
        return competeInfoCollectionService.getAllGroupInfoByUserInfo(userName);
    }

}
