package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalGroupWeightingAvgIrrDto
 * @Description 产品方案整体irr/加权irr/加权平均irr
 * @Date 2023/10/31 9:42
 */
@Data
@ApiModel(value = "产品方案整体irr/加权irr")
public class ProductCalGroupWeightingIrrDto {
    @ApiModelProperty("产品组名称")
    private String groupName;
    @ApiModelProperty("组间加权比例")
    private Double groupProportion;
    @ApiModelProperty("组间加权irr")
    private Double groupWeightingIrr;
    private List<ProductCalResultRangeDto> productCalResultRangeDtoList;
}
