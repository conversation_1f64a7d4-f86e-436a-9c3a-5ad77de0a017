package com.gwmfc.dao;

import com.gwmfc.entity.ProductCalDeductMoneyHandChargeEntity;
import com.gwmfc.entity.ProductCalLoanHandChargeEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalDao
 * @Description TODO
 * @Date 2023/9/24 10:40
 */
@Mapper
public interface ProductCalDao {
    List<ProductCalLoanHandChargeEntity> getLoanHandCharge(Double getLoanHandCharge);

    List<ProductCalDeductMoneyHandChargeEntity> getDeductMoneyHandCharge(Double getLoanHandCharge);

    List<ProductCalLoanHandChargeEntity> getLoanHandChargeDetail();

    List<ProductCalDeductMoneyHandChargeEntity> getDeductMoneyHandChargeDetail();
}
