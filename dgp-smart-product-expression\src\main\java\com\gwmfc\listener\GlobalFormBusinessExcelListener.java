package com.gwmfc.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Cell;
import com.alibaba.excel.metadata.data.DataFormatData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.gwmfc.constant.UpdateFrequencyEnum;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.service.GlobalFormBusinessService;
import com.gwmfc.util.TableUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.gwmfc.constant.constant.*;

/**
 * 通用业务表导入
 *
 * <AUTHOR>
 * @date 2023/2/24
 */
@Slf4j
public class GlobalFormBusinessExcelListener extends AnalysisEventListener<Map<Integer, String>> {
    public static final String LINE = "-";
    public static final String BIAS = "/";
    /**
     * 表头map
     */
    private Map<Integer, String> headerTableFieldMap = new HashMap<>();

    /**
     * 数据
     */
    private final List<Map<String, Object>> dataList = new ArrayList<>();

    /**
     * 通用业务处理类
     */
    private GlobalFormBusinessService globalFormBusinessService;

    /**
     * 表名称
     */
    private final String tableName;

    /**
     * 用户名
     */
    private final String userName;

    /**
     * 更新频率
     */
    private final Integer updateFrequency;

    /**
     * 批量保存最大值
     */
    private Long SAVE_BATCH_MAX;

    /**
     * 批量保存最大值
     */
    private String batch;

    private Long startTime=0L;

    public GlobalFormBusinessExcelListener(GlobalFormBusinessService globalFormBusinessService,
                                           String tableName, String userName, Integer updateFrequency, Long SAVE_BATCH_MAX, String batch) {
        this.globalFormBusinessService = globalFormBusinessService;
        this.tableName = tableName;
        this.userName = userName;
        this.updateFrequency = updateFrequency;
        this.SAVE_BATCH_MAX = SAVE_BATCH_MAX;
        this.batch = batch;
        this.startTime = System.currentTimeMillis();
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        try {
            Map<String, String> tableFieldMappings = TableUtils.findTableBusFieldMappings(tableName);
            Map<Integer, String> tempHeaderTableFieldMap = new HashMap<>(64);
            for (Map.Entry<Integer, String> headEntry : headMap.entrySet()) {
                for (Map.Entry<String, String> tableEntry : tableFieldMappings.entrySet()) {
                    if (tableEntry.getValue().equals(headEntry.getValue())) {
                        tempHeaderTableFieldMap.put(headEntry.getKey(), tableEntry.getKey());
                        break;
                    }
                }
            }
            Assert.state(tempHeaderTableFieldMap.size() == tableFieldMappings.size(), "表头不一致");
            if (tempHeaderTableFieldMap.size() != tableFieldMappings.size()) {
                throw new SystemRuntimeException("表头不一致");
            }
            this.headerTableFieldMap = tempHeaderTableFieldMap;
        } catch (IOException e) {
            log.error("[global business excel import error] [table name:{}]", tableName);
        }
    }

    @Override
    public void invoke(Map<Integer, String> cellDataMap, AnalysisContext analysisContext) {
        Map<Integer, Cell> cellMap = analysisContext.readRowHolder().getCellMap();
        Map<String, Object> dataMap = new HashMap<>(64);
        for (Map.Entry<Integer, Cell> entry : cellMap.entrySet()) {
            String tableField = headerTableFieldMap.get(entry.getKey());
            ReadCellData<?> cellData = (ReadCellData<?>) entry.getValue();
            DataFormatData dataFormatData = cellData.getDataFormatData();

            String yearMonthDayRegex = "(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|" +
                    "((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|" +
                    "((0[48]|[2468][048]|[3579][26])00))-02-29)$";
            boolean dateFormatOrNot = StringUtils.hasText(cellData.getStringValue()) && cellData.getStringValue().matches(yearMonthDayRegex);
            if (dataFormatData == null && dateFormatOrNot) {
                LocalDate localDate = toLocalDate(cellData);
                dataMap.put(tableField, localDate == null ? "" : localDate.toString());
            } else if (dateFormatOrNot || (dataFormatData != null && ("yyyy-mm-dd".equals(dataFormatData.getFormat()) || "yyyy/m/d".equals(dataFormatData.getFormat())))) {
                LocalDate localDate = toLocalDate(cellData);
                dataMap.put(tableField, localDate == null ? "" : localDate.toString());
            } else {
                dataMap.put(tableField, cellDataMap.get(entry.getKey()));
            }
        }
        Map<String, Object> targetDataMap = new HashMap<>(64);
        for (Map.Entry<Integer, String> headerEntry : headerTableFieldMap.entrySet()) {
            String field = headerEntry.getValue();
            targetDataMap.put(field, dataMap.get(field));
        }

        targetDataMap.put("create_time", this.batch);
        targetDataMap.put("create_user", this.userName);
        targetDataMap.put("data_date", dealDataDate(dataMap));
        dataList.add(targetDataMap);
        if (this.dataList.size() >= SAVE_BATCH_MAX) {
            saveBatch();
        }
    }

    /**
     * 转日期
     *
     * @param cellData
     * @return
     */
    private LocalDate toLocalDate(ReadCellData<?> cellData) {
        if (cellData.getType().equals(CellDataTypeEnum.NUMBER)) {
            LocalDate localDate = LocalDate.of(1900, 1, 1);
            //excel 有些奇怪的bug, 导致日期数差2
            localDate = localDate.plusDays(cellData.getNumberValue().longValue() - 2);
            return localDate;
        } else if (cellData.getType().equals(CellDataTypeEnum.STRING)) {
            if (cellData.getStringValue().contains(LINE)) {
                return LocalDate.parse(cellData.getStringValue(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else if (cellData.getStringValue().contains(BIAS)) {
                return LocalDate.parse(cellData.getStringValue(), DateTimeFormatter.ofPattern("yyyy/MM/dd"));

            }
        }
        return null;
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        saveBatch();
        log.info("parse {} data total cost {} s",this.tableName,(System.currentTimeMillis()-startTime)/1000);
    }

    /**
     * 批量保存
     */
    private void saveBatch() {
        log.info("[global business excel import save] [size:{}] table name :{}", this.dataList.size(),this.tableName);
        if (!dataList.isEmpty()) {
            Set<String> columnSet = dataList.get(0).keySet();
            globalFormBusinessService.batchAddData(this.tableName, columnSet, dataList);
        }
        dataList.clear();
    }

    public String dealDataDate(Map<String, Object> dataMap){
        String year = (String) dataMap.get(YEAR);
        switch (UpdateFrequencyEnum.getUpdateFrequencyEnumByType(updateFrequency)){
            case MONTH_FREQUENCY:
                String month = (String) dataMap.get(MONTH);
                return year+"-"+month;
            case WEEK_FREQUENCY:
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                int week = Integer.parseInt(dataMap.get(WEEK).toString().split("_")[1]);
                String numStr =  week< 10 ? "0" + String.valueOf(week) : String.valueOf(week);
                String weekDate = String.format("%s-W%s-%s", year, numStr, DayOfWeek.SUNDAY.getValue());
                return dateTimeFormatter.format(LocalDate.parse(weekDate, DateTimeFormatter.ISO_WEEK_DATE));
            case DAY_FREQUENCY:
                return dataMap.get(DATA_DATE).toString();
            default:
                return "";
        }
    }

}
