package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.GlobalFormBusinessDto;
import com.gwmfc.entity.ProductDataMartEntity;
import com.gwmfc.service.ExpressionService;
import com.gwmfc.service.ProductDataMartService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;

@Api(tags = "通用业务表管理")
@RestController
@RequestMapping("/expression")
public class ExpressionController {
    @Resource
    private ExpressionService expressionService;
    @Resource
    private ProductDataMartService productDataMartService;

    /**
     * 导入数据
     * @param file
     * @param formId
     * @param user
     * @return
     */
    @ApiOperation("导入数据")
    @PostMapping("/excel/import")
    public Result importData(@RequestBody @RequestParam("file") MultipartFile file,
                             @RequestBody @RequestParam("formId") int formId,
                             @ApiIgnore @CurrentUser User user) throws IOException {
        expressionService.importData(file, formId, user);
        return Result.ok();
    }

    /**
     * 导出数据
     * @param queryDto
     * @return
     */
    @ApiOperation("导出数据")
    @PostMapping("/excel/export")
    public void exportData(@RequestBody @Valid GlobalFormBusinessDto queryDto, HttpServletResponse response) throws IOException {
        ProductDataMartEntity productDataMartEntity = productDataMartService.getProductDataMartEntityByFormId(queryDto.getFormId());
        queryDto.setTableName(productDataMartEntity.getTableName());
        queryDto.setUpdateFrequency(productDataMartEntity.getUpdateFrequency());
        expressionService.export(queryDto, response);
    }

}
