package com.gwmfc.util;

/**
 * @Classname Const
 * @Description 常量类,项目中所有常量均在此类维护
 * @Date 2021/3/31 17:00
 * <AUTHOR> yub<PERSON><PERSON>
 */
public class Const {

    public static  final Integer DATAX_OPERATOR = 1;

    public static  final Integer HIVE_SQL_OPERATOR=2;

    public static  final Integer DATA_QUALITY_OPERATOR=3;

    public static  final Integer SENSOR_OPERATOR=4;

    public static  final Integer SERVICE_FILE_OPERATOR=5;

    public static final Integer SENSOR_FILE_OPERATOR=6;

    public static final Integer SERVICE_API_OPERATOR=7;

    public static final Integer DATAX_CUSTOMER_OPERATOR=8;

    public static final Integer JSON_PARSER_OPERATOR=9;

    public static final Integer SPARK_TASK_OPERATOR=10;

    public static final Integer REALTIME_TASK_OPERATOR=11;

    public static final Integer REALTIME_SQL_TASK_OPERATOR=12;

    public static final Integer SHELL_OR_PYTHON_TASK_OPERATOR=13;

    public static  final Integer LOSE_EFFICACY= 2;

    public static  final Integer ENABLE_REFERABLE= 1;

    public static  final Integer DISABLE_REFERALBE=0;

    public static  final Integer SYNCHRONOUS_INCR = 1;

    public static  final Integer SYNCHRONOUS_ALL = 0;

    public static final String DWD_LAYER="DWD";

    public static final String DM_LAYER="DM";

    public static final String DWS_LAYER="DWS";

    public static final String STG_LAYER="STG";

    public static final Integer DRAFT_STATUS=0;

    /**
     * 服务申请使用状态
     * 需要审批
     */
    public static final Integer APPROVED=1;

    public static final String ODS_LAYER="ODS";

    public static final String HIVE="hive";

    public static final String MYSQL="mysql";

    public static  final String SQLSERVER="sqlserver";

    public static  final String ORACLE="oracle";

    public static  final String POSTGRESQL="postgresql";

    public static  final String FTP_TYPE="ftp";

    public static  final String MONGODB="mongodb";

    /**
     * 服务申请使用状态
     * 不需要审批
     */
    public static final Integer DONT_APOROVE=2;

    /**
     * 服务申请
     * 已审批
     */
    public static final Integer COMPLETE_APPROVE=3;

    public static final int APPLY_ONLINE_STATUS = 1;

    public static final int APPLY_DELETE_STATUS = 5;

    public static final Integer REJECT=-1;

    public static final Integer API_INTEGR = 1;

    public static final Integer MYSQL_INTEGR = 2;

    public static final Integer FILE_INTEGR = 3;

    public static final Integer MQ_INTEGR = 4;

    public static  final String TOKEN_AUTHORIZATION="Authorization";

    public static final String TOKEN_BEARER = "Bearer";

    /**
     * 上传文件类型，如果支持多个请用逗号分割
     */
    public static final String ALLOW_FILE_TYPE = "jar";

    public static final String SYS_NOTIFY_TOPIC = "sys_notify";

    public static final String SYS_LOGOUT_TOPIC = "sys_logout";

    public static final String TABLE_STRUCTURE_NOTIFY_LIST = "structure_notifyList";

    /**
     * 任务开始状态
     * 任务结束状态
     */
    public static final int TASK_START_STATUS = 1;
    public static final int TASK_END_STATUS = 2;
}
