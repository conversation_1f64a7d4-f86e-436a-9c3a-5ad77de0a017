package com.gwmfc;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023年08月14日 13:16
 */
@SpringBootTest
public class JsoupTest {
    public static final String header = "http://cpcaauto.com/";

    @Test
    public void download() throws IOException {
        Document document = Jsoup.connect("http://cpcaauto.com/news.php?types=csjd&anid=129&nid=27").get();
        Set<String> hrefSet = new HashSet<>();
        hrefSet.add("http://cpcaauto.com/news.php?types=csjd&anid=129&nid=27");
        document.select("div[id=pager]").select("a[href]").forEach(a -> {
            hrefSet.add("http://cpcaauto.com/".concat(a.attributes().get("href")));
        });
        hrefSet.forEach(pageUrl -> {
            if (StringUtils.isNotBlank(pageUrl) && pageUrl.contains("news.php?types=csjd&anid=129&nid=27") && !pageUrl.contains("next")) {
                try {
                    Document documentListPage = Jsoup.connect(pageUrl).get();
                    Map map = new HashMap();
                    Elements links = documentListPage.select(".list_d").select("a[href]");
                    for (Element link : links) {
                        TextNode textNode = (TextNode) link.childNodes().get(0);
                        map.put(textNode.getWholeText().trim(), header.concat(link.attributes().get("href")));
                    }
                    pageContent(map);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void pageContent(Map map) {
        map.forEach((k, v) -> {
            try {
                Document document = Jsoup.connect((String) v).get();
                Elements links = document.select(".read_content").select("img");

                links.forEach(link -> {
                    downLoad(String.valueOf(k).replaceAll("([　]|\\s|\\u00A0)+", ""), "http://cpcaauto.com".concat(link.attributes().get("src")));
                });
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }

    void downLoad(String title, String picUrl) {
        FileOutputStream fos = null;
        BufferedInputStream bis = null;
        HttpURLConnection httpUrl = null;
        int size = 0;
        byte[] buf = new byte[1024];

        File dir = new File("D:\\webmagic1\\" + title);
        System.out.println(title);
        if (!dir.exists()) {
            if (dir.mkdir()) {
                System.out.println("创建目录成功！");
            } else {
                System.out.println("创建目录失败！");
            }
        }

        String picName = picUrl.substring(picUrl.lastIndexOf("/") + 1);
        System.out.println(picName);
        File file = new File("D:\\webmagic1\\" + title + "\\" + picName);
        try {
            URL url = new URL(picUrl);
            httpUrl = (HttpURLConnection) url.openConnection();
            httpUrl.connect();
            bis = new BufferedInputStream(httpUrl.getInputStream());
            fos = new FileOutputStream(file);
            while ((size = bis.read(buf)) != -1) {
                fos.write(buf, 0, size);
            }
            fos.flush();
            fos.close();
            bis.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
