package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname MonthPassengerCarEntity
 * @Description TODO
 * @Date 2023/8/1 17:26
 */
@Data
@TableName("month_passenger_car")
@ExcelIgnoreUnannotated
public class MonthPassengerCarEntity extends BaseEntity {
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("日期年")
    @TableFieldMapping(value = "date_year", comment = "日期年")
    private String dateYear;

    @ExcelProperty("车身颜色")
    @TableFieldMapping(value = "vehicle_color", comment = "车身颜色")
    private String vehicleColor;

    @ExcelProperty("排量ml")
    @TableFieldMapping(value = "displacement_ml", comment = "排量ml")
    private String displacementMl;

    @ExcelProperty("外廊宽")
    @TableFieldMapping(value = "outer_corridor_width", comment = "外廊宽")
    private String outerCorridorWidth;

    @ExcelProperty("市")
    @TableFieldMapping(value = "city", comment = "市")
    private String city;

    @ExcelProperty("车型类别")
    @TableFieldMapping(value = "vehicle_category", comment = "车型类别")
    private String vehicleCategory;

    @ExcelProperty("年份")
    @TableFieldMapping(value = "year", comment = "年份")
    private String year;

    @ExcelProperty("区")
    @TableFieldMapping(value = "county", comment = "区")
    private String county;

    @ExcelProperty("使用性质")
    @TableFieldMapping(value = "use_nature", comment = "使用性质")
    private String useNature;

    @ExcelProperty("燃料种类")
    @TableFieldMapping(value = "fuel_types", comment = "燃料种类", queryItem = true)
    private String fuelTypes;

    @ExcelProperty("厢数")
    @TableFieldMapping(value = "compartments_number", comment = "厢数")
    private String compartmentsNumber;

    @ExcelProperty("车身形式")
    @TableFieldMapping(value = "vehicle_form", comment = "车身形式")
    private String vehicleForm;

    @ExcelProperty("发动机型号")
    @TableFieldMapping(value = "engine_model", comment = "发动机型号")
    private String engineModel;

    @ExcelProperty("排放水平")
    @TableFieldMapping(value = "emission_level", comment = "排放水平")
    private String emissionLevel;

    @ExcelProperty("企业简称")
    @TableFieldMapping(value = "business_abbreviation", comment = "企业简称", queryItem = true)
    private String businessAbbreviation;

    @ExcelProperty("细化车型")
    @TableFieldMapping(value = "refined_vehicle_type", comment = "细化车型")
    private String refinedVehicleType;

    @ExcelProperty("油耗")
    @TableFieldMapping(value = "oil_consumption", comment = "油耗")
    private String oilConsumption;

    @ExcelProperty("数量")
    @TableFieldMapping(value = "number", comment = "数量")
    private String number;

    @ExcelProperty("变速器")
    @TableFieldMapping(value = "transmission", comment = "变速器")
    private String transmission;

    @ExcelProperty("省")
    @TableFieldMapping(value = "province", comment = "省")
    private String province;

    @ExcelProperty("月份")
    @TableFieldMapping(value = "data_month", comment = "月份")
    private String dataMonth;

    @ExcelProperty("车型系别")
    @TableFieldMapping(value = "vehicle_series", comment = "车型系别")
    private String vehicleSeries;

    @ExcelProperty("排量")
    @TableFieldMapping(value = "displacement", comment = "排量")
    private String displacement;

    @ExcelProperty("整备质量")
    @TableFieldMapping(value = "curb_weight", comment = "整备质量")
    private String curbWeight;

    @ExcelProperty("功率")
    @TableFieldMapping(value = "power", comment = "功率")
    private String power;

    @ExcelProperty("品牌")
    @TableFieldMapping(value = "brand", comment = "品牌", queryItem = true)
    private String brand;

    @ExcelProperty("城市级别")
    @TableFieldMapping(value = "city_level", comment = "城市级别", queryItem = true)
    private String cityLevel;

    @ExcelProperty("区域")
    @TableFieldMapping(value = "area", comment = "区域")
    private String area;

    @ExcelProperty("耗电量")
    @TableFieldMapping(value = "power_consume", comment = "耗电量")
    private String powerConsume;

    @ExcelProperty("企业名称")
    @TableFieldMapping(value = "business_name", comment = "企业名称")
    private String businessName;

    @ExcelProperty("驱动形式")
    @TableFieldMapping(value = "driving_form", comment = "驱动形式")
    private String drivingForm;

    @ExcelProperty("月份")
    @TableFieldMapping(value = "date_month", comment = "月份")
    private String dateMonth;

    @ExcelProperty("商战级别")
    @TableFieldMapping(value = "trade_level", comment = "商战级别")
    private String tradeLevel;

    @ExcelProperty("集团简称")
    @TableFieldMapping(value = "group_abbreviation", comment = "集团简称")
    private String groupAbbreviation;

    @ExcelProperty("县区分类")
    @TableFieldMapping(value = "county_classify", comment = "县区分类")
    private String countyClassify;

    @ExcelProperty("发动机企业")
    @TableFieldMapping(value = "engine_enterprise", comment = "发动机企业")
    private String engineEnterprise;

    @ExcelProperty("车型")
    @TableFieldMapping(value = "vehicle_type", comment = "车型", queryItem = true)
    private String vehicleType;

    @ExcelProperty("价格段")
    @TableFieldMapping(value = "price_segment", comment = "价格段", queryItem = true)
    private String priceSegment;

    @ExcelProperty("额定载客")
    @TableFieldMapping(value = "rated_passenger_capacity", comment = "额定载客")
    private String ratedPassengerCapacity;

    @ExcelProperty("外廊长")
    @TableFieldMapping(value = "outer_corridor_length", comment = "外廊长")
    private String outerCorridorLength;

    @ExcelProperty("电动汽车续驶里程")
    @TableFieldMapping(value = "driving_range", comment = "电动汽车续驶里程")
    private String drivingRange;

    @ExcelProperty("所有权")
    @TableFieldMapping(value = "ownership", comment = "所有权")
    private String ownership;

    @ExcelProperty("轴距")
    @TableFieldMapping(value = "wheelbase", comment = "轴距")
    private String wheelbase;

    @ExcelProperty("发动机扭矩")
    @TableFieldMapping(value = "engine_torque", comment = "发动机扭矩")
    private String engineTorque;

    @ExcelProperty("外廊高")
    @TableFieldMapping(value = "outer_corridor_height", comment = "外廊高")
    private String outerCorridorHeight;

    @ExcelProperty("车型级别")
    @TableFieldMapping(value = "vehicle_level", comment = "车型级别")
    private String vehicleLevel;

    @ExcelProperty("轮胎规格")
    @TableFieldMapping(value = "wheel_size", comment = "轮胎规格")
    private String wheelSize;

    @ExcelProperty("总质量")
    @TableFieldMapping(value = "total_mass", comment = "总质量")
    private String totalMass;
}