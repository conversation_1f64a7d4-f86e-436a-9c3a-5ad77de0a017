package com.gwmfc.constant;

/**
 * <AUTHOR>
 * @date 2023年10月18日 13:48
 */
public class ProductProposalApproveEnum {
    /**
     * 通过1
     */
    public static final int APPROVE = 1;

    /**
     * 拒绝-1
     */
    public static final int REJECT = -1;

    /**
     * 新增
     */
    public static final int ADD = 2;

    /**
     * 更新
     */
    public static final int UPDATE = 3;

    /**
     * 类型（1：风险；2：商务；3：财务）
     */
    public static final Integer RISK_VERIFY = 1;

    /**
     * 类型（1：风险；2：商务；3：财务）
     */
    public static final Integer BUSINESS_VERIFY = 2;

    /**
     * 类型（1：风险；2：商务；3：财务）
     */
    public static final Integer FINANICAL_VERIFY = 3;
}
