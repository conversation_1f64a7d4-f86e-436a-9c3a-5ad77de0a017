package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.gwmfc.bo.CpcaautoBo;
import com.gwmfc.constant.UpdateFrequencyEnum;
import com.gwmfc.entity.CpcaautoCallRecordEntity;
import com.gwmfc.entity.ProductDataMartEntity;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.JsoupUtil;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.gwmfc.constant.constant.*;

/**
 * <AUTHOR>
 * @date 2024年03月08日 16:28
 */
@Service
@Slf4j
public class MonthlyUsedCarTransactionVolumeService {
    @Resource
    private CpcaautoService cpcaautoService;
    @Resource
    private GlobalFormBusinessService globalFormBusinessService;
    @Resource
    private JsoupUtil jsoupUtil;
    @Resource
    private ProductDataMartService productDataMartService;

    public void catchMonthlyUsedCarTransactionVolume(String year, String month, String tableName, String userName) {
        String title = "";
        String dataDate = "data_date";
        Integer updateFrequency = null;
        ProductDataMartEntity productDataMartEntity = productDataMartService.getProductDataMartEntityByTableName(tableName);
        if (null != productDataMartEntity) {
            updateFrequency = productDataMartEntity.getUpdateFrequency();
        }
        switch (tableName) {
            case "price_range_trading_volume":
                dataDate = "update_date";
                title = "二手车市场深度分析";
                break;
            case "cpcaautod_weekly_sales_volume":
                title = "【周度分析】车市扫描";
                break;
            case "cpcaautod_new_energy_sales_volume":
            case "cpcaautod_monthly_sales_volume":
                title = "全国乘用车市场分析";
                break;
        }
        try {
            usedCarDownload(year, month, title, userName, tableName, updateFrequency, dataDate);
        } catch (IOException e) {
            log.error("exception:{}",e);
        }
    }

    public void usedCarDownload(String year, String month, String title, String userName, String tableName, Integer updateFrequency, String dataDate) throws IOException {
        String url = "http://www.cpcaauto.com/search.php?types=search&page=1&keywords=" + title;

        Document pageNumDocument = jsoupUtil.connect(url);
        Elements elements = pageNumDocument.select(".pagebar").get(1).select("a");
        Set pageUrlSet = new HashSet();
        pageUrlSet.add(url);
        elements.forEach(element -> {
            if (StringUtils.isNotEmpty(element.attr("href"))) {
                pageUrlSet.add("http://www.cpcaauto.com/".concat(element.attr("href")));
            }
        });
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        AtomicReference<Boolean> tag = new AtomicReference<>(false);
        pageUrlSet.forEach(pageUrl -> {
            Document documentListPage = jsoupUtil.connect(pageUrl.toString());
            Elements links = documentListPage.select(".list_d").select("ul").select("li");
            CpcaautoCallRecordEntity cpcaautoCallRecordEntity = new CpcaautoCallRecordEntity();

            for (Element link : links) {
                Boolean judgeTitle = false;
                String dataStartDate = new String();
                if (UpdateFrequencyEnum.getUpdateFrequencyEnumByType(updateFrequency).equals(UpdateFrequencyEnum.WEEK_FREQUENCY)) {
                    judgeTitle = link.select("a").text().contains(year.concat(month));
                    String pageTitle = link.select("a").text();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    try {
                        dataStartDate = dateFormat.format(simpleDateFormat.parse(pageTitle.substring(pageTitle.indexOf("(")+1,pageTitle.indexOf("-"))));
                    } catch (ParseException e) {
                        try {
                            dataStartDate = dateFormat.format(simpleDateFormat.parse(pageTitle.substring(pageTitle.indexOf("（")+1,pageTitle.indexOf("-"))));
                        } catch (ParseException ex) {
                            ex.printStackTrace();
                        }
                        e.printStackTrace();
                    }
                } else if (UpdateFrequencyEnum.getUpdateFrequencyEnumByType(updateFrequency).equals(UpdateFrequencyEnum.MONTH_FREQUENCY)) {
                    judgeTitle = link.select("a").text().contains(year.concat("年").concat(String.valueOf(Integer.valueOf(month))));
                    //判断该月在不在
                    dataStartDate = year.concat("-").concat(month);
                }

                if (judgeTitle) {
                    if (globalFormBusinessService.selectRecordCountByDate(tableName, dataStartDate, dataDate)!=0) {
                        return;
                    }
                    List picUrlList = new ArrayList<>(links.size());
                    String childPageUrl = link.select("a").attr("href");
                    Elements elements1 = jsoupUtil.connect("http://www.cpcaauto.com/".concat(String.valueOf(childPageUrl))).select(".text").select("img");
                    elements1.select("img").forEach(element -> picUrlList.add("http://www.cpcaauto.com".concat(element.attr("src"))));
                    cpcaautoCallRecordEntity.setPageTitle(String.valueOf(link.select("a").text()).replaceAll("([　]|\\s|\\u00A0)+", ""));
                    cpcaautoCallRecordEntity.setPageUrl("http://www.cpcaauto.com/".concat(String.valueOf(childPageUrl)));
                    cpcaautoCallRecordEntity.setPictureUrlList(GsonUtil.toJson(picUrlList));
                    cpcaautoCallRecordEntity.setCallTime(dateTimeFormatter.format(LocalDateTime.now()));
                    cpcaautoCallRecordEntity.setTriggerMethod(2);
                    cpcaautoCallRecordEntity.setCreateUser(userName);
                    cpcaautoCallRecordEntity.setYear(year);
                    cpcaautoCallRecordEntity.setMonth(month);
                    cpcaautoCallRecordEntity.setSource(tableName);
                    cpcaautoService.save(cpcaautoCallRecordEntity);
                    cpcaautoService.analysisData(tableName, dateTimeFormatter.format(LocalDateTime.now()), userName, cpcaautoCallRecordEntity, "post-async");
                    tag.set(true);
                }
            }
        });
        if (!tag.get()) {
            log.info("该月页面不存在！：{}", year.concat("-").concat(month));
            throw new SystemRuntimeException("获取不到乘联会网站数据，或者该月数据已存在！");
        }
    }

    public void analysisPictureCallback(CpcaautoBo cpcaautoBo) {
        log.info("analysisPictureCallback : {}", cpcaautoBo);
        CpcaautoCallRecordEntity cpcaautoCallRecordEntity = cpcaautoService.selectByArticleNameAndCallTime(cpcaautoBo.getReqTime(), cpcaautoBo.getArticleName());
        if (cpcaautoCallRecordEntity != null) {
            //查询记录入库
            cpcaautoCallRecordEntity.setCallbackStatus(cpcaautoBo.getCode());
            cpcaautoCallRecordEntity.setRecallNum(cpcaautoBo.getRecallNum());
            cpcaautoCallRecordEntity.setCallbackTime(LocalDateTime.now());
            cpcaautoService.updateById(cpcaautoCallRecordEntity);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            if (cpcaautoBo.getCode() == 200) {
                //入库
                Set<String> columnSet = cpcaautoBo.getData().get(0).keySet();
                cpcaautoBo.getData().forEach(map -> {
                    map.put(CREATE_TIME, dateTimeFormatter.format(LocalDateTime.now()));
                    map.put(CREATE_USER, cpcaautoBo.getReqUserName());
                    if (map.get(MONTH) != null && Integer.valueOf(map.get(MONTH).toString()) < 10) {
                        map.put(DATA_DATE, map.get(YEAR).toString().concat("-0").concat(map.get(MONTH).toString()));
                    } else if (map.get(MONTH) != null) {
                        map.put(DATA_DATE, map.get(YEAR).toString().concat("-").concat(map.get(MONTH).toString()));
                    }

                    /**
                     * 判断如果是新能源回调数据，对data_date，update_date 格式 进行修改
                     */
                    if (cpcaautoBo.getTableName() != null && cpcaautoBo.getTableName().startsWith("new_energy_car")) {
                        String dataDate = convertDate((String)map.get(DATA_DATE));
                        if (dataDate != null) {
                            map.put(DATA_DATE, dataDate);
                        }
                        if("new_energy_car_total_market".equals(cpcaautoBo.getTableName())){
                            String updateDate = convertDate((String)map.get("update_date"));
                            if (updateDate != null) {
                                map.put("update_date", updateDate);
                            }
                        }
                    }
                });
                globalFormBusinessService.batchAddData(cpcaautoBo.getTableName(), columnSet, cpcaautoBo.getData());
            }
        }
    }

    public String convertDate(String inputDate) {
        if(StringUtils.isEmpty(inputDate)){
            return inputDate;
        }
        String year = inputDate.split("-")[0];
        String month = inputDate.split("-")[1];
        return String.format("%s-%02d", year, Integer.parseInt(month));
    }
}
