package com.gwmfc.dto;

import com.gwmfc.annotation.TableFieldMapping;
import com.gwmfc.entity.ProductCalCapitalCostEntity;
import com.gwmfc.entity.ProductCalFixCostEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname BasicParamDto
 * @Description 基本参数
 * @Date 2023/9/19 11:33
 */
@Data
@ApiModel(value = "基本参数")
public class ProductCalBasicParamDto {
    @ApiModelProperty("实际放款额")
    private Double actualLoanMoney;

    @ApiModelProperty("客户利率（%）")
    private Double customerInterestRate;

    @ApiModelProperty("实际利率（%）")
    private Double actualInterestRate;

    @ApiModelProperty("期限")
    private Integer timeLimit;

    @ApiModelProperty("期数")
    private Double periodNum;

    @ApiModelProperty("月供")
    private Double monthPayment;

    @ApiModelProperty("贴息")
    private Double subInterest;

    @ApiModelProperty("经销商基础佣金比例(%)")
    private Double dealerBasicCommissionRatio;

    @ApiModelProperty("经销商阶梯奖金比例(%)")
    private Double dealerLadderBonusRatio;

    @ApiModelProperty("经销商促销奖金比例(%)")
    private Double dealerSaleBonusRatio;

    @ApiModelProperty("经销商销售奖金")
    private Double dealerSellBonus;

    @ApiModelProperty("60天以上的逾期率")
    private Double sixtyDaysOverdueRate;

//    @ApiModelProperty("120天以上的逾期率")
//    private Double hundAndTwOverdueRate;

    @ApiModelProperty("外包催收回款率")
    private Double outsourcingCollectionBackRate;

//    @ApiModelProperty("诉讼回款率(%)")
//    private Double lawsuitBackRate;

    @ApiModelProperty("外包催收费率(%)")
    private Double outsourcingCollectionFeeRate;

//    @ApiModelProperty("诉讼催收费率(%)")
//    private Double lawsuitCollectionFeeRate;

    @ApiModelProperty("3天以上逾期率")
    private Double threeDaysOverdueRate;

    @ApiModelProperty("短账外包费")
    private Double dzOutsourcingFee;

    @ApiModelProperty("损失率(%)")
    private Double lossRate;

    @ApiModelProperty("后市场收入")
    private Double afterMarketIncome;

    @ApiModelProperty("特殊奖励")
    private Double specialReward;

    @ApiModelProperty("放款手续费")
    private Double loanHandCharge;

    @ApiModelProperty("扣款手续费")
    private Double deductMoneyHandCharge;

    @ApiModelProperty("资金成本")
    private Double capitalCost;

    @ApiModelProperty("固定成本")
    private Double fixedCost;

    @ApiModelProperty("总利息")
    private Double totalInterest;

    @ApiModelProperty("拨备率")
    private Double provisionRate;

    @ApiModelProperty("管理费分摊系数")
    private Double manageFeeShareCoefficient;

    @ApiModelProperty("是否农户贷 1是 0否")
    private Integer isFarmerLoan;
}
