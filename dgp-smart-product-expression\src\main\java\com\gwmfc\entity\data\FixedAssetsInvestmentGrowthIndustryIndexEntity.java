package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname TotalRetailSalesEntity
 * @Description 社会消费品零售总额
 * @Date 2024/11/13 13:13
 */
@Data
@TableName("fixed_assets_investment_growth_industry_index")
@ExcelIgnoreUnannotated
public class FixedAssetsInvestmentGrowthIndustryIndexEntity extends BaseEntity{
    @TableId(type = IdType.AUTO)
    private Long id;

    private String code;

    @ExcelProperty("数据日期")@TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("指标")
    @TableFieldMapping(value = "index_name", comment = "指标", queryItem = true)
    private String indexName;

    @ExcelProperty("指标值")
    @TableFieldMapping(value = "index_value", comment = "指标值")
    private String indexValue;

    @ExcelProperty("网页地址")
    @TableFieldMapping(value = "page_url", comment = "网页地址")
    private String pageUrl;
}
