package com.gwmfc.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import static com.gwmfc.util.StatusCodeEnum.*;

/**
 * <AUTHOR> y<PERSON><PERSON><PERSON>
 * @Classname Result
 * @Description 接口返回结果类
 * @Date 2021/3/31 15:30
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> implements Serializable {
    /**
     * 返回数据
     */
    private T data;
    /**
     * 数据总条数，翻页查询时使用
     */
    private Long total;
    /**
     * 状态码
     */
    private Integer code;
    /**
     * 返回描述
     */
    private String message;

    private String token;

    /**
     * 返回成功结果
     *
     * @return
     */
    public static Result ok() {
        Result result = new Result();
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    /**
     * 返回失败结果
     *
     * @return
     */
    public static Result error() {
        Result result = new Result();
        result.setCode(FAIL.getCode());
        result.setMessage(FAIL.getDesc());
        return result;
    }

    public static Result error(String message) {
        return error(FAIL.getCode(), message);
    }

    public static Result error(int code, String message) {
        Result result = new Result();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }

    public static Result ok(String message) {
        Result result = new Result();
        result.setCode(SUCCESS.getCode());
        result.setMessage(message);
        return result;
    }

    public static Result ok(Boolean data) {
        Result result = new Result();
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        result.setData(data);
        return result;
    }

    public static Result ok(String data, String message) {
        Result result = new Result();
        result.setCode(SUCCESS.getCode());
        result.setMessage(message);
        result.setData(data);
        return result;
    }

    public static Result ok(List list, Long total) {
        Result result = new Result();
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        result.setData(list);
        result.setTotal(total);
        return result;
    }

    public static Result pending() {
        Result result = new Result();
        result.setCode(PENDING.getCode());
        result.setMessage(PENDING.getDesc());
        return result;
    }

    public static Result setResult(BusinessEnum businessEnum){
        Result result = new Result();
        result.setCode(businessEnum.getCode());
        result.setMessage(businessEnum.getMsg());
        return result;
    }

    /**
     * 设置特定数据
     *
     * @param data
     * @return
     */
    public Result data(Object data) {
        this.setData((T) data);
        return this;
    }

    public Result data(Map<String, Object> map){
        this.setData((T) map);
        return this;
    }

    /**
     * 设置特定响应信息
     *
     * @param message
     * @return
     */
    public Result message(String message) {
        this.setMessage(message);
        return this;
    }

    /**
     * 设置特定响应码
     *
     * @param code
     * @return
     */
    public Result code(Integer code) {
        this.setCode(code);
        return this;
    }
}
