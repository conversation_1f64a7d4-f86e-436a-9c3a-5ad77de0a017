package com.gwmfc;

import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gwmfc.dao.CapitalDao;
import com.gwmfc.dao.ProductRebateInformationDao;
import com.gwmfc.entity.CapitalEntity;
import com.gwmfc.service.CapitalService;
import com.gwmfc.service.CpcaautoService;
import com.gwmfc.util.GsonUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年09月26日 13:58
 */
@SpringBootTest
public class CaptialTest {
    @Resource
    private ProductRebateInformationDao productRebateInformationDao;

    @Resource
    private CpcaautoService cpcaautoService;
    @Resource
    private CapitalDao capitalDao;
    @Test
    public void list() throws IOException {
        int year = Calendar.getInstance().get(Calendar.YEAR);
        int month = Calendar.getInstance().get(Calendar.MONTH);
        String monthStr;
        if (month < 10) {
            monthStr = "0".concat(String.valueOf(month));
        } else {
            monthStr = String.valueOf(month);
        }
        String url = "http://cpcaauto.com/news.php?types=csjd&anid=129&nid=27";
        cpcaautoService.monthlySalesRanking(url,String.valueOf(year), monthStr, 1, "Scheduled");
//        log.info("catchMonthlySalesRanking:{},{}",year,month);
    }
    @Test
    public void list1() throws IOException {
        CapitalEntity capitalEntity = capitalDao.selectById(1);
        CapitalEntity capitalEntityQuery = new CapitalEntity();
        capitalEntityQuery.setOriginId(1L);
        QueryWrapper queryWrapper = new QueryWrapper<>(capitalEntityQuery);
        queryWrapper.eq("status", 3);
        List<Long> deleteIdList = capitalEntity.getProductRebateInformation();
        List<CapitalEntity> capitalEntityList = capitalDao.selectList(queryWrapper);
        capitalEntityList.forEach(capitalEntityShuffle -> deleteIdList.removeAll(capitalEntityShuffle.getProductRebateInformation()));
        System.out.println(deleteIdList);
    }

    @Test
    public void list2() throws IOException {
        List a = new ArrayList();
        a.add(12);
        a.add(13);
        productRebateInformationDao.deleteBatchIds(a);
    }
}
