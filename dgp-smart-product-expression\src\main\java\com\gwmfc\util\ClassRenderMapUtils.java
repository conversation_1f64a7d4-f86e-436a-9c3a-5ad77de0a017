package com.gwmfc.util;

import com.gwmfc.annotation.TableFieldMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * map转class
 * <AUTHOR>
 * @date 2023/2/16
 */
@Slf4j
public class ClassRenderMapUtils {

    private ClassRenderMapUtils() {
        throw new IllegalStateException("Utility class");
    }


    /**
     * map数据转class List
     * @param <H>
     * @param dataList
     * @param clazz
     * @return
     */
    public static <H> List<H> renderMapList(List<Map<String, Object>> dataList, Class<H> clazz)  {
        List<H> result = new ArrayList<>();
        for (Map<String, Object> data : dataList) {
            H h = renderMap(data, clazz);
            if (h == null) {
                continue;
            }
            result.add(h);
        }
        return result;
    }

    /**
     * map数据转class
     * @param data
     * @param clazz
     * @param <H>
     * @return
     */
    public static <H> H renderMap(Map<String, Object> data, Class<H> clazz)  {
        try {
            Field[] fields = clazz.getDeclaredFields();
            H h = clazz.newInstance();
            buildFields(fields, data, h);
            //如果存在父类 父类的字段也要安排上
            Class<? super H> superclass = clazz.getSuperclass();
            if (superclass != null) {
                buildFields(superclass.getDeclaredFields(), data, h);
            }
            return h;
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    /**
     * 构建字段值 注意:实体类的类型要对应
     * @param fields
     * @param data
     * @param h
     * @param <H>
     */
    private static <H> void buildFields(Field[] fields, Map<String, Object> data, H h) {
        if (fields.length <= 0) {
            return;
        }
        for (Field field : fields) {
            TableFieldMapping tableFieldMapping = field.getAnnotation(TableFieldMapping.class);
            String name;
            if (tableFieldMapping == null) {
                name = TableUtils.humpToUnderline(field.getName());
            } else {
                name = tableFieldMapping.value();
            }
            Object value = data.get(name);
            if (value != null) {
                ReflectionUtils.makeAccessible(field);
                ReflectionUtils.setField(field, h, value);
            }
        }
    }

}
