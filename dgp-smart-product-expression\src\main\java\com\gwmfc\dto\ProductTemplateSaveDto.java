package com.gwmfc.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年10月18日 17:00
 */
@Data
@ApiModel("产品模板保存")
public class ProductTemplateSaveDto {
    @ApiModelProperty("品牌")
    private List<ProductProposalTemplateDto> productProposalTemplateDtoList;

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("业务类型(0-本品  1-全品新车  2-二手车)")
    private Integer businessType;

    @ApiModelProperty("创建日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @ApiModelProperty("创建人")
    private String createUser;
}
