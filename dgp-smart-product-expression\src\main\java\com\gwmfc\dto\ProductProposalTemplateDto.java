package com.gwmfc.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldMapping;
import com.gwmfc.bo.ProductProfitDetailBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023年10月16日 14:43
 */
@Data
@ApiModel("产品模板")
public class ProductProposalTemplateDto {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("模板名称")
    private String name;

    @ApiModelProperty("保存次序时间戳")
    private String currentTimeSeqNo;

    @ApiModelProperty("业务类型(0-本品  1-全品新车  2-二手车)")
    private Integer businessType;

    @ApiModelProperty("自定义参数")
    private Map<String,Object> selfDefinedParameter;
    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("车辆类型")
    private String vehicleType;
    @ApiModelProperty("贷额")
    private Double loanAmount;
    @ApiModelProperty("贷期")
    private Double paymentDays;

    @ApiModelProperty("irr模板ID")
    private Double irrTemplateId;

    @ApiModelProperty("客户利率（%）")
    private Double customerInterestRate;
    @ApiModelProperty("结算利率（%）")
    private Double settledRate;
    @ApiModelProperty("还款方式(后期改枚举)")// TODO: 2023/10/16  后期改枚举
    private Integer repaymentMode;
    @ApiModelProperty("还款方式说明")
    private String repaymentModeExplain;
    @ApiModelProperty("合同占比（%）")
    private Double contractsProportion;

    //风险
    @ApiModelProperty("风险损失率（%）")
    private Double riskLossRate;

    @ApiModelProperty("基础服务费（%）")
    private Double baseCommissionRatio;
    @ApiModelProperty("阶梯奖金比例（%）")
    private Double ladderBonusRatio;
    @ApiModelProperty("促销奖金比例（%）")
    private Double promotionBonusProportion;

    @ApiModelProperty("个人奖励")
    private String personalReward;

    //政策
    @ApiModelProperty("IRR")
    private Double irr;
    @ApiModelProperty("ROA")
    private Double roa;
    @ApiModelProperty("净利润")
    private Double netMargin;
    @ApiModelProperty("万元收益")
    private Double thousandIncome;
    @ApiModelProperty("加权IRR")
    private Double weightedIrr;

    /**
     * 提前结清 使用 边际利润
     */
    @ApiModelProperty("提前结清Irr")
    private Double resultIrr;

    @ApiModelProperty("提前结清净利润")
    private Double resultNetProfitMoney;

    @ApiModelProperty("提前结清边际利润")
    private Double resultMarginalProfit;

    @ApiModelProperty("提前结清ROA")
    private Double resultRoa;

    /**
     * 联合贷结果信息中增加综合信息，我司全款信息等
     */
    @ApiModelProperty("综合Irr")
    private Double resultIntegrativeIrr;

    @ApiModelProperty("综合净利润")
    private Double resultIntegrativeNetProfitMoney;

    @ApiModelProperty("综合边际利润")
    private Double resultIntegrativeMarginalProfit;

    @ApiModelProperty("综合ROA")
    private Double resultIntegrativeRoa;

    @ApiModelProperty("我司全额贷款Irr")
    private Double resultOurFullLoanIrr;

    @ApiModelProperty("我司全额贷款净利润")
    private Double resultOurFullLoanNetProfitMoney;

    @ApiModelProperty("我司全额贷款边际利润")
    private Double resultOurFullLoanMarginalProfit;

    @ApiModelProperty("我司全额贷款Roa")
    private Double resultOurFullLoanRoa;


    @ApiModelProperty("销量预测")
    private ProductProfitDetailBo salesForecast;

    @ApiModelProperty("是否发生变化")
    private Boolean changed;

    @ApiModelProperty("还款方式")
    private ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto;

    @ApiModelProperty("是否考虑提前结清")
    private Integer considerEarlySquare;

    @ApiModelProperty("是否联合贷")
    private Integer isUnionLoan;

    @ApiModelProperty("是否农户贷")
    private Integer isFarmerLoan;

    @ApiModelProperty("默认产品分类")
    private String productClassification;

    @ApiModelProperty("默认融资类型")
    private String financingType;

    @ApiModelProperty("提前结清概率分类")
    private String earlySquareProbabilityClassification;

    @ApiModelProperty("代理商类型")
    private String agentType;

    @ApiModelProperty("赛道")
    private String track;

    @ApiModelProperty("是否总对总")
    private Integer isCorpToCorp;

    @ApiModelProperty("提前结清参数附加")
    private ProductCalEarlySquareParamSubDto productCalEarlySquareParamSubDto;

    @ApiModelProperty("联合贷参数")
    private ProductCalUnionLoanParamDto productCalUnionLoanParamDto;

    @ApiModelProperty("创建日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("历史数据分析结果")
    private ProductProposalHistoryQueryDto productProposalHistoryQueryDto;
}
