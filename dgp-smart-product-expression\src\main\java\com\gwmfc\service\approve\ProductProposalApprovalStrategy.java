package com.gwmfc.service.approve;

import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductProposalApprovalStatusDto;
import com.gwmfc.dto.ProductProposalDto;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月07日 11:37
 */
public interface ProductProposalApprovalStrategy {
    void doApproval(@Valid ProductProposalDto productProposalDto, @Valid ProductProposalApprovalStatusDto productProposalApprovalStatusDto, User user, List<MultipartFile> addFileList);
}
