package com.gwmfc.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.gwmfc.entity.MenuDO;
import com.gwmfc.entity.RoleDO;
import com.gwmfc.entity.RoleMenuDO;
import com.gwmfc.entity.UserRoleDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname RoleDao
 * @Date 2021/8/5 16:47
 */

@Mapper
public interface RoleDao extends BaseMapper<RoleDO> {

    /**
     * getMenuByRoleId
     *
     * @param id 入参
     * @return List
     */
    List<MenuDO> getMenuByRoleId(@Param("roleId") Long id);

    /**
     * getMenuByRoleIds
     *
     * @param id 入参
     * @return List
     */
    List<MenuDO> getMenuByRoleIds(@Param("ids") List<Long> id);

    /**
     * removeMenuByRoleId
     *
     * @param roleId 入参
     * @return List
     */
    void removeMenuByRoleId(@Param("roleId") Long roleId);

    /**
     * batchSave
     *
     * @param list 入参
     * @return int
     */
    int batchSave(@Param("list") List<RoleMenuDO> list);

    /**
     * getRolesByIds
     *
     * @param ids 入参
     * @return List
     */
    List<RoleDO> getRolesByIds(@Param("ids") List<Long> ids);

    /**
     * getRolesByUserId
     *
     * @param userId 入参
     * @return List
     */
    List<RoleDO> getRolesByUserId(@Param("userId") Long userId);

    /**
     * deleteRolesByUserId
     *
     * @param userId 入参
     */
    void deleteRolesByUserId(@Param("userId") Long userId);

    /**
     * saveUserRole
     *
     * @param roleDO 入参
     */
    void saveUserRole(UserRoleDO roleDO);
}
