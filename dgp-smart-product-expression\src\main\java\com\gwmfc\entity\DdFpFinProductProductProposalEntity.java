package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 产品列表-产品提案关系表
 * @Date: 2023/12/18
 * @Author: zhangxinyu
 */

@Data
@ApiModel(value = "产品列表-产品提案关系表")
@TableName("dd_fp_fin_product_product_proposal")
public class DdFpFinProductProductProposalEntity {

    @ApiModelProperty("主键id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("产品id")
    private String finProductId;

    @ApiModelProperty("产品提案id")
    private Long product_proposal_id;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
