package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname DfChlSupDInvestorManageEntity
 * @Description TODO
 * @Date 2024/12/19 15:28
 */
@Data
@ApiModel(value = "集团信息")
@TableName("df_chl_sup_d_investor_manage")
public class DfChlSupDInvestorManageEntity {
    @TableField(value = "dealer_name")
    @ApiModelProperty("经销商")
    private String dealerName;

    @TableField(value = "clique")
    @ApiModelProperty("集团")
    private String clique;

    @TableField(value = "province")
    @ApiModelProperty("省")
    private String province;

    @TableField(value = "area")
    @ApiModelProperty("区")
    private String area;

    @TableField(value = "city")
    @ApiModelProperty("市")
    private String city;

    @TableField(value = "investor")
    @ApiModelProperty("投资者")
    private String investor;

    @TableField(value = "regional_manager")
    @ApiModelProperty("区域经理")
    private String regional_manager;

    @TableField(value = "etldate")
    @ApiModelProperty("采集日期")
    private String etldate;

    @TableField(value = "update_date")
    @ApiModelProperty("更新日期")
    private String update_date;

    @TableField(value = "operator")
    @ApiModelProperty("操作者")
    private String operator;

    @TableField(value = "area_account")
    @ApiModelProperty("区域账号")
    private String area_account;

    @TableField(value = "region_account")
    @ApiModelProperty("区域经理账号")
    private String region_account;

    @TableField(value = "regional_division")
    @ApiModelProperty("区域级别")
    private String regional_division;
}
