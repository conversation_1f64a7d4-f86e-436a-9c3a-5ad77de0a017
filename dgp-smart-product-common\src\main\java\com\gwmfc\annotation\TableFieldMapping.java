package com.gwmfc.annotation;

import java.lang.annotation.*;

/**
 * 表字段描述注解
 * <AUTHOR>
 * @date 2023/2/7
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.ANNOTATION_TYPE})
public @interface TableFieldMapping {

    /**
     * 字段名
     * @return
     */
    String value() default "";

    /**
     * 注释
     * @return
     */
    String comment() default "";

    /**
     * 是否必须
     * @return
     */
    boolean required() default true;

    /**
     * 是否是查询项
     * @return
     */
    boolean queryItem() default false;

}
