package com.gwmfc.vo;

import lombok.Data;

import java.util.List;

/**
 * 列对象
 * <AUTHOR>
 * @date 2023/3/1
 */
@Data
public class TableFieldDetailVo {

    /**
     * 注释
     */
    private String comment;

    /**
     * 是否必须
     */
    private Boolean required;

    /**
     * 是否是查询项
     */
    private Boolean queryItem;

    /**
     * 数据类型
     */
    private String type;

    /**
     * 是否是枚举值
     * @return
     */
    private List<String> enumValue;

    /**
     * 是否是查询项
     */
    private Boolean dateQueryItem;
}
