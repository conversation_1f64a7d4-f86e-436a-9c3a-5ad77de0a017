package com.gwmfc.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.dao.ProductBusinessLineDao;
import com.gwmfc.dao.ProductLineDao;
import com.gwmfc.entity.ProductBusinessLineEntity;
import com.gwmfc.entity.ProductLineEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Classname ProductLineService
 * @Description TODO
 * @Date 2024/8/30 17:15
 */
@Service
@Slf4j
@RefreshScope
public class ProductLineService extends ServiceImpl<ProductLineDao, ProductLineEntity> {
}
