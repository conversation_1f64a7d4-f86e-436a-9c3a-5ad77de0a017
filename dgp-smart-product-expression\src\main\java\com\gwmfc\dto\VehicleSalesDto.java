package com.gwmfc.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Date: 2023/11/8
 * @Author: zhang<PERSON>yu
 */

@Data
@ApiModel(description = "商用车数据")
public class VehicleSalesDto {

    @ApiModelProperty(value = "编号")
    private Long id;

    @ApiModelProperty(value = "类型(1:商用车销量,2:卡车销量,3:新能源销量,4:客车厂商销量,5:卡车厂商销量)")
    private Integer type;

    @ApiModelProperty(value = "更新月份")
    private String updateMonth;

    @ApiModelProperty(value = "数据日期")
    private String dataDate;

    @ApiModelProperty(value = "商用车类型")
    private String commercialVehicleType;

    @ApiModelProperty(value = "厂商")
    private String manufacturer;

    @ApiModelProperty(value = "销量")
    private String sales;

    @ApiModelProperty(value = "渗透率")
    private String penetrationRate;

    @ApiModelProperty(value = "图片地址")
    private String imageUrl;

    @ApiModelProperty(value = "排名")
    private String salesRank;

    @ApiModelProperty("创建日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人")
    private String createUser;
}
