package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname MonthPassengerCarEntity
 * @Description TODO
 * @Date 2023/8/1 17:26
 */
@Data
@TableName("week_pickup_car")
@ExcelIgnoreUnannotated
public class WeekPickupCarEntity extends BaseEntity {
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("底盘型号")
    @TableFieldMapping(value = "chassis_model", comment = "底盘型号")
    private String chassisModel;

    @ExcelProperty("周度")
    @TableFieldMapping(value = "week", comment = "周度")
    private String week;

    @ExcelProperty("货厢宽")
    @TableFieldMapping(value = "cargo_compartment_width", comment = "货厢宽")
    private String cargoCompartmentWidth;

    @ExcelProperty("使用城市")
    @TableFieldMapping(value = "city", comment = "使用城市")
    private String city;

    @ExcelProperty("年份")
    @TableFieldMapping(value = "year", comment = "年份")
    private String year;

    @ExcelProperty("使用性质")
    @TableFieldMapping(value = "use_nature", comment = "使用性质")
    private String useNature;

    @ExcelProperty("燃料种类")
    @TableFieldMapping(value = "fuel_types", comment = "燃料种类", queryItem = true)
    private String fuelTypes;

    @ExcelProperty("额定载质量")
    @TableFieldMapping(value = "rated_load_quality", comment = "额定载质量")
    private String ratedLoadQuality;

    @ExcelProperty("排放标准")
    @TableFieldMapping(value = "layout_standard", comment = "排放标准")
    private String layoutStandard;

    @ExcelProperty("企业简称")
    @TableFieldMapping(value = "business_abbreviation", comment = "企业简称", queryItem = true)
    private String businessAbbreviation;

    @ExcelProperty("外廓高")
    @TableFieldMapping(value = "outer_profile_height", comment = "外廓高")
    private String outerProfileHeight;

    @ExcelProperty("总质量")
    @TableFieldMapping(value = "total_weight", comment = "总质量")
    private String totalWeight;

    @ExcelProperty("数量")
    @TableFieldMapping(value = "number", comment = "数量")
    private String number;

    @ExcelProperty("轴距")
    @TableFieldMapping(value = "axle_distance", comment = "轴距")
    private String axleDistance;

    @ExcelProperty("额定载客")
    @TableFieldMapping(value = "rated_concession", comment = "额定载客")
    private String ratedConcession;

    @ExcelProperty("使用省份")
    @TableFieldMapping(value = "province", comment = "使用省份")
    private String province;

    @ExcelProperty("轮胎数")
    @TableFieldMapping(value = "tire_number", comment = "轮胎数")
    private String tireNumber;

    @ExcelProperty("排量")
    @TableFieldMapping(value = "displacement", comment = "排量")
    private String displacement;

    @ExcelProperty("功率")
    @TableFieldMapping(value = "power", comment = "功率")
    private String power;

    @ExcelProperty("外廓宽")
    @TableFieldMapping(value = "outer_profile_width", comment = "外廓宽")
    private String outerProfileWidth;

    @ExcelProperty("品牌")
    @TableFieldMapping(value = "brand", comment = "品牌", queryItem = true)
    private String brand;

    @ExcelProperty("后轮距")
    @TableFieldMapping(value = "postal_track", comment = "后轮距")
    private String postalTrack;

    @ExcelProperty("数整备质量据日期")
    @TableFieldMapping(value = "relation_weight", comment = "整备质量")
    private String relationWeight;

    @ExcelProperty("企业名称")
    @TableFieldMapping(value = "business_name", comment = "企业名称")
    private String businessName;

    @ExcelProperty("驱动形式")
    @TableFieldMapping(value = "driving_form", comment = "驱动形式")
    private String drivingForm;

    @ExcelProperty("外廓长")
    @TableFieldMapping(value = "outer_profile_length", comment = "外廓长")
    private String outerProfileLength;

    @ExcelProperty("集团简称")
    @TableFieldMapping(value = "group_abbreviation", comment = "集团简称", queryItem = true)
    private String groupAbbreviation;

    @ExcelProperty("发动机企业")
    @TableFieldMapping(value = "engine_business", comment = "发动机企业")
    private String engineBusiness;

    @ExcelProperty("前轮距")
    @TableFieldMapping(value = "front_wheel", comment = "前轮距")
    private String frontWheel;

    @ExcelProperty("轮胎规格")
    @TableFieldMapping(value = "tire_specification", comment = "轮胎规格")
    private String tireSpecification;

    @ExcelProperty("车型")
    @TableFieldMapping(value = "vehicle_type", comment = "车型", queryItem = true)
    private String vehicleType;

    @ExcelProperty("驾驶室形式")
    @TableFieldMapping(value = "cab_form", comment = "驾驶室形式")
    private String cabForm;

    @ExcelProperty("底盘企业")
    @TableFieldMapping(value = "chassis_business", comment = "底盘企业")
    private String chassisBusiness;

    @ExcelProperty("轴数")
    @TableFieldMapping(value = "axle_number", comment = "轴数")
    private String axleNumber;

    @ExcelProperty("更新日期")
    @TableFieldMapping(value = "data_date_week", comment = "更新日期")
    private String dataDateWeek;

    @ExcelProperty("货厢长")
    @TableFieldMapping(value = "cargo_compartment_length", comment = "货厢长")
    private String cargoCompartmentLength;

    @ExcelProperty("所有权")
    @TableFieldMapping(value = "ownership", comment = "所有权")
    private String ownership;

    @ExcelProperty("货厢高")
    @TableFieldMapping(value = "cargo_compartment_height", comment = "货厢高")
    private String cargoCompartmentHeight;

    @ExcelProperty("车辆名称")
    @TableFieldMapping(value = "vehicle_name", comment = "车辆名称")
    private String vehicleName;
}
