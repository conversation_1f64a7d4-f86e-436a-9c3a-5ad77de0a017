package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年03月19日 15:06
 */
@Data
@TableName("cpcaautod_monthly_sales_volume")
@ExcelIgnoreUnannotated
public class CpcaautodMonthlySalesVolumeEntity extends BaseEntity{
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("更新月份") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "update_date", comment = "更新月份")
    private String updateDate;

    @ExcelProperty("数据类型") @TableFieldEnumMapping
    @TableFieldMapping(value = "data_type", comment = "数据类型")
    private String dataType;

    @ExcelProperty("车型分类") @TableFieldEnumMapping
    @TableFieldMapping(value = "vehicle_classification", comment = "车型分类")
    private String vehicleClassification;

    @ExcelProperty("销量(万辆)")
    @TableFieldMapping(value = "sales_volume", comment = "销量(万辆)")
    private String salesVolume;

    @ExcelProperty("图片地址")
    @TableFieldMapping(value = "picture_url", comment = "图片地址")
    private String pictureUrl;
}
