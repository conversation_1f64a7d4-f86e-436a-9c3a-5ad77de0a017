package com.gwmfc.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwmfc.dao.UserRoleDao;
import com.gwmfc.dto.MenuDto;
import com.gwmfc.dto.RoleDto;
import com.gwmfc.entity.UserRoleDO;
import com.gwmfc.dao.RoleDao;
import com.gwmfc.entity.MenuDO;
import com.gwmfc.entity.RoleDO;
import com.gwmfc.entity.RoleMenuDO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Classname RoleService
 * @Date 2021/8/5 11:14
 */
@Service
public class RoleService {
    @Resource
    private RoleDao roleDao;
    @Resource
    private UserRoleDao userRoleDao;

    public IPage<RoleDO> pageList (RoleDO roleDO , Integer current, Integer size){
        QueryWrapper wrapper = new QueryWrapper(roleDO);
        if (StringUtils.hasText(roleDO.getRoleName())) {
            wrapper.like("role_name", roleDO.getRoleName());
            roleDO.setRoleName(null);
        }
        IPage page = new Page(current,size);
        wrapper.orderByDesc("role_id");
        return roleDao.selectPage(page,wrapper);
    }

    public RoleDO getOne(Long id){
        return roleDao.selectById(id);
    }

    public List<Long> getAllUserIdList(){
        return userRoleDao.selectAllUserIdList();
    }

    public void update(RoleDto roleDto){
        RoleDO roleDO = new RoleDO();
        BeanUtils.copyProperties(roleDto,roleDO);
        List<MenuDto> menuList = roleDto.getMenuList();
        Long roleId = roleDto.getRoleId();
        if(menuList != null && !menuList.isEmpty()){
            List<RoleMenuDO> rms = new ArrayList<>();
            for (MenuDto menu : menuList) {
                RoleMenuDO rmDo = new RoleMenuDO();
                rmDo.setRoleId(roleId);
                rmDo.setMenuId(menu.getMenuId());
                rms.add(rmDo);
            }
            if (!rms.isEmpty()) {
                roleDao.removeMenuByRoleId(roleId);
                roleDao.batchSave(rms);
            }
        }
        roleDao.updateById(roleDO);
    }

    public void save(RoleDto roleDto ){
        RoleDO roleDO = new RoleDO();
        BeanUtils.copyProperties(roleDto,roleDO);
        List<MenuDto> menuList = roleDto.getMenuList();
        roleDao.insert(roleDO);
        if(menuList != null && !menuList.isEmpty()){
            List<RoleMenuDO> rms = new ArrayList<>();
            for (MenuDto menu : menuList) {
                RoleMenuDO rmDo = new RoleMenuDO();
                rmDo.setRoleId(roleDO.getRoleId());
                rmDo.setMenuId(menu.getMenuId());
                rms.add(rmDo);
            }
            if (!rms.isEmpty()) {
                roleDao.removeMenuByRoleId(roleDO.getRoleId());
                roleDao.batchSave(rms);
            }
        }

    }

    public void delete(Long id){
        roleDao.deleteById(id);
        roleDao.removeMenuByRoleId(id);
    }

    public List<MenuDO> getMenusByRoleId(Long id){
        return roleDao.getMenuByRoleId(id);
    }

    public List<MenuDO> getMenusByRoleIds(List<Long> ids){
        return roleDao.getMenuByRoleIds(ids);
    }


    public List<RoleDO> getRolesByIds(List<Long> roleIds) {
        return roleDao.getRolesByIds(roleIds);
    }

    public List<RoleDO> getRolesByUserId(Long userId) {
        return roleDao.getRolesByUserId(userId);
    }

    public void updateUserRole(List<UserRoleDO> roleDos){
        roleDao.deleteRolesByUserId(roleDos.get(0).getUserId());
        roleDos.stream().forEach(e->
            roleDao.saveUserRole(e)
        );
    }

}
