package com.gwmfc.util;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public enum DatabaseEnum {

    MySQL("mysql","com.mysql.cj.jdbc.Driver","io.debezium.connector.mysql.MySqlConnector","jdbc:mysql://"),
    Oracle("oracle","oracle.jdbc.driver.OracleDriver","io.debezium.connector.oracle.OracleConnector","jdbc:oracle:thin:@"),
    SqlServer("sqlserver","com.microsoft.sqlserver.jdbc.SQLServerDriver","io.debezium.connector.sqlserver.SqlServerConnector","jdbc:sqlserver://"),
    MongoDB("mongodb",null,null,"mongodb://"),
    FTP("ftp",null,null,"ftp://"),
    Postgresql("postgresql","org.postgresql.Driver","io.debezium.connector.postgresql.PostgresConnector","jdbc:postgresql://"),
    MongoDBChildHive("mongodbChildHive",null,null,null);

    private String name;

    private String code;

    private String urlHeader;

    private String  debeziumConnectClass;

    DatabaseEnum(String name, String code, String debeziumConnectClass, String urlHeader) {
        this.name = name;
        this.code = code;
        this.debeziumConnectClass = debeziumConnectClass;
        this.urlHeader = urlHeader;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public String getDebeziumConnectClass() {
        return debeziumConnectClass;
    }

    public String getUrlHeader() {
        return urlHeader;
    }

    public static String getCodeEnum(String name) {
        //获取Class对象
        Class<?> clzz = DatabaseEnum.class;
        // 获取所有常量
        Object[] objects = clzz.getEnumConstants();
        //获取指定方法
        try {
            Method nameMethod = clzz.getMethod("getName");
            Method codeMethod = clzz.getMethod("getCode");
            for (Object obj : objects) {
                if (nameMethod.invoke(obj).equals(name)) {
                    //Enum对象.invoke(obj) 方式获取指定值
                    return codeMethod.invoke(obj).toString();
                }
            }
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getDebeziumConnectClassEnum(String name) {
        //获取Class对象
        Class<?> clzz = DatabaseEnum.class;
        // 获取所有常量
        Object[] objects = clzz.getEnumConstants();
        //获取指定方法
        try {
            Method nameMethod = clzz.getMethod("getName");
            Method debeziumConnectClassMethod = clzz.getMethod("getDebeziumConnectClass");
            for (Object obj : objects) {
                if (nameMethod.invoke(obj).equals(name)) {
                    //Enum对象.invoke(obj) 方式获取指定值
                    return debeziumConnectClassMethod.invoke(obj).toString();
                }
            }
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getUrlHeaderEnum(String name) {
        //获取Class对象
        Class<?> clzz = DatabaseEnum.class;
        // 获取所有常量
        Object[] objects = clzz.getEnumConstants();
        //获取指定方法
        try {
            Method nameMethod = clzz.getMethod("getName");
            Method urlHeaderMethod = clzz.getMethod("getUrlHeader");
            for (Object obj : objects) {
                if (nameMethod.invoke(obj).equals(name)) {
                    //Enum对象.invoke(obj) 方式获取指定值
                    return urlHeaderMethod.invoke(obj).toString();
                }
            }
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }
}
