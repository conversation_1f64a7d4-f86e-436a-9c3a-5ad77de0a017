package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @date 2023年08月21日 9:07
 *
 */
@Data
@TableName("policy")
@ExcelIgnoreUnannotated
public class PolicyEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("发布时间") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "发布时间", queryItem = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String dataDate;

    @ExcelProperty("发起部门") @TableFieldEnumMapping
    @TableFieldMapping(value = "initiating_department", comment = "发起部门", queryItem = true)
    private String initiatingDepartment;

    @ExcelProperty("政策概述")
    @TableFieldMapping(value = "policy_overview", comment = "政策概述", queryItem = true)
    private String policyOverview;

    private String ftpFileJson;
}
