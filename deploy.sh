#!/bin/bash
docker_image=$1
container_name=$2
env=$3
port=$4

count=`docker ps | grep $container_name | wc -l`
if [ "$count" -gt 0 ];then
  echo "docker stop $container_name"
  docker stop $container_name
fi
count=`docker container ls -a | grep  $container_name | wc -l`
if [ "$count" -gt 0 ]; then
  echo "delete container : $container_name"
  docker rm -f $container_name
fi

count=`docker images | grep  $container_name | wc -l`
if [ "$count" -gt 0 ]; then
  echo "delete images"
  docker rmi `docker images | grep  $container_name | awk '{print \$3}'`
fi

docker pull $docker_image
docker run -u root -m 1g -p $4:$4 -v /opt:/opt -d --network host --name $container_name -e TZ=Asia/Shanghai -e NACOS_SERVER=$env $docker_image
echo "docker run "

# back service deployment information
if [ ! -d "/opt/goalive_history" ] ;
then
mkdir /opt/goalive_history
fi
if [ ! -f /opt/goalive_history/$2 ] ;
then
touch /opt/goalive_history/$2
fi
echo "当前build时间为 $(date "+%Y-%m-%d %H:%M:%S") ---> $1,$2,$3,$4" >> /opt/goalive_history/$2