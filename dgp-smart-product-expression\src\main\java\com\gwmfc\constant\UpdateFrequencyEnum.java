package com.gwmfc.constant;

/**
 * <AUTHOR>
 * @date 2023年08月08日 13:40
 * 1：日频次；2：周频次；3：月频次；4：季频次；5：不定期频次
 */
public enum UpdateFrequencyEnum {
    /**
     * 日频次
     */
    DAY_FREQUENCY(1),

    /**
     * 周频次
     */
    WEEK_FREQUENCY(2),

    /**
     * 月频次
     */
    MONTH_FREQUENCY(3),

    /**
     * 季频次
     */
    QUARTER_FREQUENCY(4),

    /**
     * 不定期频次
     */
    NON_SCHEDULED_FREQUENCY(5),
    /**
     * 年频次
     */
    YEAR_FREQUENCY(6);

    private final Integer type;

    UpdateFrequencyEnum(Integer type) {
        this.type = type;
    }

    public Integer type() {
        return type;
    }

    public static UpdateFrequencyEnum getUpdateFrequencyEnumByType(Integer type) {
        for (UpdateFrequencyEnum updateFrequencyEnum : UpdateFrequencyEnum.values()) {
            if (updateFrequencyEnum.type().equals(type)) {
                return updateFrequencyEnum;
            }
        }
        return null;
    }
}
