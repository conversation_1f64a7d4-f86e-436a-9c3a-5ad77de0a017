package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年02月28日 17:13
 */
@Data
@TableName("year_consumption_index")
@ExcelIgnoreUnannotated
public class YearConsumptionIndexEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("居民消费价格分类指数(上年=100)")
    @TableFieldMapping(value = "consumer_price_sub_index", comment = "居民消费价格分类指数(上年=100)")
    private String consumerPriceSubIndex;

    @ExcelProperty("城市居民消费价格指数(上年=100)")
    @TableFieldMapping(value = "urban_consumer_price_index", comment = "城市居民消费价格指数(上年=100)")
    private String urbanConsumerPriceIndex;

    @ExcelProperty("农村居民消费价格指数（上年=100）")
    @TableFieldMapping(value = "rural_consumer_price_index", comment = "农村居民消费价格指数（上年=100）")
    private String ruralConsumerPriceIndex;

    @ExcelProperty("商品零售价格指数(上年=100)")
    @TableFieldMapping(value = "retail_price_index", comment = "商品零售价格指数(上年=100)")
    private String retailPriceIndex;

    @ExcelProperty("工业生产者出厂价格指数(上年=100)")
    @TableFieldMapping(value = "producer_price_index", comment = "工业生产者出厂价格指数(上年=100)")
    private String producerPriceIndex;

    @ExcelProperty("工业生产者购进价格指数(上年=100)")
    @TableFieldMapping(value = "purchasing_price_industrial_producer_index", comment = "工业生产者购进价格指数(上年=100)")
    private String purchasingPriceIndustrialProducerIndex;

    @ExcelProperty("固定资产投资价格指数(上年=100)")
    @TableFieldMapping(value = "fixed_asset_investment_price_index", comment = "固定资产投资价格指数(上年=100)")
    private String fixedAssetInvestmentPriceIndex;
}
