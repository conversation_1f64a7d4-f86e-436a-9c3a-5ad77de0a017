package com.gwmfc.util;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import com.gwmfc.constant.TableFieldTypeEnum;
import com.gwmfc.dto.TableFieldDetailDto;
import com.gwmfc.service.GlobalFormBusinessService;
import com.gwmfc.vo.TableFieldDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.JarURLConnection;
import java.net.URL;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 表 工具类
 *
 * <AUTHOR>
 * @date 2023/2/7
 */
@Slf4j
public class TableUtils {

    @Resource
    private GlobalFormBusinessService globalFormBusinessService;

    public TableUtils() {
        globalFormBusinessService = (GlobalFormBusinessService) SpringUtil.getBean("globalFormBusinessService");
    }

    /**
     * 实体类包名
     */
    private static final String TABLE_ENTITY_PACKAGE = "com.gwmfc.entity.data";

    /**
     * 类后缀
     */
    private static final String CLASS_SUFFIX = ".class";

    /**
     * 本地文件协议
     */
    private static final String ELEMENT_FILE_PROTOCOL = "file";

    /**
     * jar协议
     */
    private static final String ELEMENT_JAR_PROTOCOL = "jar";

    /**
     * 根据表明获取所有的业务字段映射详情
     *
     * @param tableName
     * @return
     * @throws IOException
     */
    public Map<String, TableFieldDetailVo> findTableBusFieldDetailMappings(String tableName) throws IOException {
        Class<?> clazz = getClassByTableName(tableName);
        if (clazz == null) {
            return Collections.emptyMap();
        }
        return getFieldDetailAllMappings(tableName,clazz);
    }

    /**
     * 根据表名获取所有的业务字段详情
     *
     * @return
     * @throws IOException
     */
    public List<TableFieldDetailDto> getFieldDetail(String tableName) throws IOException {
        Class<?> clazz = getClassByTableName(tableName);
        if (clazz == null) {
            return new ArrayList<>();
        }
        return getFieldDetail(clazz);
    }

    /**
     * 根据表明获取所有的业务字段映射
     *
     * @param tableName
     * @return
     * @throws Exception
     */
    public static Map<String, String> findTableBusFieldMappings(String tableName) throws IOException {
        Class<?> clazz = getClassByTableName(tableName);
        if (clazz == null) {
            return Collections.emptyMap();
        }
        return getFieldBusMappings(clazz);
    }

    /**
     * 根据表名查询类
     *
     * @param tableName
     * @return
     */
    public static Class<?> getClassByTableName(String tableName) throws IOException {
        if (StringUtils.isEmpty(tableName)) {
            return null;
        }
        try {
            List<Class<?>> tableClass = TableUtils.getTableClass(TABLE_ENTITY_PACKAGE);
            if (tableClass.isEmpty()) {
                return null;
            }
            for (Class<?> c : tableClass) {
                TableName annotation = c.getAnnotation(TableName.class);
                if (annotation == null) {
                    continue;
                }
                String value = annotation.value();
                if (tableName.equals(value)) {
                    return c;
                }
            }
        } catch (ClassNotFoundException e) {
            log.error("", e);
        }
        return null;
    }

    /**
     * 获取所有的字段映射
     *
     *
     * @param tableName
     * @param clazz
     * @return
     */
    private Map<String, TableFieldDetailVo> getFieldDetailAllMappings(String tableName, Class<?> clazz) {
        LinkedHashMap<String, TableFieldDetailVo> map = new LinkedHashMap<>(64);
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            boolean annotationPresent = field.isAnnotationPresent(TableFieldMapping.class);
            TableFieldDetailVo tableFieldDetailVo = new TableFieldDetailVo();
            if (annotationPresent) {
                // 获取注解值
                TableFieldMapping annotation = field.getAnnotation(TableFieldMapping.class);
                String tableFieldName = annotation.value();
                tableFieldDetailVo.setComment(annotation.comment());
                tableFieldDetailVo.setRequired(annotation.required());
                tableFieldDetailVo.setQueryItem(annotation.queryItem());
                Class<?> type = field.getType();
                if (type.equals(BigDecimal.class) || type.equals(Double.class) || type.equals(double.class)
                        || type.equals(Long.class) || type.equals(long.class)
                        || type.equals(Integer.class) || type.equals(int.class)) {
                    tableFieldDetailVo.setType(TableFieldTypeEnum.NUMBER.type());
                } else {
                    tableFieldDetailVo.setType(TableFieldTypeEnum.STRING.type());
                }
                TableFieldEnumMapping tableFieldEnumMapping = field.getAnnotation(TableFieldEnumMapping.class);
                if (tableFieldEnumMapping != null) {
                    tableFieldDetailVo.setDateQueryItem(tableFieldEnumMapping.dateEnum());
                    if(!tableFieldEnumMapping.dateEnum()) {
                        tableFieldDetailVo.setEnumValue(globalFormBusinessService.selectGroupValueByField(tableName, tableFieldName));
                    }
                }
                map.put(tableFieldName, tableFieldDetailVo);
            }

        }
        Class<?> superClazz = clazz.getSuperclass();
        if (superClazz != null) {
            Field[] superClazzFields = superClazz.getDeclaredFields();
            for (Field superField : superClazzFields) {
                boolean annotationPresent = superField.isAnnotationPresent(TableFieldMapping.class);
                if (annotationPresent) {
                    // 获取注解值
                    TableFieldMapping annotation = superField.getAnnotation(TableFieldMapping.class);
                    String tableFieldName = annotation.value();
                    TableFieldDetailVo tableFieldDetailVo = new TableFieldDetailVo();
                    tableFieldDetailVo.setComment(annotation.comment());
                    tableFieldDetailVo.setRequired(annotation.required());
                    tableFieldDetailVo.setQueryItem(annotation.queryItem());
                    Class<?> type = superField.getType();
                    if (type.equals(BigDecimal.class) || type.equals(Double.class) || type.equals(double.class)
                            || type.equals(Long.class) || type.equals(long.class)
                            || type.equals(Integer.class) || type.equals(int.class)) {
                        tableFieldDetailVo.setType(TableFieldTypeEnum.NUMBER.type());
                    } else {
                        tableFieldDetailVo.setType(TableFieldTypeEnum.STRING.type());
                    }
                    map.put(tableFieldName, tableFieldDetailVo);
                }
            }
        }
        return map;
    }

    /**
     * 获取所有的字段详情
     *
     * @param clazz
     * @return
     */
    private List<TableFieldDetailDto> getFieldDetail(Class<?> clazz) {
        List<TableFieldDetailDto> list = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            boolean annotationPresent = field.isAnnotationPresent(TableFieldMapping.class);
            TableFieldDetailDto tableFieldDetailDto = new TableFieldDetailDto();
            if (annotationPresent) {
                // 获取注解值
                TableFieldMapping annotation = field.getAnnotation(TableFieldMapping.class);
                String tableFieldName = annotation.value();
                tableFieldDetailDto.setComment(annotation.comment());
                tableFieldDetailDto.setColumn(tableFieldName);
                list.add(tableFieldDetailDto);
            }

        }
        Class<?> superClazz = clazz.getSuperclass();
        if (superClazz != null) {
            Field[] superClazzFields = superClazz.getDeclaredFields();
            for (Field superField : superClazzFields) {
                boolean annotationPresent = superField.isAnnotationPresent(TableFieldMapping.class);
                if (annotationPresent) {
                    // 获取注解值
                    TableFieldMapping annotation = superField.getAnnotation(TableFieldMapping.class);
                    String tableFieldName = annotation.value();
                    TableFieldDetailDto tableFieldDetailDto = new TableFieldDetailDto();
                    tableFieldDetailDto.setComment(annotation.comment());
                    tableFieldDetailDto.setColumn(tableFieldName);
                    list.add(tableFieldDetailDto);
                }
            }
        }
        return list;
    }

    /**
     * 获取所有的字段映射
     *
     * @param clazz
     * @return
     */
    private static Map<String, String> getFieldBusMappings(Class<?> clazz) {
        LinkedHashMap<String, String> map = new LinkedHashMap<>(64);
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            boolean annotationPresent = field.isAnnotationPresent(TableFieldMapping.class);
            if (annotationPresent) {
                // 获取注解值
                TableFieldMapping annotation = field.getAnnotation(TableFieldMapping.class);
                String tableFieldName = annotation.value();
                String tableFieldComment = annotation.comment();
                map.put(tableFieldName, tableFieldComment);
            }
        }
//        Class<?> superClazz = clazz.getSuperclass();
//        if (superClazz != null) {
//            Field[] superClazzFields = superClazz.getDeclaredFields();
//            for (Field superField : superClazzFields) {
//                boolean annotationPresent = superField.isAnnotationPresent(TableFieldMapping.class);
//                if (annotationPresent) {
//                    // 获取注解值
//                    TableFieldMapping annotation = superField.getAnnotation(TableFieldMapping.class);
//                    String tableFieldName = annotation.value();
//                    String tableFieldComment = annotation.comment();
//                    map.put(tableFieldName, tableFieldComment);
//                }
//            }
//        }
        return map;
    }

    /**
     * 根据包名获取类列表
     *
     * @param packageStr
     * @return
     * @throws IOException
     */
    public static List<Class<?>> getTableClass(String packageStr) throws IOException, ClassNotFoundException {
        List<Class<?>> classList = new ArrayList<>();
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        Enumeration<URL> resources;
        if (packageStr != null && !"".equals(packageStr)) {
            String path = packageStr.replace(".", "/");
            resources = classLoader.getResources(path);
        } else {
            resources = classLoader.getResources("");
        }
        while (resources.hasMoreElements()) {
            URL nextElement = resources.nextElement();
            String protocol = nextElement.getProtocol();
            if (ELEMENT_FILE_PROTOCOL.equals(protocol)) {
                classList.addAll(getClassList(new File(nextElement.getFile())));
            } else if (ELEMENT_JAR_PROTOCOL.equals(protocol)) {
                classList.addAll(getJarClassList(nextElement, classLoader));
            }
        }
        return classList;
    }

    /**
     * 获取jar中类
     *
     * @param url
     * @param classLoader
     * @return
     * @throws IOException
     * @throws ClassNotFoundException
     */
    private static List<Class<?>> getJarClassList(URL url, ClassLoader classLoader) throws IOException, ClassNotFoundException {
        List<Class<?>> classList = new ArrayList<>();
        JarURLConnection jarUrlConnection = (JarURLConnection) url.openConnection();
        if (jarUrlConnection == null) {
            return Collections.emptyList();
        }
        JarFile jarFile = jarUrlConnection.getJarFile();
        if (jarFile == null) {
            return Collections.emptyList();
        }
        Enumeration<JarEntry> jarEntries = jarFile.entries();
        while (jarEntries.hasMoreElements()) {
            JarEntry jarEntry = jarEntries.nextElement();
            String jarEntryName = jarEntry.getName();
            if (jarEntryName.endsWith(CLASS_SUFFIX)) {
                String className = jarEntryName.substring(0, jarEntryName.lastIndexOf(".")).replace("/", ".");
                Class<?> cls = Class.forName(className, false, classLoader);
                classList.add(cls);
            }
        }
        return classList;
    }

    /**
     * 获取class
     *
     * @param dir
     * @return
     * @throws ClassNotFoundException
     */
    private static List<Class<?>> getClassList(File dir) throws ClassNotFoundException {
        List<Class<?>> classList = new ArrayList<>();
        String packageName = getPackageName(dir.getPath());
        if (!dir.isDirectory() && dir.getName().endsWith(CLASS_SUFFIX)) {
            String string = packageName + "." + dir.getName().substring(0, dir.getName().length() - 6);
            classList.add(Class.forName(string));
            return classList;
        }
        File[] files = dir.listFiles();
        if (files == null || files.length == 0) {
            return Collections.emptyList();
        }
        for (File file : files) {
            String cName;
            if (file.getName().contains(".")) {
                if ("".equals(packageName)) {
                    cName = file.getName().substring(0, file.getName().length() - 6);
                } else {
                    cName = packageName + "." + file.getName().substring(0, file.getName().length() - 6);
                }
                try {
                    classList.add(Class.forName(cName));
                } catch (Exception e) {
                    log.info(cName + " class cannot loaded");
                }
            } else {
                classList.addAll(getClassList(file));
            }
        }
        return classList;
    }

    /**
     * 取得包名
     *
     * @param packageName
     * @return
     */
    private static String getPackageName(String packageName) {
        int indexOf = packageName.indexOf("classes");
        String substring;
        try {
            //classes后面有\
            substring = packageName.substring(indexOf + 8);
        } catch (Exception e) {
            //第一次classes后面没有\
            substring = packageName.substring(indexOf + 7);
        }
        return substring.replace("\\", ".");
    }

    /**
     * 驼峰式命名法
     */
    public static String underlineToHump(String str) {
        String regex = "_(.)";
        Matcher matcher = Pattern.compile(regex).matcher(str);
        while (matcher.find()) {
            String target = matcher.group(1);
            str = str.replaceAll("_" + target, target.toUpperCase());
        }
        return str;
    }

    /**
     * 下划线式命名法
     *
     * @param str
     * @return
     */
    public static String humpToUnderline(String str) {
        String regex = "([A-Z])";
        Matcher matcher = Pattern.compile(regex).matcher(str);
        while (matcher.find()) {
            String target = matcher.group();
            str = str.replaceAll(target, "_" + target.toLowerCase());
        }
        return str;
    }

}
