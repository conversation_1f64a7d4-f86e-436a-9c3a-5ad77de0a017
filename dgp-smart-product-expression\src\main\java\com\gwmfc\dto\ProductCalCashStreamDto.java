package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname CashStreamDto
 * @Description 计算结束后返回给前端的现金流dto
 * @Date 2023/9/19 10:59
 */
@Data
@ApiModel(value = "现金流")
public class ProductCalCashStreamDto {

    @ApiModelProperty("月")
    private Integer month;

    @ApiModelProperty("日期")
    private String cashStreamDate;

    @ApiModelProperty("收")
    private Double cashStreamIncom;

    @ApiModelProperty("支")
    private Double cashStreamExpend;

    @ApiModelProperty("现金流")
    private Double cashStream;

    @ApiModelProperty("流转税")
    private Double cashStreamTurnoverTax;

    @ApiModelProperty("后市场收入")
    private Double cashStreamAfterMarketIncome;

    @ApiModelProperty("放款手续费")
    private Double cashStreamLoanHandCharge;

    @ApiModelProperty("扣款手续费")
    private Double cashStreamDeductMoneyHandCharge;

    @ApiModelProperty("其他变动成本")
    private Double cashStreamOtherChangeCost;

    @ApiModelProperty("特殊奖励")
    private Double cashStreamSpecialReward;

    @ApiModelProperty("不良")
    private Double cashStreamBadness;

    @ApiModelProperty("催收费用合计")
    private Double cashStreamCollectionFeeTotal;

    @ApiModelProperty("外包催收费")
    private Double cashStreamOutsourcingCollectionFee;

//    @ApiModelProperty("诉讼服务费")
//    private Double cashStreamLawsuitServiceFee;

    @ApiModelProperty("短账外包费")
    private Double cashStreamDzOutsourcingFee;

    @ApiModelProperty("应收客户利息/当期利息")
    private Double cashStreamReceivableCustomerInterest;

    @ApiModelProperty("本金/当期本金")
    private Double cashStreamPrincipal;

    @ApiModelProperty("实际利息")
    private Double cashStreamActualInterest;

    @ApiModelProperty("实际本金")
    private Double cashStreamActualPrincipal;

    @ApiModelProperty("剩余本金")
    private Double cashStreamRemainPrincipal;

    @ApiModelProperty("剩余金额")
    private Double cashStreamRemainMoney;

    @ApiModelProperty("利息支出")
    private Double cashStreamInterestExpend;

    /**
     * 尾款贷使用
     */
    @ApiModelProperty("当期客户月供")
    private Double cashStreamCurrentCustomMonthPayment;

    /**
     * 分段贷使用
     */
    @ApiModelProperty("还款计划")
    private Double cashStreamRepaymentPlan;

    @ApiModelProperty("实际剩余本金")
    private Double cashStreamActualRemainPrincipal;

    /**
     * 提前结清终止手续费
     */
    @ApiModelProperty("提前结清终止手续费")
    private Double cashStreamEarlySquareHandCharge;

    /**
     * 提前结清佣金扣回
     */
    @ApiModelProperty("提前结清佣金扣回")
    private Double cashStreamEarlySquareCommissionDeduct;

    /**
     * 利润计算
     */
    @ApiModelProperty("增值税")
    private Double cashStreamValueAddedTax;

    @ApiModelProperty("教育费附加")
    private Double cashStreamEducationFeeAppend;

    @ApiModelProperty("利息支出分摊")
    private Double cashStreamInterestExpendShare;

    @ApiModelProperty("管理费分摊")
    private Double cashStreamManageFeeShare;

    @ApiModelProperty("利息收入")
    private Double cashStreamInterestIncom;

    @ApiModelProperty("贴息收入")
    private Double cashStreamSubInterest;

    @ApiModelProperty("佣金摊销")
    private Double cashStreamCommissionShare;

    @ApiModelProperty("拨备")
    private Double cashStreamProvision;

    @ApiModelProperty("利润")
    private Double cashStreamProfit;

    @ApiModelProperty("现值")
    private Double presentValue;

    @ApiModelProperty("折现率")
    private Double discountRate;

    @ApiModelProperty("浮动后折现率")
    private Double postFloatingDiscountRate;

    @ApiModelProperty("月供")
    private Double monthPayment;

    @ApiModelProperty("联合贷客户本金")
    private Double cashStreamUnionLoanCustomerPrincipal;

    @ApiModelProperty("联合贷客户利息")
    private Double cashStreamUnionLoanCustomerInterest;

    @ApiModelProperty("联合贷银行本金")
    private Double cashStreamUnionLoanBankPrincipal;

    @ApiModelProperty("联合贷银行利息")
    private Double cashStreamUnionLoanBankInterest;

    @ApiModelProperty("联合贷银行返还利息")
    private Double cashStreamUnionLoanBankReturnInterest;

    @ApiModelProperty("联合贷月供")
    private Double cashStreamUnionLoanMonthPayment;

    @ApiModelProperty("现金流印花税")
    private Double cashStreamStampTax;

    @ApiModelProperty("现金流放款")
    private Double cashStreamLoanMoney;

    @ApiModelProperty("现金流服务费")
    private Double cashStreamServiceFee;

    @ApiModelProperty("现金流月供")
    private Double cashStreamMonthPayment;

    @ApiModelProperty("现金流提前结清月供")
    private Double cashStreamEarlySquareMonthPayment;

    @ApiModelProperty("现金流提前结清本金")
    private Double cashStreamEarlySquarePrincipal;

    @ApiModelProperty("现金流实际利率本金")
    private Double cashStreamActualInterestPrincipal;

    /**
     * 提前结清联合贷新增
     */

    @ApiModelProperty("联合贷银行当期利息")
    private Double cashStreamUnionLoanBankCurrentInterest;

    @ApiModelProperty("联合贷考虑提前结清银行返还利息")
    private Double cashStreamUnionLoanEarlySquareBankReturnInterest;

    @ApiModelProperty("联合贷考虑提前结清银行本金")
    private Double cashStreamUnionLoanEarlySquareBankPrincipal;

    @ApiModelProperty("联合贷贴息")
    private Double cashStreamUnionLoanSubInterest;

    @ApiModelProperty("联合贷我司本金")
    private Double cashStreamUnionLoanOurPrincipal;

    @ApiModelProperty("联合贷我司剩余本金")
    private Double cashSteramUnionLoanOurRemainPrincipal;

    @ApiModelProperty("联合贷提前结清我司手续费")
    private Double cashStreamUnionLoanEarlySquareOurHandCharge;

    @ApiModelProperty("联合贷提前结清我司月供")
    private Double cashStreamUnionLoanEarlySquareOurMonthPayment;

    @ApiModelProperty("联合贷银行剩余本金")
    private Double cashStreamUnionLoanBankRemainPrincipal;

    @ApiModelProperty("联合贷银行利息手续费")
    private Double cashStreamUnionLoanBankInterestHandCharge;

    @ApiModelProperty("联合贷我司利息")
    private Double cashStreamUnionLoanOursInterest;
}
