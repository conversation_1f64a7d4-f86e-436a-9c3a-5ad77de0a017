package com.gwmfc.constant;

import com.gwmfc.exception.SystemRuntimeException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * @Date: 2023/11/30
 * @Author: zhang<PERSON>yu
 */

@Getter
@AllArgsConstructor
public enum UsedCarMonthlyTradingTypeEnum {

    TRADING_VOLUME(1, "交易量", UsedCarMonthlyTradingTableEnum.TRADING_VOLUME),
    REGIONAL_TRADING_DATA(2, "区域交易数据", UsedCarMonthlyTradingTableEnum.REGIONAL_TRADING_DATA),
    YEAR_TRADING_DATA(3, "年交易数据", UsedCarMonthlyTradingTableEnum.YEAR_TRADING_DATA),
    VEHICLE_TYPE_TRADING_VOLUME(4, "车型交易量", UsedCarMonthlyTradingTableEnum.VEHICLE_TYPE_TRADING_VOLUME),
    CROSS_REGIONAL_CIRCULATION_VOLUME(5, "跨区域流通量", UsedCarMonthlyTradingTableEnum.CROSS_REGIONAL_CIRCULATION_VOLUME),
    ;

    /**
     * 二手车销量类型
     */
    private final Integer usedCarType;
    /**
     * 名称
     */
    private final String tradingName;
    /**
     * excel表头及查询字段枚举类
     */
    private final UsedCarMonthlyTradingTableEnum usedCarMonthlyTradingTableEnum;

    /**
     * 根据类型获取名称
     *
     * @param usedCarType
     * @return
     */
    public static String getTradingNameByType(Integer usedCarType) {
        for (UsedCarMonthlyTradingTypeEnum value : UsedCarMonthlyTradingTypeEnum.values()) {
            if (value.getUsedCarType().equals(usedCarType)) {
                return value.getTradingName();
            }
        }
        throw new SystemRuntimeException("类型不存在");
    }

    /**
     * 根据类型查询枚举类
     *
     * @param headMap
     * @param usedCarType
     * @return
     */
    public static void getUsedCarMonthlyTradingTableEnumByType(Map<String, String> headMap, Integer usedCarType) {
        for (UsedCarMonthlyTradingTypeEnum value : UsedCarMonthlyTradingTypeEnum.values()) {
            if (value.getUsedCarType().equals(usedCarType)) {
                value.getUsedCarMonthlyTradingTableEnum().getHeader(headMap);
            }
        }
    }
}
