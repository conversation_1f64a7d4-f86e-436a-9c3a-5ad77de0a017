package com.gwmfc.schedule;

import com.gwmfc.service.LogisticsIndexService;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Calendar;

/**
 * <AUTHOR>
 * @date 2024年03月22日 10:14
 */
@Component
@Slf4j
public class LogisticsIndexSchedule {
    @Resource
    private LogisticsIndexService logisticsIndexService;

    @Scheduled(cron = "0 5 0 * * ?")
    @SchedulerLock(name = "catchLogisticsIndex", lockAtMostFor = "PT1H", lockAtLeastFor = "PT1H")
    public void catchLogisticsIndex() {
        String[] tableNameArray = {"commerce_logistics_index","logistics_industry_prosperity_index","storage_index","manufacturing_pmi_index"};
        Arrays.asList(tableNameArray).forEach(tableName -> {
            try {
                logisticsIndexService.catchLogisticsIndex(tableName, "Scheduled");
            } catch (Exception e) {
                log.error("{}",e);
            }
        });
        int year = Calendar.getInstance().get(Calendar.YEAR);
        Calendar calendar = Calendar.getInstance();
        int month = calendar.get(Calendar.MONTH);
        if (month == 0) {
            year = year - 1;
            month = 12;
        }
        String monthStr;
        if (month < 10) {
            monthStr = "0".concat(String.valueOf(month));
        } else {
            monthStr = String.valueOf(month);
        }
        logisticsIndexService.manufacturingPmiIndexDownload(String.valueOf(year),monthStr,"Scheduled");
        log.info("catchLogisticsIndex:{},{}",year,month);
    }
}
