package com.gwmfc.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023年03月08日 15:44
 */
@Data
public class UploadFileBo implements Serializable {
    private static final long serialVersionUID = 2405172041950251807L;
    private String fileName;
    private String filePath;
    private String overview;
    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createUser;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UploadFileBo that = (UploadFileBo) o;
        return Objects.equals(fileName, that.fileName) && Objects.equals(filePath, that.filePath) && Objects.equals(overview, that.overview);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fileName, filePath, overview);
    }
}
