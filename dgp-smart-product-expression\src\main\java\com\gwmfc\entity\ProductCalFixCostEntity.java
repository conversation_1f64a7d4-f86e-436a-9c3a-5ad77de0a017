package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductCalFixCost
 * @Description product_cal_fix_cost
 * @Date 2023/9/22 13:15
 */
@Data
@TableName("product_cal_fix_cost")
public class ProductCalFixCostEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "year", comment = "年")
    private String year;

    @TableFieldMapping(value = "bp_car", comment = "本品")
    private Double bpCar;

    @TableFieldMapping(value = "qp_new_car", comment = "全品新车")
    private Double qpNewCar;

    @TableFieldMapping(value = "second_car", comment = "全拼二手车")
    private Double secondCar;

    @TableFieldMapping(value = "create_date", comment = "创建日期")
    private String createDate;

    @TableFieldMapping(value = "create_time", comment = "创建时间")
    private Date createTime;

    @TableFieldMapping(value = "create_user", comment = "创建用户")
    private String createUser;

    @TableFieldMapping(value = "update_time", comment = "更新时间")
    private Date updateTime;

    @TableFieldMapping(value = "update_user", comment = "更新用户")
    private String updateUser;
}
