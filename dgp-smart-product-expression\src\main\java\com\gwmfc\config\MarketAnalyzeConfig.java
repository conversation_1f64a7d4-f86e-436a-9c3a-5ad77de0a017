package com.gwmfc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * @Date: 2023/11/15
 * @Author: zhang<PERSON>yu
 */

@Data
@Configuration
@ConfigurationProperties(prefix = "market-analyze")
@RefreshScope
public class MarketAnalyzeConfig {

    private String commercialVehicleUrl;
}
