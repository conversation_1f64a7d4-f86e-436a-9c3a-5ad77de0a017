package com.gwmfc.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gwmfc.entity.data.VehicleSalesEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Date: 2023/11/9
 * @Author: zhang<PERSON>yu
 */

@Mapper
public interface VehicleSalesDao extends BaseMapper<VehicleSalesEntity> {

    /**
     * 导出excel
     * @param tableName
     * @param columnList
     * @param conditionParams
     * @return
     */
    List<Map<String, Object>> selectByType(String tableName, List<String> columnList, Map<String, Object> conditionParams);
}
