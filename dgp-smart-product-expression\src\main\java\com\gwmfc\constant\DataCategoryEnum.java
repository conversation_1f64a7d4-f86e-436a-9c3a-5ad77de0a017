package com.gwmfc.constant;

/**
 * <AUTHOR>
 * @date 2023年08月08日 13:40
 *
 * 数据分类：
 * 1:宏观数据、2:同业数据、3:市场数据、4:集团数据、5:政策数据、6:业务数据
 */
public enum DataCategoryEnum {
    /**
     * 宏观数据
     */
    MACROECONOMIC_DATA(1),

    /**
     * 同业数据
     */
    PEER_DATA(2),

    /**
     * 市场数据
     */
    MARKET_DATA(3),

    /**
     * 集团数据
     */
    BLOC_DATA(4),

    /**
     * 政策数据
     */
    POLICY_DATA(5),

    /**
     * 业务数据
     */
    BUSINESS_DATA(6);

    private final Integer type;

    DataCategoryEnum(Integer type) {
        this.type = type;
    }

    public Integer type() {
        return type;
    }
}
