package com.gwmfc.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.util.CellRangeAddress;

import java.awt.Color;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class ExcelUtil {
    // 样式封装
    private static class Styles {
        final XSSFCellStyle blueHeader, greenHeader, titleCell, yellowCell, normalCell;
        Styles(Workbook workbook) {
            Font headerFont = workbook.createFont();
            headerFont.setFontName("微软雅黑");
            headerFont.setFontHeightInPoints((short) 16);
            headerFont.setBold(true);

            Font titleFont = workbook.createFont();
            titleFont.setFontName("微软雅黑");
            titleFont.setFontHeightInPoints((short) 11);
            titleFont.setBold(true);

            Font contentFont = workbook.createFont();
            contentFont.setFontName("微软雅黑");
            contentFont.setFontHeightInPoints((short) 11);
            contentFont.setBold(false);

            blueHeader = (XSSFCellStyle) workbook.createCellStyle();
            blueHeader.setFillForegroundColor(new XSSFColor(new Color(0, 176, 240), null));
            blueHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            blueHeader.setAlignment(HorizontalAlignment.LEFT);
            blueHeader.setVerticalAlignment(VerticalAlignment.CENTER);
            blueHeader.setBorderBottom(BorderStyle.THIN);
            blueHeader.setBorderTop(BorderStyle.THIN);
            blueHeader.setBorderLeft(BorderStyle.THIN);
            blueHeader.setBorderRight(BorderStyle.THIN);
            blueHeader.setFont(headerFont);

            greenHeader = (XSSFCellStyle) workbook.createCellStyle();
            greenHeader.setFillForegroundColor(new XSSFColor(new Color(198, 224, 180), null));
            greenHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            greenHeader.setAlignment(HorizontalAlignment.LEFT);
            greenHeader.setVerticalAlignment(VerticalAlignment.CENTER);
            greenHeader.setBorderBottom(BorderStyle.THIN);
            greenHeader.setBorderTop(BorderStyle.THIN);
            greenHeader.setBorderLeft(BorderStyle.THIN);
            greenHeader.setBorderRight(BorderStyle.THIN);
            greenHeader.setFont(headerFont);

            titleCell = (XSSFCellStyle) workbook.createCellStyle();
            titleCell.setFillForegroundColor(new XSSFColor(new Color(201, 201, 201), null));
            titleCell.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            titleCell.setAlignment(HorizontalAlignment.CENTER);
            titleCell.setVerticalAlignment(VerticalAlignment.CENTER);
            titleCell.setBorderBottom(BorderStyle.THIN);
            titleCell.setBorderTop(BorderStyle.THIN);
            titleCell.setBorderLeft(BorderStyle.THIN);
            titleCell.setBorderRight(BorderStyle.THIN);
            titleCell.setFont(titleFont);

            yellowCell = (XSSFCellStyle) workbook.createCellStyle();
            yellowCell.setFillForegroundColor(new XSSFColor(new Color(255, 255, 0), null));
            yellowCell.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            yellowCell.setAlignment(HorizontalAlignment.LEFT);
            yellowCell.setVerticalAlignment(VerticalAlignment.CENTER);
            yellowCell.setBorderBottom(BorderStyle.THIN);
            yellowCell.setBorderTop(BorderStyle.THIN);
            yellowCell.setBorderLeft(BorderStyle.THIN);
            yellowCell.setBorderRight(BorderStyle.THIN);
            yellowCell.setFont(contentFont);

            normalCell = (XSSFCellStyle) workbook.createCellStyle();
            normalCell.setAlignment(HorizontalAlignment.CENTER);
            normalCell.setVerticalAlignment(VerticalAlignment.CENTER);
            normalCell.setBorderBottom(BorderStyle.THIN);
            normalCell.setBorderTop(BorderStyle.THIN);
            normalCell.setBorderLeft(BorderStyle.THIN);
            normalCell.setBorderRight(BorderStyle.THIN);
            normalCell.setFont(contentFont);
        }
    }

    // 创建合并标题行
    private static void createMergedTitleRow(Sheet sheet, int rowIndex, int mergeLength, String title, CellStyle style) {
        Row row = sheet.createRow(rowIndex);
        Cell cell = row.createCell(0);
        cell.setCellValue(title);
        cell.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, mergeLength));
    }

    // 创建表头行
    private static void createHeaderRow(Sheet sheet, int rowIndex, List<String> headers, CellStyle style) {
        Row row = sheet.createRow(rowIndex);
        for (int i = 0; i < headers.size(); i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(headers.get(i));
            cell.setCellStyle(style);
        }
    }

    // 创建数据行
    private static void createDataRows(Sheet sheet, int startRow, List<String> rowLabels, int colCount, CellStyle labelStyle, CellStyle cellStyle) {
        for (int i = 0; i < rowLabels.size(); i++) {
            Row row = sheet.createRow(startRow + i);
            Cell cell = row.createCell(0);
            cell.setCellValue(rowLabels.get(i));
            cell.setCellStyle(labelStyle);
            for (int j = 1; j < colCount; j++) {
                row.createCell(j).setCellStyle(cellStyle);
            }
        }
    }

    /**
     * 更简洁的Excel导出方法
     */
    public static void generateSampleExcel(String title, String otherParty, List<String> otherPartyRate, List<String> tgwRate, List<String> otherPartyRewardType,
                                           List<String> tgwRewardType, List<String> otherPartyCustomRating, List<String> tgwCustomRating) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");
        for (int i = 0; i < 20; i++) {
            sheet.setColumnWidth(i, 10 * 256);
        }
        Styles styles = new Styles(workbook);
        int index = 0;

        // 第一张表
        createMergedTitleRow(sheet, index++, otherPartyRate.size(), "综合返利差额-[长城-" + otherParty + "]-" + title, styles.blueHeader);
        List<String> header1 = new java.util.ArrayList<>();
        header1.add("奖励类型");
        header1.addAll(otherPartyRate);
        createHeaderRow(sheet, index++, header1, styles.titleCell);
        createDataRows(sheet, index, otherPartyRewardType, header1.size(), styles.titleCell, styles.normalCell);
        index += otherPartyRewardType.size();

        // 第二张表
        createMergedTitleRow(sheet, index++, otherPartyRate.size(), "返车商综合返利差额-[长城-" + otherParty + "]-" + title, styles.blueHeader);
        List<String> header2 = new java.util.ArrayList<>();
        header2.add("评级");
        header2.addAll(otherPartyRate);
        createHeaderRow(sheet, index++, header2, styles.titleCell);
        createDataRows(sheet, index, otherPartyCustomRating, header2.size(), styles.titleCell, styles.normalCell);
        index += otherPartyCustomRating.size();

        // 第三张表
        createMergedTitleRow(sheet, index++, tgwRate.size()+2, "商务返利-长城-" + title, styles.greenHeader);
        List<String> header3 = new java.util.ArrayList<>();
        header3.add("奖励类型");
        header3.addAll(tgwRate);
        header3.add("可得性");
        header3.add("可得性说明");
        createHeaderRow(sheet, index++, header3, styles.titleCell);
        createDataRows(sheet, index, tgwRewardType, header3.size(), styles.titleCell, styles.normalCell);
        index += tgwRewardType.size();

        // 第四张表
        createMergedTitleRow(sheet, index++, tgwRate.size()+2, "综合返利-长城-" + title, styles.greenHeader);
        List<String> header4 = new java.util.ArrayList<>();
        header4.add("评级");
        header4.addAll(tgwRate);
        header4.add("可得性");
        header4.add("可得性说明");
        createHeaderRow(sheet, index++, header4, styles.titleCell);
        createDataRows(sheet, index, tgwCustomRating, header4.size(), styles.titleCell, styles.normalCell);
        index += tgwCustomRating.size();

        // 第五张表
        createMergedTitleRow(sheet, index++, otherPartyRate.size()+2, "商务返利-" + otherParty + "-" + title, styles.blueHeader);
        List<String> header5 = new java.util.ArrayList<>();
        header5.add("奖励类型");
        header5.addAll(otherPartyRate);
        header5.add("可得性");
        header5.add("可得性说明");
        createHeaderRow(sheet, index++, header5, styles.titleCell);
        createDataRows(sheet, index, otherPartyRewardType, header5.size(), styles.titleCell, styles.normalCell);
        index += otherPartyRewardType.size();

        // 第六张表
        createMergedTitleRow(sheet, index++, otherPartyRate.size()+2, "综合返利-" + otherParty + "-" + title, styles.blueHeader);
        List<String> header6 = new java.util.ArrayList<>();
        header6.add("评级");
        header6.addAll(otherPartyRate);
        header6.add("可得性");
        header6.add("可得性说明");
        createHeaderRow(sheet, index++, header6, styles.titleCell);
        createDataRows(sheet, index, otherPartyCustomRating, header6.size(), styles.titleCell, styles.normalCell);
        index += otherPartyCustomRating.size();

        // 第七张表
        createMergedTitleRow(sheet, index++, tgwRate.size()+2, "返车商-长城-" + title, styles.greenHeader);
        List<String> header7 = new java.util.ArrayList<>();
        header7.add("评级");
        header7.addAll(tgwRate);
        header7.add("可得性");
        header7.add("可得性说明");
        createHeaderRow(sheet, index++, header7, styles.titleCell);
        createDataRows(sheet, index, tgwCustomRating, header7.size(), styles.titleCell, styles.normalCell);
        index += tgwCustomRating.size();

        // 第八张表
        createMergedTitleRow(sheet, index++, otherPartyRate.size()+2, "返车商-" + otherParty + "-" + title, styles.greenHeader);
        List<String> header8 = new java.util.ArrayList<>();
        header8.add("评级");
        header8.addAll(otherPartyRate);
        header8.add("可得性");
        header8.add("可得性说明");
        createHeaderRow(sheet, index++, header8, styles.titleCell);
        createDataRows(sheet, index, otherPartyRewardType, header8.size(), styles.titleCell, styles.normalCell);
        // index += otherPartyRewardType.size(); // 最后一张表无需再递增

        // 保存文件
        File dir = new File("D:/test2/excel_generation");
        if (!dir.exists()) dir.mkdirs();
        try (FileOutputStream fos = new FileOutputStream(new File(dir, "样例导出" + System.currentTimeMillis() + ".xlsx"))) {
            workbook.write(fos);
        }
        workbook.close();
    }

    public static void main(String[] args) {
        try {
            generateSampleExcel("[新车/二手车]-[信贷/融资租]-[车型]-[N期]-[N月]-[省份]-[SP]", "竟对方A",
                    Arrays.asList("8.98%", "9.98%", "10.98%", "11.98%", "12.98%", "13.16%", "13.98%", "14.98%", "15.20%", "15.98%", "16.28%", "16.98%", "17.98%"),
                    Arrays.asList("8.98%", "9.98%", "10.98%", "11.98%", "12.98%", "13.16%", "13.98%", "14.98%", "15.20%", "15.98%", "16.28%", "16.98%"),
                    Arrays.asList("A", "B", "C", "D"),
                    Arrays.asList("基础", "评级", "质量", "AB客户", "对赌", "促销-增速", "促销-C"),
                    Arrays.asList("A", "B", "C", "D", "E"),
                    Arrays.asList("S", "S+", "S++", "X", "E")
            );
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
