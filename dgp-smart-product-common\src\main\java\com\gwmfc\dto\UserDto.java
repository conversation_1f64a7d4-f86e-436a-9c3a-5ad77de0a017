package com.gwmfc.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Classname UserDo
 * @Description
 * @Date 2021/8/4 13:29
 */
@Data
public class UserDto {
    private Long id;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户中文名
     */
    private String name;
    /**
     * 钉钉号
     */
    private String dingNo;
    /**
     * 工号
     */
    private String staffNo;
}
