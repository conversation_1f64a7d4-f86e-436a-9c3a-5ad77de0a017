package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.vo.ProductAssociationVo;
import com.gwmfc.vo.DdFpFinProductVo;
import com.gwmfc.vo.DdFpFinProductParamVo;
import com.gwmfc.service.DdFpFinProductService;
import com.gwmfc.util.PageForm;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.util.Result;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2023-10-11 09:20
 */

@Api(value = "产品列表接口", tags = "产品列表接口")
@RestController
@RequestMapping("/ddFpFinProduct")
public class DdFpFinProductController {

    @Resource
    DdFpFinProductService ddFpFinProductService;

    @ApiOperation(value = "产品列表查询接口")
    @PostMapping("/list")
    public Result list(@RequestBody PageForm<DdFpFinProductParamVo> pageForm) {
        IPage<DdFpFinProductVo> data = ddFpFinProductService.listPage(pageForm.getParam(), pageForm.getCurrent(), pageForm.getSize());
        return Result.ok(data.getRecords(), data.getTotal());
    }

    @ApiOperation(value = "根据id添加关联关系")
    @PostMapping("/addAssociation")
    public Result saveAssociation(@RequestBody ProductAssociationVo productAssociationVo, @CurrentUser User user) {
        ddFpFinProductService.addAssociation(productAssociationVo, user.getName());
        return Result.ok("添加关联关系成功");
    }

    @ApiOperation(value = "根据id取消关联关系")
    @PostMapping("/cancelAssociation")
    public Result cancelAssociation(@RequestBody ProductAssociationVo productAssociationVo) {
        if (ddFpFinProductService.cancelAssociation(productAssociationVo)) {
            return Result.ok("取消关联关系成功");
        } else {
            return Result.error("取消关联关系失败");
        }
    }
}

