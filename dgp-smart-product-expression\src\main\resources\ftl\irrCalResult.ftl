<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?mso-application progid="Word.Document"?>
<w:wordDocument xmlns:aml="http://schemas.microsoft.com/aml/2001/core" xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas" xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w10="urn:schemas-microsoft-com:office:word" xmlns:w="http://schemas.microsoft.com/office/word/2003/wordml" xmlns:wx="http://schemas.microsoft.com/office/word/2003/auxHint" xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml" xmlns:wsp="http://schemas.microsoft.com/office/word/2003/wordml/sp2" xmlns:sl="http://schemas.microsoft.com/schemaLibrary/2003/core" w:macrosPresent="no" w:embeddedObjPresent="no" w:ocxPresent="no" xml:space="preserve">
    <w:ignoreSubtree w:val="http://schemas.microsoft.com/office/word/2003/wordml/sp2" />
    <w:fonts>
        <w:defaultFonts w:ascii="Calibri" w:fareast="宋体" w:h-ansi="Calibri" w:cs="Times New Roman" />
        <w:font w:name="Times New Roman">
            <w:panose-1 w:val="02020603050405020304" />
            <w:charset w:val="00" />
            <w:family w:val="Roman" />
            <w:pitch w:val="variable" />
            <w:sig w:usb-0="E0002EFF" w:usb-1="C000785B" w:usb-2="00000009" w:usb-3="00000000" w:csb-0="000001FF" w:csb-1="00000000" />
        </w:font>
        <w:font w:name="宋体">
            <w:altName w:val="SimSun" />
            <w:panose-1 w:val="02010600030101010101" />
            <w:charset w:val="86" />
            <w:family w:val="auto" />
            <w:pitch w:val="variable" />
            <w:sig w:usb-0="00000003" w:usb-1="288F0000" w:usb-2="00000016" w:usb-3="00000000" w:csb-0="00040001" w:csb-1="00000000" />
        </w:font>
        <w:font w:name="宋体">
            <w:altName w:val="SimSun" />
            <w:panose-1 w:val="02010600030101010101" />
            <w:charset w:val="86" />
            <w:family w:val="auto" />
            <w:pitch w:val="variable" />
            <w:sig w:usb-0="00000003" w:usb-1="288F0000" w:usb-2="00000016" w:usb-3="00000000" w:csb-0="00040001" w:csb-1="00000000" />
        </w:font>
        <w:font w:name="Calibri">
            <w:panose-1 w:val="020F0502020204030204" />
            <w:charset w:val="00" />
            <w:family w:val="Swiss" />
            <w:pitch w:val="variable" />
            <w:sig w:usb-0="E4002EFF" w:usb-1="C000247B" w:usb-2="00000009" w:usb-3="00000000" w:csb-0="000001FF" w:csb-1="00000000" />
        </w:font>
        <w:font w:name="@宋体">
            <w:panose-1 w:val="02010600030101010101" />
            <w:charset w:val="86" />
            <w:family w:val="auto" />
            <w:pitch w:val="variable" />
            <w:sig w:usb-0="00000003" w:usb-1="288F0000" w:usb-2="00000016" w:usb-3="00000000" w:csb-0="00040001" w:csb-1="00000000" />
        </w:font>
    </w:fonts>
    <w:styles>
        <w:versionOfBuiltInStylenames w:val="7" />
        <w:latentStyles w:defLockedState="off" w:latentStyleCount="371">
            <w:lsdException w:name="Normal" />
            <w:lsdException w:name="heading 1" />
            <w:lsdException w:name="heading 2" />
            <w:lsdException w:name="heading 3" />
            <w:lsdException w:name="heading 4" />
            <w:lsdException w:name="heading 5" />
            <w:lsdException w:name="heading 6" />
            <w:lsdException w:name="heading 7" />
            <w:lsdException w:name="heading 8" />
            <w:lsdException w:name="heading 9" />
            <w:lsdException w:name="index 1" />
            <w:lsdException w:name="index 2" />
            <w:lsdException w:name="index 3" />
            <w:lsdException w:name="index 4" />
            <w:lsdException w:name="index 5" />
            <w:lsdException w:name="index 6" />
            <w:lsdException w:name="index 7" />
            <w:lsdException w:name="index 8" />
            <w:lsdException w:name="index 9" />
            <w:lsdException w:name="toc 1" />
            <w:lsdException w:name="toc 2" />
            <w:lsdException w:name="toc 3" />
            <w:lsdException w:name="toc 4" />
            <w:lsdException w:name="toc 5" />
            <w:lsdException w:name="toc 6" />
            <w:lsdException w:name="toc 7" />
            <w:lsdException w:name="toc 8" />
            <w:lsdException w:name="toc 9" />
            <w:lsdException w:name="Normal Indent" />
            <w:lsdException w:name="footnote text" />
            <w:lsdException w:name="annotation text" />
            <w:lsdException w:name="header" />
            <w:lsdException w:name="footer" />
            <w:lsdException w:name="index heading" />
            <w:lsdException w:name="caption" />
            <w:lsdException w:name="table of figures" />
            <w:lsdException w:name="envelope address" />
            <w:lsdException w:name="envelope return" />
            <w:lsdException w:name="footnote reference" />
            <w:lsdException w:name="annotation reference" />
            <w:lsdException w:name="line number" />
            <w:lsdException w:name="page number" />
            <w:lsdException w:name="endnote reference" />
            <w:lsdException w:name="endnote text" />
            <w:lsdException w:name="table of authorities" />
            <w:lsdException w:name="macro" />
            <w:lsdException w:name="toa heading" />
            <w:lsdException w:name="List" />
            <w:lsdException w:name="List Bullet" />
            <w:lsdException w:name="List Number" />
            <w:lsdException w:name="List 2" />
            <w:lsdException w:name="List 3" />
            <w:lsdException w:name="List 4" />
            <w:lsdException w:name="List 5" />
            <w:lsdException w:name="List Bullet 2" />
            <w:lsdException w:name="List Bullet 3" />
            <w:lsdException w:name="List Bullet 4" />
            <w:lsdException w:name="List Bullet 5" />
            <w:lsdException w:name="List Number 2" />
            <w:lsdException w:name="List Number 3" />
            <w:lsdException w:name="List Number 4" />
            <w:lsdException w:name="List Number 5" />
            <w:lsdException w:name="Title" />
            <w:lsdException w:name="Closing" />
            <w:lsdException w:name="Signature" />
            <w:lsdException w:name="Default Paragraph Font" />
            <w:lsdException w:name="Body Text" />
            <w:lsdException w:name="Body Text Indent" />
            <w:lsdException w:name="List Continue" />
            <w:lsdException w:name="List Continue 2" />
            <w:lsdException w:name="List Continue 3" />
            <w:lsdException w:name="List Continue 4" />
            <w:lsdException w:name="List Continue 5" />
            <w:lsdException w:name="Message Header" />
            <w:lsdException w:name="Subtitle" />
            <w:lsdException w:name="Salutation" />
            <w:lsdException w:name="Date" />
            <w:lsdException w:name="Body Text First Indent" />
            <w:lsdException w:name="Body Text First Indent 2" />
            <w:lsdException w:name="Note Heading" />
            <w:lsdException w:name="Body Text 2" />
            <w:lsdException w:name="Body Text 3" />
            <w:lsdException w:name="Body Text Indent 2" />
            <w:lsdException w:name="Body Text Indent 3" />
            <w:lsdException w:name="Block Text" />
            <w:lsdException w:name="Hyperlink" />
            <w:lsdException w:name="FollowedHyperlink" />
            <w:lsdException w:name="Strong" />
            <w:lsdException w:name="Emphasis" />
            <w:lsdException w:name="Document Map" />
            <w:lsdException w:name="Plain Text" />
            <w:lsdException w:name="E-mail Signature" />
            <w:lsdException w:name="HTML Top of Form" />
            <w:lsdException w:name="HTML Bottom of Form" />
            <w:lsdException w:name="Normal (Web)" />
            <w:lsdException w:name="HTML Acronym" />
            <w:lsdException w:name="HTML Address" />
            <w:lsdException w:name="HTML Cite" />
            <w:lsdException w:name="HTML Code" />
            <w:lsdException w:name="HTML Definition" />
            <w:lsdException w:name="HTML Keyboard" />
            <w:lsdException w:name="HTML Preformatted" />
            <w:lsdException w:name="HTML Sample" />
            <w:lsdException w:name="HTML Typewriter" />
            <w:lsdException w:name="HTML Variable" />
            <w:lsdException w:name="Normal Table" />
            <w:lsdException w:name="annotation subject" />
            <w:lsdException w:name="No List" />
            <w:lsdException w:name="Outline List 1" />
            <w:lsdException w:name="Outline List 2" />
            <w:lsdException w:name="Outline List 3" />
            <w:lsdException w:name="Table Simple 1" />
            <w:lsdException w:name="Table Simple 2" />
            <w:lsdException w:name="Table Simple 3" />
            <w:lsdException w:name="Table Classic 1" />
            <w:lsdException w:name="Table Classic 2" />
            <w:lsdException w:name="Table Classic 3" />
            <w:lsdException w:name="Table Classic 4" />
            <w:lsdException w:name="Table Colorful 1" />
            <w:lsdException w:name="Table Colorful 2" />
            <w:lsdException w:name="Table Colorful 3" />
            <w:lsdException w:name="Table Columns 1" />
            <w:lsdException w:name="Table Columns 2" />
            <w:lsdException w:name="Table Columns 3" />
            <w:lsdException w:name="Table Columns 4" />
            <w:lsdException w:name="Table Columns 5" />
            <w:lsdException w:name="Table Grid 1" />
            <w:lsdException w:name="Table Grid 2" />
            <w:lsdException w:name="Table Grid 3" />
            <w:lsdException w:name="Table Grid 4" />
            <w:lsdException w:name="Table Grid 5" />
            <w:lsdException w:name="Table Grid 6" />
            <w:lsdException w:name="Table Grid 7" />
            <w:lsdException w:name="Table Grid 8" />
            <w:lsdException w:name="Table List 1" />
            <w:lsdException w:name="Table List 2" />
            <w:lsdException w:name="Table List 3" />
            <w:lsdException w:name="Table List 4" />
            <w:lsdException w:name="Table List 5" />
            <w:lsdException w:name="Table List 6" />
            <w:lsdException w:name="Table List 7" />
            <w:lsdException w:name="Table List 8" />
            <w:lsdException w:name="Table 3D effects 1" />
            <w:lsdException w:name="Table 3D effects 2" />
            <w:lsdException w:name="Table 3D effects 3" />
            <w:lsdException w:name="Table Contemporary" />
            <w:lsdException w:name="Table Elegant" />
            <w:lsdException w:name="Table Professional" />
            <w:lsdException w:name="Table Subtle 1" />
            <w:lsdException w:name="Table Subtle 2" />
            <w:lsdException w:name="Table Web 1" />
            <w:lsdException w:name="Table Web 2" />
            <w:lsdException w:name="Table Web 3" />
            <w:lsdException w:name="Balloon Text" />
            <w:lsdException w:name="Table Grid" />
            <w:lsdException w:name="Table Theme" />
            <w:lsdException w:name="Placeholder Text" />
            <w:lsdException w:name="No Spacing" />
            <w:lsdException w:name="Light Shading" />
            <w:lsdException w:name="Light List" />
            <w:lsdException w:name="Light Grid" />
            <w:lsdException w:name="Medium Shading 1" />
            <w:lsdException w:name="Medium Shading 2" />
            <w:lsdException w:name="Medium List 1" />
            <w:lsdException w:name="Medium List 2" />
            <w:lsdException w:name="Medium Grid 1" />
            <w:lsdException w:name="Medium Grid 2" />
            <w:lsdException w:name="Medium Grid 3" />
            <w:lsdException w:name="Dark List" />
            <w:lsdException w:name="Colorful Shading" />
            <w:lsdException w:name="Colorful List" />
            <w:lsdException w:name="Colorful Grid" />
            <w:lsdException w:name="Light Shading Accent 1" />
            <w:lsdException w:name="Light List Accent 1" />
            <w:lsdException w:name="Light Grid Accent 1" />
            <w:lsdException w:name="Medium Shading 1 Accent 1" />
            <w:lsdException w:name="Medium Shading 2 Accent 1" />
            <w:lsdException w:name="Medium List 1 Accent 1" />
            <w:lsdException w:name="Revision" />
            <w:lsdException w:name="List Paragraph" />
            <w:lsdException w:name="Quote" />
            <w:lsdException w:name="Intense Quote" />
            <w:lsdException w:name="Medium List 2 Accent 1" />
            <w:lsdException w:name="Medium Grid 1 Accent 1" />
            <w:lsdException w:name="Medium Grid 2 Accent 1" />
            <w:lsdException w:name="Medium Grid 3 Accent 1" />
            <w:lsdException w:name="Dark List Accent 1" />
            <w:lsdException w:name="Colorful Shading Accent 1" />
            <w:lsdException w:name="Colorful List Accent 1" />
            <w:lsdException w:name="Colorful Grid Accent 1" />
            <w:lsdException w:name="Light Shading Accent 2" />
            <w:lsdException w:name="Light List Accent 2" />
            <w:lsdException w:name="Light Grid Accent 2" />
            <w:lsdException w:name="Medium Shading 1 Accent 2" />
            <w:lsdException w:name="Medium Shading 2 Accent 2" />
            <w:lsdException w:name="Medium List 1 Accent 2" />
            <w:lsdException w:name="Medium List 2 Accent 2" />
            <w:lsdException w:name="Medium Grid 1 Accent 2" />
            <w:lsdException w:name="Medium Grid 2 Accent 2" />
            <w:lsdException w:name="Medium Grid 3 Accent 2" />
            <w:lsdException w:name="Dark List Accent 2" />
            <w:lsdException w:name="Colorful Shading Accent 2" />
            <w:lsdException w:name="Colorful List Accent 2" />
            <w:lsdException w:name="Colorful Grid Accent 2" />
            <w:lsdException w:name="Light Shading Accent 3" />
            <w:lsdException w:name="Light List Accent 3" />
            <w:lsdException w:name="Light Grid Accent 3" />
            <w:lsdException w:name="Medium Shading 1 Accent 3" />
            <w:lsdException w:name="Medium Shading 2 Accent 3" />
            <w:lsdException w:name="Medium List 1 Accent 3" />
            <w:lsdException w:name="Medium List 2 Accent 3" />
            <w:lsdException w:name="Medium Grid 1 Accent 3" />
            <w:lsdException w:name="Medium Grid 2 Accent 3" />
            <w:lsdException w:name="Medium Grid 3 Accent 3" />
            <w:lsdException w:name="Dark List Accent 3" />
            <w:lsdException w:name="Colorful Shading Accent 3" />
            <w:lsdException w:name="Colorful List Accent 3" />
            <w:lsdException w:name="Colorful Grid Accent 3" />
            <w:lsdException w:name="Light Shading Accent 4" />
            <w:lsdException w:name="Light List Accent 4" />
            <w:lsdException w:name="Light Grid Accent 4" />
            <w:lsdException w:name="Medium Shading 1 Accent 4" />
            <w:lsdException w:name="Medium Shading 2 Accent 4" />
            <w:lsdException w:name="Medium List 1 Accent 4" />
            <w:lsdException w:name="Medium List 2 Accent 4" />
            <w:lsdException w:name="Medium Grid 1 Accent 4" />
            <w:lsdException w:name="Medium Grid 2 Accent 4" />
            <w:lsdException w:name="Medium Grid 3 Accent 4" />
            <w:lsdException w:name="Dark List Accent 4" />
            <w:lsdException w:name="Colorful Shading Accent 4" />
            <w:lsdException w:name="Colorful List Accent 4" />
            <w:lsdException w:name="Colorful Grid Accent 4" />
            <w:lsdException w:name="Light Shading Accent 5" />
            <w:lsdException w:name="Light List Accent 5" />
            <w:lsdException w:name="Light Grid Accent 5" />
            <w:lsdException w:name="Medium Shading 1 Accent 5" />
            <w:lsdException w:name="Medium Shading 2 Accent 5" />
            <w:lsdException w:name="Medium List 1 Accent 5" />
            <w:lsdException w:name="Medium List 2 Accent 5" />
            <w:lsdException w:name="Medium Grid 1 Accent 5" />
            <w:lsdException w:name="Medium Grid 2 Accent 5" />
            <w:lsdException w:name="Medium Grid 3 Accent 5" />
            <w:lsdException w:name="Dark List Accent 5" />
            <w:lsdException w:name="Colorful Shading Accent 5" />
            <w:lsdException w:name="Colorful List Accent 5" />
            <w:lsdException w:name="Colorful Grid Accent 5" />
            <w:lsdException w:name="Light Shading Accent 6" />
            <w:lsdException w:name="Light List Accent 6" />
            <w:lsdException w:name="Light Grid Accent 6" />
            <w:lsdException w:name="Medium Shading 1 Accent 6" />
            <w:lsdException w:name="Medium Shading 2 Accent 6" />
            <w:lsdException w:name="Medium List 1 Accent 6" />
            <w:lsdException w:name="Medium List 2 Accent 6" />
            <w:lsdException w:name="Medium Grid 1 Accent 6" />
            <w:lsdException w:name="Medium Grid 2 Accent 6" />
            <w:lsdException w:name="Medium Grid 3 Accent 6" />
            <w:lsdException w:name="Dark List Accent 6" />
            <w:lsdException w:name="Colorful Shading Accent 6" />
            <w:lsdException w:name="Colorful List Accent 6" />
            <w:lsdException w:name="Colorful Grid Accent 6" />
            <w:lsdException w:name="Subtle Emphasis" />
            <w:lsdException w:name="Intense Emphasis" />
            <w:lsdException w:name="Subtle Reference" />
            <w:lsdException w:name="Intense Reference" />
            <w:lsdException w:name="Book Title" />
            <w:lsdException w:name="Bibliography" />
            <w:lsdException w:name="TOC Heading" />
            <w:lsdException w:name="Plain Table 1" />
            <w:lsdException w:name="Plain Table 2" />
            <w:lsdException w:name="Plain Table 3" />
            <w:lsdException w:name="Plain Table 4" />
            <w:lsdException w:name="Plain Table 5" />
            <w:lsdException w:name="Grid Table Light" />
            <w:lsdException w:name="Grid Table 1 Light" />
            <w:lsdException w:name="Grid Table 2" />
            <w:lsdException w:name="Grid Table 3" />
            <w:lsdException w:name="Grid Table 4" />
            <w:lsdException w:name="Grid Table 5 Dark" />
            <w:lsdException w:name="Grid Table 6 Colorful" />
            <w:lsdException w:name="Grid Table 7 Colorful" />
            <w:lsdException w:name="Grid Table 1 Light Accent 1" />
            <w:lsdException w:name="Grid Table 2 Accent 1" />
            <w:lsdException w:name="Grid Table 3 Accent 1" />
            <w:lsdException w:name="Grid Table 4 Accent 1" />
            <w:lsdException w:name="Grid Table 5 Dark Accent 1" />
            <w:lsdException w:name="Grid Table 6 Colorful Accent 1" />
            <w:lsdException w:name="Grid Table 7 Colorful Accent 1" />
            <w:lsdException w:name="Grid Table 1 Light Accent 2" />
            <w:lsdException w:name="Grid Table 2 Accent 2" />
            <w:lsdException w:name="Grid Table 3 Accent 2" />
            <w:lsdException w:name="Grid Table 4 Accent 2" />
            <w:lsdException w:name="Grid Table 5 Dark Accent 2" />
            <w:lsdException w:name="Grid Table 6 Colorful Accent 2" />
            <w:lsdException w:name="Grid Table 7 Colorful Accent 2" />
            <w:lsdException w:name="Grid Table 1 Light Accent 3" />
            <w:lsdException w:name="Grid Table 2 Accent 3" />
            <w:lsdException w:name="Grid Table 3 Accent 3" />
            <w:lsdException w:name="Grid Table 4 Accent 3" />
            <w:lsdException w:name="Grid Table 5 Dark Accent 3" />
            <w:lsdException w:name="Grid Table 6 Colorful Accent 3" />
            <w:lsdException w:name="Grid Table 7 Colorful Accent 3" />
            <w:lsdException w:name="Grid Table 1 Light Accent 4" />
            <w:lsdException w:name="Grid Table 2 Accent 4" />
            <w:lsdException w:name="Grid Table 3 Accent 4" />
            <w:lsdException w:name="Grid Table 4 Accent 4" />
            <w:lsdException w:name="Grid Table 5 Dark Accent 4" />
            <w:lsdException w:name="Grid Table 6 Colorful Accent 4" />
            <w:lsdException w:name="Grid Table 7 Colorful Accent 4" />
            <w:lsdException w:name="Grid Table 1 Light Accent 5" />
            <w:lsdException w:name="Grid Table 2 Accent 5" />
            <w:lsdException w:name="Grid Table 3 Accent 5" />
            <w:lsdException w:name="Grid Table 4 Accent 5" />
            <w:lsdException w:name="Grid Table 5 Dark Accent 5" />
            <w:lsdException w:name="Grid Table 6 Colorful Accent 5" />
            <w:lsdException w:name="Grid Table 7 Colorful Accent 5" />
            <w:lsdException w:name="Grid Table 1 Light Accent 6" />
            <w:lsdException w:name="Grid Table 2 Accent 6" />
            <w:lsdException w:name="Grid Table 3 Accent 6" />
            <w:lsdException w:name="Grid Table 4 Accent 6" />
            <w:lsdException w:name="Grid Table 5 Dark Accent 6" />
            <w:lsdException w:name="Grid Table 6 Colorful Accent 6" />
            <w:lsdException w:name="Grid Table 7 Colorful Accent 6" />
            <w:lsdException w:name="List Table 1 Light" />
            <w:lsdException w:name="List Table 2" />
            <w:lsdException w:name="List Table 3" />
            <w:lsdException w:name="List Table 4" />
            <w:lsdException w:name="List Table 5 Dark" />
            <w:lsdException w:name="List Table 6 Colorful" />
            <w:lsdException w:name="List Table 7 Colorful" />
            <w:lsdException w:name="List Table 1 Light Accent 1" />
            <w:lsdException w:name="List Table 2 Accent 1" />
            <w:lsdException w:name="List Table 3 Accent 1" />
            <w:lsdException w:name="List Table 4 Accent 1" />
            <w:lsdException w:name="List Table 5 Dark Accent 1" />
            <w:lsdException w:name="List Table 6 Colorful Accent 1" />
            <w:lsdException w:name="List Table 7 Colorful Accent 1" />
            <w:lsdException w:name="List Table 1 Light Accent 2" />
            <w:lsdException w:name="List Table 2 Accent 2" />
            <w:lsdException w:name="List Table 3 Accent 2" />
            <w:lsdException w:name="List Table 4 Accent 2" />
            <w:lsdException w:name="List Table 5 Dark Accent 2" />
            <w:lsdException w:name="List Table 6 Colorful Accent 2" />
            <w:lsdException w:name="List Table 7 Colorful Accent 2" />
            <w:lsdException w:name="List Table 1 Light Accent 3" />
            <w:lsdException w:name="List Table 2 Accent 3" />
            <w:lsdException w:name="List Table 3 Accent 3" />
            <w:lsdException w:name="List Table 4 Accent 3" />
            <w:lsdException w:name="List Table 5 Dark Accent 3" />
            <w:lsdException w:name="List Table 6 Colorful Accent 3" />
            <w:lsdException w:name="List Table 7 Colorful Accent 3" />
            <w:lsdException w:name="List Table 1 Light Accent 4" />
            <w:lsdException w:name="List Table 2 Accent 4" />
            <w:lsdException w:name="List Table 3 Accent 4" />
            <w:lsdException w:name="List Table 4 Accent 4" />
            <w:lsdException w:name="List Table 5 Dark Accent 4" />
            <w:lsdException w:name="List Table 6 Colorful Accent 4" />
            <w:lsdException w:name="List Table 7 Colorful Accent 4" />
            <w:lsdException w:name="List Table 1 Light Accent 5" />
            <w:lsdException w:name="List Table 2 Accent 5" />
            <w:lsdException w:name="List Table 3 Accent 5" />
            <w:lsdException w:name="List Table 4 Accent 5" />
            <w:lsdException w:name="List Table 5 Dark Accent 5" />
            <w:lsdException w:name="List Table 6 Colorful Accent 5" />
            <w:lsdException w:name="List Table 7 Colorful Accent 5" />
            <w:lsdException w:name="List Table 1 Light Accent 6" />
            <w:lsdException w:name="List Table 2 Accent 6" />
            <w:lsdException w:name="List Table 3 Accent 6" />
            <w:lsdException w:name="List Table 4 Accent 6" />
            <w:lsdException w:name="List Table 5 Dark Accent 6" />
            <w:lsdException w:name="List Table 6 Colorful Accent 6" />
            <w:lsdException w:name="List Table 7 Colorful Accent 6" />
        </w:latentStyles>
        <w:style w:type="paragraph" w:default="on" w:styleId="a">
            <w:name w:val="Normal" />
            <wx:uiName wx:val="正文" />
            <w:pPr>
                <w:widowControl w:val="off" />
                <w:jc w:val="both" />
            </w:pPr>
            <w:rPr>
                <wx:font wx:val="Calibri" />
                <w:kern w:val="2" />
                <w:sz w:val="21" />
                <w:sz-cs w:val="22" />
                <w:lang w:val="EN-US" w:fareast="ZH-CN" w:bidi="AR-SA" />
            </w:rPr>
        </w:style>
        <w:style w:type="character" w:default="on" w:styleId="a0">
            <w:name w:val="Default Paragraph Font" />
            <wx:uiName wx:val="默认段落字体" />
        </w:style>
        <w:style w:type="table" w:default="on" w:styleId="a1">
            <w:name w:val="Normal Table" />
            <wx:uiName wx:val="普通表格" />
            <w:rPr>
                <wx:font wx:val="Calibri" />
                <w:lang w:val="EN-US" w:fareast="ZH-CN" w:bidi="AR-SA" />
            </w:rPr>
            <w:tblPr>
                <w:tblInd w:w="0" w:type="dxa" />
                <w:tblCellMar>
                    <w:top w:w="0" w:type="dxa" />
                    <w:left w:w="108" w:type="dxa" />
                    <w:bottom w:w="0" w:type="dxa" />
                    <w:right w:w="108" w:type="dxa" />
                </w:tblCellMar>
            </w:tblPr>
        </w:style>
        <w:style w:type="list" w:default="on" w:styleId="a2">
            <w:name w:val="No List" />
            <wx:uiName wx:val="无列表" />
        </w:style>
        <w:style w:type="table" w:styleId="a3">
            <w:name w:val="Table Grid" />
            <wx:uiName wx:val="网格型" />
            <w:basedOn w:val="a1" />
            <w:rsid w:val="00AD7064" />
            <w:rPr>
                <wx:font wx:val="Calibri" />
            </w:rPr>
            <w:tblPr>
                <w:tblInd w:w="0" w:type="dxa" />
                <w:tblBorders>
                    <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                    <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                    <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                    <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                    <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                    <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                </w:tblBorders>
                <w:tblCellMar>
                    <w:top w:w="0" w:type="dxa" />
                    <w:left w:w="108" w:type="dxa" />
                    <w:bottom w:w="0" w:type="dxa" />
                    <w:right w:w="108" w:type="dxa" />
                </w:tblCellMar>
            </w:tblPr>
        </w:style>
    </w:styles>
    <w:divs>
        <w:div w:id="806969113">
            <w:bodyDiv w:val="on" />
            <w:marLeft w:val="0" />
            <w:marRight w:val="0" />
            <w:marTop w:val="0" />
            <w:marBottom w:val="0" />
            <w:divBdr>
                <w:top w:val="none" w:sz="0" wx:bdrwidth="0" w:space="0" w:color="auto" />
                <w:left w:val="none" w:sz="0" wx:bdrwidth="0" w:space="0" w:color="auto" />
                <w:bottom w:val="none" w:sz="0" wx:bdrwidth="0" w:space="0" w:color="auto" />
                <w:right w:val="none" w:sz="0" wx:bdrwidth="0" w:space="0" w:color="auto" />
            </w:divBdr>
        </w:div>
        <w:div w:id="1713188808">
            <w:bodyDiv w:val="on" />
            <w:marLeft w:val="0" />
            <w:marRight w:val="0" />
            <w:marTop w:val="0" />
            <w:marBottom w:val="0" />
            <w:divBdr>
                <w:top w:val="none" w:sz="0" wx:bdrwidth="0" w:space="0" w:color="auto" />
                <w:left w:val="none" w:sz="0" wx:bdrwidth="0" w:space="0" w:color="auto" />
                <w:bottom w:val="none" w:sz="0" wx:bdrwidth="0" w:space="0" w:color="auto" />
                <w:right w:val="none" w:sz="0" wx:bdrwidth="0" w:space="0" w:color="auto" />
            </w:divBdr>
        </w:div>
    </w:divs>
    <w:shapeDefaults>
        <o:shapedefaults v:ext="edit" spidmax="1026" />
        <o:shapelayout v:ext="edit">
            <o:idmap v:ext="edit" data="1" />
        </o:shapelayout>
    </w:shapeDefaults>
    <w:docPr>
        <w:view w:val="print" />
        <w:zoom w:percent="100" />
        <w:doNotEmbedSystemFonts />
        <w:bordersDontSurroundHeader />
        <w:bordersDontSurroundFooter />
        <w:proofState w:spelling="clean" w:grammar="clean" />
        <w:defaultTabStop w:val="420" />
        <w:drawingGridHorizontalSpacing w:val="105" />
        <w:drawingGridVerticalSpacing w:val="156" />
        <w:displayHorizontalDrawingGridEvery w:val="0" />
        <w:displayVerticalDrawingGridEvery w:val="2" />
        <w:punctuationKerning />
        <w:characterSpacingControl w:val="CompressPunctuation" />
        <w:optimizeForBrowser />
        <w:allowPNG />
        <w:validateAgainstSchema />
        <w:saveInvalidXML w:val="off" />
        <w:ignoreMixedContent w:val="off" />
        <w:alwaysShowPlaceholderText w:val="off" />
        <w:compat>
            <w:spaceForUL />
            <w:balanceSingleByteDoubleByteWidth />
            <w:doNotLeaveBackslashAlone />
            <w:ulTrailSpace />
            <w:doNotExpandShiftReturn />
            <w:adjustLineHeightInTable />
            <w:breakWrappedTables />
            <w:snapToGridInCell />
            <w:wrapTextWithPunct />
            <w:useAsianBreakRules />
            <w:dontGrowAutofit />
            <w:useFELayout />
        </w:compat>
        <wsp:rsids>
            <wsp:rsidRoot wsp:val="00F65F40" />
            <wsp:rsid wsp:val="00012CD7" />
            <wsp:rsid wsp:val="000B2EE5" />
            <wsp:rsid wsp:val="0031098D" />
            <wsp:rsid wsp:val="004C1E34" />
            <wsp:rsid wsp:val="005E0BAD" />
            <wsp:rsid wsp:val="006D014F" />
            <wsp:rsid wsp:val="00A63118" />
            <wsp:rsid wsp:val="00AD7064" />
            <wsp:rsid wsp:val="00B82C2F" />
            <wsp:rsid wsp:val="00C16E74" />
            <wsp:rsid wsp:val="00C603A0" />
            <wsp:rsid wsp:val="00D006EF" />
            <wsp:rsid wsp:val="00F65F40" />
            <wsp:rsid wsp:val="00F834A7" />
            <wsp:rsid wsp:val="00F84610" />
            <wsp:rsid wsp:val="00FC6BD4" />
        </wsp:rsids>
    </w:docPr>
    <w:body>
        <wx:sect>
            <w:p wsp:rsidR="00B82C2F" wsp:rsidRDefault="00D006EF">
                <w:r>
                    <w:rPr>
                        <w:rFonts w:hint="fareast" />
                        <wx:font wx:val="宋体" />
                        <w:b />
                    </w:rPr>
                    <w:t>附件：</w:t>
                </w:r>
                <w:r wsp:rsidR="00B82C2F">
                    <w:rPr>
                        <w:b />
                    </w:rPr>
                    <w:t>${productsName}</w:t>
                </w:r>
            </w:p>
            <w:tbl>
                <w:tblPr>
                    <w:tblW w:w="12250" w:type="dxa" />
                    <w:jc w:val="center" />
                    <w:tblBorders>
                        <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                        <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                        <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                        <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                        <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                        <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto" />
                    </w:tblBorders>
                    <w:tblLayout w:type="Fixed" />
                    <w:tblLook w:val="04A0" />
                </w:tblPr>
                <w:tr wsp:rsidR="00A63118" wsp:rsidRPr="00F834A7" wsp:rsidTr="00F834A7">
                    <w:trPr>
                        <w:jc w:val="center" />
                    </w:trPr>
                    <#if (groupSize>1)>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="889" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>产品组名称</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    </#if>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="889" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${firstEleName}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
<#--                    <#list addElementNames as addElementName>-->
<#--                    <w:tc>-->
<#--                        <w:tcPr>-->
<#--                            <w:tcW w:w="889" w:type="dxa" />-->
<#--                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />-->
<#--                            <w:vAlign w:val="center" />-->
<#--                        </w:tcPr>-->
<#--                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">-->
<#--                            <w:pPr>-->
<#--                                <w:jc w:val="center" />-->
<#--                                <w:rPr>-->
<#--                                    <w:b />-->
<#--                                    <w:sz w:val="15" />-->
<#--                                    <w:sz-cs w:val="15" />-->
<#--                                </w:rPr>-->
<#--                            </w:pPr>-->
<#--                            <w:r wsp:rsidRPr="00F834A7">-->
<#--                                <w:rPr>-->
<#--                                    <wx:font wx:val="宋体" />-->
<#--                                    <w:b />-->
<#--                                    <w:sz w:val="15" />-->
<#--                                    <w:sz-cs w:val="15" />-->
<#--                                </w:rPr>-->
<#--                                <w:t>${addElementName}</w:t>-->
<#--                            </w:r>-->
<#--                        </w:p>-->
<#--                    </w:tc>-->
<#--                    </#list>-->
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="889" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>贷款额</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="889" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>客户利率</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="889" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>结算利率</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>期限</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>商务返利</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>个人奖励</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>风险损失</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>其他成本</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>IRR</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:rFonts w:hint="fareast" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>R</w:t>
                            </w:r>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>OA</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:rFonts w:hint="fareast" />
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>净利润</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <#if weightedComputationOrNot==1>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>合同占比</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:gridSpan w:val="2" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <wx:font wx:val="宋体" />
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>加权IRR</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    </#if>
                    <#if weightedBetweenGroupsOrNot==1>
                        <w:tc>
                            <w:tcPr>
                                <w:tcW w:w="890" w:type="dxa" />
                                <w:gridSpan w:val="2" />
                                <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                                <w:vAlign w:val="center" />
                            </w:tcPr>
                            <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                                <w:pPr>
                                    <w:jc w:val="center" />
                                    <w:rPr>
                                        <w:b />
                                        <w:sz w:val="15" />
                                        <w:sz-cs w:val="15" />
                                    </w:rPr>
                                </w:pPr>
                                <w:r wsp:rsidRPr="00F834A7">
                                    <w:rPr>
                                        <wx:font wx:val="宋体" />
                                        <w:b />
                                        <w:sz w:val="15" />
                                        <w:sz-cs w:val="15" />
                                    </w:rPr>
                                    <w:t>组间加权比例</w:t>
                                </w:r>
                            </w:p>
                        </w:tc>
                        <w:tc>
                            <w:tcPr>
                                <w:tcW w:w="890" w:type="dxa" />
                                <w:gridSpan w:val="2" />
                                <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                                <w:vAlign w:val="center" />
                            </w:tcPr>
                            <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00B82C2F" wsp:rsidP="00F834A7">
                                <w:pPr>
                                    <w:jc w:val="center" />
                                    <w:rPr>
                                        <w:b />
                                        <w:sz w:val="15" />
                                        <w:sz-cs w:val="15" />
                                    </w:rPr>
                                </w:pPr>
                                <w:r wsp:rsidRPr="00F834A7">
                                    <w:rPr>
                                        <wx:font wx:val="宋体" />
                                        <w:b />
                                        <w:sz w:val="15" />
                                        <w:sz-cs w:val="15" />
                                    </w:rPr>
                                    <w:t>组间加权IRR</w:t>
                                </w:r>
                            </w:p>
                        </w:tc>
                    </#if>
                </w:tr>
                <#list values as item>
                <w:tr wsp:rsidR="00A63118" wsp:rsidRPr="00F834A7" wsp:rsidTr="00F834A7">
                    <w:trPr>
                        <w:jc w:val="center" />
                    </w:trPr>
                    <#if (groupSize>1)>
                        <w:tc>
                            <w:tcPr>
                                <w:tcW w:w="889" w:type="dxa" />
                                <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                                <w:vAlign w:val="center" />
                                <#if item.strMap.now == "1">
                                    <#if item.strMap.pre == "0">
                                        <w:vmerge w:val="restart"/>
                                    <#else>
                                        <w:vmerge/>
                                    </#if>
                                <#else>
                                    <#if item.strMap.pre != "0">
                                        <w:vmerge/>
                                    </#if>
                                </#if>
                            </w:tcPr>
                            <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00FC6BD4" wsp:rsidP="00F834A7">
                                <w:pPr>
                                    <w:jc w:val="center" />
                                    <w:rPr>
                                        <w:b />
                                        <w:sz w:val="15" />
                                        <w:sz-cs w:val="15" />
                                    </w:rPr>
                                </w:pPr>
                                <w:proofErr w:type="spellStart" />
                                <w:r wsp:rsidRPr="00F834A7">
                                    <w:rPr>
                                        <w:sz w:val="15" />
                                        <w:sz-cs w:val="15" />
                                    </w:rPr>
                                    <w:t>${item.groupName}</w:t>
                                </w:r>
                                <w:proofErr w:type="spellEnd" />
                            </w:p>
                        </w:tc>
                    </#if>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="889" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00FC6BD4" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:b />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.firstEleValue}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
<#--                    <#list item.addElementValues as addElementValue>-->
<#--                    <w:tc>-->
<#--                        <w:tcPr>-->
<#--                            <w:tcW w:w="889" w:type="dxa" />-->
<#--                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />-->
<#--                            <w:vAlign w:val="center" />-->
<#--                        </w:tcPr>-->
<#--                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00FC6BD4" wsp:rsidP="00F834A7">-->
<#--                            <w:pPr>-->
<#--                                <w:jc w:val="center" />-->
<#--                                <w:rPr>-->
<#--                                    <w:b />-->
<#--                                    <w:sz w:val="15" />-->
<#--                                    <w:sz-cs w:val="15" />-->
<#--                                </w:rPr>-->
<#--                            </w:pPr>-->
<#--                            <w:proofErr w:type="spellStart" />-->
<#--                            <w:r wsp:rsidRPr="00F834A7">-->
<#--                                <w:rPr>-->
<#--                                    <w:sz w:val="15" />-->
<#--                                    <w:sz-cs w:val="15" />-->
<#--                                </w:rPr>-->
<#--                                <w:t>${addElementValue}</w:t>-->
<#--                            </w:r>-->
<#--                            <w:proofErr w:type="spellEnd" />-->
<#--                        </w:p>-->
<#--                    </w:tc>-->
<#--                    </#list>-->
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="889" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00FC6BD4" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.loanMoney}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="889" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00FC6BD4" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.customerInterestRate}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="889" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00FC6BD4" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.actualInterestRate}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00FC6BD4" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.timeLimit}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C16E74" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.dealerBasicCommissionRatio}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C16E74" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.specialReward}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C16E74" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.lossRate}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C16E74" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.fixedCost}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C16E74" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.resultIrr}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C16E74" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.resultRoa}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C16E74" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.resultNetProfitMoney}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <#if weightedComputationOrNot==1>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:gridSpan w:val="2" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C16E74" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.contractProportion}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="890" w:type="dxa" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <#if (groupSize>1)>
                                <#if item.strMap.now == "1">
                                    <#if item.strMap.pre == "0">
                                        <w:vmerge w:val="restart"/>
                                    <#else>
                                        <w:vmerge/>
                                    </#if>
                                <#else>
                                    <#if item.strMap.pre != "0">
                                        <w:vmerge/>
                                    </#if>
                                </#if>
                            <#else>
                                <#if item.index==1>
                                    <w:vmerge w:val="restart"/>
                                <#else>
                                    <w:vmerge/>
                                </#if>
                            </#if>
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C16E74" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="center" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:proofErr w:type="spellStart" />
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${item.weightingIrr}</w:t>
                            </w:r>
                            <w:proofErr w:type="spellEnd" />
                        </w:p>
                    </w:tc>
                    </#if>
                    <#if weightedBetweenGroupsOrNot==1>
                        <w:tc>
                            <w:tcPr>
                                <w:tcW w:w="890" w:type="dxa" />
                                <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                                <#if (groupSize>1)>
                                    <#if item.strMap.now == "1">
                                        <#if item.strMap.pre == "0">
                                            <w:vmerge w:val="restart"/>
                                        <#else>
                                            <w:vmerge/>
                                        </#if>
                                    <#else>
                                        <#if item.strMap.pre != "0">
                                            <w:vmerge/>
                                        </#if>
                                    </#if>
                                <#else>
                                    <#if item.index==1>
                                        <w:vmerge w:val="restart"/>
                                    <#else>
                                        <w:vmerge/>
                                    </#if>
                                </#if>
                                <w:vAlign w:val="center" />
                            </w:tcPr>
                            <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C16E74" wsp:rsidP="00F834A7">
                                <w:pPr>
                                    <w:jc w:val="center" />
                                    <w:rPr>
                                        <w:sz w:val="15" />
                                        <w:sz-cs w:val="15" />
                                    </w:rPr>
                                </w:pPr>
                                <w:proofErr w:type="spellStart" />
                                <w:r wsp:rsidRPr="00F834A7">
                                    <w:rPr>
                                        <w:sz w:val="15" />
                                        <w:sz-cs w:val="15" />
                                    </w:rPr>
                                    <w:t>${item.groupProportion}</w:t>
                                </w:r>
                                <w:proofErr w:type="spellEnd" />
                            </w:p>
                        </w:tc>
                        <w:tc>
                            <w:tcPr>
                                <w:tcW w:w="890" w:type="dxa" />
                                <w:gridSpan w:val="2" />
                                <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                                <#if item.index==1>
                                    <w:vmerge w:val="restart"/>
                                <#else>
                                    <w:vmerge/>
                                </#if>
                                <w:vAlign w:val="center" />
                            </w:tcPr>
                            <w:p wsp:rsidR="00B82C2F" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C16E74" wsp:rsidP="00F834A7">
                                <w:pPr>
                                    <w:jc w:val="center" />
                                    <w:rPr>
                                        <w:sz w:val="15" />
                                        <w:sz-cs w:val="15" />
                                    </w:rPr>
                                </w:pPr>
                                <w:proofErr w:type="spellStart" />
                                <w:r wsp:rsidRPr="00F834A7">
                                    <w:rPr>
                                        <w:sz w:val="15" />
                                        <w:sz-cs w:val="15" />
                                    </w:rPr>
                                    <w:t>${item.groupWeightingIrr}</w:t>
                                </w:r>
                                <w:proofErr w:type="spellEnd" />
                            </w:p>
                        </w:tc>
                    </#if>
                </w:tr>
                </#list>
                <w:tr wsp:rsidR="00C603A0" wsp:rsidRPr="00F834A7" wsp:rsidTr="00F834A7">
                    <w:trPr>
                        <w:jc w:val="center" />
                    </w:trPr>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="12456" w:type="dxa" />
                            <w:gridSpan w:val="${tatalColum}" />
                            <w:shd w:val="clear" w:color="auto" w:fill="auto" />
                            <w:vAlign w:val="center" />
                        </w:tcPr>
                        <w:p wsp:rsidR="00C603A0" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C603A0" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="left" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:rFonts w:hint="fareast" />
                                    <wx:font wx:val="宋体" />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>说明</w:t>
                            </w:r>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:rFonts w:hint="fareast" />
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>:</w:t>
                            </w:r>
                        </w:p>
                        <w:p wsp:rsidR="00C603A0" wsp:rsidRPr="00F834A7" wsp:rsidRDefault="00C603A0" wsp:rsidP="00F834A7">
                            <w:pPr>
                                <w:jc w:val="left" />
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                            </w:pPr>
                            <w:r wsp:rsidRPr="00F834A7">
                                <w:rPr>
                                    <w:sz w:val="15" />
                                    <w:sz-cs w:val="15" />
                                </w:rPr>
                                <w:t>${instructions}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                </w:tr>
            </w:tbl>
            <w:p wsp:rsidR="00B82C2F" wsp:rsidRDefault="00B82C2F" />
            <w:sectPr wsp:rsidR="00B82C2F" wsp:rsidSect="00AD7064">
                <w:pgSz w:w="15120" w:h="10440" w:orient="landscape" w:code="7" />
                <w:pgMar w:top="1800" w:right="1440" w:bottom="1800" w:left="1440" w:header="851" w:footer="992" w:gutter="0" />
                <w:cols w:space="425" />
                <w:docGrid w:type="lines" w:line-pitch="312" />
            </w:sectPr>
        </wx:sect>
    </w:body>
</w:wordDocument>