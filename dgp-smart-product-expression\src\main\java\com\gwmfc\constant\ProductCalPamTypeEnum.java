package com.gwmfc.constant;

/**
 * <AUTHOR>
 * @Classname ProductCalBussType
 * @Description 业务类型
 * @Date 2023/9/22 14:26
 */
public enum ProductCalPamTypeEnum {
    /**
     * 基本参数
     */
    BASIC_PAM("0"),

    /**
     * 税费参数
     */
    TAX_FEE_PAM("1"),

    /**
     * 还款方式参数
     */
    REPAYMENTY_METHOD_PAM("2"),

    /**
     * 提前结清参数
     */
    EARLY_SQUARE_PAM("3");


    private final String type;

    ProductCalPamTypeEnum(String type) {
        this.type = type;
    }

    public String type() {
        return type;
    }

    public static ProductCalPamTypeEnum getProductCalPamTypeEnumByType(String type) {
        for (ProductCalPamTypeEnum productCalPamTypeEnum : ProductCalPamTypeEnum.values()) {
            if (productCalPamTypeEnum.type().equals(type)) {
                return productCalPamTypeEnum;
            }
        }
        return null;
    }
}
