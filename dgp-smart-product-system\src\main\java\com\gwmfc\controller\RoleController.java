package com.gwmfc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;

import com.gwmfc.dto.RoleDto;
import com.gwmfc.entity.UserRoleDO;
import com.gwmfc.service.RoleService;

import com.gwmfc.entity.RoleDO;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;


/**
 * <AUTHOR>
 * @Classname RoleController
 * @Date 2021/8/5 17:05
 */
@Api(value = "角色接口",tags = "角色接口")
@RestController
@RequestMapping("/role")
public class RoleController {

    @Autowired
    private RoleService roleService;

    @ApiOperation(value="获取列表接口")
    @PostMapping("/list")
    public Result list (@RequestBody PageForm<RoleDto> pageForm){
        Result result = new Result();
        RoleDO roleDO = new RoleDO();
        if(pageForm != null){
            if (pageForm.getParam() != null) {
                BeanUtils.copyProperties(pageForm.getParam(),roleDO);
            }
            IPage<RoleDO> list = roleService.pageList(roleDO, pageForm.getCurrent(), pageForm.getSize());
            result.setTotal(list.getTotal());
            result.setData(list.getRecords());
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        } else {
            result.setCode(FAIL.getCode());
            result.setMessage(FAIL.getDesc());
            result.setData(0);
        }
        return result;
    }

    @ApiOperation(value="保存接口")
    @PostMapping("/save")
    public Result save (@RequestBody  RoleDto roleDto){
        Result result = new Result();
        roleService.save(roleDto);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }


    @ApiOperation(value="查询详情")
    @GetMapping("/{id}")
    public Result getOne (@PathVariable("id") Long id){
        Result result = new Result();
        RoleDO data = roleService.getOne(id);
        result.setData(data);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    @ApiOperation(value="更新接口")
    @PostMapping("/update")
    public Result update (@RequestBody RoleDto roleDto){
        Result result = new Result();
        roleService.update(roleDto);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    @ApiOperation(value="删除接口")
    @GetMapping("/delete/{id}")
    public Result delete (@PathVariable("id") Long id){
        Result result = new Result();
        roleService.delete(id);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }


    @ApiOperation(value="获取用户角色")
    @GetMapping("/getRolesByUserId")
    public Result<RoleDto> getRolesByUserId(@RequestParam Long id){
        List<RoleDO> roleDos = roleService.getRolesByUserId(id);
        List<RoleDto> list = new ArrayList<>(roleDos.size());
        for(RoleDO roleDO : roleDos){
            RoleDto roleDto = new RoleDto();
            BeanUtils.copyProperties(roleDO,roleDto);
            list.add(roleDto);
        }
        return Result.ok(list, Long.valueOf(list.size()));
    }

    @ApiOperation(value="用户绑定角色接口")
    @PostMapping("/save/user/role")
    public Result saveUserRole(@RequestBody List<UserRoleDO> roles) {
        roleService.updateUserRole(roles);
        return Result.ok();
    }



}
