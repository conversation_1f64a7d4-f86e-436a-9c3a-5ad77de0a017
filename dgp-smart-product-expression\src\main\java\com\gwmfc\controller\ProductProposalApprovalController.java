package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductProposalApprovalDto;
import com.gwmfc.service.approve.ProductProposalApprovalStrategyContext;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * 风险策略提案
 *
 * <AUTHOR>
 * @date 2023年10月12日 13:35
 */
@Api(tags = "产品提案审核")
@RestController
@Slf4j
@RequestMapping("/product/proposal/approval")
public class ProductProposalApprovalController {

    @Resource
    private ProductProposalApprovalStrategyContext productProposalApprovalStrategyContext;
    /**
     * 风控商务审核
     *
     * @param productProposalApprovalDto
     * @return
     * @RequestBody DingDingNotifyDto dingDingNotifyDto,
     */
    @ApiOperation("审核")
    @PostMapping("/productProposalApproval")
    public Result productProposalApproval(@RequestBody ProductProposalApprovalDto productProposalApprovalDto, @ApiIgnore @CurrentUser User user) {
        productProposalApprovalStrategyContext.executeStrategy(productProposalApprovalDto, user);
        return Result.ok();
    }

    /**
     * 产品提案生成保存
     *
     * @param productProposalApprovalDto
     * @return
     */
    @ApiOperation("产品提案生成保存（第六步）")
    @PostMapping(value = "/productProposalSave", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result productProposalSave(@RequestPart("productProposalApprovalDto") ProductProposalApprovalDto productProposalApprovalDto, @RequestPart(name = "addFileList", required = false) List<MultipartFile> addFileList, @ApiIgnore @CurrentUser User user) {
        productProposalApprovalStrategyContext.productProposalSave(productProposalApprovalDto, addFileList, user);
        return Result.ok();
    }

    /**
     *
     * @param productProposalId
     * @return
     */
    @ApiOperation("审核历史查询")
    @GetMapping("/approvalHistory")
    public Result approvalHistory(@RequestParam Long productProposalId) {
        Result result = new Result<>();
        result.setData(productProposalApprovalStrategyContext.approvalHistory(productProposalId));
        result.setCode(SUCCESS.getCode());
        return result;
    }
}
