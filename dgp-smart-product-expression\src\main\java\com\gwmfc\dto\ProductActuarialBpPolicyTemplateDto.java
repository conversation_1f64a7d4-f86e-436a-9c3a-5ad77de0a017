package com.gwmfc.dto;

import com.gwmfc.entity.ProductActuarialBpCalPlanAssembleDimensionDetailEntity;
import com.gwmfc.entity.ProductActuarialBpPolicyTemplateAssembleDimensionDetailEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductActuarialBpPolicyTemplateDto
 * @Description TODO
 * @Date 2025/4/23 13:20
 */
@Data
@ApiModel(value = "政策模板dto")
public class ProductActuarialBpPolicyTemplateDto {
    private Long id;

    @ApiModelProperty("政策模板名称")
    private String policyTemplateName;

    @ApiModelProperty("一级维度名称")
    private String firstDimensionName;

    @ApiModelProperty("一级维度内容")
    private List<String> firstDimensionContentList;

    @ApiModelProperty("二级维度名称")
    private String secondDimensionName;

    @ApiModelProperty("二级维度内容")
    private List<String> secondDimensionContentList;

    @ApiModelProperty("三级维度名称")
    private String thirdDimensionName;

    @ApiModelProperty("三级维度内容")
    private List<String> thirdDimensionContentList;

    @ApiModelProperty("四级维度名称")
    private String forthDimensionName;

    @ApiModelProperty("四级维度内容")
    private List<String> forthDimensionContentList;

    @ApiModelProperty("五级维度名称")
    private String fifthDimensionName;

    @ApiModelProperty("五级维度内容")
    private List<String> fifthDimensionContentList;

    @ApiModelProperty("六级维度名称")
    private String sixthDimensionName;

    @ApiModelProperty("六级维度内容")
    private List<String> sixthDimensionContentList;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("生效时间")
    private Date effectiveTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("更新人")
    private String updateUser;

    @ApiModelProperty("维度组合详情")
    private List<ProductActuarialBpPolicyTemplateAssembleDimensionDetailEntity> productActuarialBpPolicyTemplateAssembleDimensionDetailEntityList;
}
