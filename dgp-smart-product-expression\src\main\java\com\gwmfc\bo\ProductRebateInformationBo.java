package com.gwmfc.bo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023年09月07日 13:04
 */
@Data
@TableName("product_rebate_information")
public class ProductRebateInformationBo {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("期数")
    private Integer periods;
    @ApiModelProperty("对客年利率")
    private String perAnnumRate;

    @ApiModelProperty("对客总费率")
    private String totalRateForGuests;

    @ApiModelProperty("返佣点")
    private String rebatePoint;
    @ApiModelProperty("合同量")
    private String contractNum;
    @ApiModelProperty("返佣趋势(返佣不变、返佣增加、返佣减少)")
    private String commissionRebateTrend;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("政策类型(1:上月延续；2：新增；3：变更；4：终止)")
    private Integer policyType;
    @ApiModelProperty("政策适用区域（1：本集团；2：本市；3：本省；4：通用）")
    private Integer policyApplyArea;
    @ApiModelProperty("适用品牌")
    private String brand;

    @ApiModelProperty("状态")
    private Integer status;

}
