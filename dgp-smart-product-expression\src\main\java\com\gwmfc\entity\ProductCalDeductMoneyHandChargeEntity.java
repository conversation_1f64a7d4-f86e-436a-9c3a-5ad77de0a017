package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductCalLoanHandCharge
 * @Description product_cal_deduct_money_hand_charge
 * @Date 2023/9/22 13:32
 */
@Data
@TableName("product_cal_deduct_money_hand_charge")
public class ProductCalDeductMoneyHandChargeEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "year", comment = "年")
    private String year;

    @TableFieldMapping(value = "bank", comment = "银行")
    private String bank;

    @TableFieldMapping(value = "range_low", comment = "范围最低值")
    private Double rangeLow;

    @TableFieldMapping(value = "range_high", comment = "范围最高值")
    private Double rangeHigh;

    @TableFieldMapping(value = "money_range", comment = "金额范围")
    private String moneyRange;

    @TableFieldMapping(value = "hand_charge", comment = "手续费")
    private Double handCharge;

    @TableFieldMapping(value = "deduct_probability", comment = "扣款概率")
    private Double deductProbability;

    @TableFieldMapping(value = "proportion", comment = "占比")
    private Double proportion;

    @TableFieldMapping(value = "create_time", comment = "创建时间")
    private Date createTime;

    @TableFieldMapping(value = "create_user", comment = "创建用户")
    private String createUser;

    @TableFieldMapping(value = "update_time", comment = "更新时间")
    private Date updateTime;

    @TableFieldMapping(value = "update_user", comment = "更新用户")
    private String updateUser;
}
