package com.gwmfc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;

import com.gwmfc.dto.MenuDto;
import com.gwmfc.dto.UserDto;
import com.gwmfc.entity.MenuDO;
import com.gwmfc.service.MenuService;

import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.gwmfc.util.StatusCodeEnum.SUCCESS;


/**
 * <AUTHOR>
 * @Classname MenuController
 * @Date 2021/8/5 11:38
 */
@Api(value = "系统菜单接口",tags = "系统菜单")
@RestController
@RequestMapping("/menu")
public class MenuController {

    @Autowired
    private MenuService menuService;


    @ApiOperation(value="获取用户对应的菜单")
    @PostMapping("/listByUserId")
    public Result getMenus(@RequestBody UserDto userDto){
        Result result = new Result();
        List<MenuDO> data = menuService.listMenuTree(userDto.getUserId());
        result.setData(data);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    @ApiOperation(value="获取角色树形菜单")
    @GetMapping("/tree/{roleId}")
    @ResponseBody
    public Result tree(@PathVariable("roleId") Long roleId) {
        Result result = new Result();
        List<Long> tree = menuService.getTree(roleId);
        result.setData(tree);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    @ApiOperation(value="菜单列表")
    @PostMapping("/list")
    public Result list(@RequestBody PageForm<MenuDto> menuDtoPageForm){
        Result result = new Result();
        MenuDO menuDO = new MenuDO();
        if(menuDtoPageForm.getParam() !=null){
            BeanUtils.copyProperties(menuDtoPageForm.getParam(),menuDO);
        }
        IPage<MenuDO> data = menuService.page(menuDO, menuDtoPageForm.getCurrent(), menuDtoPageForm.getSize());
        result.setData(data.getRecords());
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    @ApiOperation(value="保存")
    @PostMapping("/save")
    public Result save(@RequestBody MenuDto menuDto){
        Result result = new Result();
        menuService.save(menuDto);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }


    @ApiOperation(value="根据id查询接口")
    @GetMapping("/{id}")
    public Result getOne(@PathVariable("id") Long id){
        Result result = new Result();
        MenuDO menuDO =menuService.getById(id);
        result.setData(menuDO);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }


    @ApiOperation(value="更新接口")
    @PostMapping("/update")
    public Result update(@RequestBody MenuDto menuDto){
        Result result = new Result();
        menuService.update(menuDto);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    @ApiOperation("删除接口")
    @GetMapping("/remove/{id}")
    public Result remove(@PathVariable("id") Long id){
        Result result = new Result();
        menuService.remove(id);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }



}

