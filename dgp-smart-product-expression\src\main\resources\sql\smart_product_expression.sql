-- smart_product_expression.used_car_monthly_trading definition

CREATE TABLE `used_car_monthly_trading` (
                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `update_month` varchar(20) DEFAULT NULL COMMENT '更新月份',
                                            `data_date` varchar(20) DEFAULT NULL COMMENT '数据日期',
                                            `type` int DEFAULT '1' COMMENT '类型（1：交易量，2.区域交易量，3.年限交易量，4.车型区域交易量，5.跨区域交易量）',
                                            `region` varchar(50) DEFAULT NULL COMMENT '区域',
                                            `useful_life` varchar(50) DEFAULT NULL COMMENT '使用年限',
                                            `car_type` varchar(50) DEFAULT NULL COMMENT '车型类型',
                                            `vehicle_type` varchar(50) DEFAULT NULL COMMENT '车型',
                                            `trading_volume` varchar(50) DEFAULT NULL COMMENT '交易量',
                                            `trading_proportion` varchar(50) DEFAULT NULL COMMENT '交易量占比',
                                            `transfer_rate` varchar(50) DEFAULT NULL COMMENT '转籍率',
                                            `img_url` varchar(500) DEFAULT NULL COMMENT '图片地址',
                                            `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
                                            `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                            `update_time` datetime DEFAULT NULL COMMENT '修改时间',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='二手车周度交易量';

-- smart_product_expression.used_car_weekly_trading definition

CREATE TABLE `used_car_weekly_trading` (
                                           `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `title_start_date` varchar(20) DEFAULT NULL COMMENT '标题开始日期',
                                           `title_end_date` varchar(20) DEFAULT NULL COMMENT '标题结束日期',
                                           `start_date` varchar(20) DEFAULT NULL COMMENT '开始日期',
                                           `end_date` varchar(20) DEFAULT NULL COMMENT '结束日期',
                                           `type` int DEFAULT '1' COMMENT '类型（1：总交易量，2.区域交易量）',
                                           `region` varchar(100) DEFAULT NULL COMMENT '区域',
                                           `avg_daily_trading` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '日均交易量',
                                           `img_url` varchar(500) DEFAULT NULL COMMENT '图片地址',
                                           `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
                                           `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                           `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='二手车周度交易量';

-- smart_product_expression.data_processing_manage definition

CREATE TABLE `data_processing_manage` (
                                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                          `data_name` varchar(50) DEFAULT NULL COMMENT '数据名称',
                                          `data_id` bigint DEFAULT NULL COMMENT '数据名称id',
                                          `table_name` varchar(100) DEFAULT NULL COMMENT '数据名称对应表名',
                                          `source_column` varchar(100) DEFAULT NULL COMMENT '源字段',
                                          `source_column_name` varchar(100) DEFAULT NULL COMMENT '源字段中文名称',
                                          `mapping_column` varchar(100) DEFAULT NULL COMMENT '映射后字段',
                                          `mapping_column_name` varchar(100) DEFAULT NULL COMMENT '映射后字段中文名称',
                                          `mapping_label` varchar(100) DEFAULT NULL COMMENT '映射后标签',
                                          `keyword` text DEFAULT NULL COMMENT '关键字',
                                          `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
                                          `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `update_user` varchar(50) DEFAULT NULL COMMENT '修改人',
                                          `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='数据加工管理';

