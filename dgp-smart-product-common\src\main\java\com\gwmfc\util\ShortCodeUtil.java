package com.gwmfc.util;

import java.security.MessageDigest;

/**
 * <AUTHOR>
 * @Classname ShortCodeUtil
 * @Description TODO
 * @Date 2021/6/3 16:52
 */
public class ShortCodeUtil {

    /**
     *
     *
     *
     * @param text 明文
     * @param salt 盐
     * @param length 生成码值长度
     * @return
     */
    public static String getShortCode(String text,String salt,int length){
        String[] aResult = shortText(text, salt,length);
        return aResult[0];
    }


    private static String[] shortText(String text,String salt,int length) {
        String[] chars = new String[] { "a", "b", "c", "d", "e", "f", "g", "h",
                "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t",
                "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5",
                "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H",
                "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T",
                "U", "V", "W", "X", "Y", "Z"
        };
        String hex = md5ByHex(salt + text);
        String[] resUrl = new String[4];
        for (int i = 0; i < 4; i++) {
            String sTempSubString = hex.substring(i * 8, i * 8 + 8);
            long lHexLong = 0x3FFFFFFF & Long.parseLong(sTempSubString, 16);
            String outChars = "";
            for (int j = 0; j < length; j++) {
                long index = 0x0000003D & lHexLong;
                outChars += chars[(int) index];
                lHexLong = lHexLong >> 5;
            }
            resUrl[i] = outChars;
        }
        return resUrl;
    }
    /**
     * MD5加密(32位大写)
     * @param src
     * @return
     */
    private static String md5ByHex(String src) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] b = src.getBytes();
            md.reset();
            md.update(b);
            byte[] hash = md.digest();
            String hs = "";
            String stmp = "";
            for (int i = 0; i < hash.length; i++) {
                stmp = Integer.toHexString(hash[i] & 0xFF);
                if (stmp.length() == 1) {
                    hs = hs + "0" + stmp;
                }
                else {
                    hs = hs + stmp;
                }
            }
            return hs.toUpperCase();
        } catch (Exception e) {
            return "";
        }
    }

    public static  void main(String []args){
        System.out.println(getShortCode("12356","sa",8));
    }


}
