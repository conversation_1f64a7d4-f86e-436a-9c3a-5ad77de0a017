package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalEarlySquareResultInfoDto
 * @Description TODO
 * @Date 2024/1/10 9:14
 */
@Data
@ApiModel(value = "提前结清计算结果信息")
public class ProductCalEarlySquareResultInfoDto{
    private Integer time;

    @ApiModelProperty("IRR")
    private Double resultIrr;

    @ApiModelProperty("XIRR")
    private Double resultXirr;

    @ApiModelProperty("净利润额")
    private Double resultNetProfitMoney;

    @ApiModelProperty("税前收益")
    private Double resultBeforeTaxIncome;

    @ApiModelProperty("ROA(%)")
    private Double resultRoa;

    @ApiModelProperty("万元收益")
    private Double resultPerTenThousandIncome;

    @ApiModelProperty("边际利润")
    private Double resultMarginalProfit;

    private List<ProductCalCashStreamDto> productCalCashStreamDtoList;
}
