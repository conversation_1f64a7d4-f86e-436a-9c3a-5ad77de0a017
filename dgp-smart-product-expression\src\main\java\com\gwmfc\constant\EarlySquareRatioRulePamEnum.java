package com.gwmfc.constant;

/**
 * <AUTHOR>
 * @Classname EarlySquareRatioRulePamEnum
 * @Description TODO
 * @Date 2024/7/3 13:14
 */
public enum EarlySquareRatioRulePamEnum {
    BUSSINESS_TYPE("\"业务类型\"", "businessTypeStr"),
    VEHICLE_TYPE("\"提前期次\"","month"),
    PRODUCT_CLASSIFICATION("\"产品分类\"","productClassification"),
    ACTUAL_INTEREST_RATE("\"融资类型\"","financingType"),
    TIME_LIMIT("\"贷期\"","timeLimit"),
    EARLY_TIME("\"提前期次\"","month"),
    CUSTOMER_INTEREST_RATE("\"客户利率\"","customerInterestRate"),
    ACUTAL_INTEREST_RATE ("\"实际利率\"","actualInterestRate"),
    DEALER_BASIC_COMMISSION_RATIO("\"基础服务费比例\"","dealerBasicCommissionRatio"),
    DEALER_LADDER_BONUS_RATIO("\"阶梯奖金比例\"","dealerLadderBonusRatio"),
    DEALER_SALE_BONUS_RATIO("\"促销奖金比例\"","dealerSaleBonusRatio"),
    ACTUAL_LOAN_MONEY("\"贷额\"","actualLoanMoney"),
    REMAIN_PRINCIPAL("\"剩余本金\"","remainPrincipal"),
    AGENT_TYPE("\"代理商类型\"","agentType"),
    TRACK("\"赛道\"","track"),
    IS_CORP_TO_CORP("\"是否总对总\"","isCorpToCorpStr");

    private final String type;

    private final String value;

    EarlySquareRatioRulePamEnum(String type, String value) {
        this.type = type;
        this.value = value;
    }

    public String type() {
        return type;
    }

    public String value() {
        return value;
    }

}
