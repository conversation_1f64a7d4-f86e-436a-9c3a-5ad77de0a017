package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname DeptDto
 * @Date 2021/8/6 11:45
 */
@ApiModel(value = "DeptDto")
@Data
public class DeptDto {

    @ApiModelProperty("父级部门信息")
    private DeptDto parentDept;

    @ApiModelProperty("部门Id")
    private Long deptId;

    @ApiModelProperty("部门名称")
    private String name;

    @ApiModelProperty("部门顺序")
    private Integer orderNum;

    @ApiModelProperty("删除标记")
    private Integer delFlag;
}
