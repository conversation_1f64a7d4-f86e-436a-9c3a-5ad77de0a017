package com.gwmfc.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.bo.CpcaautoBo;
import com.gwmfc.dao.CpcaautoCallRecordDao;
import com.gwmfc.entity.CpcaautoCallRecordEntity;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.util.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.gwmfc.constant.constant.*;

/**
 * <AUTHOR>
 * @date 2023年08月15日 14:08
 */
@Service
@Slf4j
@RefreshScope
public class CpcaautoService extends ServiceImpl<CpcaautoCallRecordDao, CpcaautoCallRecordEntity> {
    public static final String HEADER = "http://cpcaauto.com";
    public static final String TABLE_NAME = "cpcaautod";

    @Value("${cpcatable-analysis-url}")
    private String cpcatableAnalysisUrl;
    @Value("${manufacturing-pmi-url}")
    private String manufacturingPmiUrl;
    @Value("${manufacturing-pmi-url-async}")
    private String manufacturingPmiUrlAsync;
    @Value("${newEnergy-data-url-async}")
    private String newEnergyDataUrlAsync;

    public static final String LIST_D = ".list_d";
    public static final String LI = "li";
    public static final String SPAN = "span";
    public static final String A = "a";
    public static final String HREF = "href";
    public static final String SRC = "src";
    public static final String READ_CONTENT = ".read_content";
    public static final String IMG = "img";


    public static final String MONTH = "cumulative_month";
    public static final String CALL_TIME = "call_time";

    @Resource
    private HttpUtil httpUtil;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private GlobalFormBusinessService globalFormBusinessService;

    public void monthlySalesRanking(String url, String year, String month, Integer triggerMethod, String userName) {
        CpcaautoCallRecordEntity cpcaautoCallRecordEntity = new CpcaautoCallRecordEntity();
        cpcaautoCallRecordEntity.setYear(year);
        cpcaautoCallRecordEntity.setMonth(month);
        cpcaautoCallRecordEntity.setSource(TABLE_NAME);
        cpcaautoCallRecordEntity.setCallbackStatus(200);
        QueryWrapper queryWrapper = new QueryWrapper<>(cpcaautoCallRecordEntity);
        if (baseMapper.selectList(queryWrapper) != null && baseMapper.selectList(queryWrapper).size() != 0) {
            throw new SystemRuntimeException("该月数据已经有提取记录！如有需要请先删除批次");
        }
        log.info("乘联会爬虫：{}，{}，{}，{}，{}", url, year, month, triggerMethod, userName);
        ResponseEntity<String> html = restTemplate.getForEntity(url, String.class);
        Document documentListPage = Jsoup.parse(html.getBody());
        Elements links = documentListPage.select(LIST_D).select(LI);
        if (links.size() == 0) {
            throw new SystemRuntimeException("当前url无法获取数据！");
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String callTime = dateTimeFormatter.format(LocalDateTime.now());
        Boolean tag = false;
        for (Element link : links) {
            if (link.select(SPAN).text().contains(year.concat("-").concat(month))) {
                TextNode textNode = (TextNode) link.select(A).get(0).childNodes().get(0);
                log.info("乘联会爬虫获取链接：{}", textNode.getWholeText());
                pageContent(callTime, year, month, textNode.getWholeText(), HEADER.concat("/").concat(link.select(A).get(0).attributes().get(HREF)), triggerMethod, userName);
                tag = true;
            }
        }
        if (!tag) {
            log.info("该月页面不存在！：{}", year.concat("-").concat(month));
            throw new SystemRuntimeException("获取不到乘联会网站数据！");
        }
    }

    public void pageContent(String callTime, String year, String month, String wholeText, String href, Integer triggerMethod, String userName) {
        ResponseEntity<String> html = restTemplate.getForEntity(href, String.class);
        Document document = Jsoup.parse(html.getBody());
        Elements links = document.select(READ_CONTENT).select(IMG);
        links.forEach(link -> {
            CpcaautoCallRecordEntity cpcaautoCallRecordEntity = new CpcaautoCallRecordEntity();
            cpcaautoCallRecordEntity.setPageTitle(String.valueOf(wholeText).replaceAll("([　]|\\s|\\u00A0)+", ""));
            cpcaautoCallRecordEntity.setPictureUrl(HEADER.concat(link.attributes().get(SRC)));
            cpcaautoCallRecordEntity.setCallTime(callTime);
            cpcaautoCallRecordEntity.setTriggerMethod(triggerMethod);
            cpcaautoCallRecordEntity.setPageUrl(href);
            cpcaautoCallRecordEntity.setYear(year);
            cpcaautoCallRecordEntity.setMonth(month);
            cpcaautoCallRecordEntity.setCreateUser(userName);
            baseMapper.insert(cpcaautoCallRecordEntity);
            //调用算法接口
            analysisData(TABLE_NAME, callTime, userName, cpcaautoCallRecordEntity, "get");
        });
    }

    public void analysisData(String tableName, String callTime, String userName, CpcaautoCallRecordEntity cpcaautoCallRecordEntity, String requestMethod) {
        CpcaautoBo cpcaautoBo = null;
        if (requestMethod.equals("get")) {
            cpcaautoBo = httpUtil.analysisPictureGet(cpcatableAnalysisUrl.concat("?url=").concat(cpcaautoCallRecordEntity.getPictureUrl()));
        } else if (requestMethod.equals("post")) {
            cpcaautoBo = httpUtil.analysisPicturePost(manufacturingPmiUrl, tableName , cpcaautoCallRecordEntity);
        } else if (requestMethod.equals("post-async")) {
            cpcaautoBo = httpUtil.analysisPicturePost(manufacturingPmiUrlAsync, tableName , cpcaautoCallRecordEntity);
        } else if (requestMethod.equals("post-energy")){
            cpcaautoBo = httpUtil.analysisPicturePost(newEnergyDataUrlAsync, tableName , cpcaautoCallRecordEntity);
        }
        if (cpcaautoBo != null) {
            if (cpcaautoBo.getCode() == 200 && cpcaautoBo.getData() != null) {
                //入库
                Set<String> columnSet = cpcaautoBo.getData().get(0).keySet();
                cpcaautoBo.getData().forEach(map -> {
                    map.put(CREATE_TIME, callTime);
                    map.put(CREATE_USER, userName);
                    if (map.get(MONTH) != null && Integer.valueOf(map.get(MONTH).toString()) < 10) {
                        map.put(DATA_DATE, map.get(YEAR).toString().concat("-0").concat(map.get(MONTH).toString()));
                    } else if (map.get(MONTH) != null) {
                        map.put(DATA_DATE, map.get(YEAR).toString().concat("-").concat(map.get(MONTH).toString()));
                    }
                });
                if (tableName == null) {
                    tableName = cpcaautoBo.getTableName();
                }
                globalFormBusinessService.batchAddData(tableName, columnSet, cpcaautoBo.getData());
            }
            log.info("requestMethod:{}",requestMethod);
            log.info("cpcaautoBo:{}",cpcaautoBo);
            if (!requestMethod.equals("post-async") || !requestMethod.equals("post-energy")) {
                //查询记录入库
                cpcaautoCallRecordEntity.setCallbackStatus(cpcaautoBo.getCode());
                cpcaautoCallRecordEntity.setRecallNum(cpcaautoBo.getRecallNum());
                cpcaautoCallRecordEntity.setCallbackTime(LocalDateTime.now());
                baseMapper.updateById(cpcaautoCallRecordEntity);
            }
        } else {
            throw new SystemRuntimeException("调用乘联会网站数据解析接口失败！");
        }
    }

    public Integer deleteByCreateTime(String batch) {
        int delNum = globalFormBusinessService.deleteByCreateTime(TABLE_NAME, batch);
        if (delNum > 0) {
            HashMap map = new HashMap<>();
            map.put(CALL_TIME, batch);
            return baseMapper.deleteByMap(map);
        }
        return 0;
    }

    public CpcaautoCallRecordEntity selectByArticleNameAndCallTime(String reqTime, String articleName) {
        CpcaautoCallRecordEntity cpcaautoCallRecordEntity = new CpcaautoCallRecordEntity();
        cpcaautoCallRecordEntity.setCallTime(reqTime);
        cpcaautoCallRecordEntity.setPageTitle(articleName);
        LambdaQueryWrapper lambdaQueryWrapper = new LambdaQueryWrapper<>(cpcaautoCallRecordEntity);
        return baseMapper.selectOne(lambdaQueryWrapper);
    }
}
