package com.gwmfc.service.approve;

import com.gwmfc.constant.ProductProposalApproveEnum;
import com.gwmfc.constant.ProductProposalEnum;
import com.gwmfc.dao.ProductProposalApprovalDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductProposalApprovalStatusDto;
import com.gwmfc.dto.ProductProposalDto;
import com.gwmfc.entity.ProductProposalApprovalStatusEntity;
import com.gwmfc.service.ProductProposalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月07日 15:20
 */
@Slf4j
@Component("risk_product_proposal_approve")
public class RiskFinalApproval implements ProductProposalApprovalStrategy {
    @Resource
    private ProductProposalApprovalDao productProposalApprovalDao;
    @Resource
    private ProductProposalService productProposalService;

    @Override
    public void doApproval(ProductProposalDto productProposalDto, ProductProposalApprovalStatusDto productProposalApprovalStatusDto, User user, List<MultipartFile> addFileList) {
        ProductProposalApprovalStatusEntity productProposalApprovalStatusEntity = new ProductProposalApprovalStatusEntity();
        BeanUtils.copyProperties(productProposalApprovalStatusDto, productProposalApprovalStatusEntity);
        productProposalApprovalStatusEntity.setProductProposalId(productProposalDto.getId());
        productProposalApprovalStatusEntity.setCreateTime(LocalDateTime.now());
        productProposalApprovalStatusEntity.setCreateUser(user.getName());
        productProposalApprovalStatusEntity.setStep(ProductProposalEnum.RISK_PRODUCT_PROPOSAL_APPROVE);//4

        productProposalApprovalStatusEntity.setApproveStatus(productProposalApprovalStatusEntity.getStatus());

        productProposalApprovalDao.insert(productProposalApprovalStatusEntity);

        if (productProposalApprovalStatusDto.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
            productProposalDto.setCurrentStep(ProductProposalEnum.FINANICAL_PRODUCT_PROPOSAL_PASSED);//5
            List<ProductProposalApprovalStatusEntity> historyProductProposalApprovalStatusEntityList2 = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), 3, ProductProposalEnum.FINANICAL_PRODUCT_PROPOSAL_PASSED, -1);
            if (!historyProductProposalApprovalStatusEntityList2.isEmpty()) {
                log.info("riskFinalApproval:", historyProductProposalApprovalStatusEntityList2.size());
                ProductProposalApprovalStatusEntity historyProductProposalApprovalStatusEntity = historyProductProposalApprovalStatusEntityList2.get(0);
                if (historyProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.REJECT)) {
                    historyProductProposalApprovalStatusEntity.setDealTag(true);
                    productProposalApprovalDao.updateById(historyProductProposalApprovalStatusEntity);
                }
            }
        } else if (productProposalApprovalStatusDto.getStatus().equals(ProductProposalApproveEnum.REJECT)) {
            productProposalDto.setCurrentStep(ProductProposalEnum.COST_ACCOUNTING);
            //  刪除風險损失率
            if (!productProposalDto.getProductProposalTemplateGroupDtoList().isEmpty()) {
                productProposalDto.getProductProposalTemplateGroupDtoList().forEach(productProposalTemplateGroupDto -> {
                    if (!productProposalTemplateGroupDto.getProductProposalTemplateList().isEmpty()) {
                        productProposalTemplateGroupDto.getProductProposalTemplateList().forEach(productProposalTemplateDto -> {
                            productProposalTemplateDto.setRiskLossRate(null);
                        });
                    }
                });
            }

            List<ProductProposalApprovalStatusEntity> historyProductProposalApprovalStatusEntityList = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), 0, ProductProposalEnum.COST_ACCOUNTING, 1);
            if (!historyProductProposalApprovalStatusEntityList.isEmpty()) {
                log.info("riskFinalApproval:", historyProductProposalApprovalStatusEntityList.size());
                ProductProposalApprovalStatusEntity historyProductProposalApprovalStatusEntity = historyProductProposalApprovalStatusEntityList.get(0);
                if (historyProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
                    historyProductProposalApprovalStatusEntity.setDealTag(true);
                    historyProductProposalApprovalStatusEntity.setStatus(ProductProposalApproveEnum.REJECT);
                    productProposalApprovalDao.updateById(historyProductProposalApprovalStatusEntity);
                }
            }
        }
        productProposalDto.setResetId(productProposalApprovalStatusEntity.getId());
        productProposalService.updateProductProposal(productProposalDto, user);
    }
}
