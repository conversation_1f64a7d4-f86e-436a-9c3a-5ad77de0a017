package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalEarlySquareBasicCommissionRebateRatioDto
 * @Description
 * @Date 2024/6/25 14:04
 */
@Data
@ApiModel(value = "提前结清基础服务费扣返比例")
public class ProductCalEarlySquareBasicCommissionRebateRatioDto {
    @ApiModelProperty("结清期次")
    private Integer payoutRentalId;

    @ApiModelProperty("服务费扣返比例")
    private Double basicCommissionRebateRatio;
}
