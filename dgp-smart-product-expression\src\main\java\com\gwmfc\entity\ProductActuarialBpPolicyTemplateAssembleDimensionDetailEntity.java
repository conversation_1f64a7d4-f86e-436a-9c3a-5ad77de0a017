package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductActuarialBpCalAssembleDimensionDetail
 * @Description TODO
 * @Date 2025/4/21 14:27
 */
@Data
@TableName("product_actuarial_bp_policy_template_assemble_dimension_detail")
public class ProductActuarialBpPolicyTemplateAssembleDimensionDetailEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "template_id")
    @ApiModelProperty("模板id")
    private Long templateId;

    @TableField(value = "assemble_dimension_num")
    @ApiModelProperty("组合维度行号")
    private Integer assembleDimensionNum;

    @TableField(value = "assemble_dimension_grade")
    @ApiModelProperty("组合维度等级（列号）")
    private Integer assembleDimensionGrade;

    @TableField(value = "assemble_dimension_name")
    @ApiModelProperty("组合维度名称")
    private String assembleDimensionName;

    @TableField(value = "assemble_dimension_content")
    @ApiModelProperty("组合维度内容")
    private String assembleDimensionContent;

    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField(value = "create_user")
    @ApiModelProperty("创建人")
    private String createUser;

    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @TableField(value = "update_user")
    @ApiModelProperty("更新人")
    private String updateUser;
}
