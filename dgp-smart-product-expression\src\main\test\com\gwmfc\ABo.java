package com.gwmfc;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年02月22日 15:19
 */
@Data
public class ABo {
    private Integer returncode;

    private Returndata returndata;

}
@Data
class Returndata {
    private List<Datanode> datanodes;
    private List<wdnode> wdnodes;
}
@Data
class Datanode {
    private String code;
    private DataObj data;
    private List<Wds> wds;
}
@Data
class Wds {
    private String valuecode;
    private String wdcode;
}
@Data
class DataObj {
    private String data;
    private Integer dotcount;
    private Boolean hasdata;
    private String strdata;
}
@Data
class wdnode {
    private List<node> nodes;
}
@Data
class node {
    private String cname;
}