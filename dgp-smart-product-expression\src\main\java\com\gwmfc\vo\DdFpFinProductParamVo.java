package com.gwmfc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023年10月11日 9:49
 */
@Data
@ApiModel(value = "产品列表查询入参")
public class DdFpFinProductParamVo {

    @ApiModelProperty("产品提案名称")
    private String productProposalName;

    @ApiModelProperty("产品名称")
    private String finProductName;

    @ApiModelProperty("产品提案时间(查询起始时间)")
    private String productProposalStartDate;

    @ApiModelProperty("产品提案时间(查询结束时间)")
    private String productProposalEndDate;

    @ApiModelProperty("产品开始日期(查询起始时间)")
    private String proBeginStartDate;

    @ApiModelProperty("产品开始日期(查询结束时间)")
    private String proBeginEndDate;

    @ApiModelProperty("产品结束日期(查询起始时间)")
    private String proEndStartDate;

    @ApiModelProperty("产品结束日期(查询结束时间)")
    private String proEndEndDate;

    @ApiModelProperty("BOSS车辆类型")
    private String bossCarType;

    @ApiModelProperty("ACS车辆类型")
    private String acsCarType;

    @ApiModelProperty("状态描述")
    private String statusDsc;
}
