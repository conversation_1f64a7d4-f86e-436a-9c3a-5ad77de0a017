package com.gwmfc.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * <AUTHOR> l<PERSON><PERSON><PERSON>
 * @Classname PrestoConfig
 * @Description presto连接kafka的数据源配置
 * @Date 2022/02/10
 */
@Slf4j
@Configuration
public class DataSourceConfig {
    @Bean(name = "prestoKafkaDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.presto")
    public DataSource prestoKafkaDataSource() {
        log.info("--------------------kafka presto init ---------------------");
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "prestoKafkaTemplate")
    public JdbcTemplate prestoKafkaJdbcTemplate(@Qualifier("prestoKafkaDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
    @Bean(name = "standardizationDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.standardization")
    public DataSource quotaDataSource() {
        return DataSourceBuilder.create().build();
    }

}
