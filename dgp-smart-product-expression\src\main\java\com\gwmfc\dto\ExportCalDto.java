package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Classname ExportCalDto
 * @Description TODO
 * @Date 2024/2/2 10:55
 */
@Data
@ApiModel(value = "模板导出")
public class ExportCalDto {
    @ApiModelProperty("导出code")
    private String exportCode;
    @ApiModelProperty("导出内容")
    private String exportDataJson;
}
