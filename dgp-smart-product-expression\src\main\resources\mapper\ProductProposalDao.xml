<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.ProductProposalDao">

    <select id="selectEntityPage" resultType="com.gwmfc.entity.ProductProposalEntity">
        select
            *,ppas.create_time as approve_time
        from
            product_proposal pp left join
        (select product_proposal_id,deal_tag,status,create_time from product_proposal_approval_status a where deal_tag = false and approve_status is not null
        <if test="productProposalDto.type != null">
            and a.type = #{productProposalDto.type}
        </if>
        <if test="productProposalDto.currentStep != '' and productProposalDto.currentStep != null">
            and a.step = #{productProposalDto.currentStep}
        </if>
        group by product_proposal_id,deal_tag,status,create_time) ppas on
        pp.id = ppas.product_proposal_id
        where (ppas.deal_tag = false or ppas.deal_tag is null)
        <if test="productProposalDto.status != null and productProposalDto.status == -1">
            and (ppas.product_proposal_id is null or ppas.status = -1)
        </if>
        <if test="productProposalDto.status != null and productProposalDto.status == 1">
            and ppas.status = 1
        </if>
        <if test="productProposalDto.id != '' and productProposalDto.id != null">
            and pp.product_proposal_id = #{productProposalDto.id}
        </if>
        <if test="productProposalDto.businessType != null">
            and pp.business_type = #{productProposalDto.businessType}
        </if>
        <if test="productProposalDto.name != '' and productProposalDto.name != null">
            and pp.name like concat('%',#{productProposalDto.name},'%')
        </if>
        <if test="productProposalDto.status != null and productProposalDto.status == -1 and productProposalDto.currentStep != '' and productProposalDto.currentStep != null">
            and pp.current_step = #{productProposalDto.currentStep}
        </if>
        <if test="productProposalDto.createUser != '' and productProposalDto.createUser != null">
            and pp.create_user like concat('%',#{productProposalDto.createUser},'%')
        </if>
        <if test="productProposalDto.startTime != null and productProposalDto.endTime != null">
            and DATE_FORMAT(pp.create_time, '%Y-%m-%d') &gt;= #{productProposalDto.startTime} and DATE_FORMAT(pp.create_time, '%Y-%m-%d') &lt;= #{productProposalDto.endTime}
        </if>
        order by pp.create_time desc
    </select>
</mapper>