package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductProposalHistoryMiddleDto
 * @Description TODO
 * @Date 2023/11/9 15:22
 */
@Data
@ApiModel(value = "方案提案历史数据查询中间dto")
public class ProductProposalHistoryMiddleDto {
    private Integer contractNum;
    private Double sumVehiclePrice;
    private Integer sumLoanTrm;
    private Double sumTotalRate;
    private Double sumCustomerRate;
    private Double sumLoanAmt;
    private Double sumPayPct;
    private Double sumAddCreAmt;
    private Double sumRemainPrincipalAmt;
    private Double badRemainPrincipalAmt;
    private Double beforeHx18times30dRemainPrincipalAmt;
    private Double beforeHx12times30dRemainPrincipalAmt;
    private Double beforeHx30dRemainPrincipalAmt;
    private Double afterHx18times30dRemainPrincipalAmt;
    private Double afterHx12times30dRemainPrincipalAmt;
    private Double afterHx30dRemainPrincipalAmt;
    private Double sumLoss;
    private Double sumMarginContribution;
    private Double sumSaleProfit;
    private Double sumRebeatRewPct;
}
