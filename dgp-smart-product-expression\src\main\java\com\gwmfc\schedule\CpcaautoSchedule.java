package com.gwmfc.schedule;

import com.gwmfc.service.CpcaautoService;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;

/**
 * <AUTHOR>
 * @date 2023年08月14日 10:14
 */
@Component
@Slf4j
public class CpcaautoSchedule {
    @Resource
    private CpcaautoService cpcaautoService;

    @Scheduled(cron = "0 0 0 * * ?")
    @SchedulerLock(name = "catchMonthlyCpcaauto", lockAtMostFor = "PT1H", lockAtLeastFor = "PT1H")
    public void catchMonthlySalesRanking() {
        int year = Calendar.getInstance().get(Calendar.YEAR);
        int month = Calendar.getInstance().get(Calendar.MONTH) + 1;
        String monthStr;
        if (month < 10) {
            monthStr = "0".concat(String.valueOf(month));
        } else {
            monthStr = String.valueOf(month);
        }
        String url = "http://cpcaauto.com/news.php?types=csjd&anid=129&nid=27";
        cpcaautoService.monthlySalesRanking(url,String.valueOf(year), monthStr, 1, "Scheduled");
        log.info("catchMonthlySalesRanking:{},{}",year,month);
    }

}