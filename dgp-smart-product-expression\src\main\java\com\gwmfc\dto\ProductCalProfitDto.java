package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname ProductCalProfitDto
 * @Description 利润测算dto
 * @Date 2023/12/13 9:01
 */
@Data
@ApiModel(value = "利润测算")
public class ProductCalProfitDto {
    @ApiModelProperty("irr测算dto")
    private ProductCalculateDto productCalculateDto;
    @ApiModelProperty("利润详情")
    private List<ProductCalProfitDetailDto> productCalProfitDetailDtoList;
    @ApiModelProperty("利润汇总")
    private List<ProductCalProfitCollectDto> productCalProfitCollectDtoList;
}
