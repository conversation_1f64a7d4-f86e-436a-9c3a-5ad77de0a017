package com.gwmfc.controller;


import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.bo.RecordBatchDeleteBo;
import com.gwmfc.domain.User;
import com.gwmfc.dto.GlobalFormBusinessDto;
import com.gwmfc.dto.ProductDataMartAndFieldDto;
import com.gwmfc.dto.TableFieldDetailDto;
import com.gwmfc.entity.ProductDataMartEntity;
import com.gwmfc.service.GlobalFormBusinessService;
import com.gwmfc.service.ProductDataMartService;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import com.gwmfc.util.TableUtils;
import com.gwmfc.vo.GlobalFormBusinessVo;
import com.gwmfc.vo.TableFieldDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;

import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @date 2023年08月02日 16:02
 */
@Api(tags = "通用业务表管理")
@Slf4j
@RestController
@RequestMapping("/business")
public class GlobalFormBusinessController {
    /**
     * 通用业务处理类
     */
    @Resource
    private GlobalFormBusinessService globalFormBusinessService;

    @Resource
    private ProductDataMartService productDataMartService;

    /**
     * 新增
     *
     * @param saveDto
     * @return
     */
    @ApiOperation("通用业务表新增")
    @PostMapping("/add")
    public Result add(@RequestBody @Valid GlobalFormBusinessDto saveDto, @ApiIgnore @CurrentUser User user) {
        saveDto.setTableName(productDataMartService.getTableNameByFormId(saveDto.getFormId()));
        globalFormBusinessService.add(saveDto, user);
        return Result.ok();
    }

    /**
     * 修改
     *
     * @return
     */
    @ApiOperation("通用业务表修改")
    @PostMapping("/update")
    public Result update(@RequestBody @Valid GlobalFormBusinessDto saveDto, @ApiIgnore @CurrentUser User user) {
        saveDto.setTableName(productDataMartService.getTableNameByFormId(saveDto.getFormId()));
        globalFormBusinessService.update(saveDto, user);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @return
     */
    @ApiOperation("单条删除")
    @GetMapping("/delete")
    public Result delete(@RequestParam Integer formId, @RequestParam Long recordId) {
        globalFormBusinessService.delete(productDataMartService.getTableNameByFormId(formId), recordId);
        return Result.ok();
    }

    /**
     * 批次删除
     *
     * @return
     */
    @ApiOperation("批次删除")
    @GetMapping("/deleteByCreateTime")
    public Result deleteByCreateTime(@RequestParam Integer formId, @RequestParam String batch) {
        if (globalFormBusinessService.deleteByCreateTime(productDataMartService.getTableNameByFormId(formId), batch) > 0) {
            return Result.ok();
        }
        return Result.error("删除0条数据！");
    }

    /**
     * 统计批次多少条
     *
     * @return
     */
    @ApiOperation("统计批次多少条")
    @GetMapping("/sumBatchNum")
    public Result sumBatchNum(@RequestParam Integer formId, @RequestParam String batch) {
        Result result = new Result();
        result.setData(globalFormBusinessService.sumBatchNum(productDataMartService.getTableNameByFormId(formId), batch));
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    /**
     * 删除
     *
     * @return
     */
    @ApiOperation("批量删除")
    @PostMapping("/deleteBatch")
    public Result deleteBatch(@RequestBody RecordBatchDeleteBo recordBatchDeleteBo) {
        globalFormBusinessService.deleteBatch(productDataMartService.getTableNameByFormId(recordBatchDeleteBo.getFormId()), recordBatchDeleteBo.getRecordIdList());
        return Result.ok();
    }

    /**
     * 列表查询
     *
     * @param pageForm
     * @return
     */
    @ApiOperation("通用业务表列表查询")
    @PostMapping("/list")
    public Result<GlobalFormBusinessVo> selectByPage(@RequestBody @Valid PageForm<GlobalFormBusinessDto> pageForm) throws IOException {
        ProductDataMartEntity productDataMartEntity;
        if (StringUtils.isNotEmpty(pageForm.getParam().getTableName())) {
            productDataMartEntity = productDataMartService.getProductDataMartEntityByTableName(pageForm.getParam().getTableName());
        } else {
            productDataMartEntity = productDataMartService.getProductDataMartEntityByFormId(pageForm.getParam().getFormId());
            pageForm.getParam().setTableName(productDataMartEntity.getTableName());
        }

        pageForm.getParam().setUpdateFrequency(productDataMartEntity.getUpdateFrequency());
        IPage<Map<String, Object>> mapPage = globalFormBusinessService.listByPage(pageForm);
        Map<String, TableFieldDetailVo> tableFieldMappings = new TableUtils().findTableBusFieldDetailMappings(pageForm.getParam().getTableName());

        Map<String, TableFieldDetailVo> finalTableFieldMappingList = new LinkedHashMap();
        TableFieldDetailVo startTableFieldDetailVo = new TableFieldDetailVo();
        BeanUtils.copyProperties(tableFieldMappings.get("data_date") != null ? tableFieldMappings.get("data_date") : null,startTableFieldDetailVo);
        startTableFieldDetailVo.setComment(tableFieldMappings.get("data_date").getComment().concat("(起)"));
        finalTableFieldMappingList.put("start_data_date", startTableFieldDetailVo);
        TableFieldDetailVo endTableFieldDetailVo = new TableFieldDetailVo();
        BeanUtils.copyProperties(tableFieldMappings.get("data_date") != null ? tableFieldMappings.get("data_date") : null,endTableFieldDetailVo);
        endTableFieldDetailVo.setComment(tableFieldMappings.get("data_date").getComment().concat("(止)"));
        finalTableFieldMappingList.put("end_data_date", endTableFieldDetailVo);
        finalTableFieldMappingList.putAll(tableFieldMappings);

        Result<GlobalFormBusinessVo> result = new Result<>();
        GlobalFormBusinessVo globalFormBusinessVo = new GlobalFormBusinessVo();
        globalFormBusinessVo.setTableHeaders(finalTableFieldMappingList);
        globalFormBusinessVo.setTableData(mapPage.getRecords());
        globalFormBusinessVo.setFormId(productDataMartEntity.getId());

        result.setData(globalFormBusinessVo);
        result.setTotal(mapPage.getTotal());
        result.setCode(200);
        return result;
    }

    /**
     * 数据加工数据名称及源字段列表
     *
     * @return
     */
    @ApiOperation("数据加工数据名称及源字段列表")
    @PostMapping("/selectProductDataMartAndField")
    public Result<List<ProductDataMartAndFieldDto>> selectProductDataMartAndField(){
        List<ProductDataMartEntity> list = productDataMartService.getProductDataMartList();
        PageForm<GlobalFormBusinessDto> pageForm = new PageForm<>();
        pageForm.setCurrent(1);
        pageForm.setSize(999);
        pageForm.setParam(new GlobalFormBusinessDto());
        List<ProductDataMartAndFieldDto> productDataMartAndFieldList = new ArrayList<>();
        list.forEach(productDataMartEntity -> {
            ProductDataMartAndFieldDto productDataMartAndFieldDto = new ProductDataMartAndFieldDto();
            BeanUtils.copyProperties(productDataMartEntity,productDataMartAndFieldDto);
            pageForm.getParam().setTableName(productDataMartEntity.getTableName());
            pageForm.getParam().setUpdateFrequency(productDataMartEntity.getUpdateFrequency());
            try {
                List<TableFieldDetailDto> fieldList = new TableUtils().getFieldDetail(pageForm.getParam().getTableName());
                productDataMartAndFieldDto.setList(fieldList);
                productDataMartAndFieldList.add(productDataMartAndFieldDto);
            } catch (IOException e) {
                e.printStackTrace();
                log.error("[selectProductDataMartAndField error] [table name:{}]", productDataMartEntity.getTableName());
            }
        });
        Result<List<ProductDataMartAndFieldDto>> result = new Result<>();
        result.setData(productDataMartAndFieldList);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }
}
