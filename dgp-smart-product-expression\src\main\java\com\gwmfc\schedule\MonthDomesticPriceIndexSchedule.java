package com.gwmfc.schedule;

import com.gwmfc.service.DomesticPriceIndexService;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024年03月22日 11:46
 */
@Component
@Slf4j
public class MonthDomesticPriceIndexSchedule {
    @Resource
    private DomesticPriceIndexService domesticPriceIndexService;

    @Scheduled(cron = "0 0 0 * * ?")
    @SchedulerLock(name = "catchDomesticPriceIndexMonth", lockAtMostFor = "PT1H", lockAtLeastFor = "PT1H")
    public void catchDomesticPriceIndex() {
        domesticPriceIndexService.catchDomesticPriceIndex(1,"month_consumption_index", "Scheduled");
        log.info("catchMonthDomesticPriceIndex:{},{}","month_consumption_index","Scheduled");
    }
}
