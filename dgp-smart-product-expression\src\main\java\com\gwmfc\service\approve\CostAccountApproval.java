package com.gwmfc.service.approve;

import com.gwmfc.constant.ProductProposalApproveEnum;
import com.gwmfc.constant.ProductProposalEnum;
import com.gwmfc.dao.ProductProposalApprovalDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductProposalApprovalStatusDto;
import com.gwmfc.dto.ProductProposalDto;
import com.gwmfc.dto.ProductProposalTemplateDto;
import com.gwmfc.dto.ProductProposalTemplateGroupDto;
import com.gwmfc.entity.ProductProposalApprovalStatusEntity;
import com.gwmfc.service.ProductProposalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static com.gwmfc.constant.ProductProposalEnum.RISK_BUSINESS_CHECK;

/**
 * <AUTHOR>
 * @date 2024年04月07日 15:07
 */
@Slf4j
@Component("cost_accounting")
public class CostAccountApproval implements ProductProposalApprovalStrategy {
    @Resource
    private ProductProposalApprovalDao productProposalApprovalDao;
    @Resource
    private ProductProposalService productProposalService;

    @Override
    public void doApproval(ProductProposalDto productProposalDto, ProductProposalApprovalStatusDto productProposalApprovalStatusDto, User user, List<MultipartFile> addFileList) {
        ProductProposalApprovalStatusEntity productProposalApprovalStatusEntity = new ProductProposalApprovalStatusEntity();
        BeanUtils.copyProperties(productProposalApprovalStatusDto, productProposalApprovalStatusEntity);
        productProposalApprovalStatusEntity.setProductProposalId(productProposalDto.getId());
        productProposalApprovalStatusEntity.setCreateTime(LocalDateTime.now());
        productProposalApprovalStatusEntity.setCreateUser(user.getName());
        productProposalApprovalStatusEntity.setStep(ProductProposalEnum.COST_ACCOUNTING);//3

        productProposalApprovalStatusEntity.setApproveStatus(productProposalApprovalStatusEntity.getStatus());

        productProposalApprovalDao.insert(productProposalApprovalStatusEntity);

        if (productProposalApprovalStatusDto.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
            if (productProposalDto.getRiskMeasureOrNot()) {
                productProposalDto.setCurrentStep(ProductProposalEnum.RISK_PRODUCT_PROPOSAL_APPROVE);//4
            } else {
                productProposalDto.setCurrentStep(ProductProposalEnum.FINANICAL_PRODUCT_PROPOSAL_PASSED);//5
            }
            List<ProductProposalApprovalStatusEntity> historyProductProposalApprovalStatusEntityList1 = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), 1, ProductProposalEnum.RISK_PRODUCT_PROPOSAL_APPROVE, -1);
            if (!historyProductProposalApprovalStatusEntityList1.isEmpty()) {
                log.info("riskFinalApproval:", historyProductProposalApprovalStatusEntityList1.size());
                ProductProposalApprovalStatusEntity historyProductProposalApprovalStatusEntity = historyProductProposalApprovalStatusEntityList1.get(0);
                if (historyProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.REJECT)) {
                    historyProductProposalApprovalStatusEntity.setDealTag(true);
                    productProposalApprovalDao.updateById(historyProductProposalApprovalStatusEntity);
                }
            }
            List<ProductProposalApprovalStatusEntity> historyProductProposalApprovalStatusEntityList2 = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), 3, ProductProposalEnum.FINANICAL_PRODUCT_PROPOSAL_PASSED, -1);
            if (!historyProductProposalApprovalStatusEntityList2.isEmpty()) {
                log.info("riskFinalApproval:", historyProductProposalApprovalStatusEntityList2.size());
                ProductProposalApprovalStatusEntity historyProductProposalApprovalStatusEntity = historyProductProposalApprovalStatusEntityList2.get(0);
                if (historyProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.REJECT)) {
                    historyProductProposalApprovalStatusEntity.setDealTag(true);
                    productProposalApprovalDao.updateById(historyProductProposalApprovalStatusEntity);
                }
            }
        } else if (productProposalApprovalStatusDto.getStatus().equals(ProductProposalApproveEnum.REJECT)) {
            //把审批这条的通过记录标记位改为已处理
            List<ProductProposalApprovalStatusEntity> historyProductProposalApprovalStatusEntityList = productProposalApprovalDao.queryApprovalStatus(productProposalDto.getId(), productProposalApprovalStatusDto.getType(), RISK_BUSINESS_CHECK, 1);
            log.info("productProposalApproval BUSINESS_VERIFY:{}", historyProductProposalApprovalStatusEntityList);
            if (!historyProductProposalApprovalStatusEntityList.isEmpty()) {
                historyProductProposalApprovalStatusEntityList.forEach(historyProductProposalApprovalStatusEntity -> {
                    if (historyProductProposalApprovalStatusEntity.getStatus().equals(ProductProposalApproveEnum.APPROVE)) {
                        historyProductProposalApprovalStatusEntity.setDealTag(true);
                        historyProductProposalApprovalStatusEntity.setStatus(ProductProposalApproveEnum.REJECT);
                        productProposalApprovalDao.updateById(historyProductProposalApprovalStatusEntity);
                    }
                });
            }
            productProposalDto.setCurrentStep(RISK_BUSINESS_CHECK);//2
        }
        productProposalDto.setResetId(productProposalApprovalStatusEntity.getId());

        /**
         * 对提前结清和联合贷的结果，进行特殊处理，为了后面提案word文档的生成，需要将对应的综合irr，resultirr，roa，净利润等赋值到原有的irr，roa，netMargin中
         */
        for (ProductProposalTemplateGroupDto productProposalTemplateGroupDto : productProposalDto.getProductProposalTemplateGroupDtoList()) {
            for (ProductProposalTemplateDto productProposalTemplateDto : productProposalTemplateGroupDto.getProductProposalTemplateList()) {
                if(productProposalTemplateDto.getConsiderEarlySquare() == 1){
                    if(productProposalTemplateDto.getIsUnionLoan() == 1){
                        productProposalTemplateDto.setIrr(productProposalTemplateDto.getResultIntegrativeIrr());
                        productProposalTemplateDto.setRoa(productProposalTemplateDto.getResultIntegrativeRoa());
                        productProposalTemplateDto.setNetMargin(productProposalTemplateDto.getResultIntegrativeNetProfitMoney());
                    }else{
                        productProposalTemplateDto.setIrr(productProposalTemplateDto.getResultIrr());
                        productProposalTemplateDto.setRoa(productProposalTemplateDto.getResultRoa());
                        productProposalTemplateDto.setNetMargin(productProposalTemplateDto.getResultNetProfitMoney());
                    }
                }
            }
        }

        productProposalService.updateProductProposal(productProposalDto, user);
    }
}
