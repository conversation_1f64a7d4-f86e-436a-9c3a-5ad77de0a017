package com.gwmfc.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.entity.DataProcessingManageEntity;
import com.gwmfc.entity.DataProcessingManageMapEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月16日 10:40
 */
@Data
@ApiModel(value = "数据加工管理")
public class DataProcessingManageDto {
    @TableId(type = IdType.AUTO)
    private Long id;
    @ApiModelProperty("数据名称")
    private String dataName;
    @ApiModelProperty("数据名称id")
    private Long dataId;
    @ApiModelProperty("数据名称对应表名")
    private String tableName;
    @ApiModelProperty("源字段")
    private String sourceColumn;
    @ApiModelProperty("源字段中文名称")
    private String sourceColumnName;
    @ApiModelProperty("映射后字段")
    private String mappingColumn;
    @ApiModelProperty("映射后字段中文名称")
    private String mappingColumnName;
    @ApiModelProperty("映射后字段")
    private List<DataProcessingManageMapEntity> dataProcessingManageMapEntityList;
    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @ApiModelProperty("创建人")
    private String createUser;
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    @ApiModelProperty("修改人")
    private String updateUser;
}
