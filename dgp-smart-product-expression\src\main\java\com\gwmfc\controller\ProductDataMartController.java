package com.gwmfc.controller;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.GlobalFormBusinessDto;
import com.gwmfc.dto.ProductDataMartDto;
import com.gwmfc.entity.ProductDataMartEntity;
import com.gwmfc.service.ProductDataMartService;
import com.gwmfc.util.BeanListCopyUtil;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;

import java.util.List;

import static com.gwmfc.constant.constant.NOT_VALID;
import static com.gwmfc.constant.constant.VALID;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @date 2023年08月03日 10:01
 */
@Api(tags = "数据集市管理")
@RestController
@RequestMapping("/product/datamart")
public class ProductDataMartController {
    @Resource
    private ProductDataMartService productDataMartService;

    /**
     * 列表查询
     *
     * @param pageForm
     * @return
     */
    @ApiOperation("数据集市列表查询")
    @PostMapping("/list")
    public Result list(@RequestBody PageForm<ProductDataMartDto> pageForm) {
        IPage<ProductDataMartEntity> productDataMartEntityIPage = productDataMartService.queryPage(pageForm);
        List<ProductDataMartDto> productDataMartDtoList = BeanListCopyUtil.copyListProperties(productDataMartEntityIPage.getRecords(), ProductDataMartDto::new);
        productDataMartDtoList.forEach(productDataMartDto -> {
            GlobalFormBusinessDto globalFormBusinessDto = new GlobalFormBusinessDto();
            // todo 以后改为校验表是否有效
            if (StringUtils.isNotEmpty(productDataMartDto.getTableName())) {
                globalFormBusinessDto.setTableName(productDataMartDto.getTableName());
            }
        });
        return Result.ok(productDataMartDtoList, productDataMartEntityIPage.getTotal());
    }

    /**
     * 统计批次多少条
     *
     * @return
     */
    @ApiOperation("获取formid")
    @GetMapping("/getFormIdByName")
    public Result getFormIdByName(@RequestParam String tableName) {
        Result result = new Result();
        result.setData(productDataMartService.getFormIdByName(tableName));
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    /**
     * 新增
     *
     * @param productDataMartEntity
     * @return
     */
    @ApiOperation("数据集市新增")
    @PostMapping("/add")
    public Result add(@RequestBody ProductDataMartEntity productDataMartEntity, @ApiIgnore @CurrentUser User user) {
        Result result = productDataMartService.addForm(productDataMartEntity, user);
        return result;
    }

    /**
     * 更新
     *
     * @param productDataMartEntity
     * @return
     */
    @ApiOperation("数据集市更新")
    @PostMapping("/update")
    public Result update(@RequestBody ProductDataMartEntity productDataMartEntity, @ApiIgnore @CurrentUser User user) {
        boolean result = productDataMartService.updateForm(productDataMartEntity, user);
        Assert.state(result, "更新失败");
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation("数据集市删除")
    @GetMapping("/delete/{id}")
    public Result delete(@PathVariable("id") int id) {
        ProductDataMartEntity productDataMartEntity = productDataMartService.getById(id);
        Assert.notNull(productDataMartEntity, "该记录不存在");
        productDataMartEntity.setValid(NOT_VALID);
        boolean result = productDataMartService.updateById(productDataMartEntity);
        Assert.state(result, "删除失败");
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @ApiOperation("数据集市恢复")
    @GetMapping("/recover/{id}")
    public Result recover(@PathVariable("id") int id) {
        ProductDataMartEntity productDataMartEntity = productDataMartService.getById(id);
        Assert.notNull(productDataMartEntity, "该记录不存在");
        productDataMartEntity.setValid(VALID);
        boolean result = productDataMartService.updateById(productDataMartEntity);
        Assert.state(result, "删除失败");
        return Result.ok();
    }



}
