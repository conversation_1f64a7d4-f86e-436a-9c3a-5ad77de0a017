package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalculateGroupDto
 * @Description 提案分组IRR计算
 * @Date 2024/4/2 10:15
 */
@Data
@ApiModel(value = "组间加权IRR计算")
public class ProductCalculateGroupDto {
    @ApiModelProperty("产品组名称")
    private String groupName;
    @ApiModelProperty("组间加权比例")
    private Double groupProportion;
    @ApiModelProperty("一组IRR计算提案")
    private List<ProductCalculateRangeDto> productCalculateRangeDtoList;
}
