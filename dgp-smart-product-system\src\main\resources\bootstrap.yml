spring:
  application:
    name: smart-product-system
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER:10.50.133.144:8848}
        group: DEFAULT_GROUP
        username: nacos
        password: <EMAIL>
      config:
        server-addr: ${NACOS_SERVER:10.50.133.144:8848}
        username: nacos
        password: <EMAIL>
        group: DEFAULT_GROUP
        file-extension: yaml
