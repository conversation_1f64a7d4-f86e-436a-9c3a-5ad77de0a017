<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.DdFpFinProductDao">

    <select id="listPage" resultType="com.gwmfc.vo.DdFpFinProductVo">
        SELECT dffp.*,
        pp.id product_proposal_id,
        pp.name product_proposal_name,
        pp.create_time product_proposal_create_time
        FROM dd_fp_fin_product dffp
        LEFT JOIN dd_fp_fin_product_product_proposal dffppp
            ON dffp.fin_product_id = dffppp.fin_product_id
        LEFT JOIN product_proposal pp
            ON pp.id = dffppp.product_proposal_id
        <where>
            <if test="param.finProductName != null and param.finProductName != ''">
                AND dffp.fin_product_name LIKE CONCAT('%',#{param.finProductName},'%')
            </if>
            <if test="param.bossCarType != null and param.bossCarType != ''">
                AND dffp.boss_car_type LIKE CONCAT('%',#{param.bossCarType},'%')
            </if>
            <if test="param.acsCarType != null and param.acsCarType != ''">
                AND dffp.acs_car_type LIKE CONCAT('%',#{param.acsCarType},'%')
            </if>
            <if test="param.productProposalName != null and param.productProposalName != ''">
                AND pp.name LIKE CONCAT('%',#{param.productProposalName},'%')
            </if>
            <if test="param.productProposalStartDate != null and param.productProposalStartDate != ''">
                AND pp.create_time &gt;= #{param.productProposalStartDate}
            </if>
            <if test="param.productProposalEndDate != null and param.productProposalEndDate != ''">
                AND pp.create_time &lt;= #{param.productProposalEndDate}
            </if>
            <if test="param.proBeginStartDate != null and param.proBeginStartDate != ''">
                AND dffp.pro_begin_date &gt;= #{param.proBeginStartDate}
            </if>
            <if test="param.proBeginEndDate != null and param.proBeginEndDate != ''">
                AND dffp.pro_begin_date &lt;= #{param.proBeginEndDate}
            </if>
            <if test="param.proEndStartDate != null and param.proEndStartDate != ''">
                AND dffp.pro_end_date &gt;= #{param.proEndStartDate}
            </if>
            <if test="param.proEndEndDate != null and param.proEndEndDate != ''">
                AND dffp.pro_end_date &lt;= #{param.proEndEndDate}
            </if>
            <if test="param.statusDsc != null and param.statusDsc != ''">
                <choose>
                    <when test='param.statusDsc == "无"'>
                        AND dffp.status_dsc = ''
                    </when>
                    <otherwise>
                        AND dffp.status_dsc = #{param.statusDsc}
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY dffp.pro_begin_date DESC,fin_product_id DESC
    </select>
</mapper>
