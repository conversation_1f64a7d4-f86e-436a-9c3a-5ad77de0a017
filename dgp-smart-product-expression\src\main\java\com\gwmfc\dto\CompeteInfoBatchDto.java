package com.gwmfc.dto;

import com.gwmfc.bo.UploadFileBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname CompeteInfoBatchDto
 * @Description TODO
 * @Date 2024/12/16 16:05
 */
@Data
@ApiModel(value = "竞对信息批次dto")
public class CompeteInfoBatchDto {
    private Long id;

    @ApiModelProperty("数据类型")
    private String dataType;

    @ApiModelProperty("附件信息")
    private List<UploadFileBo> attachmentList;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建用户")
    private String createUser;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("更新用户")
    private String updateUser;
}
