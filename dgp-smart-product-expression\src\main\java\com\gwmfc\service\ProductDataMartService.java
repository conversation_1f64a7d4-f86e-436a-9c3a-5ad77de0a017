package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.dao.ProductDataMartDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductDataMartDto;
import com.gwmfc.entity.ProductDataMartEntity;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品数据集市
 */
@Service
public class ProductDataMartService extends ServiceImpl<ProductDataMartDao, ProductDataMartEntity> {

    /**
     * 获取表名
     * @param formId
     * @return
     */
    public String getTableNameByFormId(Integer formId) {
        Assert.notNull(formId, "表单id不能为空");
        ProductDataMartEntity productDataMartEntity = this.getById(formId);
        Assert.notNull(productDataMartEntity, "该表不存在");
        Assert.hasLength(productDataMartEntity.getTableName(), "该表单没有关联数据表");
        return productDataMartEntity.getTableName();
    }

    /**
     * 获取表名
     * @param tableName
     * @return
     */
    public ProductDataMartEntity getProductDataMartEntityByTableName(String tableName) {
        Assert.notNull(tableName, "表名不能为空");
        ProductDataMartEntity productDataMartEntityQuery = new ProductDataMartEntity();
        productDataMartEntityQuery.setTableName(tableName);
        QueryWrapper queryWrapper = new QueryWrapper<>(productDataMartEntityQuery);
        ProductDataMartEntity productDataMartEntity = this.getOne(queryWrapper);
        Assert.notNull(productDataMartEntity, "该表不存在");
        Assert.hasLength(productDataMartEntity.getTableName(), "该表单没有关联数据表");
        return productDataMartEntity;
    }

    /**
     * 获取表名
     * @param formId
     * @return
     */
    public ProductDataMartEntity getProductDataMartEntityByFormId(Integer formId) {
        Assert.notNull(formId, "表单id不能为空");
        ProductDataMartEntity productDataMartEntity = this.getById(formId);
        Assert.notNull(productDataMartEntity, "该表不存在");
        Assert.hasLength(productDataMartEntity.getTableName(), "该表单没有关联数据表");
        return productDataMartEntity;
    }

    /**
     * 分页查询
     * @param productDataMartDtoPageForm
     * @return
     */
    public IPage<ProductDataMartEntity> queryPage(PageForm<ProductDataMartDto> productDataMartDtoPageForm) {
        ProductDataMartDto param = productDataMartDtoPageForm.getParam();
        ProductDataMartEntity productDataMartEntityParam = new ProductDataMartEntity();
        BeanUtils.copyProperties(param, productDataMartEntityParam);
        LambdaQueryWrapper<ProductDataMartEntity> queryWrapper = new LambdaQueryWrapper<>(productDataMartEntityParam);
        if (!StringUtils.isEmpty(param.getFormName())) {
            productDataMartEntityParam.setFormName(null);
            queryWrapper.like(StringUtils.isNotEmpty(param.getFormName()), ProductDataMartEntity::getFormName, param.getFormName());
        }
        if (!StringUtils.isEmpty(param.getTableName())) {
            productDataMartEntityParam.setTableName(null);
            queryWrapper.like(StringUtils.isNotEmpty(param.getTableName()), ProductDataMartEntity::getTableName, param.getTableName());
        }
        queryWrapper.orderByDesc(ProductDataMartEntity::getId);
        IPage page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page(productDataMartDtoPageForm.getCurrent(), productDataMartDtoPageForm.getSize());
        IPage<ProductDataMartEntity> productDataMartEntityIPage = baseMapper.selectPage(page, queryWrapper);
        return productDataMartEntityIPage;
    }

    /**
     * 新增
     * @param productDataMartEntity
     * @param user
     * @return
     */
    public Result addForm(ProductDataMartEntity productDataMartEntity, User user) {
        ProductDataMartEntity dbProductDataMartEntity = this.getOne(new LambdaQueryWrapper<ProductDataMartEntity>()
                .eq(ProductDataMartEntity::getFormName, productDataMartEntity.getFormName()));
        if (dbProductDataMartEntity != null) {
            return Result.error("当前表单名称已存在");
        }
        productDataMartEntity.setCreateTime(LocalDateTime.now());
        productDataMartEntity.setCreateUser(StringUtils.isEmpty(user.getName()) ? "" : user.getName());
        if (this.save(productDataMartEntity)) {
            return Result.ok();
        }
        return Result.error("新增失败");
    }

    /**
     * 更新
     * @param productDataMartEntity
     * @param user
     * @return
     */
    public boolean updateForm(ProductDataMartEntity productDataMartEntity, User user) {
        ProductDataMartEntity oldProductDataMartEntity = this.getById(productDataMartEntity.getId());
        Assert.notNull(oldProductDataMartEntity, "该记录不存在");
        ProductDataMartEntity dbProductDataMartEntity = this.getOne(new LambdaQueryWrapper<ProductDataMartEntity>()
                .eq(ProductDataMartEntity::getFormName, productDataMartEntity.getFormName()).eq(ProductDataMartEntity::getTableName, productDataMartEntity.getTableName()));
        Assert.state(dbProductDataMartEntity == null || dbProductDataMartEntity.getId().equals(productDataMartEntity.getId()), "当前表单名称已存在");
        productDataMartEntity.setUpdateTime(LocalDateTime.now());
        productDataMartEntity.setUpdateUser(StringUtils.isEmpty(user.getName()) ? "" : user.getName());
        productDataMartEntity.setCreateTime(oldProductDataMartEntity.getCreateTime());
        productDataMartEntity.setCreateUser(oldProductDataMartEntity.getCreateUser());
        return this.updateById(productDataMartEntity);
    }

    /**
     * 获取列表
     * @return
     */
    public List<ProductDataMartEntity> getProductDataMartList() {
         return this.list();
    }

    /**
     * 根据数据库表名获取表单名称
     * @param tableName
     * @return
     */
    public String getFormNameByTableName(String tableName) {
        try {
            LambdaQueryWrapper<ProductDataMartEntity> queryWrapper = new QueryWrapper<ProductDataMartEntity>().lambda()
                    .eq(ProductDataMartEntity::getTableName, tableName);
            return this.getOne(queryWrapper).getFormName();
        } catch (Exception e) {
            throw new SystemRuntimeException("获取文件名失败");
        }
    }

    public Integer getFormIdByName(String tableName) {
        LambdaQueryWrapper<ProductDataMartEntity> queryWrapper = new QueryWrapper<ProductDataMartEntity>().lambda()
                .eq(ProductDataMartEntity::getTableName, tableName);
        return this.getOne(queryWrapper).getId();
    }
}
