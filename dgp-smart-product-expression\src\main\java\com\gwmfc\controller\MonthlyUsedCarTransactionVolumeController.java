package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.bo.CpcaautoBo;
import com.gwmfc.domain.User;
import com.gwmfc.service.MonthlyUsedCarTransactionVolumeService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 二手车月度交易量
 * <AUTHOR>
 * @date 2024年03月08日 16:27
 */
@Api(tags = "二手车月度交易量，乘联会乘用车销量")
@RestController
@RequestMapping("/monthlyusedcar/transactionvolume")
public class MonthlyUsedCarTransactionVolumeController {
    @Resource
    private MonthlyUsedCarTransactionVolumeService monthlyUsedCarTransactionVolumeService;

    /**
     * 爬取二手车月度交易量
     *
     * @return
     */
    @ApiOperation("二手车市场深度分析,【周度分析】车市扫描,全国乘用车市场分析")
    @GetMapping("/catchMonthlyUsedCarTransactionVolume")
    public Result catchMonthlyUsedCarTransactionVolume(@RequestParam String year, @RequestParam String month, @RequestParam String tableName, @CurrentUser User user) {
        monthlyUsedCarTransactionVolumeService.catchMonthlyUsedCarTransactionVolume(year, month, tableName, user.getUserName());
        return Result.ok();
    }

    /**
     * 爬取二手车月度交易量
     *
     * @return
     */
    @ApiOperation("二手车市场深度分析,【周度分析】车市扫描,全国乘用车市场分析")
    @PostMapping("/analysisPictureCallback")
    public Result analysisPictureCallback(@RequestBody CpcaautoBo cpcaautoBo) {
        monthlyUsedCarTransactionVolumeService.analysisPictureCallback(cpcaautoBo);
        return Result.ok();
    }
}
