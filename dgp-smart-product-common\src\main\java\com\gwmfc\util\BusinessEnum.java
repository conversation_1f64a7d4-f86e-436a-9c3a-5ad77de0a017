package com.gwmfc.util;

import lombok.Getter;

@Getter
public enum BusinessEnum {

    /**
     * 数据重复
     */
    DUPLICATE_KEY(701,"数据重复"),
    /**
     * 传入参数与服务定义参数不一致
     */
    PARAM_NAME_NOT_MATCH(603,"传入参数与服务定义参数不一致"),
    /**
     * 服务定义尚未进行,请联系大数据部
     */
    DATA_SERVICE_NOT_DEFINE(602,"服务定义尚未进行,请联系大数据部"),
    /**
     * 请确保下游部门审批通过后在进行审批
     */
    DEPENDEND_NOT_APPROCE(601,"请确保下游部门审批通过后在进行审批"),
    /**
     * 数据被引用不能删除
     */
    CANT_DEL(205,"数据被引用不能删除"),
    /**
     * 请求参数不能为空
     */
    PARAM_BLANK(300,"请求参数不能为空"),
    /**
     * 数据库中未找数据
     */
    NO_DATA_FOUND(204,"数据库中未找数据"),
    /**
     *   系统错误,请联系管理员
     */
    ERROR(500,"系统错误,请联系管理员"),

    /**
     * 登录token失效
     */
    TOKEN_EXPIRE(402,"登录过期，请重新登录"),

    /**
     * 刷新token失效
     */
    REFRESH_TOKEN_EXPIRE(403,"刷新token失效"),

    /**
     * 未授权
     */

    FORBIDDEN(401,"未授权,请联系管理员"),


    /**
     *  成功
     */
    SUCCESS(200,"成功");


    BusinessEnum(Integer code, String msg){
        this.code= code;
        this.msg =msg;
    }

    /**
     * 返回码
     */
    private Integer code;
    /**
     * 返回信息
     */
    private String msg;
}
