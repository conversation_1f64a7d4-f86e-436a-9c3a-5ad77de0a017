<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.ProductCalBaseTemplateDao">

    <select id="getProductCalBaseTemplateEntityList" resultType="com.gwmfc.entity.ProductCalBaseTemplateEntity">
        select
            *
        from
            product_cal_base_template
        where buss_type = #{businessType} or (repayment_method = #{repaymentMethod} and buss_type is null)
    </select>
</mapper>