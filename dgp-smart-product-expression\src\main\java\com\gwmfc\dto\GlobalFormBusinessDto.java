package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 表实体
 * <AUTHOR>
 * @date 2023/2/16
 */
@Data
@ApiModel(value = "表实体")
public class GlobalFormBusinessDto {

    /**
     * 表单ID
     */
    @NotNull(message = "表单id不能为空")
    @ApiModelProperty("表单ID")
    private Integer formId;

    /**
     * 表名
     */
    @ApiModelProperty("表名")
    private String tableName;

    /**
     * 表频次类型
     */
    @ApiModelProperty("更新频率（1：日频次；2：周频次；3：月频次；4：季频次；5：不定期频次; 6:年频次）")
    private Integer updateFrequency;

    /**
     * 条件参数
     */
    @ApiModelProperty("条件参数")
    private Map<String, Object> conditionParams;


}
