package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductCalEarlySquareProbabilityEntity
 * @Description product_cal_capital_cost
 * @Date 2023/9/22 13:26
 */
@Data
@TableName("product_cal_early_square_probability")
public class ProductCalEarlySquareProbabilityEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "business_type", comment = "业务类型")
    private String businessType;

    @TableFieldMapping(value = "if_dis_insterst", comment = "是否贴息")
    private String ifDisInsterst;

    @TableFieldMapping(value = "contract_trm", comment = "合同期数")
    private Integer contractTrm;

    @TableFieldMapping(value = "payout_rental_id", comment = "结清期次")
    private Integer payoutRentalId;

    @TableFieldMapping(value = "payout_probability", comment = "结清概率")
    private Double payoutProbability;

    @TableFieldMapping(value = "create_time", comment = "创建日期")
    private Date createTime;

    @TableFieldMapping(value = "create_user", comment = "创建人")
    private String createUser;

    @TableFieldMapping(value = "update_time", comment = "修改日期")
    private Date updateTime;

    @TableFieldMapping(value = "update_user", comment = "修改人")
    private String updateUser;
}
