package com.gwmfc.controller;

import com.gwmfc.dto.DepartmentNode;
import com.gwmfc.feign.DataApplyApi;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.gwmfc.util.StatusCodeEnum.SUCCESS;


/**
 * <AUTHOR>
 * @Classname RoleController
 * @Date 2021/8/5 17:05
 */
@Api(value = "用户接口", tags = "用户接口")
@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private DataApplyApi dataApplyApi;

    @ApiOperation(value = "获取列表接口")
    @GetMapping("/getAllUserList")
    public Result getAllUserList() {
        Result result = new Result();

        List<DepartmentNode> departmentNodeList = dataApplyApi.findDepartmentsAndStaffsInfo();
        result.setData(departmentNodeList);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

}
