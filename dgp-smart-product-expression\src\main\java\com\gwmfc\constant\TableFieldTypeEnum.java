package com.gwmfc.constant;

/**
 * 字段类型
 * <AUTHOR>
 * @date 2023/3/1
 */
public enum TableFieldTypeEnum {

    /**
     * 字符串
     */
    STRING("string"),

    /**
     * 数字
     */
    NUMBER("number"),

    /**
     * 日期
     */
    DATE("date");

    private final String type;

    TableFieldTypeEnum(String type) {
        this.type = type;
    }

    public String type() {
        return type;
    }
}
