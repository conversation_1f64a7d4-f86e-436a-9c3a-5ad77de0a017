package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月01日 15:06
 */
@Data
@ApiModel(value = "产品提案组")
public class ProductProposalTemplateGroupDto {
    @ApiModelProperty("uuid")
    private String uuid;

    @ApiModelProperty("组名称")
    private String groupName;

    @ApiModelProperty("提案实体")
    private List<ProductProposalTemplateDto> productProposalTemplateList;

    @ApiModelProperty("该组所占加权比例(%)")
    private String weightedBetweenGroupsRatio;

    @ApiModelProperty("组间加权IRR(%)")
    private String groupWeightingIrr;

    @ApiModelProperty("是否组内加权计算")
    private Integer weightedOrNot;
}
