package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductLineEntity
 * @Description TODO
 * @Date 2024/8/29 15:59
 */
@Data
@TableName("product_line")
public class ProductLineEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "buss_type", comment = "业务类型")
    private String bussType;

    @TableFieldMapping(value = "track", comment = "赛道")
    private String track;

    @TableFieldMapping(value = "subdivision", comment = "细分领域")
    private String subdivision;

    @TableFieldMapping(value = "product_classification", comment = "产品分类")
    private String productClassification;

    @TableFieldMapping(value = "product_secondary_classification", comment = "产品二级分类")
    private String productSecondaryClassification;

    @TableFieldMapping(value = "product_position", comment = "产品定位")
    private String productPosition;

    @TableFieldMapping(value = "product_position_description", comment = "产品定位描述")
    private String productPositionDescription;

    @TableFieldMapping(value = "product_program_description", comment = "产品方案描述")
    private String productProgramDescription;

    @TableFieldMapping(value = "state", comment = "状态")
    private Integer state;

    @TableFieldMapping(value = "effective_date", comment = "生效开始日期")
    private String effectiveDate;

    @TableFieldMapping(value = "expiry_date", comment = "有效截止日期")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String expiryDate;

    @TableFieldMapping(value = "create_user", comment = "创建人")
    private String createUser;

    @TableFieldMapping(value = "update_user", comment = "更新人")
    private String updateUser;

    @TableFieldMapping(value = "create_time", comment = "创建时间")
    private Date createTime;

    @TableFieldMapping(value = "update_time", comment = "更新时间")
    private Date updateTime;
}
