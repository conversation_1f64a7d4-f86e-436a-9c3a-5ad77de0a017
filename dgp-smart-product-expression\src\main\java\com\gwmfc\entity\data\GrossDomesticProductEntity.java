package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * 国内生产总值数
 * <AUTHOR>
 * @date 2024年03月06日 9:25
 */
@Data
@TableName("gross_domestic_product")
@ExcelIgnoreUnannotated
public class GrossDomesticProductEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("国内生产总值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_gross_domestic_product", comment = "国内生产总值当季值(亿元)", queryItem = true)
    private String quarterlyGrossDomesticProduct;

    @ExcelProperty("国内生产总值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_gross_domestic_product", comment = "国内生产总值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfGrossDomesticProduct;

    @ExcelProperty("第一产业增加值当季值(亿元)")
    @TableFieldMapping(value = "current_quarterly_value_of_added_value_of_primary_industry", comment = "第一产业增加值当季值(亿元)", queryItem = true)
    private String currentQuarterlyValueOfAddedValueOfPrimaryIndustry;

    @ExcelProperty("第一产业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_added_value_of_primary_industry", comment = "第一产业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfAddedValueOfPrimaryIndustry;

    @ExcelProperty("第二产业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_added_value_of_secondary_industry", comment = "第二产业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfAddedValueOfSecondaryIndustry;

    @ExcelProperty("第二产业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_added_value_of_secondary_industry", comment = "第二产业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfAddedValueOfSecondaryIndustry;

    @ExcelProperty("第三产业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_added_value_of_tertiary_industry", comment = "第三产业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfAddedValueOfTertiaryIndustry;

    @ExcelProperty("第三产业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_added_value_of_tertiary_industry", comment = "第三产业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfAddedValueOfTertiaryIndustry;

    @ExcelProperty("农林牧渔业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_added_value_of_agriculture", comment = "农林牧渔业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfAddedValueOfAgriculture;

    @ExcelProperty("农林牧渔业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_added_value_of_agriculture", comment = "农林牧渔业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfAddedValueOfAgriculture;

    @ExcelProperty("工业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_industrial_added_value", comment = "工业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfIndustrialAddedValue;

    @ExcelProperty("工业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_industrial_added_value", comment = "工业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfIndustrialAddedValue;

    @ExcelProperty("制造业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_manufacturing_added_value", comment = "制造业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfManufacturingAddedValue;

    @ExcelProperty("制造业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_manufacturing_added_value", comment = "制造业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfManufacturingAddedValue;

    @ExcelProperty("建筑业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_construction_added_value", comment = "建筑业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfConstructionAddedValue;

    @ExcelProperty("建筑业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_construction_added_value", comment = "建筑业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfConstructionAddedValue;

    @ExcelProperty("批发和零售业增加值当季值(亿元)")
    @TableFieldMapping(value = "wholesale_retail_added_value_for_quarter", comment = "批发和零售业增加值当季值(亿元)", queryItem = true)
    private String wholesaleRetailAddedValueForQuarter;

    @ExcelProperty("批发和零售业增加值累计值(亿元)")
    @TableFieldMapping(value = "cumulative_value_added_in_wholesale_retail_trade", comment = "批发和零售业增加值累计值(亿元)", queryItem = true)
    private String cumulativeValueAddedInWholesaleRetailTrade;

    @ExcelProperty("交通运输、仓储和邮政业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_added_of_transportation_storage_postal", comment = "交通运输、仓储和邮政业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueAddedOfTransportationStoragePostal;

    @ExcelProperty("交通运输、仓储和邮政业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_added_of_transportation_storage_postal", comment = "交通运输、仓储和邮政业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueAddedOfTransportationStoragePostal;

    @ExcelProperty("住宿和餐饮业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_hotel_and_catering_industry_added_value", comment = "住宿和餐饮业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfHotelAndCateringIndustryAddedValue;

    @ExcelProperty("住宿和餐饮业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_hotel_and_catering_industry_added_value", comment = "住宿和餐饮业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfHotelAndCateringIndustryAddedValue;

    @ExcelProperty("金融业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_financial_industry_added_value", comment = "金融业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfFinancialIndustryAddedValue;

    @ExcelProperty("金融业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_financial_industry_added_value", comment = "金融业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfFinancialIndustryAddedValue;

    @ExcelProperty("房地产业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_real_estate_added_value", comment = "房地产业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfRealEstateAddedValue;

    @ExcelProperty("房地产业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_real_estate_added_value", comment = "房地产业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfRealEstateAddedValue;

    @ExcelProperty("信息传输、软件和信息技术服务业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_information_technology_added_value", comment = "信息传输、软件和信息技术服务业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfInformationTechnologyAddedValue;

    @ExcelProperty("信息传输、软件和信息技术服务业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_information_technology_added_value", comment = "信息传输、软件和信息技术服务业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfInformationTechnologyAddedValue;

    @ExcelProperty("租赁和商务服务业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_leasing_and_business_service_added_value", comment = "租赁和商务服务业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfLeasingAndBusinessServiceAddedValue;

    @ExcelProperty("租赁和商务服务业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_leasing_and_business_service_added_value", comment = "租赁和商务服务业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfLeasingAndBusinessServiceAddedValue;

    @ExcelProperty("其他行业增加值当季值(亿元)")
    @TableFieldMapping(value = "quarterly_value_of_other_industry_added_value", comment = "其他行业增加值当季值(亿元)", queryItem = true)
    private String quarterlyValueOfOtherIndustryAddedValue;

    @ExcelProperty("其他行业增加值累计值(亿元)")
    @TableFieldMapping(value = "accumulated_value_of_other_industry_added_value", comment = "其他行业增加值累计值(亿元)", queryItem = true)
    private String accumulatedValueOfOtherIndustryAddedValue;
}
