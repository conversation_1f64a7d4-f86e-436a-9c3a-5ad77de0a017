package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024年01月31日 15:55
 */
@Data
@ApiModel(value = "irr测算结果变更历史")
@TableName("adjust_cal_result_history")
public class AdjustCalResultHistoryEntity {
    @ApiModelProperty("主键id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "product_proposal_id", comment = "提案id")
    private Long productProposalId;

    @TableFieldMapping(value = "current_time_seq_no", comment = "保存次序时间戳")
    private String currentTimeSeqNo;

    @TableFieldMapping(value = "product_proposal_group_uuid", comment = "产品提案组uuid")
    private String productProposalGroupUuid;

    @TableFieldMapping(value = "brand", comment = "品牌")
    private String brand;
    @TableFieldMapping(value = "loan_amount", comment = "贷额")
    private Double loanAmount;
    @TableFieldMapping(value = "payment_days", comment = "贷期")
    private Double paymentDays;
    @TableFieldMapping(value = "vehicle_type", comment = "车辆类型")
    private String vehicleType;
    @TableFieldMapping(value = "customer_interest_rate", comment = "客户利率（%）")
    private Double customerInterestRate;
    @TableFieldMapping(value = "settled_rate", comment = "结算利率（%）")
    private Double settledRate;
    @TableFieldMapping(value = "repayment_mode", comment = "还款方式(后期改枚举)")
    private Integer repaymentMode;
    @TableFieldMapping(value = "repayment_mode_explain", comment = "还款方式说明")
    private String repaymentModeExplain;
    @TableFieldMapping(value = "contracts_proportion", comment = "合同占比（%）")
    private Double contractsProportion;

    //风险
    @TableFieldMapping(value = "risk_loss_rate", comment = "风险损失率（%）")
    private Double riskLossRate;

    @TableFieldMapping(value = "base_commission_ratio", comment = "基础服务费（%）")
    private Double baseCommissionRatio;
    @TableFieldMapping(value = "ladder_bonus_ratio", comment = "阶梯奖金比例（%）")
    private Double ladderBonusRatio;
    @TableFieldMapping(value = "promotion_bonus_proportion", comment = "促销奖金比例（%）")
    private Double promotionBonusProportion;
    @TableFieldMapping(value = "personal_reward", comment = "个人奖励")
    private String personalReward;

    //政策
    @TableFieldMapping(value = "irr", comment = "IRR")
    private Double irr;
    @TableFieldMapping(value = "roa", comment = "ROA")
    private Double roa;
    @TableFieldMapping(value = "net_margin", comment = "净利润")
    private Double netMargin;
    @TableFieldMapping(value = "thousand_income", comment = "万元收益")
    private Double thousandIncome;
    @TableFieldMapping(value = "weighted_irr", comment = "加权IRR")
    private Double weightedIrr;

    //基础信息
    @TableFieldMapping(value = "change_reason", comment = "调整原因")
    private String changeReason;

    @TableFieldMapping(value = "change_user", comment = "调整人员")
    private String changeUser;

    @TableFieldMapping(value = "change_time", comment = "调整时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime changeTime;

    @TableFieldMapping(value = "consider_early_square", comment = "是否考虑提前结清")
    private Integer considerEarlySquare;

    @TableFieldMapping(value = "is_union_loan", comment = "是否联合贷")
    private Integer isUnionLoan;

    @TableFieldMapping(value = "is_farmer_loan", comment = "是否农户贷")
    private Integer isFarmerLoan;

    @TableFieldMapping(value = "product_classification", comment ="默认产品分类")
    private String productClassification;

    @TableFieldMapping(value = "financing_type", comment ="默认融资类型")
    private String financingType;

    @TableFieldMapping(value = "early_square_probability_classification", comment = "提前结清概率分类")
    private String earlySquareProbabilityClassification;

    @TableFieldMapping(value = "agent_type", comment = "代理商类型")
    private String agentType;

    @TableFieldMapping(value = "track", comment = "赛道")
    private String track;

    @TableFieldMapping(value = "is_corp_to_corp", comment = "是否总对总")
    private Integer isCorpToCorp;

    @TableFieldMapping(value="product_cal_early_square_param_sub_dto",comment="提前结清附加")
    private String productCalEarlySquareParamSubDto;

    @TableFieldMapping(value="product_cal_union_loan_param_dto",comment="联合贷参数")
    private String productCalUnionLoanParamDto;
}
