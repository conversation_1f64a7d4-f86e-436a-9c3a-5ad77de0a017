<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.ProductProposalHistoryDao">

    <select id="getHistoryData" resultType="com.gwmfc.bo.ProductProposalHistoryQueryBo">
        select
        count(ppmhd.id) as contract_num,
        sum(case when ppmhd.vehicle_price is null then 0 else ppmhd.vehicle_price end) as sum_vehicle_price,
        sum(case when ppmhd.loan_trm is null then 0 else ppmhd.loan_trm end) as sum_loan_trm,
        sum(case when ppmhd.total_rate is null then 0 else ppmhd.total_rate end) as sum_total_rate,
        sum(case when ppmhd.customer_rte is null then 0 else ppmhd.customer_rte end) as sum_customer_rte,
        sum(case when ppmhd.loan_amt is null then 0 else ppmhd.loan_amt end) as sum_loan_amt,
        sum(case when ppmhd.first_pay_pct is null then 0 else ppmhd.first_pay_pct end) as sum_pay_pct,
        sum(case when ppmhd.add_cre_amt is null then 0 else ppmhd.add_cre_amt end) as sum_add_cre_amt,
        sum(case when ppmhd.loan_classification_dsc in ('次级','可疑','损失') then (case when ppmhd.remain_principal_amt is null then 0 else ppmhd.remain_principal_amt end) else 0 end) as sum_bad_remain_principal_amt,
        sum(case when ppmhd.remain_principal_amt is null then 0 else (case when ppmhd.remain_principal_amt is null then 0 else ppmhd.remain_principal_amt end) end) as sum_remain_principal_amt,
        sum(case when ppmhd.if_writeoff in ('0','1') and ppmhd.if_30d_overdue in ('1','99999') and DATE_FORMAT(ppmhd.activation_time, '%Y-%m-%d') > (select last_day(DATE_ADD(now(),INTERVAL -19 MONTH))) then (case when ppmhd.remain_principal_amt is null then 0 else ppmhd.remain_principal_amt end) else 0 end) as before_hx18times30d_remain_principal_amt,
        sum(case when ppmhd.if_writeoff in ('0','1') and ppmhd.if_30d_overdue in ('1','99999') and DATE_FORMAT(ppmhd.activation_time, '%Y-%m-%d') > (select last_day(DATE_ADD(now(),INTERVAL -13 MONTH))) then (case when ppmhd.remain_principal_amt is null then 0 else ppmhd.remain_principal_amt end) else 0 end) as before_hx12times30d_remain_principal_amt,
        sum(case when ppmhd.if_writeoff in ('0','1') and ppmhd.if_30d_overdue in ('1','99999') then (case when ppmhd.remain_principal_amt is null then 0 else ppmhd.remain_principal_amt end) else 0 end) as before_hx30d_remain_principal_amt,
        sum(case when ppmhd.if_writeoff = '0' and ppmhd.if_30d_overdue in ('1','99999') and DATE_FORMAT(ppmhd.activation_time, '%Y-%m-%d') > (select last_day(DATE_ADD(now(),INTERVAL -19 MONTH))) then (case when ppmhd.remain_principal_amt is null then 0 else ppmhd.remain_principal_amt end) else 0 end) as after_hx18times30d_remain_principal_amt,
        sum(case when ppmhd.if_writeoff = '0' and ppmhd.if_30d_overdue in ('1','99999') and DATE_FORMAT(ppmhd.activation_time, '%Y-%m-%d') > (select last_day(DATE_ADD(now(),INTERVAL -13 MONTH))) then (case when ppmhd.remain_principal_amt is null then 0 else ppmhd.remain_principal_amt end) else 0 end) as after_hx12times30d_remain_principal_amt,
        sum(case when ppmhd.if_writeoff = '0' and ppmhd.if_30d_overdue in ('1','99999') then (case when ppmhd.remain_principal_amt is null then 0 else ppmhd.remain_principal_amt end) else 0 end) as after_hx30d_remain_principal_amt,
        sum(case when ppmhd.loss is null then 0 else ppmhd.loss end) as sum_loss,
        sum(case when ppmhd.margin_contribution is null then 0 else ppmhd.margin_contribution end) as sum_margin_contribution,
        sum(case when ppmhd.sale_profit is null then 0 else ppmhd.sale_profit end) as sum_sale_profit,
        sum(case when ppmhd.rebeat_rew_pct is null then 0 else ppmhd.rebeat_rew_pct end) as sum_rebeat_rew_pct
        from
            product_proposal_market_history_data ppmhd
        where
            ppmhd.contract_status_cn != '当月取消'
        and ppmhd.business_type = #{productProposalHistoryQueryDto.businessType}
        <if test="productProposalHistoryQueryDto.activationTimeLow != null">
            and ppmhd.activation_time &gt;= #{productProposalHistoryQueryDto.activationTimeLow}
        </if>
        <if test="productProposalHistoryQueryDto.activationTimeHigh != null">
            and ppmhd.activation_time &lt;= #{productProposalHistoryQueryDto.activationTimeHigh}
        </if>
        <if test="productProposalHistoryQueryDto.brandNameList != null and productProposalHistoryQueryDto.brandNameList.size() >0">
            <if test="!productProposalHistoryQueryDto.brandNameList.contains('null')">
                and ppmhd.brand_name in
                <foreach collection="productProposalHistoryQueryDto.brandNameList" index="index" item="brandName" open="(" separator="," close=")">
                    #{brandName}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.brandNameList.contains('null')">
                and (ppmhd.brand_name is null
                <if test="productProposalHistoryQueryDto.brandNameList.remove('null') and productProposalHistoryQueryDto.brandNameList.size() >0">
                    or brand_name in
                    <foreach collection="productProposalHistoryQueryDto.brandNameList" index="index" item="brandName" open="(" separator="," close=")">
                        #{brandName}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.vehiclemodel != null and productProposalHistoryQueryDto.vehiclemodel != ''">
            and ppmhd.vehiclemodel like concat('%',#{productProposalHistoryQueryDto.vehiclemodel},'%')
        </if>
        <if test="productProposalHistoryQueryDto.areaList != null and productProposalHistoryQueryDto.areaList.size() >0">
            <if test="!productProposalHistoryQueryDto.areaList.contains('null')">
                and ppmhd.area in
                <foreach collection="productProposalHistoryQueryDto.areaList" index="index" item="area" open="(" separator="," close=")">
                    #{area}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.areaList.contains('null')">
                and (ppmhd.area is null
                <if test="productProposalHistoryQueryDto.areaList.remove('null') and productProposalHistoryQueryDto.areaList.size() >0">
                    or ppmhd.area in
                    <foreach collection="productProposalHistoryQueryDto.areaList" index="index" item="area" open="(" separator="," close=")">
                        #{area}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.dealerProvince != null and productProposalHistoryQueryDto.dealerProvince != ''">
            and ppmhd.dealer_province like concat('%',#{productProposalHistoryQueryDto.dealerProvince},'%')
        </if>
        <if test="productProposalHistoryQueryDto.dealerLevelList != null and productProposalHistoryQueryDto.dealerLevelList.size() >0">
            <if test="!productProposalHistoryQueryDto.dealerLevelList.contains('null')">
                and ppmhd.dealer_level in
                <foreach collection="productProposalHistoryQueryDto.dealerLevelList" index="index" item="dealerLevel" open="(" separator="," close=")">
                    #{dealerLevel}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.dealerLevelList.contains('null')">
                and (ppmhd.dealer_level is null
                <if test="productProposalHistoryQueryDto.dealerLevelList.remove('null') and productProposalHistoryQueryDto.dealerLevelList.size() >0">
                    or ppmhd.dealer_level in
                    <foreach collection="productProposalHistoryQueryDto.dealerLevelList" index="index" item="dealerLevel" open="(" separator="," close=")">
                        #{dealerLevel}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.clique != null and productProposalHistoryQueryDto.clique != ''">
            and ppmhd.clique like concat('%',#{productProposalHistoryQueryDto.clique},'%')
        </if>
        <if test="productProposalHistoryQueryDto.investor != null and productProposalHistoryQueryDto.investor != ''">
            and ppmhd.investor like concat('%',#{productProposalHistoryQueryDto.investor},'%')
        </if>
        <if test="productProposalHistoryQueryDto.dealerName != null and productProposalHistoryQueryDto.dealerName != ''">
            and ppmhd.dealer_name like concat('%',#{productProposalHistoryQueryDto.dealerName},'%')
        </if>
        <if test="productProposalHistoryQueryDto.productTypeList != null and productProposalHistoryQueryDto.productTypeList.size() >0">
            <if test="!productProposalHistoryQueryDto.productTypeList.contains('null')">
                and ppmhd.product_type in
                <foreach collection="productProposalHistoryQueryDto.productTypeList" index="index" item="productType" open="(" separator="," close=")">
                    #{productType}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.productTypeList.contains('null')">
                and (ppmhd.product_type is null
                <if test="productProposalHistoryQueryDto.productTypeList.remove('null') and productProposalHistoryQueryDto.productTypeList.size() >0">
                    or ppmhd.product_type in
                    <foreach collection="productProposalHistoryQueryDto.productTypeList" index="index" item="productType" open="(" separator="," close=")">
                        #{productType}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.finProductNme != null and productProposalHistoryQueryDto.finProductNme != ''">
            and ppmhd.fin_product_nme like concat('%',#{productProposalHistoryQueryDto.finProductNme},'%')
        </if>
        <if test="productProposalHistoryQueryDto.customerRteLow != null">
            and ppmhd.customer_rte &gt;= (#{productProposalHistoryQueryDto.customerRteLow}/100)
        </if>
        <if test="productProposalHistoryQueryDto.customerRteHigh != null">
            and ppmhd.customer_rte &lt;= (#{productProposalHistoryQueryDto.customerRteHigh}/100)
        </if>
        <if test="productProposalHistoryQueryDto.subsidyPctLow != null">
            and ppmhd.subsidy_pct &gt;= (#{productProposalHistoryQueryDto.subsidyPctLow}/100)
        </if>
        <if test="productProposalHistoryQueryDto.subsidyPctHigh != null">
            and ppmhd.subsidy_pct &lt;= (#{productProposalHistoryQueryDto.subsidyPctHigh}/100)
        </if>
        <if test="productProposalHistoryQueryDto.totalRateLow != null">
            and ppmhd.total_rate &gt;= (#{productProposalHistoryQueryDto.totalRateLow}/100)
        </if>
        <if test="productProposalHistoryQueryDto.totalRateHigh != null">
            and ppmhd.total_rate &lt;= (#{productProposalHistoryQueryDto.totalRateHigh}/100)
        </if>
        <if test="productProposalHistoryQueryDto.loanTrmLow != null">
            and ppmhd.loan_trm &gt;= #{productProposalHistoryQueryDto.loanTrmLow}
        </if>
        <if test="productProposalHistoryQueryDto.loanTrmHigh != null">
            and ppmhd.loan_trm &lt;= #{productProposalHistoryQueryDto.loanTrmHigh}
        </if>
        <if test="productProposalHistoryQueryDto.firstPayPctLow != null">
            and ppmhd.first_pay_pct &gt;= (#{productProposalHistoryQueryDto.firstPayPctLow}/100)
        </if>
        <if test="productProposalHistoryQueryDto.firstPayPctHigh != null">
            and ppmhd.first_pay_pct &lt;= (#{productProposalHistoryQueryDto.firstPayPctHigh}/100)
        </if>
        <if test="productProposalHistoryQueryDto.loanAmtLow != null">
            and ppmhd.loan_amt &gt;= #{productProposalHistoryQueryDto.loanAmtLow}
        </if>
        <if test="productProposalHistoryQueryDto.loanAmtHigh != null">
            and ppmhd.loan_amt &lt;= #{productProposalHistoryQueryDto.loanAmtHigh}
        </if>
        <if test="productProposalHistoryQueryDto.addCreAmtLow != null">
            and ppmhd.add_cre_amt &gt;= #{productProposalHistoryQueryDto.addCreAmtLow}
        </if>
        <if test="productProposalHistoryQueryDto.addCreAmtHigh != null">
            and ppmhd.add_cre_amt &lt;= #{productProposalHistoryQueryDto.addCreAmtHigh}
        </if>
        <if test="productProposalHistoryQueryDto.vehiclePriceLow != null">
            and ppmhd.vehicle_price &gt;= #{productProposalHistoryQueryDto.vehiclePriceLow}
        </if>
        <if test="productProposalHistoryQueryDto.vehiclePriceHigh != null">
            and ppmhd.vehicle_price &lt;= #{productProposalHistoryQueryDto.vehiclePriceHigh}
        </if>
        <if test="productProposalHistoryQueryDto.vehicleTypeList != null and productProposalHistoryQueryDto.vehicleTypeList.size() >0">
            <if test="!productProposalHistoryQueryDto.vehicleTypeList.contains('null')">
                and ppmhd.vehicle_type in
                <foreach collection="productProposalHistoryQueryDto.vehicleTypeList" index="index" item="vehicleType" open="(" separator="," close=")">
                    #{vehicleType}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.vehicleTypeList.contains('null')">
                and (ppmhd.vehicle_type is null
                <if test="productProposalHistoryQueryDto.vehicleTypeList.remove('null') and productProposalHistoryQueryDto.vehicleTypeList.size() >0">
                    or ppmhd.vehicle_type in
                    <foreach collection="productProposalHistoryQueryDto.vehicleTypeList" index="index" item="vehicleType" open="(" separator="," close=")">
                        #{vehicleType}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.isNewEnergyList != null and productProposalHistoryQueryDto.isNewEnergyList.size() >0">
            and ppmhd.is_new_energy in
            <foreach collection="productProposalHistoryQueryDto.isNewEnergyList" index="index" item="isNewEnergy" open="(" separator="," close=")">
                #{isNewEnergy}
            </foreach>
        </if>
        <if test="productProposalHistoryQueryDto.affiliatedTypeDscList != null and productProposalHistoryQueryDto.affiliatedTypeDscList.size() >0">
            <if test="!productProposalHistoryQueryDto.affiliatedTypeDscList.contains('null')">
                and ppmhd.affiliated_type_dsc in
                <foreach collection="productProposalHistoryQueryDto.affiliatedTypeDscList" index="index" item="affiliatedTypeDsc" open="(" separator="," close=")">
                    #{affiliatedTypeDsc}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.affiliatedTypeDscList.contains('null')">
                and (ppmhd.affiliated_type_dsc is null
                <if test="productProposalHistoryQueryDto.affiliatedTypeDscList.remove('null') and productProposalHistoryQueryDto.affiliatedTypeDscList.size() >0">
                    or ppmhd.affiliated_type_dsc in
                    <foreach collection="productProposalHistoryQueryDto.affiliatedTypeDscList" index="index" item="affiliatedTypeDsc" open="(" separator="," close=")">
                        #{affiliatedTypeDsc}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.usetypeList != null and productProposalHistoryQueryDto.usetypeList.size() >0">
            <if test="!productProposalHistoryQueryDto.usetypeList.contains('null')">
                and ppmhd.usetype in
                <foreach collection="productProposalHistoryQueryDto.usetypeList" index="index" item="usetype" open="(" separator="," close=")">
                    #{usetype}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.usetypeList.contains('null')">
                and (ppmhd.usetype is null
                <if test="productProposalHistoryQueryDto.usetypeList.remove('null') and productProposalHistoryQueryDto.usetypeList.size() >0">
                    or ppmhd.usetype in
                    <foreach collection="productProposalHistoryQueryDto.usetypeList" index="index" item="usetype" open="(" separator="," close=")">
                        #{usetype}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.cardGradeList != null and productProposalHistoryQueryDto.cardGradeList.size() >0">
            <if test="!productProposalHistoryQueryDto.cardGradeList.contains('null')">
                and ppmhd.card_grade in
                <foreach collection="productProposalHistoryQueryDto.cardGradeList" index="index" item="cardGrade" open="(" separator="," close=")">
                    #{cardGrade}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.cardGradeList.contains('null')">
                and (ppmhd.card_grade is null
                <if test="productProposalHistoryQueryDto.cardGradeList.remove('null') and productProposalHistoryQueryDto.cardGradeList.size() >0">
                    or ppmhd.card_grade in
                    <foreach collection="productProposalHistoryQueryDto.cardGradeList" index="index" item="cardGrade" open="(" separator="," close=")">
                        #{cardGrade}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.ficoLvList != null and productProposalHistoryQueryDto.ficoLvList.size() >0">
            <if test="!productProposalHistoryQueryDto.ficoLvList.contains('null')">
                and ppmhd.fico_lv in
                <foreach collection="productProposalHistoryQueryDto.ficoLvList" index="index" item="ficoLv" open="(" separator="," close=")">
                    #{ficoLv}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.ficoLvList.contains('null')">
                and (ppmhd.fico_lv is null
                <if test="productProposalHistoryQueryDto.ficoLvList.remove('null') and productProposalHistoryQueryDto.ficoLvList.size() >0">
                    or ppmhd.fico_lv in
                    <foreach collection="productProposalHistoryQueryDto.ficoLvList" index="index" item="ficoLv" open="(" separator="," close=")">
                        #{ficoLv}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.sexList != null and productProposalHistoryQueryDto.sexList.size() >0">
            <if test="!productProposalHistoryQueryDto.sexList.contains('null')">
                and ppmhd.sex in
                <foreach collection="productProposalHistoryQueryDto.sexList" index="index" item="sex" open="(" separator="," close=")">
                    #{sex}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.sexList.contains('null')">
                and (ppmhd.sex is null
                <if test="productProposalHistoryQueryDto.sexList.remove('null') and productProposalHistoryQueryDto.sexList.size() >0">
                    or ppmhd.sex in
                    <foreach collection="productProposalHistoryQueryDto.sexList" index="index" item="sex" open="(" separator="," close=")">
                        #{sex}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.ageLow != null">
            and ppmhd.age &gt;= #{productProposalHistoryQueryDto.ageLow}
        </if>
        <if test="productProposalHistoryQueryDto.ageHigh != null">
            and ppmhd.age &lt;= #{productProposalHistoryQueryDto.ageHigh}
        </if>
        <if test="productProposalHistoryQueryDto.educationIdDscList != null and productProposalHistoryQueryDto.educationIdDscList.size() >0">
            <if test="!productProposalHistoryQueryDto.educationIdDscList.contains('null')">
                and ppmhd.education_id_dsc in
                <foreach collection="productProposalHistoryQueryDto.educationIdDscList" index="index" item="educationIdDsc" open="(" separator="," close=")">
                    #{educationIdDsc}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.educationIdDscList.contains('null')">
                and (ppmhd.education_id_dsc is null
                <if test="productProposalHistoryQueryDto.educationIdDscList.remove('null') and productProposalHistoryQueryDto.educationIdDscList.size() >0">
                    or ppmhd.education_id_dsc in
                    <foreach collection="productProposalHistoryQueryDto.educationIdDscList" index="index" item="educationIdDsc" open="(" separator="," close=")">
                        #{educationIdDsc}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.incomeLow != null">
            and ppmhd.income &gt;= #{productProposalHistoryQueryDto.incomeLow}
        </if>
        <if test="productProposalHistoryQueryDto.incomeHigh != null">
            and ppmhd.income &lt;= #{productProposalHistoryQueryDto.incomeHigh}
        </if>
        <if test="productProposalHistoryQueryDto.vehicleTypeDetailList != null and productProposalHistoryQueryDto.vehicleTypeDetailList.size() >0">
            <if test="!productProposalHistoryQueryDto.vehicleTypeDetailList.contains('null')">
                and ppmhd.vehicle_type_detail in
                <foreach collection="productProposalHistoryQueryDto.vehicleTypeDetailList" index="index" item="vehicleTypeDetail" open="(" separator="," close=")">
                    #{vehicleTypeDetail}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.vehicleTypeDetailList.contains('null')">
                and (ppmhd.vehicle_type_detail is null
                <if test="productProposalHistoryQueryDto.vehicleTypeDetailList.remove('null') and productProposalHistoryQueryDto.vehicleTypeDetailList.size() >0">
                    or ppmhd.vehicle_type_detail in
                    <foreach collection="productProposalHistoryQueryDto.vehicleTypeDetailList" index="index" item="vehicleTypeDetail" open="(" separator="," close=")">
                        #{vehicleTypeDetail}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.productGroup1List != null and productProposalHistoryQueryDto.productGroup1List.size() >0">
            <if test="!productProposalHistoryQueryDto.productGroup1List.contains('null')">
                and ppmhd.product_group1 in
                <foreach collection="productProposalHistoryQueryDto.productGroup1List" index="index" item="productGroup1" open="(" separator="," close=")">
                    #{productGroup1}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.productGroup1List.contains('null')">
                and (ppmhd.product_group1 is null
                <if test="productProposalHistoryQueryDto.productGroup1List.remove('null') and productProposalHistoryQueryDto.productGroup1List.size() >0">
                    or ppmhd.product_group1 in
                    <foreach collection="productProposalHistoryQueryDto.productGroup1List" index="index" item="productGroup1" open="(" separator="," close=")">
                        #{productGroup1}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.productGroup2List != null and productProposalHistoryQueryDto.productGroup2List.size() >0">
            <if test="!productProposalHistoryQueryDto.productGroup2List.contains('null')">
                and ppmhd.product_group2 in
                <foreach collection="productProposalHistoryQueryDto.productGroup2List" index="index" item="productGroup2" open="(" separator="," close=")">
                    #{productGroup2}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.productGroup2List.contains('null')">
                and (ppmhd.product_group2 is null
                <if test="productProposalHistoryQueryDto.productGroup2List.remove('null') and productProposalHistoryQueryDto.productGroup2List.size() >0">
                    or ppmhd.product_group2 in
                    <foreach collection="productProposalHistoryQueryDto.productGroup2List" index="index" item="productGroup2" open="(" separator="," close=")">
                        #{productGroup2}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.xfcxNew2List != null and productProposalHistoryQueryDto.xfcxNew2List.size() >0">
            <if test="!productProposalHistoryQueryDto.xfcxNew2List.contains('null')">
                and ppmhd.xfcx_new2 in
                <foreach collection="productProposalHistoryQueryDto.xfcxNew2List" index="index" item="xfcxNew2" open="(" separator="," close=")">
                    #{xfcxNew2}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.xfcxNew2List.contains('null')">
                and (ppmhd.xfcx_new2 is null
                <if test="productProposalHistoryQueryDto.xfcxNew2List.remove('null') and productProposalHistoryQueryDto.xfcxNew2List.size() >0">
                    or ppmhd.xfcx_new2 in
                    <foreach collection="productProposalHistoryQueryDto.xfcxNew2List" index="index" item="xfcxNew2" open="(" separator="," close=")">
                        #{xfcxNew2}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.ifGpcList != null">
            and ppmhd.if_gpc in
            <foreach collection="productProposalHistoryQueryDto.ifGpcList" index="index" item="ifGpc" open="(" separator="," close=")">
                #{ifGpc}
            </foreach>
        </if>
        <if test="productProposalHistoryQueryDto.ifZycList != null">
            and ppmhd.if_zyc in
            <foreach collection="productProposalHistoryQueryDto.ifZycList" index="index" item="ifZyc" open="(" separator="," close=")">
                #{ifZyc}
            </foreach>
        </if>
        <if test="productProposalHistoryQueryDto.ifParImVehicleList != null">
            and ppmhd.if_par_im_vehicle in
            <foreach collection="productProposalHistoryQueryDto.ifParImVehicleList" index="index" item="ifParImVehicle" open="(" separator="," close=")">
                #{ifParImVehicle}
            </foreach>
        </if>
        <if test="productProposalHistoryQueryDto.pricethirdpartyGroupList != null and productProposalHistoryQueryDto.pricethirdpartyGroupList.size() >0">
            <if test="!productProposalHistoryQueryDto.pricethirdpartyGroupList.contains('null')">
                and ppmhd.pricethirdparty_group in
                <foreach collection="productProposalHistoryQueryDto.pricethirdpartyGroupList" index="index" item="pricethirdpartyGroup" open="(" separator="," close=")">
                    #{pricethirdpartyGroup}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.pricethirdpartyGroupList.contains('null')">
                and (ppmhd.pricethirdparty_group is null
                <if test="productProposalHistoryQueryDto.pricethirdpartyGroupList.remove('null') and productProposalHistoryQueryDto.pricethirdpartyGroupList.size() >0">
                    or ppmhd.pricethirdparty_group in
                    <foreach collection="productProposalHistoryQueryDto.pricethirdpartyGroupList" index="index" item="pricethirdpartyGroup" open="(" separator="," close=")">
                        #{pricethirdpartyGroup}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.ifJpcList != null">
            and ppmhd.if_jpc in
            <foreach collection="productProposalHistoryQueryDto.ifJpcList" index="index" item="ifJpc" open="(" separator="," close=")">
                #{ifJpc}
            </foreach>
        </if>
        <if test="productProposalHistoryQueryDto.carAgeGroupList != null and productProposalHistoryQueryDto.carAgeGroupList.size() >0">
            <if test="!productProposalHistoryQueryDto.carAgeGroupList.contains('null')">
                and ppmhd.car_age_group in
                <foreach collection="productProposalHistoryQueryDto.carAgeGroupList" index="index" item="carAgeGroup" open="(" separator="," close=")">
                    #{carAgeGroup}
                </foreach>
            </if>
            <if test="productProposalHistoryQueryDto.carAgeGroupList.contains('null')">
                and (ppmhd.car_age_group is null
                <if test="productProposalHistoryQueryDto.carAgeGroupList.remove('null') and productProposalHistoryQueryDto.carAgeGroupList.size() >0">
                    or ppmhd.car_age_group in
                    <foreach collection="productProposalHistoryQueryDto.carAgeGroupList" index="index" item="carAgeGroup" open="(" separator="," close=")">
                        #{carAgeGroup}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <if test="productProposalHistoryQueryDto.carAgeLow != null">
            and ppmhd.car_age &gt;= #{productProposalHistoryQueryDto.carAgeLow}
        </if>
        <if test="productProposalHistoryQueryDto.carAgeHigh != null">
            and ppmhd.car_age &lt;= #{productProposalHistoryQueryDto.carAgeHigh}
        </if>
        <if test="productProposalHistoryQueryDto.invoicepriceLow != null">
            and ppmhd.invoiceprice &gt;= #{productProposalHistoryQueryDto.invoicepriceLow}
        </if>
        <if test="productProposalHistoryQueryDto.invoicepriceHigh != null">
            and ppmhd.invoiceprice &lt;= #{productProposalHistoryQueryDto.invoicepriceHigh}
        </if>
    </select>

    <select id="getBussTypeContractNum" resultType="java.lang.Integer">
        select count(*) from product_proposal_market_history_data where business_type = #{bussType}
    </select>
</mapper>