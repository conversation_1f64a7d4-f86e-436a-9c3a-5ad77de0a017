<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <property name="LOG_HOME" value="/opt/smart-gateway/logs"/>
    <property name="APP_NAME" value="smart-gateway" />

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d [%t] %X{ip} %-5level %logger{36}.%M\(%file:%line\) - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/smart-gateway.%d{yyyy-MM-dd.HH}.%i.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>GMT+8</timeZone>
                </timestamp>
                <pattern>
                    <pattern>
                        { "level": "%level",
                        "serviceName": "${APP_NAME:-}",
                        "pid": "${PID:-}",
                        "thread": "%thread",
                        "class": "%logger{40}",
                        "message": "%message" }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/error.%d{yyyy-MM-dd.HH}.%i.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>GMT+8</timeZone>
                </timestamp>
                <pattern>
                    <pattern>
                        { "level": "%level",
                        "serviceName": "${APP_NAME:-}",
                        "pid": "${PID:-}",
                        "thread": "%thread",
                        "class": "%logger{40}",
                        "message": "%message",
                        "stack_trace" :  "%exception"}
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>


    <root level="INFO">
        <!--本地调试 将 FILE 改为 CONSOLE 可以在控制台打印日志 提交代码时需要使用FILE-->
        <appender-ref ref="FILE"/>
        <appender-ref ref="ERROR"/>
    </root>
    <logger name="o.c.m" level="INFO"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="com.apache.ibatis" level="INFO" />
    <logger name="java.sql.Connection" level="INFO" />
    <logger name="java.sql.Statement" level="INFO" />
    <logger name="java.sql.PreparedStatement" level="INFO" />
</configuration>
