package com.gwmfc.util;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.util.*;

/**
 * <AUTHOR>
 * @Classname DynamicDataListener
 * @Description TODO
 * @Date 2025/4/24 14:33
 */
public class DynamicDataListener  extends AnalysisEventListener<Map<Integer, String>> {
    private final List<Map<String, String>> dataList = new ArrayList<>();
    private List<String> columnNames = new ArrayList<>();

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 获取表头信息（列名）
        columnNames = new ArrayList<>(headMap.values());
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        // 每解析一行数据，都会调用一次此方法
        System.out.println("正在解析的数据：" + data);
        Map<String, String> rowData = new LinkedHashMap<>();
        for (Map.Entry<Integer, String> entry : data.entrySet()) {
            int columnIndex = entry.getKey();
            if (columnIndex < columnNames.size()) {
                rowData.put(columnNames.get(columnIndex), entry.getValue());
            }
        }
        dataList.add(rowData);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 所有数据解析完成后的操作
        System.out.println("所有数据解析完成！");
    }

    public List<Map<String, String>> getDataList() {
        return dataList;
    }

    public List<String> getColumnNames() {
        return columnNames;
    }
}
