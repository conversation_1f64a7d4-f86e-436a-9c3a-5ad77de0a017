package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductActuarialBpCalPlanAssembleDimensionDetailDto;
import com.gwmfc.dto.ProductActuarialBpPolicyTemplateDto;
import com.gwmfc.entity.ProductActuarialBpCalPlanMainEntity;
import com.gwmfc.entity.ProductActuarialBpPolicyTemplateEntity;
import com.gwmfc.entity.ProductActuarialBpRewardTypeEntity;
import com.gwmfc.service.ProductActuarialService;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Classname ProductActuarialController
 * @Description TODO
 * @Date 2025/4/21 13:29
 */
@Api(tags = "产品精算")
@RestController
@RefreshScope
@RequestMapping("/roductActuarial")
public class ProductActuarialController {
    @Autowired
    private ProductActuarialService productActuarialService;

    @ApiOperation("获取精算基础配置信息")
    @GetMapping("/getProductActuarialBasicConfig")
    public Result getProductActuarialBasicConfig(@RequestParam Integer type){
        return productActuarialService.getProductActuarialBasicConfig(type);
    }
    @ApiOperation("获取精算奖励类型列表")
    @PostMapping("/rewardType/list")
    public Result getProductActuarialRewardTypeList(@RequestBody PageForm<ProductActuarialBpRewardTypeEntity> pageFormReq){
        return productActuarialService.getProductActuarialRewardTypeList(pageFormReq.getParam(), pageFormReq.getCurrent(), pageFormReq.getSize());
    }

    @ApiOperation("保存精算奖励类型")
    @PostMapping("/rewardType/save")
    public Result saveProductActuarialRewardType(@RequestBody ProductActuarialBpRewardTypeEntity productActuarialBpRewardTypeEntity, @CurrentUser User user){
        return productActuarialService.saveProductActuarialRewardType(productActuarialBpRewardTypeEntity,user);
    }

    @ApiOperation("删除精算奖励类型")
    @DeleteMapping("/rewardType/delete")
    public Result deleteProductActuarialRewardType(@RequestParam Long id){
        return productActuarialService.deleteProductActuarialRewardType(id);
    }

    @ApiOperation("更新精算奖励类型")
    @PostMapping("/rewardType/update")
    public Result updateProductActuarialRewardType(@RequestBody ProductActuarialBpRewardTypeEntity productActuarialBpRewardTypeEntity, @CurrentUser User user){
        return productActuarialService.updateProductActuarialRewardType(productActuarialBpRewardTypeEntity,user);
    }

    @ApiOperation("更新状态(启用/停用)")
    @GetMapping("/rewardType/status")
    public Result updateStatus(@RequestParam Integer status, @RequestParam Long id, @CurrentUser User user){
        return productActuarialService.updateStatus(status,id,user);
    }

    @ApiOperation("政策模板保存/更新")
    @PostMapping("/policyTemplate/saveOrUpdate")
    public Result saveOrUpdateProductActuarialPolicyTemplate(@RequestBody ProductActuarialBpPolicyTemplateDto productActuarialBpPolicyTemplateDto, @CurrentUser User user){
        return productActuarialService.saveOrUpdateProductActuarialPolicyTemplate(productActuarialBpPolicyTemplateDto,user);
    }

    @ApiOperation("获取政策模板列表")
    @PostMapping("/policyTemplate/list")
    public Result getProductActuarialPolicyTemplateList(@RequestBody PageForm<ProductActuarialBpPolicyTemplateEntity> pageFormReq){
        return productActuarialService.getProductActuarialPolicyTemplateList(pageFormReq.getParam(), pageFormReq.getCurrent(), pageFormReq.getSize());
    }

    @ApiOperation("获取政策模板详情")
    @GetMapping("/policyTemplate/getTemplateDetail")
    public Result getProductActuarialPolicyTemplateDetail(Long id){
        return productActuarialService.getProductActuarialPolicyTemplateDetail(id);
    }

    @ApiOperation("删除政策模板")
    @DeleteMapping("/policyTemplate/delete")
    public Result deleteProductActuarialPolicyTemplate(@RequestParam Long id){
        return productActuarialService.deleteProductActuarialPolicyTemplate(id);
    }

    @ApiOperation("更新模板状态(启用/停用)")
    @GetMapping("/policyTemplate/status")
    public Result updatePolicyTemplateStatus(@RequestParam Integer status, @RequestParam Long id, @CurrentUser User user){
        return productActuarialService.updatePolicyTemplateStatus(status,id,user);
    }

    @ApiOperation("下载政策模板")
    @GetMapping("/policyTemplate/download")
    public void downloadPolicyTemplate(@RequestParam Long id, HttpServletResponse response){
        productActuarialService.downloadPolicyTemplate(id, response);
    }

    @ApiOperation("政策测算基础信息保存或更新")
    @PostMapping("/policyCal/basicInfo/saveOrUpdate")
    public Result policyCalBasicInfoSaveOrUpdate(@RequestBody ProductActuarialBpCalPlanMainEntity productActuarialBpCalPlanMainEntity, @CurrentUser User user){
        return productActuarialService.policyCalBasicInfoSaveOrUpdate(productActuarialBpCalPlanMainEntity,user);
    }

    @ApiOperation("获取政策测算基础信息列表")
    @PostMapping("/policyCal/basicInfo/list")
    public Result getPolicyCalBasicInfoList(@RequestBody PageForm<ProductActuarialBpCalPlanMainEntity> pageFormReq){
        return productActuarialService.getPolicyCalBasicInfoList(pageFormReq.getParam(), pageFormReq.getCurrent(), pageFormReq.getSize());
    }

    @ApiOperation("更新政策测算状态")
    @GetMapping("/policyCal/basicInfo/updateStatus")
    public Result updatePolicyCalBasicInfoStatus(@RequestParam Integer status, @RequestParam Long id, @CurrentUser User user){
        return productActuarialService.updatePolicyCalBasicInfoStatus(status,id,user);
    }

    @ApiOperation("删除政策测算方案")
    @DeleteMapping("/policyCal/basicInfo/delete")
    public Result deletePolicyCal(Long id){
        return productActuarialService.deletePolicyCal(id);
    }

    @ApiOperation("政策测算系数补录录入")
    @PostMapping("/policyCal/coefficientSupplement/import")
    public Result policyCalCoefficientSupplement(@RequestParam Long templateId,@RequestPart("file") MultipartFile file){
        return productActuarialService.policyCalCoefficientSupplement(templateId,file);
    }

    @ApiOperation("政策测算系数补录保存")
    @PostMapping("/policyCal/coefficientSupplement/save")
    public Result policyCalCoefficientSupplementSave(@RequestBody ProductActuarialBpCalPlanAssembleDimensionDetailDto productActuarialBpCalPlanAssembleDimensionDetailDto, @CurrentUser User user){
        return productActuarialService.policyCalCoefficientSupplementSave(productActuarialBpCalPlanAssembleDimensionDetailDto,user);
    }

    @ApiOperation("获取政策测算系数补录详情")
    @GetMapping("/policyCal/coefficientSupplement/getDetail")
    public Result getPolicyCalCoefficientSupplement(@RequestParam Long calId){
        return productActuarialService.getPolicyCalCoefficientSupplement(calId);
    }

    @ApiOperation("计算提前结清")
    @GetMapping("/policyCal/earlySquare/cal")
    public Result calEarlySquare(@RequestParam Long calId){
        return productActuarialService.calEarlySquare(calId);
    }

    @ApiOperation("保存提前结清")
    @PostMapping("/policyCal/earlySquare/save")
    public Result saveEarlySquare(@RequestBody ProductActuarialBpCalPlanAssembleDimensionDetailDto productActuarialBpCalPlanAssembleDimensionDetailDto, @CurrentUser User user){
        return productActuarialService.saveEarlySquare(productActuarialBpCalPlanAssembleDimensionDetailDto, user);
    }

    @ApiOperation("获取提前结清详情")
    @GetMapping("/policyCal/earlySquare/getDetail")
    public Result getEarlySquare(@RequestParam Long calId){
        return productActuarialService.getEarlySquare(calId);
    }

    @ApiOperation("计算参数指标")
    @PostMapping("/policyCal/paramIndex/cal")
    public Result calParamIndex(@RequestBody ProductActuarialBpCalPlanAssembleDimensionDetailDto productActuarialBpCalPlanAssembleDimensionDetailDto){
        return productActuarialService.calParamIndex(productActuarialBpCalPlanAssembleDimensionDetailDto);
    }

    @ApiOperation("保存参数指标")
    @PostMapping("/policyCal/paramIndex/save")
    public Result saveParamIndex(@RequestBody ProductActuarialBpCalPlanAssembleDimensionDetailDto productActuarialBpCalPlanAssembleDimensionDetailDto, @CurrentUser User user){
        return productActuarialService.saveParamIndex(productActuarialBpCalPlanAssembleDimensionDetailDto,user);
    }

    @ApiOperation("获取参数指标")
    @GetMapping("/policyCal/paramIndex/getDetail")
    public Result getParamIndex(@RequestParam Long calId){
        return productActuarialService.getParamIndex(calId);
    }

    @ApiOperation("获取参数指标日期")
    @GetMapping("/policyCal/paramIndexDate/getDetail")
    public Result getParamIndexDate(@RequestParam Long calId){
        return productActuarialService.getParamIndexDate(calId);
    }

    @ApiOperation("计算结果值")
    @GetMapping("/policyCal/result/cal")
    public Result calResult(@RequestParam Long calId){
        return productActuarialService.calResult(calId);
    }

    @ApiOperation("保存结果值")
    @PostMapping("/policyCal/result/save")
    public Result saveResult(@RequestBody ProductActuarialBpCalPlanAssembleDimensionDetailDto productActuarialBpCalPlanAssembleDimensionDetailDto, @CurrentUser User user){
        return productActuarialService.saveResult(productActuarialBpCalPlanAssembleDimensionDetailDto,user);
    }

    @ApiOperation("下载结果值")
    @GetMapping("/policyCal/result/download")
    public void downloadResult(@RequestParam Long id, HttpServletResponse response){
        productActuarialService.downloadResult(id, response);
    }

    @ApiOperation("获取结果值")
    @GetMapping("/policyCal/result/getDetail")
    public Result getResult(@RequestParam Long calId){
        return productActuarialService.getResult(calId);
    }

    @ApiOperation("获取政策测算详情")
    @GetMapping("/policyCal/getDetail")
    public Result getPolicyCalDetail(Long calId){
        return productActuarialService.getPolicyCalDetail(calId);
    }
}
