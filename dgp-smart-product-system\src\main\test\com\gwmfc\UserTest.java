package com.gwmfc;

import com.gwmfc.service.RoleService;
import com.gwmfc.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年09月26日 13:58
 */
@SpringBootTest
public class UserTest {
    @Resource
    private UserService userService;
    @Resource
    private RoleService roleService;

    @Test
    public void list() throws IOException {

        List<Long> userIdList = roleService.getAllUserIdList();
        userService.selectUserByIdList(userIdList);
    }

}
