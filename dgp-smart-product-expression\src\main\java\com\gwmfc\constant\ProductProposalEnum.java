package com.gwmfc.constant;

/**
 * <AUTHOR>
 * @date 2023年10月18日 10:36
 */
public class ProductProposalEnum {

    /**
     * 产品提交初始状态 1
     */
    public static final String PRODUCT_PROPOSAL_SUBMIT = "product_proposal_submit";

    /**
     * 风险，商务 填写信息状态位 2
     */
    public static final String RISK_BUSINESS_CHECK = "risk_business_check";

    /**
     * 成本核算状态位 3
     */
    public static final String COST_ACCOUNTING = "cost_accounting";

    /**
     * 风险 确认状态位 4
     */
    public static final String RISK_PRODUCT_PROPOSAL_APPROVE = "risk_product_proposal_approve";

    /**
     * 财务确认状态位 5
     */
    public static final String FINANICAL_PRODUCT_PROPOSAL_PASSED = "finanical_product_proposal_passed";

    /**
     * 产品提案通过 6
     */
    public static final String PRODUCT_PROPOSAL_PASSED = "product_proposal_passed";

    /**
     * 产品提案文档生成 7
     */
    public static final String PRODUCT_PROPOSAL_DOC_CREATE = "product_proposal_doc_create";
}
