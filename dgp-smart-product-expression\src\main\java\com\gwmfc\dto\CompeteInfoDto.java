package com.gwmfc.dto;

import com.gwmfc.entity.CompeteInfoAfcMustEntity;
import com.gwmfc.entity.CompeteInfoAfcOptionalEntity;
import com.gwmfc.entity.CompeteInfoBankEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname CompeteInfoDto
 * @Description TODO
 * @Date 2024/12/11 16:15
 */
@Data
@ApiModel(value = "竞对信息dto")
public class CompeteInfoDto {
    @ApiModelProperty("竞对批次信息")
    private CompeteInfoBatchDto competeInfoBatchDto;

    @ApiModelProperty("银行竞对信息")
    private List<CompeteInfoBankEntity> competeInfoBankEntityList;

    @ApiModelProperty("AFC必填竞对信息")
    private List<CompeteInfoAfcMustEntity> competeInfoAfcMustEntityList;

    @ApiModelProperty("AFC选填竞对信息")
    private List<CompeteInfoAfcOptionalEntity> competeInfoAfcOptionalEntityList;
}
