package com.gwmfc.service;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.exception.ExcelCommonException;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.FileUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.gwmfc.domain.User;
import com.gwmfc.dto.GlobalFormBusinessDto;
import com.gwmfc.entity.ProductDataMartEntity;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.listener.GlobalFormBusinessExcelListener;
import com.gwmfc.properties.ExcelConstant;
import com.gwmfc.util.ClassRenderMapUtils;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.TableUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ExpressionService {
    @Resource
    private GlobalFormBusinessService globalFormBusinessService;

    @Resource
    private ProductDataMartService productDataMartService;

    /**
     * 导入数据
     *
     * @param file
     * @param formId
     * @param user
     */
    public void importData(MultipartFile file, int formId, User user) {
        ProductDataMartEntity productDataMartEntity = productDataMartService.getById(formId);
        Assert.notNull(productDataMartEntity, "该表单不存在");
        Assert.hasLength(productDataMartEntity.getTableName(), "该表单未配置数据表");
        try {
            /**
             * 文件类型校验
             */
            Assert.isTrue(file.getOriginalFilename().endsWith(".csv") || file.getOriginalFilename().endsWith(".xlsx") || file.getOriginalFilename().endsWith(".xls"), "文件类型错误");
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

            if (StringUtils.hasText(file.getOriginalFilename())) {
                if (file.getOriginalFilename().endsWith(".csv")) {
                    EasyExcelFactory.read(file.getInputStream(), new GlobalFormBusinessExcelListener(globalFormBusinessService,
                                    productDataMartEntity.getTableName(), user.getName(), productDataMartEntity.getUpdateFrequency(), productDataMartEntity.getSingleBatchCount(),
                                    dateTimeFormatter.format(LocalDateTime.now())))
                            .excelType(ExcelTypeEnum.CSV).charset(Charset.forName("GBK"))
                            .headRowNumber(1).sheet().doRead();
                } else if (file.getOriginalFilename().endsWith(".xlsx") || file.getOriginalFilename().endsWith(".xls")) {
                    EasyExcelFactory.read(file.getInputStream(), new GlobalFormBusinessExcelListener(globalFormBusinessService,
                                    productDataMartEntity.getTableName(), user.getName(), productDataMartEntity.getUpdateFrequency(), productDataMartEntity.getSingleBatchCount(),
                                    dateTimeFormatter.format(LocalDateTime.now())))
                            .headRowNumber(1).sheet().doRead();
                }
            }
            productDataMartEntity.setUpdateTime(LocalDateTime.now());
            productDataMartEntity.setUpdateUser(user.getUserName());
        } catch (ExcelCommonException e) {
            throw new SystemRuntimeException("文件无法解析!");
        } catch (IOException e) {
            throw new SystemRuntimeException("文件IO读取失败！");
        }
    }

    public void export(GlobalFormBusinessDto queryDto, HttpServletResponse response) throws IOException {
        ProductDataMartEntity productDataMartEntity = productDataMartService.getById(queryDto.getFormId());
        Assert.notNull(productDataMartEntity, "表单不存在");
        String fileName = URLEncoder.encode(productDataMartEntity.getFormName(), "UTF-8");
        Class<?> clazz = TableUtils.getClassByTableName(productDataMartEntity.getTableName());
        Assert.notNull(clazz, "找不到表单");
        try (OutputStream outputStream = response.getOutputStream()) {
            int totalCount = (int) globalFormBusinessService.selectRecordCount(queryDto);
            ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).build();
            if (totalCount == 0) {
                buildEmptyExcel(excelWriter, clazz);
            } else {
                buildExcelByPage(totalCount, queryDto, clazz, excelWriter);
            }
            fileName = fileName + ".xlsx";
            response.setCharacterEncoding("utf-8");
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);
            excelWriter.finish();
            outputStream.flush();
        } catch (IOException e) {
            log.error("导出Excel失败！{}", e);
        }
    }

    /**
     * 构建空文件
     *
     * @param excelWriter
     * @param clazz
     */
    private void buildEmptyExcel(ExcelWriter excelWriter, Class<?> clazz) {
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, "Sheet1").head(clazz)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
        excelWriter.write(Collections.emptyList(), writeSheet);
    }

    /**
     * 分页构建excel
     *
     * @param totalCount
     * @param queryDto
     * @param clazz
     * @param excelWriter
     */
    private void buildExcelByPage(int totalCount, GlobalFormBusinessDto queryDto, Class<?> clazz, ExcelWriter excelWriter) throws IOException {
        Instant start = Instant.now();
        //每一个Sheet存放100w条数据
        int sheetDataRows = ExcelConstant.sheetMaxRows;
        //每次写入的数据量20w,每页查询20W
        int writeDataRows = ExcelConstant.everyWriteMaxRows;
        //计算需要的Sheet数量
        int sheetNum = totalCount % sheetDataRows == 0 ? (totalCount / sheetDataRows) : (totalCount / sheetDataRows + 1);
        //计算一般情况下每一个Sheet需要写入的次数(一般情况不包含最后一个sheet,因为最后一个sheet不确定会写入多少条数据)
        int oneSheetWriteCount = sheetDataRows / writeDataRows;
        //计算最后一个sheet需要写入的次数
        Integer lastSheetWriteCount = totalCount % sheetDataRows == 0 ?
                oneSheetWriteCount :
                (totalCount % sheetDataRows % writeDataRows == 0 ? totalCount % sheetDataRows / writeDataRows : (totalCount % sheetDataRows / writeDataRows + 1));

        //开始分批查询分次写入
        Long id = 0L;
        log.info("临时的xml存储在:{}", FileUtils.getPoiFilesPath());
        for (int i = 0; i < sheetNum; i++) {
            //循环写入次数: j的自增条件是当不是最后一个Sheet的时候写入次数为正常的每个Sheet写入的次数,如果是最后一个就需要使用计算的次数lastSheetWriteCount
            for (int j = 0; j < (i != sheetNum - 1 ? oneSheetWriteCount : lastSheetWriteCount); j++) {
                //分页查询一次20w
                PageForm<GlobalFormBusinessDto> pageForm = new PageForm<>();
                pageForm.setParam(queryDto);
                pageForm.setCurrent(Math.toIntExact(j + 1 + (long) oneSheetWriteCount * i));
                pageForm.setSize(writeDataRows);
                List<Map<String, Object>> record = globalFormBusinessService.listByPageCursor(pageForm,id);
                if (record != null && !record.isEmpty()) {
                    Map<String, Object> cursorMap = record.get(record.size() - 1);
                    if (cursorMap != null) {
                        id = (Long) cursorMap.get("id");
                    }
                }
                List<?> list = ClassRenderMapUtils.renderMapList(record, clazz);

                //写入到excel:
                WriteSheet writeSheet = EasyExcelFactory.writerSheet(i, "Sheet" + (i + 1)).head(clazz)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
                excelWriter.write(list, writeSheet);
            }
        }
        Instant end = Instant.now();
        Duration duration = Duration.between(start, end);
        long seconds = duration.getSeconds();

        log.info("耗时: " + seconds + " 秒 ");
    }
}
