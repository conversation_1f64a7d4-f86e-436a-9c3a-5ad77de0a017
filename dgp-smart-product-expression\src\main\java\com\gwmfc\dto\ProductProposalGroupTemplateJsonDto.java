package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname ProductProposalTemplateJsonDto
 * @Description TODO
 * @Date 2023/11/16 16:43
 */
@Data
@ApiModel("产品组模板json对应dto")
public class ProductProposalGroupTemplateJsonDto {
    private String uuid;
    private String groupName;
    private String weightedBetweenGroupsRatio;
    private Integer weightedOrNot;
    private List<ProductProposalTemplateJsonDto> productProposalTemplateList;
    private Double groupWeightingIrr;
}
