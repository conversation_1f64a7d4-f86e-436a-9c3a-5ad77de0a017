package com.gwmfc.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gwmfc.dao.DdFpFinProductDao;
import com.gwmfc.dao.DdFpFinProductProductProposalDao;
import com.gwmfc.entity.DdFpFinProductProductProposalEntity;
import com.gwmfc.vo.ProductAssociationVo;
import com.gwmfc.vo.DdFpFinProductParamVo;
import com.gwmfc.vo.DdFpFinProductVo;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-10-11 09:20
 */
@Slf4j
@Service
public class DdFpFinProductService {

    @Resource
    DdFpFinProductDao ddFpFinProductDao;

    @Resource
    DdFpFinProductProductProposalDao ddFpFinProductProductProposalDao;

    /**
     * 分页查询
     *
     * @param paramDto
     * @param current
     * @param size
     * @return
     */
    public IPage<DdFpFinProductVo> listPage(DdFpFinProductParamVo paramDto, Integer current, Integer size) {
        IPage<DdFpFinProductVo> page = new Page<>(current, size);
        return ddFpFinProductDao.listPage(page, paramDto);
    }

    /**
     * 添加关联关系
     *
     * @param productAssociationVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void addAssociation(ProductAssociationVo productAssociationVo, String userName) {
        // 产品id列表
        List<String> finProductIdList = productAssociationVo.getFinProductId();
        // 需要关联的产品方案id
        Long productProposalId = productAssociationVo.getProductProposalId();

        // 保存关联关系
        finProductIdList.forEach(id -> {
            DdFpFinProductProductProposalEntity finProductProductProposal = new DdFpFinProductProductProposalEntity();
            finProductProductProposal.setProduct_proposal_id(productProposalId);
            finProductProductProposal.setFinProductId(id);
            finProductProductProposal.setCreateTime(LocalDateTime.now());
            finProductProductProposal.setCreateUser(userName);
            ddFpFinProductProductProposalDao.insert(finProductProductProposal);
        });
    }

    /**
     * 取消关联关系
     *
     * @param productAssociationVo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelAssociation(ProductAssociationVo productAssociationVo) {
        // 产品id列表
        List<String> finProductIdList = productAssociationVo.getFinProductId();

        LambdaQueryWrapper<DdFpFinProductProductProposalEntity> wrapper = new QueryWrapper<DdFpFinProductProductProposalEntity>().lambda()
                .in(DdFpFinProductProductProposalEntity::getFinProductId, finProductIdList);
        int count = ddFpFinProductProductProposalDao.delete(wrapper);
        return count > 0;
    }
}
