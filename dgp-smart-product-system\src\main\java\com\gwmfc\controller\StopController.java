package com.gwmfc.controller;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.gwmfc.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Properties;

/**
 *
 * interface graceful shutdown
 * <AUTHOR>
 *
 */

@Slf4j
@RestController
@RequestMapping("/nacos")
public class StopController {
    @Resource
    private NacosDiscoveryProperties nacosDiscoveryProperties;

    @GetMapping("/deregister")
    public Result stop() throws NacosException {
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.NAMESPACE, nacosDiscoveryProperties.getNamespace());
        properties.put(PropertyKeyConst.SERVER_ADDR, nacosDiscoveryProperties.getServerAddr());
        String serviceName = nacosDiscoveryProperties.getService();
        NamingService namingService = NacosFactory.createNamingService(properties);
        List<Instance> instanceList = namingService.getAllInstances(serviceName);
        for (Instance instance : instanceList) {
            log.info(instance.toString());
            if (instance.getIp().equals(nacosDiscoveryProperties.getIp())) {
                instance.setWeight(0);
                namingService.registerInstance(serviceName, instance);
            }
        }
        log.info("deregister set nacos weight to 0");
        return Result.ok();
    }
}