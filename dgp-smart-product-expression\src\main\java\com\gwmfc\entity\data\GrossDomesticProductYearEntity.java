package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * 国内生产总值数
 * <AUTHOR>
 * @date 2024年03月06日 9:25
 */
@Data
@TableName("gross_domestic_product_year")
@ExcelIgnoreUnannotated
public class GrossDomesticProductYearEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("国民总收入指数(上年=100)")
    @TableFieldMapping(value = "gross_national_income_index", comment = "国民总收入指数(上年=100)", queryItem = true)
    private String grossNationalIncomeIndex;

    @ExcelProperty("国内生产总值指数(上年=100)")
    @TableFieldMapping(value = "gross_domestic_product_deflator", comment = "国内生产总值指数(上年=100)", queryItem = true)
    private String grossDomesticProductDeflator;

    @ExcelProperty("第一产业增加值指数(上年=100)")
    @TableFieldMapping(value = "value_added_index_of_primary_industry", comment = "第一产业增加值指数(上年=100)", queryItem = true)
    private String valueAddedIndexOfPrimaryIndustry;

    @ExcelProperty("第二产业增加值指数(上年=100)")
    @TableFieldMapping(value = "value_added_index_of_secondary_industry", comment = "第二产业增加值指数(上年=100)", queryItem = true)
    private String valueAddedIndexOfSecondaryIndustry;

    @ExcelProperty("第三产业增加值指数(上年=100)")
    @TableFieldMapping(value = "value_added_index_of_tertiary_industry", comment = "第三产业增加值指数(上年=100)", queryItem = true)
    private String valueAddedIndexOfTertiaryIndustry;

    @ExcelProperty("人均国内生产总值指数(上年=100)")
    @TableFieldMapping(value = "gdp_per_capita_index", comment = "人均国内生产总值指数(上年=100)", queryItem = true)
    private String gdpPerCapitaIndex;

}
