package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.api.AuthServiceFeignApi;
import com.gwmfc.bo.DingNotification;
import com.gwmfc.bo.ProductRebateInformationBo;
import com.gwmfc.bo.UploadFileBo;
import com.gwmfc.dao.*;
import com.gwmfc.domain.User;
import com.gwmfc.dto.CapitalDto;
import com.gwmfc.dto.DepartmentUserDto;
import com.gwmfc.dto.UserAndDepartmentDto;
import com.gwmfc.dto.UserDto;
import com.gwmfc.entity.*;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.util.*;
import com.gwmfc.util.PageForm;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.gwmfc.constant.DingDingConstant.NOTIFICATION_SUBMIT;


/**
 * <AUTHOR>
 * @date 2023年09月07日 15:26
 */
@Service
@Slf4j
@RefreshScope
public class CapitalService extends ServiceImpl<CapitalDao, CapitalEntity> {
    private final static Integer NORMAL_CAPITAL = 1;
    private final static Integer LITTLE_BEE_CAPITAL = 2;
    @Resource
    private FtpService ftpService;
    @Resource
    private LittleBeeProductRebateInformationDao littleBeeProductRebateInformationDao;
    @Resource
    private ProductRebateInformationDao productRebateInformationDao;
    @Resource
    private OtherInformationDao otherInformationDao;
    @Resource
    private DingDingService dingDingService;
    @Resource
    private AuthServiceFeignApi authServiceFeignApi;
    /**
     * 文件目录
     */
    @Value("${attachment-dir}")
    private String attachmentDir;

    /**
     * 文件目录
     */
    @Value("${dingding-notify-users}")
    private String dingdingNotifyUsers;

    /**
     * 是否发送大区经理
     */
    @Value("${dingding-push-to-manager}")
    private Boolean dingdingPushToManager;

    /**
     * 是否发送大区经理
     */
    @Value("#{'${view-all-data-user}'.split(',')}")
    private List<String> viewAllUserList;

    @Value("${dingding.captial.agentId}")
    private String captialAgentId;

    public void add(CapitalDto capitalDto, List<MultipartFile> addFileList, UserAndDepartmentDto user) {
        CapitalEntity capitalEntity = new CapitalEntity();
        BeanUtils.copyProperties(capitalDto, capitalEntity);
        List productRebateInformationIdList = new ArrayList(6);
        if (capitalDto.getSource().equals(LITTLE_BEE_CAPITAL) && capitalDto.getLittleBeeProductRebateInformation() != null) {
            capitalDto.getLittleBeeProductRebateInformation().forEach(littleBeeProductRebateInformationEntity -> {
                // 政策状态为终止 置位删除
                if (littleBeeProductRebateInformationEntity.getPolicyType().equals(4) && capitalDto.getStatus().equals(2)) {
                    littleBeeProductRebateInformationEntity.setStatus(3);
                }
                littleBeeProductRebateInformationDao.insert(littleBeeProductRebateInformationEntity);
                productRebateInformationIdList.add(littleBeeProductRebateInformationEntity.getId());
            });
        } else if (capitalDto.getSource().equals(NORMAL_CAPITAL)) {
            if (capitalDto.getProductRebateInformation() != null) {
                capitalDto.getProductRebateInformation().forEach(productRebateInformationBo -> {
                    ProductRebateInformationEntity productRebateInformationEntity = new ProductRebateInformationEntity();
                    BeanUtils.copyProperties(productRebateInformationBo, productRebateInformationEntity);
                    // 政策状态为终止 置位删除
                    if (productRebateInformationEntity.getPolicyType().equals(4) && capitalDto.getStatus().equals(2)) {
                        productRebateInformationEntity.setStatus(3);
                    }
                    productRebateInformationDao.insert(productRebateInformationEntity);
                    productRebateInformationIdList.add(productRebateInformationEntity.getId());
                });
            }
        }
        capitalEntity.setProductRebateInformation(productRebateInformationIdList);

        if (capitalDto.getOtherInformation() != null) {
            List otherInformationIdList = new ArrayList(6);
            capitalDto.getOtherInformation().forEach(otherInformation -> {
                otherInformationDao.insert(otherInformation);
                otherInformationIdList.add(otherInformation.getId());
            });
            capitalEntity.setOtherInformation(otherInformationIdList);
        }
        capitalEntity.setAttachment(GsonUtil.toJson(ftpService.uploadFileToFtp(attachmentDir, addFileList, user.getName())));
        capitalEntity.setCreateTime(LocalDateTime.now());
        capitalEntity.setCreateUser(user.getName());
        baseMapper.insert(capitalEntity);

        if (capitalEntity.getStatus() != null && capitalEntity.getStatus().equals(2)) {
            List<String> userList = Arrays.asList(dingdingNotifyUsers.split(","));
            LocalDateTime now = LocalDateTime.now();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(capitalEntity.getCapitalProvince()).append(capitalEntity.getCapitalProvince().equals(capitalEntity.getCapitalCity()) ? "" : capitalEntity.getCapitalCity())
                    .append(capitalEntity.getCapitalName()).append("竞对信息收集");
            //判断是否发送大区经理
            if (dingdingPushToManager) {
                //查询提交人对应大区领导
                List<String> managerList = user.getDeptLeader();
                if (null != managerList) {
                    managerList.forEach(manager -> {
                        log.info("getUserByStaffNo StaffNo:{}", manager);
                        Result<UserDto> result = authServiceFeignApi.getUserByStaffNo(manager);
                        if (result != null && result.getData() != null && StringUtils.isNotEmpty(result.getData().getDingNo())) {
                            log.info("getUserByStaffNo  dingNo:{}", result.getData().getDingNo());
                            userList.add(result.getData().getDingNo());
                        }
                    });
                }
            }
            userList.forEach(userId -> {
                DingNotification dingNotification = DingNotification.builder()
                        .content("")
                        .name(stringBuilder.toString())
                        .applyUser(user.getName()).url(dingDingService.getCaptialApprovalUrl(capitalEntity.getId(), capitalEntity.getSource()))
                        .applyTime(DateUtil.formatDateTime(now)).userId(userId).status(NOTIFICATION_SUBMIT)
                        .accessToken(dingDingService.acquireCaptialAccessToken()).build();
                dingDingService.pushNotification(dingNotification, captialAgentId);
            });

        }
    }

    public void updateDraft(CapitalDto capitalDto, List<MultipartFile> addFileList, UserAndDepartmentDto user) {
        CapitalDto capitalDtoOld = this.detail(capitalDto.getId(), false);

        CapitalEntity capitalEntity = baseMapper.selectById(capitalDto.getId());
        List<Long> productRebateInformationEntityIdList = GsonUtil.jsonToList(capitalEntity.getProductRebateInformation().toString(), Long.class);
        List<Long> otherInformationEntityIdList = GsonUtil.jsonToList(capitalEntity.getOtherInformation().toString(), Long.class);

        BeanUtils.copyProperties(capitalDto, capitalEntity);
        if (capitalDto.getSource().equals(LITTLE_BEE_CAPITAL)) {
            List<LittleBeeProductRebateInformationEntity> productRebateInformationEntityList = capitalDto.getLittleBeeProductRebateInformation();
            List<LittleBeeProductRebateInformationEntity> productRebateInformationEntityListOld = capitalDtoOld.getLittleBeeProductRebateInformation();
            if (productRebateInformationEntityListOld != null && productRebateInformationEntityList != null) {
                List<Long> delIdList = productRebateInformationEntityListOld.stream().filter(m -> !productRebateInformationEntityList.stream().map(d -> d.getId()).collect(Collectors.toList()).contains(m.getId())).map(d -> d.getId()).collect(Collectors.toList());
                if (delIdList != null && !delIdList.isEmpty()) {
                    littleBeeProductRebateInformationDao.deleteBatchIds(delIdList);
                }
            }
            if (productRebateInformationEntityList != null) {
                productRebateInformationEntityList.forEach(productRebateInformationEntity -> {
                    // 政策状态为终止 置位删除
                    if (capitalDto.getStatus().equals(2)) {
                        if (productRebateInformationEntity.getPolicyType().equals(4)) {
                            productRebateInformationEntity.setStatus(3);
                        }
                    }
                    if (productRebateInformationEntity.getId() == null) {
                        littleBeeProductRebateInformationDao.insert(productRebateInformationEntity);
                        productRebateInformationEntityIdList.add(productRebateInformationEntity.getId());
                    } else {
                        if (productRebateInformationEntityListOld != null) {
                            LittleBeeProductRebateInformationEntity productRebateInformationEntityOldGet = productRebateInformationEntityListOld.stream().filter(productRebateInformationEntityOld -> productRebateInformationEntityOld.getId().equals(productRebateInformationEntity.getId())).findAny().get();
                            if (!productRebateInformationEntityOldGet.equals(productRebateInformationEntity)) {
                                littleBeeProductRebateInformationDao.updateById(productRebateInformationEntity);
                            }
                        }
                    }
                });
            }
        } else if (capitalDto.getSource().equals(NORMAL_CAPITAL)) {
            List<ProductRebateInformationBo> productRebateInformationBoList = capitalDto.getProductRebateInformation();
            List<ProductRebateInformationBo> productRebateInformationBoListOld = capitalDtoOld.getProductRebateInformation();
            if (productRebateInformationBoListOld != null && productRebateInformationBoList != null) {
                List<Long> delIdList = productRebateInformationBoListOld.stream().filter(m -> !productRebateInformationBoList.stream().map(d -> d.getId()).collect(Collectors.toList()).contains(m.getId())).map(d -> d.getId()).collect(Collectors.toList());
                if (delIdList != null && !delIdList.isEmpty()) {
                    productRebateInformationDao.deleteBatchIds(delIdList);
                }
            }

            if (productRebateInformationBoList != null) {
                productRebateInformationBoList.forEach(productRebateInformationBo -> {
                    ProductRebateInformationEntity productRebateInformationEntity = new ProductRebateInformationEntity();
                    BeanUtils.copyProperties(productRebateInformationBo, productRebateInformationEntity);
                    // 政策状态为终止 置位删除
                    if (capitalDto.getStatus().equals(2)) {
                        if (productRebateInformationEntity.getPolicyType().equals(4)) {
                            productRebateInformationEntity.setStatus(3);
                            productRebateInformationBo.setStatus(3);
                        }
                    }
                    if (productRebateInformationBo.getId() == null) {
                        productRebateInformationDao.insert(productRebateInformationEntity);
                        productRebateInformationEntityIdList.add(productRebateInformationEntity.getId());
                    } else {
                        if (productRebateInformationBoListOld != null) {
                            ProductRebateInformationBo productRebateInformationBoOldGet = productRebateInformationBoListOld.stream().filter(productRebateInformationEntityOld -> productRebateInformationEntityOld.getId().equals(productRebateInformationBo.getId())).findAny().get();
                            if (!productRebateInformationBoOldGet.equals(productRebateInformationBo)) {
                                productRebateInformationDao.updateById(productRebateInformationEntity);
                            }
                        }
                    }
                });
            }
        }
        capitalEntity.setProductRebateInformation(productRebateInformationEntityIdList);

        List<OtherInformationEntity> otherInformationEntityList = capitalDto.getOtherInformation();
        List<OtherInformationEntity> otherInformationEntityOldList = capitalDtoOld.getOtherInformation();
        if (otherInformationEntityOldList != null) {
            List<Long> delIdList = otherInformationEntityOldList.stream().filter(m -> !otherInformationEntityList.stream().map(d -> d.getId()).collect(Collectors.toList()).contains(m.getId())).map(d -> d.getId()).collect(Collectors.toList());
            if (delIdList != null && !delIdList.isEmpty()) {
                otherInformationDao.deleteBatchIds(delIdList);
            }
        }
        if (otherInformationEntityList != null) {
            otherInformationEntityList.forEach(otherInformationEntity -> {
                if (otherInformationEntity.getId() == null) {
                    otherInformationDao.insert(otherInformationEntity);
                    otherInformationEntityIdList.add(otherInformationEntity.getId());
                } else {
                    OtherInformationEntity otherInformationEntityOldGet = otherInformationEntityOldList.stream().filter(otherInformationEntityOld -> otherInformationEntityOld.getId().equals(otherInformationEntity.getId())).findAny().get();
                    if (!otherInformationEntityOldGet.equals(otherInformationEntity)) {
                        otherInformationDao.updateById(otherInformationEntity);
                    }
                }
            });
        }
        capitalEntity.setOtherInformation(otherInformationEntityIdList);

        List<UploadFileBo> uploadFileBoListOld = capitalDtoOld.getAttachmentUploadFileBo();
        List<UploadFileBo> uploadFileBoList = capitalDto.getAttachmentUploadFileBo();
        List<UploadFileBo> saveFileList = new ArrayList<>();
        if (addFileList != null && addFileList.size() != 0) {
            saveFileList = ftpService.uploadFileToFtp(attachmentDir, addFileList, user.getName());
        }
        if (uploadFileBoListOld != null && !uploadFileBoListOld.isEmpty()) {
            List<UploadFileBo> delList = uploadFileBoListOld.stream().filter(m -> !uploadFileBoList.stream().map(d -> d.getFileName()).collect(Collectors.toList()).contains(m.getFileName())).collect(Collectors.toList());
            if (delList != null && delList.size() != 0) {
                uploadFileBoListOld.removeAll(delList);
                delList.forEach(uploadFileBo -> ftpService.deleteFile(uploadFileBo.getFilePath()));
            }
            saveFileList.addAll(uploadFileBoListOld);
        }
        capitalEntity.setAttachment(GsonUtil.toJson(saveFileList));

        capitalEntity.setUpdateTime(LocalDateTime.now());
        capitalEntity.setUpdateUser(user.getName());
        baseMapper.updateById(capitalEntity);
    }

    public void update(CapitalDto capitalDto, List<MultipartFile> addFileList, UserAndDepartmentDto user) {
        CapitalDto capitalDtoOld = this.detail(capitalDto.getId(), false);

        Boolean changed = judgeChange(capitalDto, capitalDtoOld);
        if (changed) {
            Long notifyId = capitalDto.getId();
            if (capitalDtoOld.getStatus().equals(1)) {
                updateDraft(capitalDto, addFileList, user);
            } else if (capitalDtoOld.getStatus().equals(2)) {
                notifyId = updateOfficial(capitalDto, addFileList, user);
            }
            if (capitalDto.getStatus() != null && capitalDto.getStatus().equals(2)) {
                List<String> userList = Arrays.asList(dingdingNotifyUsers.split(","));
                LocalDateTime now = LocalDateTime.now();
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(capitalDto.getCapitalProvince()).append(capitalDto.getCapitalProvince().equals(capitalDto.getCapitalCity()) ? "" : capitalDto.getCapitalCity())
                        .append(capitalDto.getCapitalName()).append("竞对信息收集");
                //判断是否发送大区经理
                if (dingdingPushToManager) {
                    //查询提交人对应大区领导
                    List<String> managerList = user.getDeptLeader();
                    managerList.forEach(manager -> {
                        log.info("getUserByStaffNo StaffNo:{}", manager);
                        Result<UserDto> result = authServiceFeignApi.getUserByStaffNo(manager);
                        if (result != null && result.getData() != null && StringUtils.isNotEmpty(result.getData().getDingNo())) {
                            log.info("getUserByStaffNo  dingNo:{}", result.getData().getDingNo());
                            userList.add(result.getData().getDingNo());
                        }
                    });
                }
                Long finalNotifyId = notifyId;
                userList.forEach(userId -> {
                    DingNotification dingNotification = DingNotification.builder()
                            .content("")
                            .name(stringBuilder.toString())
                            .applyUser(user.getName()).url(dingDingService.getCaptialApprovalUrl(finalNotifyId, capitalDto.getSource()))
                            .applyTime(DateUtil.formatDateTime(now)).userId(userId).status(NOTIFICATION_SUBMIT)
                            .accessToken(dingDingService.acquireCaptialAccessToken()).build();
                    dingDingService.pushNotification(dingNotification, captialAgentId);
                });
            }
        } else {
            throw new SystemRuntimeException("表单没有任何变化！");
        }
    }

    private Boolean judgeChange(CapitalDto capitalDto, CapitalDto capitalDtoOld) {
        if (capitalDto.equals(capitalDtoOld)) {
            return false;
        }
        return true;
    }

    public Long updateOfficial(CapitalDto capitalDto, List<MultipartFile> addFileList, UserAndDepartmentDto user) {
        CapitalDto capitalDtoOld = this.detail(capitalDto.getId(), false);
        CapitalEntity capitalEntity = baseMapper.selectById(capitalDto.getId());
        List<Long> productRebateInformationEntityIdList = GsonUtil.jsonToList(capitalEntity.getProductRebateInformation().toString(), Long.class);
        List<Long> otherInformationEntityIdList = GsonUtil.jsonToList(capitalEntity.getOtherInformation().toString(), Long.class);

        BeanUtils.copyProperties(capitalDto, capitalEntity);
        if (capitalDto.getSource().equals(LITTLE_BEE_CAPITAL)) {
            List<LittleBeeProductRebateInformationEntity> productRebateInformationEntityList = capitalDto.getLittleBeeProductRebateInformation();
            List<LittleBeeProductRebateInformationEntity> productRebateInformationEntityListOld = capitalDtoOld.getLittleBeeProductRebateInformation();
            if (productRebateInformationEntityListOld != null && productRebateInformationEntityList != null) {
                List<Long> delIdList = productRebateInformationEntityListOld.stream().filter(m -> !productRebateInformationEntityList.stream().map(d -> d.getId()).collect(Collectors.toList()).contains(m.getId())).map(d -> d.getId()).collect(Collectors.toList());
                if (delIdList != null && !delIdList.isEmpty()) {
                    productRebateInformationEntityIdList.removeAll(delIdList);
                    littleBeeProductRebateInformationDao.updateBatchIds(delIdList);
                }
            }
            if (productRebateInformationEntityList != null) {
                productRebateInformationEntityList.forEach(productRebateInformationEntity -> {
                    if (productRebateInformationEntity.getId() == null) {
                        // 政策状态为终止 置位删除
                        if (capitalDto.getStatus().equals(2)) {
                            if (productRebateInformationEntity.getPolicyType().equals(4)) {
                                productRebateInformationEntity.setStatus(3);
                            }
                        }
                        littleBeeProductRebateInformationDao.insert(productRebateInformationEntity);
                        productRebateInformationEntityIdList.add(productRebateInformationEntity.getId());
                    } else {
                        LittleBeeProductRebateInformationEntity productRebateInformationEntityOldGet = productRebateInformationEntityListOld.stream().filter(productRebateInformationEntityOld -> productRebateInformationEntityOld.getId().equals(productRebateInformationEntity.getId())).findAny().get();
                        if (!productRebateInformationEntityOldGet.equals(productRebateInformationEntity)) {
                            Long id = productRebateInformationEntity.getId();
                            // 政策状态为终止 直接置位删除 但关联关系不移除
                            if (capitalDto.getStatus().equals(2)) {
                                if (productRebateInformationEntity.getPolicyType().equals(4)) {
                                    productRebateInformationEntity.setStatus(3);
                                    littleBeeProductRebateInformationDao.updateById(productRebateInformationEntity);
                                    return;
                                }
                            }
                            littleBeeProductRebateInformationDao.updateStatus(id);
                            productRebateInformationEntityIdList.remove(id);
                            productRebateInformationEntity.setId(null);
                            littleBeeProductRebateInformationDao.insert(productRebateInformationEntity);
                            productRebateInformationEntityIdList.add(productRebateInformationEntity.getId());
                        }
                    }
                });
            }
        } else if (capitalDto.getSource().equals(NORMAL_CAPITAL)) {

            List<ProductRebateInformationBo> productRebateInformationBoList = capitalDto.getProductRebateInformation();
            List<ProductRebateInformationBo> productRebateInformationBoListOld = capitalDtoOld.getProductRebateInformation();

            if (productRebateInformationBoList != null && !productRebateInformationBoList.isEmpty() && productRebateInformationBoListOld != null) {
                List<Long> delIdList = productRebateInformationBoListOld.stream().filter(m -> !productRebateInformationBoList.stream().map(d -> d.getId()).collect(Collectors.toList()).contains(m.getId())).map(d -> d.getId()).collect(Collectors.toList());
                if (delIdList != null && !delIdList.isEmpty()) {
                    productRebateInformationEntityIdList.removeAll(delIdList);
                    productRebateInformationDao.updateBatchIds(delIdList);
                }
            }

            if (productRebateInformationBoList != null) {
                productRebateInformationBoList.forEach(productRebateInformationBo -> {
                    ProductRebateInformationEntity productRebateInformationEntity = new ProductRebateInformationEntity();
                    BeanUtils.copyProperties(productRebateInformationBo, productRebateInformationEntity);

                    if (productRebateInformationBo.getId() == null) {
                        // 政策状态为终止 置位删除
                        if (capitalDto.getStatus().equals(2)) {
                            if (productRebateInformationEntity.getPolicyType().equals(4)) {
                                productRebateInformationEntity.setStatus(3);
                            }
                        }
                        productRebateInformationDao.insert(productRebateInformationEntity);
                        productRebateInformationEntityIdList.add(productRebateInformationEntity.getId());
                    } else {
                        ProductRebateInformationBo productRebateInformationBoOldGet = productRebateInformationBoListOld.stream().filter(productRebateInformationEntityOld -> productRebateInformationEntityOld.getId().equals(productRebateInformationBo.getId())).findAny().get();
                        if (!productRebateInformationBoOldGet.equals(productRebateInformationBo)) {
                            Long id = productRebateInformationEntity.getId();
                            // 政策状态为终止 置位删除
                            if (capitalDto.getStatus().equals(2)) {
                                if (productRebateInformationEntity.getPolicyType().equals(4)) {
                                    productRebateInformationEntity.setStatus(3);
                                    productRebateInformationDao.updateById(productRebateInformationEntity);
                                    return;
                                }
                            }
                            productRebateInformationDao.updateStatus(id);
                            productRebateInformationEntityIdList.remove(id);
                            productRebateInformationEntity.setId(null);
                            productRebateInformationDao.insert(productRebateInformationEntity);
                            productRebateInformationEntityIdList.add(productRebateInformationEntity.getId());
                        }
                    }
                });
            }
        }
        capitalEntity.setProductRebateInformation(productRebateInformationEntityIdList);

        List<OtherInformationEntity> otherInformationEntityList = capitalDto.getOtherInformation();
        List<OtherInformationEntity> otherInformationEntityOldList = capitalDtoOld.getOtherInformation();
        if (otherInformationEntityOldList != null && otherInformationEntityList != null) {
            List<Long> delIdList = otherInformationEntityOldList.stream().filter(m -> !otherInformationEntityList.stream().map(d -> d.getId()).collect(Collectors.toList()).contains(m.getId())).map(d -> d.getId()).collect(Collectors.toList());
            if (delIdList != null && !delIdList.isEmpty()) {
                otherInformationEntityIdList.removeAll(delIdList);
                otherInformationDao.updateBatchIds(delIdList);
            }
        }
        if (otherInformationEntityList != null) {
            otherInformationEntityList.forEach(otherInformationEntity -> {
                if (otherInformationEntity.getId() == null) {
                    otherInformationDao.insert(otherInformationEntity);
                    otherInformationEntityIdList.add(otherInformationEntity.getId());
                } else {
                    if (otherInformationEntityOldList != null && !otherInformationEntityOldList.isEmpty()) {
                        OtherInformationEntity otherInformationEntityOldGet = otherInformationEntityOldList.stream().filter(otherInformationEntityOld -> otherInformationEntityOld.getId().equals(otherInformationEntity.getId())).findAny().get();
                        if (!otherInformationEntityOldGet.equals(otherInformationEntity)) {
                            Long id = otherInformationEntity.getId();
                            otherInformationDao.updateStatus(id);
                            otherInformationEntity.setId(null);
                            otherInformationEntityIdList.remove(id);
                            otherInformationDao.insert(otherInformationEntity);
                            otherInformationEntityIdList.add(otherInformationEntity.getId());
                        }
                    }
                }
            });
        }
        capitalEntity.setOtherInformation(otherInformationEntityIdList);

        List<UploadFileBo> uploadFileBoListOld = capitalDtoOld.getAttachmentUploadFileBo();
        List<UploadFileBo> uploadFileBoList = capitalDto.getAttachmentUploadFileBo();
        List<UploadFileBo> saveFileList = new ArrayList<>();
        if (addFileList != null && addFileList.size() != 0) {
            saveFileList = ftpService.uploadFileToFtp(attachmentDir, addFileList, user.getName());
        }
        if (uploadFileBoListOld != null && !uploadFileBoListOld.isEmpty()) {
            List<UploadFileBo> delList = uploadFileBoListOld.stream().filter(m -> !uploadFileBoList.stream().map(d -> d.getFileName()).collect(Collectors.toList()).contains(m.getFileName())).collect(Collectors.toList());
            uploadFileBoListOld.removeAll(delList);
            saveFileList.addAll(uploadFileBoListOld);
        }
        capitalEntity.setAttachment(GsonUtil.toJson(saveFileList));

        capitalEntity.setUpdateTime(LocalDateTime.now());
        capitalEntity.setUpdateUser(user.getName());
        Long id = capitalEntity.getId();
        capitalEntity.setId(null);
        if (capitalEntity.getOriginId() == null) {
            capitalEntity.setOriginId(id);
        }
        baseMapper.updateStatus(id);
        baseMapper.insert(capitalEntity);
        return capitalEntity.getId();
    }

    public void delete(Long recordId) {
        CapitalEntity capitalEntity = baseMapper.selectById(recordId);
        if (capitalEntity.getStatus() != null && capitalEntity.getStatus().equals(2)) {
            capitalEntity.setStatus(3);
            if (capitalEntity.getProductRebateInformation() != null && !capitalEntity.getProductRebateInformation().isEmpty()) {
                if (capitalEntity.getSource().equals(LITTLE_BEE_CAPITAL)) {
                    littleBeeProductRebateInformationDao.updateBatchIds(capitalEntity.getProductRebateInformation());
                } else if (capitalEntity.getSource().equals(NORMAL_CAPITAL)) {
                    productRebateInformationDao.updateBatchIds(capitalEntity.getProductRebateInformation());
                }
            }
            if (capitalEntity.getOtherInformation() != null && !capitalEntity.getOtherInformation().isEmpty()) {
                otherInformationDao.updateBatchIds(capitalEntity.getOtherInformation());
            }
            baseMapper.updateById(capitalEntity);
        } else if (capitalEntity.getStatus() != null && capitalEntity.getStatus().equals(1)) {
            //测试之前历史数据中是否有关联
            List<CapitalEntity> capitalEntityList = this.selectHistory(capitalEntity.getOriginId());
            if (capitalEntity.getSource().equals(LITTLE_BEE_CAPITAL) && capitalEntity.getProductRebateInformation() != null && !capitalEntity.getProductRebateInformation().isEmpty()) {
                List<Long> deleteIdList = capitalEntity.getProductRebateInformation();
                capitalEntityList.forEach(capitalEntityShuffle -> deleteIdList.removeAll(capitalEntityShuffle.getProductRebateInformation()));
                if (deleteIdList != null && !deleteIdList.isEmpty()) {
                    littleBeeProductRebateInformationDao.deleteBatchIds(deleteIdList);
                }
            } else if (capitalEntity.getSource().equals(NORMAL_CAPITAL) && capitalEntity.getProductRebateInformation() != null && !capitalEntity.getProductRebateInformation().isEmpty()) {
                List<Long> deleteIdList = capitalEntity.getProductRebateInformation();
                capitalEntityList.forEach(capitalEntityShuffle -> deleteIdList.removeAll(capitalEntityShuffle.getProductRebateInformation()));
                if (deleteIdList != null && !deleteIdList.isEmpty()) {
                    productRebateInformationDao.deleteBatchIds(deleteIdList);
                }
            }
            if (capitalEntity.getOtherInformation() != null && !capitalEntity.getOtherInformation().isEmpty()) {
                List<Long> deleteIdList = capitalEntity.getOtherInformation();
                capitalEntityList.forEach(capitalEntityShuffle -> deleteIdList.removeAll(capitalEntityShuffle.getOtherInformation()));
                if (deleteIdList != null && !deleteIdList.isEmpty()) {
                    otherInformationDao.deleteBatchIds(capitalEntity.getOtherInformation());
                }
            }
            baseMapper.deleteById(capitalEntity.getId());
        }
    }

    private List<CapitalEntity> selectHistory(Long originId) {
        CapitalEntity capitalEntityQuery = new CapitalEntity();
        capitalEntityQuery.setId(originId);
        QueryWrapper queryWrapper = new QueryWrapper<>(capitalEntityQuery);
        queryWrapper.eq("status", 3);
        List<CapitalEntity> capitalEntityList = baseMapper.selectList(queryWrapper);

        capitalEntityQuery = new CapitalEntity();
        capitalEntityQuery.setOriginId(originId);
        queryWrapper = new QueryWrapper<>(capitalEntityQuery);
        queryWrapper.eq("status", 3);
        capitalEntityList.addAll(baseMapper.selectList(queryWrapper));

        return capitalEntityList;
    }

    public void deleteBatch(List<Long> recordIdList) {
        recordIdList.forEach(recordId -> this.delete(recordId));
    }

    public CapitalDto detail(Long recordId, Boolean origin) {
        CapitalEntity capitalEntity;
        if (origin) {
            CapitalEntity capitalEntityQuery = new CapitalEntity();
            capitalEntityQuery.setOriginId(recordId);
            QueryWrapper queryWrapper = new QueryWrapper<>(capitalEntityQuery);
            queryWrapper.ne("status", 3);
            capitalEntity = baseMapper.selectOne(queryWrapper);
        } else {
            capitalEntity = baseMapper.selectById(recordId);
        }
        CapitalDto capitalDto = new CapitalDto();
        if (null != capitalEntity) {
            BeanUtils.copyProperties(capitalEntity, capitalDto);
            if (capitalEntity.getSource().equals(LITTLE_BEE_CAPITAL)) {
                if (capitalEntity.getProductRebateInformation() != null && !capitalEntity.getProductRebateInformation().isEmpty()) {
                    List<LittleBeeProductRebateInformationEntity> littleBeeProductRebateInformationEntityList = littleBeeProductRebateInformationDao.selectBatchIds(capitalEntity.getProductRebateInformation());
                    if (littleBeeProductRebateInformationEntityList != null && littleBeeProductRebateInformationEntityList.size() != 0) {
                       capitalDto.setLittleBeeProductRebateInformation(littleBeeProductRebateInformationEntityList.stream().filter(littleBeeProductRebateInformationEntity -> !littleBeeProductRebateInformationEntity.getStatus().equals(3)).collect(Collectors.toList()));
                    }
                }
            } else if (capitalEntity.getSource().equals(NORMAL_CAPITAL)) {
                if (capitalEntity.getProductRebateInformation() != null && !capitalEntity.getProductRebateInformation().isEmpty()) {
                    List<ProductRebateInformationEntity> productRebateInformationEntityList = productRebateInformationDao.selectBatchIds(capitalEntity.getProductRebateInformation());
                    List<ProductRebateInformationBo> productRebateInformationBoList = new ArrayList<>(productRebateInformationEntityList.size());
                    productRebateInformationEntityList.forEach(productRebateInformationEntity -> {
                        ProductRebateInformationBo productRebateInformationBo = new ProductRebateInformationBo();
                        BeanUtils.copyProperties(productRebateInformationEntity, productRebateInformationBo);
                        productRebateInformationBoList.add(productRebateInformationBo);
                    });
                    if (productRebateInformationBoList != null && productRebateInformationBoList.size() != 0) {
                        capitalDto.setProductRebateInformation(
                                productRebateInformationBoList.stream().filter(productRebateInformationEntity -> !productRebateInformationEntity.getStatus().equals(3)).collect(Collectors.toList())
                        );
                    }
                }
            }
            if (capitalEntity.getOtherInformation() != null && !capitalEntity.getOtherInformation().isEmpty()) {
                capitalDto.setOtherInformation(otherInformationDao.selectBatchIds(capitalEntity.getOtherInformation()));
            }
            if (StringUtils.isNotEmpty(capitalEntity.getAttachment())) {
                capitalDto.setAttachmentUploadFileBo(GsonUtil.jsonToList(capitalEntity.getAttachment(), UploadFileBo.class));
            }
            return capitalDto;
        }
        return null;
    }

    public IPage<CapitalEntity> queryPage(PageForm<CapitalDto> pageForm, User user) {
        CapitalDto param = pageForm.getParam();
        CapitalEntity capitalEntity = new CapitalEntity();
        BeanUtils.copyProperties(param, capitalEntity);

        List investorList = this.findDepartmentsAndStaffsInfo(user);
        capitalEntity.setCapitalName(null);
        capitalEntity.setCapitalProvince(null);
        capitalEntity.setCapitalCity(null);
        capitalEntity.setDealerGroup(null);
        LambdaQueryWrapper<CapitalEntity> queryWrapper = new LambdaQueryWrapper<>(capitalEntity);
        queryWrapper.ne(CapitalEntity::getStatus, 3);
        if (investorList != null && investorList.size() != 0) {
            queryWrapper.in(CapitalEntity::getCreateUser, investorList);
        }
        queryWrapper.orderByDesc(CapitalEntity::getCreateTime);
        queryWrapper.like(StringUtils.isNotEmpty(param.getCapitalName()), CapitalEntity::getCapitalName, param.getCapitalName());
        queryWrapper.like(StringUtils.isNotEmpty(param.getCapitalProvince()), CapitalEntity::getCapitalProvince, param.getCapitalProvince());
        queryWrapper.like(StringUtils.isNotEmpty(param.getCapitalCity()), CapitalEntity::getCapitalCity, param.getCapitalCity());
        queryWrapper.like(StringUtils.isNotEmpty(param.getDealerGroup()), CapitalEntity::getDealerGroup, param.getDealerGroup());

        IPage page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page(pageForm.getCurrent(), pageForm.getSize());
        IPage<CapitalEntity> capitalEntityIPage = baseMapper.selectPage(page, queryWrapper);
        return capitalEntityIPage;
    }

    /**
     * 下载文件
     *
     * @param
     * @param response
     * @throws IOException
     */
    public void downloadFile(String fileName, String filePath, HttpServletResponse response) throws IOException {

        try {
            fileName = URLEncoder.encode(fileName, "UTF-8");
        } catch (Exception e) {
            log.error("context", e);
        }
        response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.addHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "utf-8"));
        ftpService.downloadFile(filePath, response.getOutputStream());
    }

    private Double divHundred(String value) {
        Double calValue;
        if (StringUtils.isNotEmpty(value)) {
            calValue = Double.parseDouble(value) / 100.0;
        } else {
            return null;
        }
        return calValue;
    }

    public Double calPerAnnumRate(Integer period, String value, String type) {
        switch (type) {
            case "total_rate_for_guests":
                return RATEUtils.simpleCalculateRate(period, 10000 * (1 + divHundred(value)) / period, -10000) * 12;
            case "annual_rates_for_guests":
                return RATEUtils.simpleCalculateRate(period, 10000 * (1 + divHundred(value) * period / 12) / period, -10000) * 12;
            case "monthly_rates_for_guests":
                return RATEUtils.simpleCalculateRate(period, 10000 * (1 + divHundred(value) * period) / period, -10000) * 12;
            case "coefficient_of_ten_thousand":
                if (StringUtils.isNotEmpty(value)) {
                    return RATEUtils.simpleCalculateRate(period, Double.parseDouble(value), -10000) * 12;
                }
            default:
                return null;
        }
    }

    public List<String> findDepartmentsAndStaffsInfo(User user) {
        UserAndDepartmentDto userAndDepartmentDto = dingDingService.getUserAndDepartmentInfoForSmart(user.getDingNo());
        if (userAndDepartmentDto.getRoleName().equals("总监") || userAndDepartmentDto.getRoleName().equals("副部长")) {
            return new ArrayList<>();
        } else if (viewAllUserList.contains(userAndDepartmentDto.getStaffNo())) {
            return new ArrayList<>();
        } else if (userAndDepartmentDto.getRoleName().equals("大区经理")) {
            List<DepartmentUserDto> departmentUserDtoList = dingDingService.getUsersByDepartmentId(userAndDepartmentDto.getDeptId());
            if (null != departmentUserDtoList) {
                return departmentUserDtoList.stream().map(departmentUserDto -> departmentUserDto.getName()).collect(Collectors.toList());
            }
        } else {
            List<String> investorList = new ArrayList<>();
            investorList.add(userAndDepartmentDto.getName());
            return investorList;
        }
        return null;
    }
}
