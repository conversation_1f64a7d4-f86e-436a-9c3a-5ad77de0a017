package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.gwmfc.domain.User;
import com.gwmfc.entity.CpcaautoCallRecordEntity;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.JsoupUtil;
import com.gwmfc.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @Classname NewEnergyCarDataCaptureService
 * @Description TODO
 * @Date 2025/1/13 11:43
 */
@Service
@Slf4j
@RefreshScope
public class NewEnergyCarDataCaptureService {

    @Value("${dataCapture.newEnergyCarDataUrl}")
    private String newEnergyCarDataUrl;

    @Resource
    private GlobalFormBusinessService globalFormBusinessService;

    @Resource
    private CpcaautoService cpcaautoService;

    public static final String READ_CONTENT = ".read_content";
    public static final String IMG = "img";

    @Resource
    private JsoupUtil jsoupUtil;

    public Result newEnergyCarDataCapture(String tableName, String date, User user){
        log.info("处理新能源汽车数据采集开始，处理表：{}，传入时间：{}",tableName,date);
        Result result = new Result();
        try{
            /**
             * 解析日期date得到指定的年月
             */
            String dateStr;
            String year;
            String month;
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            if(user == null || StringUtils.isEmpty(user.getUserName())){
                LocalDate localDate = LocalDate.parse(date,dateTimeFormatter);
                LocalDate localDateBefore = localDate.minusMonths(1);
                year = String.valueOf(localDateBefore.getYear());
                month = String.valueOf(localDateBefore.getMonth().getValue());

                if(localDateBefore.getMonth().getValue() < 10){
                    dateStr = year.concat("-0").concat(month);
                }else{
                    dateStr = year.concat("-").concat(month);
                }

            }else{
                dateStr = date;
                year = dateStr.substring(0,4);
                month = dateStr.substring(5,7);

                if(Integer.parseInt(month)<10){
                    month = month.substring(1,2);
                }
            }


            String dataDate = "data_date";
            if("new_energy_car_total_market".equals(tableName)){
                dataDate = "update_date";
            }
            Set<String> pageUrlSet = new HashSet();
            pageUrlSet.add(newEnergyCarDataUrl);

            /**
             * 拿到所有符合title关键字描述的页码对应的url
             */
            Document pageNumDocument = jsoupUtil.connect(newEnergyCarDataUrl);
            if(pageNumDocument.select(".pagebar").size()>0){
                Elements elements = pageNumDocument.select(".pagebar").get(1).select("a");
                elements.forEach(element -> {
                    if(StringUtils.isNotEmpty(element.attr("href"))){
                        pageUrlSet.add("http://www.cpcaauto.com/".concat(element.attr("href")));
                    }
                });
            }

            String articleNameTag = "";
            if("new_energy_car_total_market".equals(tableName)){
                articleNameTag = "-整体市场";
            }else if("new_energy_car_brand_category".equals(tableName)){
                articleNameTag = "-品牌大类";
            }else if("new_energy_car_price_range".equals(tableName)){
                articleNameTag = "-价格区间";
            }else if("new_energy_car_manufacturer_ranking".equals(tableName)){
                articleNameTag = "-厂商排名";
            }

            log.info("articleNameTag:{}",articleNameTag);
            for(String pageUrl : pageUrlSet){
                Document documentListPage = jsoupUtil.connect(pageUrl);
                Elements links = documentListPage.select(".list_d").select("ul").select("li");
                for(Element link : links){
                    boolean existFlag = link.select("a").text().contains(year.concat("年").concat(month).concat("月"));
                    if(existFlag){
                        log.info("存在该月数据，进行处理");
                        long dataCount = globalFormBusinessService.selectRecordCountByDate(tableName,dateStr,dataDate);
                        if(dataCount>0){
                            log.info("该月:{}数据已存在",dateStr);
                            result.setCode(FAIL.getCode());
                            result.setMessage("该月数据已存在");
                            return result;
                        }
                        DateTimeFormatter dateTimeFormatterCpcaauto = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

                        CpcaautoCallRecordEntity cpcaautoCallRecordEntity = new CpcaautoCallRecordEntity();
                        cpcaautoCallRecordEntity.setPageTitle((link.select("a").text().replaceAll("([　]|\\s|\\u00A0)+", ""))+articleNameTag);
                        cpcaautoCallRecordEntity.setPageUrl("http://www.cpcaauto.com/".concat(link.select("a").attr("href")));

                        List<String> picUrlList = new ArrayList();

                        String pageUrlStr = "http://www.cpcaauto.com/".concat(link.select("a").attr("href"));
                        Document document = jsoupUtil.connect(pageUrlStr);
                        Elements imgLinks = document.select(READ_CONTENT).select(IMG);
                        if(imgLinks.size()>0){
                            for (int i = 0; i < imgLinks.size(); i++) {
                                String imgUrl = "http://www.cpcaauto.com/".concat(imgLinks.get(i).attr("src"));
                                picUrlList.add(imgUrl);
                            }
                        }
                        cpcaautoCallRecordEntity.setPictureUrlList(GsonUtil.toJson(picUrlList));
                        cpcaautoCallRecordEntity.setCallTime(dateTimeFormatterCpcaauto.format(LocalDateTime.now()));

                        String userName;

                        if(user == null || StringUtils.isEmpty(user.getUserName())){
                            cpcaautoCallRecordEntity.setTriggerMethod(1);
                            cpcaautoCallRecordEntity.setCreateUser("Scheduled");
                            userName = "Scheduled";
                        }else{
                            cpcaautoCallRecordEntity.setTriggerMethod(2);
                            cpcaautoCallRecordEntity.setCreateUser(user.getUserName());
                            userName = user.getUserName();
                        }
                        cpcaautoCallRecordEntity.setYear(year);
                        cpcaautoCallRecordEntity.setMonth(Integer.parseInt(month)<10?"0"+month:month);
                        cpcaautoCallRecordEntity.setSource(tableName);
                        cpcaautoService.save(cpcaautoCallRecordEntity);
                        log.info("保存乘联会请求记录表成功");
                        cpcaautoService.analysisData(tableName, dateTimeFormatter.format(LocalDateTime.now()), userName, cpcaautoCallRecordEntity, "post-energy");
                    }
                }
            }

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("新能源汽车数据:{}采集异常",tableName,e);
            result.setCode(FAIL.getCode());
            result.setMessage("新能源汽车数据:"+tableName+"采集异常");
        }
        return result;
    }

    public void saveImageFromUrl(String imgUrl, String destinationFile) {
        try (InputStream in = new URL(imgUrl).openStream()) {
            Path destinationPath = Paths.get(destinationFile);
            Files.copy(in, destinationPath, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("图片已成功保存到: " + destinationPath);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("保存图片时发生错误: " + e.getMessage());
        }
    }
}
