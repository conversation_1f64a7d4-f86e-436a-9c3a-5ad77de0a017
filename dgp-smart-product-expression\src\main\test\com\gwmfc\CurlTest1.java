package com.gwmfc;

import com.alibaba.nacos.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.gwmfc.dao.MonthConsumptionIndexDao;
import com.gwmfc.dao.YearConsumptionIndexDao;
import com.gwmfc.entity.data.GrossDomesticProductEntity;
import com.gwmfc.entity.data.MonthConsumptionIndexEntity;
import com.gwmfc.entity.data.YearConsumptionIndexEntity;
import com.gwmfc.service.DingDingService;
import com.gwmfc.service.GlobalFormBusinessService;
import com.gwmfc.util.GsonUtil;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2023年08月16日 13:24
 */
@SpringBootTest
public class CurlTest1 {
    @Value("${dingding-notify-users}")
    private String dingdingNotifyUsers;
    @Resource
    private DingDingService dingDingService;
    @Resource
    private GlobalFormBusinessService globalFormBusinessService;
    @Resource
    private MonthConsumptionIndexDao monthConsumptionIndexDao;
    @Resource
    private YearConsumptionIndexDao yearConsumptionIndexDao;

    public static void main(String[] args) {
        String dateStr = "203203";
        dateStr = dateStr.substring(0, 4) + "-" + dateStr.substring(4);
        System.out.println(dateStr);
    }
    @Test
    public void curl() throws IOException {
        String[] command = {"curl", "-H", "User-Agent: curl/7.55.1", "-H", "Accept: */*", "http://10.16.16.224:8892/cpcatable_getdata?url=http://cpcaauto.com/admin/ewebeditor/uploadfile/20230609160624491.jpg"};

        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);

        Process process = processBuilder.start();

        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line;
        while ((line = reader.readLine()) != null) {
            System.out.println(line);
        }
    }

    public static final String LIST_D = ".list_d";
    public static final String LI = "li";
    public static final String SPAN = "span";
    public static final String A = "a";
    public static final String HREF = "href";
    public static final String SRC = "src";
    public static final String READ_CONTENT = ".read_content";
    public static final String IMG = "img";

    @Resource
    private RestTemplate restTemplate;

    @Test
    public void logisticsIndexDownload() throws IOException {
        String url = "http://www.clic.org.cn/search.jspx?q=制造业PMI数据";

        Document pageNumDocument = Jsoup.connect(url).get();
        String pageNumStr = pageNumDocument.select(".fy").select("span").get(0).text();
        Integer pageNum = Integer.parseInt(pageNumStr.substring(pageNumStr.indexOf("/") + 1, pageNumStr.lastIndexOf("页")));

        Map resMap = new HashMap<>();
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (int i = 1; i <= pageNum; i++) {
            System.out.println(i);
            String selUrl = "http://www.clic.org.cn/search_" + i + ".jspx?q=制造业PMI数据";
            Document documentListPage = Jsoup.connect(selUrl).get();
            Elements links = documentListPage.select(".lbbox").select("ul").select("li");
            for (Element link : links) {
                String childUrl = link.children().first().select("a").attr("href");
                Document childDocument = Jsoup.connect(childUrl).get();
                String picUrl = childDocument.select(".art-zw").select("img").attr("src");
            }
        }
        resMap.forEach((k, v) -> {
            Map map = new HashMap<>();
            map.put("data_date", k);
            map.put("commerce_logistics_index_val", v);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            map.put("create_time", Long.parseLong(dateTimeFormatter.format(LocalDateTime.now())));
            dataList.add(map);
        });


        Set<String> columnSet = new HashSet<>();
        columnSet.add("data_date");
        columnSet.add("commerce_logistics_index_val");
        columnSet.add("create_time");

//        globalFormBusinessService.batchAddData("commerce_logistics_index", columnSet, dataList);
        System.out.println();
    }

    @Test
    public void usedCarDownload() throws IOException {
        String url = "http://www.cpcaauto.com/search.php?types=search&page=1&keywords=二手车市场深度分析";

        Document pageNumDocument = Jsoup.connect(url).get();
        Elements elements = pageNumDocument.select(".pagebar").get(1).select("a");
        List pageUrlList = new ArrayList<>();
        elements.forEach(element -> {
            if (StringUtils.isNotEmpty(element.attr("href"))) {
                pageUrlList.add(element.attr("href"));
            }
        });

        pageUrlList.forEach(pageUrl -> {
            Document documentListPage = null;
            try {
                documentListPage = Jsoup.connect("http://www.cpcaauto.com/".concat(String.valueOf(pageUrl))).get();
                Elements links = documentListPage.select(".list_d").select("ul").select("li");
                List picUrlList = new ArrayList<>();
                for (Element link : links) {
                    String childPageUrl = link.select("a").attr("href");
                    Jsoup.connect("http://www.cpcaauto.com/".concat(String.valueOf(childPageUrl))).get();
                    Elements elements1 = Jsoup.connect("http://www.cpcaauto.com/".concat(String.valueOf(childPageUrl))).get().select(".text").select("img");
                    String picUrl = elements1.select("img").attr("src");
                    picUrlList.add(picUrl);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }

    @Test
    public void carMarketDownload() throws IOException {
        String url = "http://www.cpcaauto.com/search.php?types=search&page=1&keywords=【周度分析】车市扫描";

        Document pageNumDocument = Jsoup.connect(url).get();
        Elements elements = pageNumDocument.select(".pagebar").get(1).select("a");
        List pageUrlList = new ArrayList<>();
        elements.forEach(element -> {
            if (StringUtils.isNotEmpty(element.attr("href"))) {
                pageUrlList.add(element.attr("href"));
            }
        });
        pageUrlList.forEach(pageUrl -> {
            Document documentListPage = null;
            try {
                documentListPage = Jsoup.connect("http://www.cpcaauto.com/".concat(String.valueOf(pageUrl))).get();
                Elements links = documentListPage.select(".list_d").select("ul").select("li");
        List picUrlList = new ArrayList<>(links.size());
                for (Element link : links) {
                    String childPageUrl = link.select("a").attr("href");
                    Jsoup.connect("http://www.cpcaauto.com/".concat(String.valueOf(childPageUrl))).get();
                    Elements elements1 = Jsoup.connect("http://www.cpcaauto.com/".concat(String.valueOf(childPageUrl))).get().select(".text").select("img");
                    String picUrl = elements1.select("img").attr("src");
                    picUrlList.add(picUrl);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }

    @Test
    public void passengerCarMarketDownload() throws IOException {
        String url = "http://www.cpcaauto.com/search.php?types=search&page=1&keywords=全国乘用车市场分析";

        Document pageNumDocument = Jsoup.connect(url).get();
        Elements elements = pageNumDocument.select(".pagebar").get(1).select("a");
        Set pageUrlSet = new HashSet();
        pageUrlSet.add(url);
        elements.forEach(element -> {
            if (StringUtils.isNotEmpty(element.attr("href"))) {
                pageUrlSet.add(element.attr("href"));
            }
        });

        pageUrlSet.forEach(pageUrl -> {
            Document documentListPage = null;
            try {
                documentListPage = Jsoup.connect(String.valueOf(pageUrl)).get();
                Elements links = documentListPage.select(".list_d").select("ul").select("li");
                List picUrlList = new ArrayList<>(links.size());
                for (Element link : links) {
                    String childPageUrl = link.select("a").attr("href");
                    Jsoup.connect("http://www.cpcaauto.com/".concat(String.valueOf(childPageUrl))).get();
                    Elements elements1 = Jsoup.connect("http://www.cpcaauto.com/".concat(String.valueOf(childPageUrl))).get().select(".text").select("img");
                    String picUrl = elements1.select("img").attr("src");
                    picUrlList.add(picUrl);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }
}