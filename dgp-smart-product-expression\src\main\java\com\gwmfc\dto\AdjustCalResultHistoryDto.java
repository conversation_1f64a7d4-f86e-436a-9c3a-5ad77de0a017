package com.gwmfc.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024年01月31日 15:50
 */
@Data
@ApiModel(value = "irr结果调整")
public class AdjustCalResultHistoryDto {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("提案id")
    private Long productProposalId;

    @ApiModelProperty("保存次序时间戳")
    private String currentTimeSeqNo;

    @ApiModelProperty("品牌")
    private String brand;

    @ApiModelProperty("贷额")
    private Double loanAmount;
    @ApiModelProperty("贷期")
    private Double paymentDays;

    @ApiModelProperty("客户利率（%）")
    private Double customerInterestRate;
    @ApiModelProperty("结算利率（%）")
    private Double settledRate;
    @ApiModelProperty("还款方式(后期改枚举)")// TODO: 2023/10/16  后期改枚举
    private Integer repaymentMode;
    @ApiModelProperty("还款方式说明")
    private String repaymentModeExplain;
    @ApiModelProperty("合同占比（%）")
    private Double contractsProportion;
    @ApiModelProperty("车辆类型")
    private String vehicleType;
    //风险
    @ApiModelProperty("风险损失率（%）")
    private Double riskLossRate;

    @ApiModelProperty("基础服务费（%）")
    private Double baseCommissionRatio;
    @ApiModelProperty("阶梯奖金比例（%）")
    private Double ladderBonusRatio;
    @ApiModelProperty("促销奖金比例（%）")
    private Double promotionBonusProportion;
    @ApiModelProperty("个人奖励")
    private String personalReward;

    //政策
    @ApiModelProperty("IRR")
    private Double irr;
    @ApiModelProperty("ROA")
    private Double roa;
    @ApiModelProperty("净利润")
    private Double netMargin;
    @ApiModelProperty("万元收益")
    private Double thousandIncome;
    @ApiModelProperty("加权IRR")
    private Double weightedIrr;

    //基础信息
    @ApiModelProperty("调整原因")
    private String changeReason;

    @ApiModelProperty("调整人员")
    private String changeUser;

    @ApiModelProperty("调整时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime changeTime;

    @ApiModelProperty("是否考虑提前结清")
    private Integer considerEarlySquare;

    @ApiModelProperty("是否联合贷")
    private Integer isUnionLoan;

    @ApiModelProperty("是否农户贷")
    private Integer isFarmerLoan;

    @ApiModelProperty("默认产品分类")
    private String productClassification;

    @ApiModelProperty("默认融资类型")
    private String financingType;

    @ApiModelProperty("提前结清概率分类")
    private String earlySquareProbabilityClassification;

    @ApiModelProperty("代理商类型")
    private String agentType;

    @ApiModelProperty("赛道")
    private String track;

    @ApiModelProperty("是否总对总")
    private Integer isCorpToCorp;

    @ApiModelProperty("提前结清参数附加")
    private ProductCalEarlySquareParamSubDto productCalEarlySquareParamSubDto;

    @ApiModelProperty("联合贷参数")
    private ProductCalUnionLoanParamDto productCalUnionLoanParamDto;
}
