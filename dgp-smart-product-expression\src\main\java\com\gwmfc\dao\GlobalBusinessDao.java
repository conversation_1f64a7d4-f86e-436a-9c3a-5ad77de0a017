package com.gwmfc.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwmfc.dto.GlobalFormBusinessDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 通用业务处理DAO
 * <AUTHOR>
 * @date 2023/2/16
 */
@Mapper
public interface GlobalBusinessDao {

    /**
     * 查询数据总条数
     * @param tableName
     * @param dataDate
     * @return
     */
    Long selectTotalCountByTableName(String tableName, String dataDate);

    /**
     * 分页查询
     * @param current
     * @param size
     * @param queryDto
     * @return
     */
    List<Map<String, Object>> selectRecordByPage(@Param("current") Integer current, @Param("size") Integer size, @Param("queryDto") GlobalFormBusinessDto queryDto);

    /**
     * 分页查询
     * @param queryDto
     * @return
     */
    Long selectRecordByCount(GlobalFormBusinessDto queryDto);

    /**
     * 分页查询
     * @param
     * @param size
     * @param queryDto
     * @param id
     * @return
     */
    List<Map<String, Object>> selectRecordByPageCursor(Integer size, GlobalFormBusinessDto queryDto, Long id);

    /**
     * 查询条数
     * @param globalFormBusinessDto
     * @return
     */
    Long selectRecordCount(GlobalFormBusinessDto globalFormBusinessDto);

    /**
     * 新增数据
     * @param paramsMap
     */
    void addData(Map<String, Object> paramsMap);

    /**
     * 批量新增数据
     * @param tableName
     * @param columnSet
     * @param paramsMapList
     */
    void batchAddData(String tableName, Set<String> columnSet, List<Map<String, Object>> paramsMapList);

    /**
     * 更新数据
     * @param paramsMap
     */
    void updateData(Map<String, Object> paramsMap);

    /**
     * 根据id查询
     * @param tableName
     * @param id
     * @return
     */
    Map<String, Object> queryById(String tableName, Long id);

    /**
     * 删除
     * @param tableName
     * @param id
     * @return
     */
    void delete(String tableName, Long id);

    void deleteBatch(String tableName, List<Long> recordIdList);

    Integer deleteByCreateTime(String tableName, String batch);

    Integer sumBatchNum(String tableName, String batch);

    List<String> selectGroupValueByField(String tableName, String name);

    void truncateTable(String tableName);

    long selectRecordCountByDate(String tableName, String dateStr, String dataDate);
}
