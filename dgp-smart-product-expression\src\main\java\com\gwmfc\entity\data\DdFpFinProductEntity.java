package com.gwmfc.entity.data;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023年10月11日 16:07
 * @Description 产品列表, 放入data目录下, 方便数据加工查找对应关系
 */
@Data
@ApiModel(value = "产品列表")
@TableName("dd_fp_fin_product")
public class DdFpFinProductEntity {

    @ApiModelProperty("金融产品ID")
    private String finProductId;

    @ApiModelProperty(value = "源系统")
    private String srcSys;

    @ApiModelProperty("金融产品编码")
    private String finProductCode;

    @ApiModelProperty(value = "金融产品名称")
    private String finProductName;

    @ApiModelProperty("金融产品组ID")
    private String finProductGroupId;

    @ApiModelProperty("金融产品组编码")
    private String finProductGroupCode;

    @ApiModelProperty("金融产品组名称")
    private String finProductGroupName;

    @ApiModelProperty("金融产品类型")
    private String finProductType;

    @ApiModelProperty("利率计算方式代码")
    private String rateCalType;

    @ApiModelProperty("利率计算方式描述")
    private String rateCalTypeDsc;

    @ApiModelProperty("付款频率代码")
    private String payFreq;

    @ApiModelProperty("付款频率描述")
    private String payFreqDsc;

    @ApiModelProperty("利率方式代码")
    private String rateType;

    @ApiModelProperty("利率方式描述")
    private String rateTypeDsc;

    @ApiModelProperty("最大期数")
    private Integer maxTerm;

    @ApiModelProperty("最小期数")
    private Integer minTerm;

    @ApiModelProperty("最大利率")
    private BigDecimal maxRate;

    @ApiModelProperty("最小利率")
    private BigDecimal minRate;

    @ApiModelProperty("最低首付比例")
    private BigDecimal minPayPct;

    @ApiModelProperty("尾款比例")
    private BigDecimal tailPayPct;

    @ApiModelProperty("最小贷款额")
    private BigDecimal minLoanLimit;

    @ApiModelProperty("最大贷款额")
    private BigDecimal maxLoanLimit;

    @ApiModelProperty("是否支持附加贷")
    private String ifSupAdd;

    @ApiModelProperty("佣金比率")
    private BigDecimal commissionPct;

    @ApiModelProperty("期限范围")
    private String termRange;

    @ApiModelProperty("首付比例范围")
    private String firstPayRange;

    @ApiModelProperty("尾款比例范围")
    private String tailPayRange;

    @ApiModelProperty("产品开始日期")
    private String proBeginDate;

    @ApiModelProperty("产品结束日期")
    private String proEndDate;

    @ApiModelProperty("结构化还款期数")
    private Integer structuredmonth;

    @ApiModelProperty("结构化还款类型")
    private String structuredtype;

    @ApiModelProperty("结构化还款金额")
    private BigDecimal structuredmoney;

    @ApiModelProperty("状态编码")
    private String statusCde;

    @ApiModelProperty("状态描述")
    @TableField(value = "status_dsc", whereStrategy = FieldStrategy.NOT_NULL)
    private String statusDsc;

    @ApiModelProperty("是否机构贷产品")
    private String isOrgan;

    @ApiModelProperty("是否产品模板")
    private String isModel;

    @ApiModelProperty("BOSS车辆类型")
    private String bossCarType;

    @ApiModelProperty("ACS车辆类型")
    private String acsCarType;

    @ApiModelProperty("厂商贴息比例")
    private BigDecimal dealerDiscountPct;

    @ApiModelProperty("厂商最高贴息额")
    private BigDecimal dealerDiscountAmount;
}

