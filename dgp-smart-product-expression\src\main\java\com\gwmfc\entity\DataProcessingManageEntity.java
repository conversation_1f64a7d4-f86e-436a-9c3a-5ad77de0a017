package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Classname DataProcessingManage
 * @Description 数据加工管理
 * @Date 2023/11/20
 * <AUTHOR> zhangke
 */
@Data
@ApiModel(value = "数据加工管理")
@TableName("data_processing_manage")
public class DataProcessingManageEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    @TableFieldMapping("数据名称")
    private String dataName;
    @TableFieldMapping("数据名称id")
    private Long dataId;
    @TableFieldMapping("数据名称对应表名")
    private String tableName;
    @TableFieldMapping("源字段")
    private String sourceColumn;
    @TableFieldMapping("源字段中文名称")
    private String sourceColumnName;
    @TableFieldMapping("映射后字段")
    private String mappingColumn;
    @TableFieldMapping("映射后字段中文名称")
    private String mappingColumnName;
    @TableFieldMapping(value = "mapping_label", comment = "映射后标签")
    private String mappingLabel;
    @TableFieldMapping("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @TableFieldMapping("创建人")
    private String createUser;
    @TableFieldMapping(value = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    @TableFieldMapping("修改人")
    private String updateUser;
    @TableFieldMapping(value = "keyword", comment = "关键字")
    private String keyword;
}