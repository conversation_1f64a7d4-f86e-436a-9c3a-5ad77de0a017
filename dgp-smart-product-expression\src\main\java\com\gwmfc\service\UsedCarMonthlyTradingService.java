package com.gwmfc.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.dao.UsedCarMonthlyTradingDao;
import com.gwmfc.dto.UsedCarQueryDto;
import com.gwmfc.entity.data.UsedCarMonthlyTradingEntity;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.util.StatusCodeEnum;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-10-31 09:57
 */
@Slf4j
@Service
public class UsedCarMonthlyTradingService extends ServiceImpl<UsedCarMonthlyTradingDao, UsedCarMonthlyTradingEntity> {

    /**
     * 分页查询
     */
    public IPage<UsedCarMonthlyTradingEntity> page(UsedCarQueryDto query, Integer current, Integer size) {
        IPage<UsedCarMonthlyTradingEntity> pageParam = new Page<>(current, size);
        return baseMapper.selectUsedCarMonthlyTradingPageVo(pageParam, query.getStartDate(), query.getEndDate(), query.getType());
    }

    /**
     * 保存
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSave(List<UsedCarMonthlyTradingEntity> list) {
        log.info("batchSave: UsedCarMonthlyTradingEntity.size:{}", list.size());
        if (!CollectionUtils.isEmpty(list)) {
            return this.saveBatch(list);
        } else {
            throw new SystemRuntimeException(StatusCodeEnum.SUCCESS.getCode(), "无新增数据");
        }
    }

    /**
     * 判断是否有重复数据
     */
    public Boolean dataExist(UsedCarMonthlyTradingEntity usedCarMonthlyTradingEntity) {
        UsedCarMonthlyTradingEntity monthlyTradingEntity = new UsedCarMonthlyTradingEntity();
        monthlyTradingEntity.setDataDate(usedCarMonthlyTradingEntity.getDataDate());
        monthlyTradingEntity.setType(usedCarMonthlyTradingEntity.getType());
        monthlyTradingEntity.setRegion(usedCarMonthlyTradingEntity.getRegion());
        monthlyTradingEntity.setUsefulLife(usedCarMonthlyTradingEntity.getUsefulLife());
        monthlyTradingEntity.setCarType(usedCarMonthlyTradingEntity.getCarType());
        monthlyTradingEntity.setVehicleType(usedCarMonthlyTradingEntity.getVehicleType());
        monthlyTradingEntity.setTradingVolume(usedCarMonthlyTradingEntity.getTradingVolume());
        monthlyTradingEntity.setTradingProportion(usedCarMonthlyTradingEntity.getTradingProportion());
        monthlyTradingEntity.setTransferRate(usedCarMonthlyTradingEntity.getTransferRate());
        QueryWrapper<UsedCarMonthlyTradingEntity> wrapper = new QueryWrapper<>(monthlyTradingEntity);
        if (baseMapper.selectCount(wrapper) > 0) {
            return false;
        }
        return true;
    }

    /**
     * 查询单个数据
     */
    public UsedCarMonthlyTradingEntity getOne(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 更新
     */
    public Integer update(UsedCarMonthlyTradingEntity usedCarMonthlyTradingEntity) {
        return baseMapper.updateById(usedCarMonthlyTradingEntity);
    }


    /**
     * 删除
     *
     * @param id
     */
    public Integer remove(Long id) {
        return baseMapper.deleteById(id);
    }
}
