package com.gwmfc.schedule;

import com.gwmfc.service.DomesticPriceIndexService;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024年03月22日 11:46
 */
@Component
@Slf4j
public class YearDomesticPriceIndexSchedule {
    @Resource
    private DomesticPriceIndexService domesticPriceIndexService;

    @Scheduled(cron = "0 0 0 * * ?")
    @SchedulerLock(name = "catchDomesticPriceIndexYear", lockAtMostFor = "PT1H", lockAtLeastFor = "PT1H")
    public void catchDomesticPriceIndex() {
        domesticPriceIndexService.catchDomesticPriceIndex(1,"year_consumption_index", "Scheduled");
        log.info("catchYearDomesticPriceIndex:{}","Scheduled");
    }
}
