package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023年08月16日 14:39
 *
 *    {
 *       "cumulativemonth": "5",
 *       "dataonimage": "2023年5月",
 *       "datatype": "厂商批发",
 *       "firm": "比亚迪汽车",
 *       "isaddup": "0",
 *       "month": "5",
 *       "quotient": "11.9%",
 *       "ranking": "1",
 *       "sale": "239,092",
 *       "year": "2023",
 *       "yoy": "109.4%"
 *     },
 */
@Data
@TableName("cpcaautod")
@ExcelIgnoreUnannotated
public class CpcaautoEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("结束月份")
    @TableFieldMapping(value = "cumulative_month", comment = "结束月份", queryItem = true)
    private String cumulativeMonth;

    @ExcelProperty("销量排名")
    @TableFieldMapping(value = "ranking", comment = "销量排名")
    private String ranking;

    @ExcelProperty("销量")
    @TableFieldMapping(value = "sale", comment = "销量")
    private String sale;

    @ExcelProperty("年份")
    @TableFieldMapping(value = "year", comment = "年份")
    private String year;

    @ExcelProperty("初始月份")
    @TableFieldMapping(value = "month", comment = "初始月份")
    private String month;

    @ExcelProperty("同比")
    @TableFieldMapping(value = "yoy", comment = "同比")
    private String yoy;

    @ExcelProperty("份额")
    @TableFieldMapping(value = "quotient", comment = "份额")
    private String quotient;

    @ExcelProperty("图片标题显示日期")
    @TableFieldMapping(value = "date_on_image", comment = "图片标题显示日期")
    private String dateOnImage;

    @ExcelProperty("是否累计") @TableFieldEnumMapping
    @TableFieldMapping(value = "add_up_or_not", comment = "是否累计", queryItem = true)
    private String addUpOrNot;

    @ExcelProperty("数据类型")
    @TableFieldMapping(value = "datatype", comment = "数据类型")
    private String datatype;

    @ExcelProperty("公司/车型")
    @TableFieldMapping(value = "company", comment = "公司/车型", queryItem = true)
    private String company;

    @ExcelProperty("图片地址")
    @TableFieldMapping(value = "url", comment = "图片地址")
    private String url;
}
