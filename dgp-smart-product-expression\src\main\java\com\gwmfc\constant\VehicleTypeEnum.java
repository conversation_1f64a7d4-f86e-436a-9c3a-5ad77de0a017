package com.gwmfc.constant;

import com.gwmfc.exception.SystemRuntimeException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * @Date: 2023/11/30
 * @Author: zhang<PERSON>yu
 */

@Getter
@AllArgsConstructor
public enum VehicleTypeEnum {

    COMMERCIAL_VEHICLE_SALES(1, "商用车数据", VehicleSalesTableEnum.COMMERCIAL_VEHICLE_SALES),
    TRUCK_SALES(2, "卡车销量", VehicleSalesTableEnum.TRUCK_SALES),
    NEW_ENERGY_VEHICLE_SALES(3, "新能源销量", VehicleSalesTableEnum.NEW_ENERGY_VEHICLE_SALES),
    PASSENGER_BUS_MANUFACTURER_SALES(4, "客车厂商销量", VehicleSalesTableEnum.PASSENGER_BUS_MANUFACTURER_SALES),
    TRUCK_MANUFACTURER_SALES(5, "卡车厂商销量", VehicleSalesTableEnum.TRUCK_MANUFACTURER_SALES),
    ;

    /**
     * 类型
     */
    private final Integer vehicleType;
    /**
     * 名称
     */
    private final String vehicleName;
    /**
     * excel表头及查询字段枚举类
     */
    private final VehicleSalesTableEnum vehicleSalesTableEnum;

    /**
     * 根据类型查询导出文件名
     *
     * @param vehicleType
     * @return
     */
    public static String getVehicleNameByType(Integer vehicleType) {
        for (VehicleTypeEnum value : VehicleTypeEnum.values()) {
            if (value.getVehicleType().equals(vehicleType)) {
                return value.getVehicleName();
            }
        }
        throw new SystemRuntimeException("类型不存在");
    }

    /**
     * 根据类型查询枚举类
     * @param headMap
     * @param vehicleType
     * @return
     */
    public static void getVehicleSalesTableEnumByType(Map<String, String> headMap, Integer vehicleType) {
        for (VehicleTypeEnum value : VehicleTypeEnum.values()) {
            if (value.getVehicleType().equals(vehicleType)) {
                value.getVehicleSalesTableEnum().getHeader(headMap);
            }
        }
    }
}
