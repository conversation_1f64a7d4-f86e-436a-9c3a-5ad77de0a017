package com.gwmfc.dto;

import com.gwmfc.domain.User;
import com.gwmfc.entity.CompeteInfoAfcMustEntity;
import com.gwmfc.entity.CompeteInfoAfcOptionalEntity;
import com.gwmfc.entity.CompeteInfoBankEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname CompeteInfoQueryDto
 * @Description TODO
 * @Date 2024/12/11 17:05
 */
@Data
@ApiModel(value = "竞对信息查询dto")
public class CompeteInfoQueryDto {
    @ApiModelProperty("数据类型 bank afcMust afcOptional")
    private String type;

    private CompeteInfoBankEntity competeInfoBankEntity;

    @ApiModelProperty("AFC必填竞对信息")
    private CompeteInfoAfcMustEntity competeInfoAfcMustEntity;

    @ApiModelProperty("AFC选填竞对信息")
    private CompeteInfoAfcOptionalEntity competeInfoAfcOptionalEntity;

    @ApiModelProperty("用户信息")
    private User user;
}
