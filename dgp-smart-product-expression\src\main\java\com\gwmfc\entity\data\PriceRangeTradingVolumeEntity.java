package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年03月19日 14:21
 */
@Data
@TableName("price_range_trading_volume")
@ExcelIgnoreUnannotated
public class PriceRangeTradingVolumeEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("更新月份") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "update_date", comment = "更新月份")
    private String updateDate;

    @ExcelProperty("价格区间")
    @TableFieldMapping(value = "price_range", comment = "价格区间")
    private String priceRange;

    @ExcelProperty("交易量占比")
    @TableFieldMapping(value = "trading_volume_percentage", comment = "交易量占比")
    private String tradingVolumePercentage;

    @ExcelProperty("图片地址")
    @TableFieldMapping(value = "picture_url", comment = "图片地址")
    private String pictureUrl;
}
