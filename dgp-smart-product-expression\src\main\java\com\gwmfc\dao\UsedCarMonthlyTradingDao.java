package com.gwmfc.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.entity.data.UsedCarMonthlyTradingEntity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface UsedCarMonthlyTradingDao extends BaseMapper<UsedCarMonthlyTradingEntity>{

    IPage<UsedCarMonthlyTradingEntity> selectUsedCarMonthlyTradingPageVo(IPage<?> page, String startDate, String endDate, Integer type);
}
