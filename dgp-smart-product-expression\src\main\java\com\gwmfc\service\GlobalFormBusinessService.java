package com.gwmfc.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwmfc.dao.GlobalBusinessDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.GlobalFormBusinessDto;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.TableUtils;
import com.gwmfc.vo.TableFieldDetailVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 通用表单业务操作类
 * <AUTHOR>
 * @date 2023/2/16
 */
@Service
public class GlobalFormBusinessService {
    private static final String CREATE_TIME = "create_time";
    private static final String CREATE_USER = "create_user";
    private static final String UPDATE_TIME = "update_time";
    private static final String UPDATE_USER = "update_user";

    @Resource
    private GlobalBusinessDao globalBusinessDao;


    /**
     * 查询总数
     * @param queryDto
     * @return
     */
    public long selectRecordCount(GlobalFormBusinessDto queryDto) {
        return globalBusinessDao.selectRecordCount(queryDto);
    }

    /**
     * 查询总数
     * @param
     * @return
     */
    public long selectRecordCountByDate(String tableName, String dateStr, String dataDate) {
        return globalBusinessDao.selectRecordCountByDate(tableName, dateStr, dataDate);
    }

    /**
     * 新增
     * @param saveDto
     * @param user
     */
    public void add(GlobalFormBusinessDto saveDto, User user) {
        saveDto.getConditionParams().put(CREATE_USER, user.getName());
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        saveDto.getConditionParams().put(CREATE_TIME, dateTimeFormatter.format(LocalDateTime.now()));
        globalBusinessDao.addData(buildParamMap(saveDto));
    }

    /**
     * 新增
     * @param columnSet
     * @param paramsMapList
     */
    public void batchAddData(String tableName, Set<String> columnSet, List<Map<String, Object>> paramsMapList) {
        globalBusinessDao.batchAddData(tableName, columnSet, paramsMapList);
    }

    /**
     * 更新
     * @param saveDto
     * @param user
     */
    public void update(GlobalFormBusinessDto saveDto, User user) {
        saveDto.getConditionParams().put(UPDATE_USER, user.getName());
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        saveDto.getConditionParams().put(UPDATE_TIME, dateTimeFormatter.format(LocalDateTime.now()));
        globalBusinessDao.updateData(buildParamMap(saveDto));
    }

    /**
     * 构建参数
     * @param saveDto
     * @return
     */
    private Map<String, Object> buildParamMap(GlobalFormBusinessDto saveDto) {
        Map<String, Object> paramsMap = new HashMap<>(2);
        paramsMap.put("tableName", saveDto.getTableName());
        paramsMap.put("columnMap", saveDto.getConditionParams());
        return paramsMap;
    }

    /**
     * 删除
     * @param tableName
     * @param id
     */
    public void delete(String tableName, Long id) {
        globalBusinessDao.delete(tableName, id);
    }

    public IPage<Map<String, Object>> listByPage(PageForm<GlobalFormBusinessDto> pageForm) throws IOException {
        GlobalFormBusinessDto queryDto = pageForm.getParam();

        Set conditionSet = queryDto.getConditionParams().keySet();
        Map<String, TableFieldDetailVo> tableFieldMappings = new TableUtils().findTableBusFieldDetailMappings(pageForm.getParam().getTableName());
        tableFieldMappings.forEach((s, tableFieldDetailVo) -> {
           if (conditionSet.contains(s)) {
               TableFieldDetailVo fieldDetailVo = tableFieldMappings.get(s);
               if (fieldDetailVo != null && fieldDetailVo.getEnumValue() != null) {
                   Object value = queryDto.getConditionParams().get(s);
                   queryDto.getConditionParams().remove(s);
                   queryDto.getConditionParams().put("@".concat(s),value);
               }
           }
        });

        IPage<Map<String, Object>> res = new Page<>();
        res.setRecords(globalBusinessDao.selectRecordByPage((pageForm.getCurrent()-1)*pageForm.getSize(), pageForm.getSize(), queryDto));
        res.setTotal(globalBusinessDao.selectRecordByCount(queryDto));
        return res;
    }

    public List<Map<String, Object>> listByPageCursor(PageForm<GlobalFormBusinessDto> pageForm, Long id) throws IOException {
        GlobalFormBusinessDto queryDto = pageForm.getParam();

        Set conditionSet = queryDto.getConditionParams().keySet();
        Map<String, TableFieldDetailVo> tableFieldMappings = new TableUtils().findTableBusFieldDetailMappings(pageForm.getParam().getTableName());
        tableFieldMappings.forEach((s, tableFieldDetailVo) -> {
            if (conditionSet.contains(s)) {
                TableFieldDetailVo fieldDetailVo = tableFieldMappings.get(s);
                if (fieldDetailVo != null && fieldDetailVo.getEnumValue() != null) {
                    Object value = queryDto.getConditionParams().get(s);
                    queryDto.getConditionParams().remove(s);
                    queryDto.getConditionParams().put("@".concat(s),value);
                }
            }
        });
        return globalBusinessDao.selectRecordByPageCursor(pageForm.getSize(), queryDto, id);
    }

    /**
     * 批量删除
     * @param tableName
     * @param recordIdList
     */
    public void deleteBatch(String tableName, List<Long> recordIdList) {
        globalBusinessDao.deleteBatch(tableName, recordIdList);
    }

    public Integer deleteByCreateTime(String tableName, String batch) {
        return globalBusinessDao.deleteByCreateTime(tableName, batch);
    }

    public Integer sumBatchNum(String tableName, String batch) {
        return globalBusinessDao.sumBatchNum(tableName, batch);
    }

    public List<String> selectGroupValueByField(String tableName, String name) {
        return globalBusinessDao.selectGroupValueByField(tableName, name);
    }

    public void truncateTable(String tableName) {
        globalBusinessDao.truncateTable(tableName);
    }

}
