package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname CompeteInfoAfcMustEntity
 * @Description TODO
 * @Date 2024/12/11 14:14
 */
@Data
@TableName("compete_info_afc_must")
public class CompeteInfoAfcMustEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "batch_id")
    @ApiModelProperty("批次id")
    private Long batchId;

    @TableField(value = "car_finance")
    @ApiModelProperty("汽金")
    private String carFinance;

    @TableField(value = "product_name")
    @ApiModelProperty("产品名称")
    private String productName;

    @TableField(value = "interest_rate")
    @ApiModelProperty("利率")
    private String interestRate;

    @TableField(value = "loan_term")
    @ApiModelProperty("期数")
    private String loanTerm;

    @TableField(value = "rebate")
    @ApiModelProperty("返利")
    private String rebate;

    @TableField(value = "other_subsidy")
    @ApiModelProperty("其他补贴")
    private String otherSubsidy;

    @TableField(value = "staff_no")
    @ApiModelProperty("工号")
    private String staffNo;

    @TableField("create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField("create_user")
    @ApiModelProperty("创建用户")
    private String createUser;

    @TableField("update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @TableField("update_user")
    @ApiModelProperty("更新用户")
    private String updateUser;
}
