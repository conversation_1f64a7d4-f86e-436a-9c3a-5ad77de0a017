package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalculateEarlySquareDto
 * @Description TODO
 * @Date 2023/12/4 15:05
 */
@Data
@ApiModel(value = "提前结清产品测算dto")
public class ProductCalculateEarlySquareDto{
    @ApiModelProperty("期限")
    private Integer timeLimit;
    @ApiModelProperty("提前结清期次")
    private Integer earlySquareTimeLimit;
}
