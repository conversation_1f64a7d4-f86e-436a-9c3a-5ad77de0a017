<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.ProductCalDao">

    <!-- 查询放款信息 -->
    <select id="getLoanHandCharge" resultType="com.gwmfc.entity.ProductCalLoanHandChargeEntity">
        select * from product_cal_loan_hand_charge where (#{actualLoanMoney}&lt;=range_high or range_high is null) and #{actualLoanMoney}&gt;range_low and `year` = (select max(`year`) from product_cal_loan_hand_charge)
    </select>

    <!-- 查询扣款信息 -->
    <select id="getDeductMoneyHandCharge" resultType="com.gwmfc.entity.ProductCalDeductMoneyHandChargeEntity">
        select * from product_cal_deduct_money_hand_charge where (#{actualLoanMoney}&lt;=range_high or range_high is null) and #{actualLoanMoney}&gt;range_low and `year` = (select max(`year`) from product_cal_deduct_money_hand_charge)
    </select>

    <!-- 查询放款手续费表详情 -->
    <select id="getLoanHandChargeDetail" resultType="com.gwmfc.entity.ProductCalLoanHandChargeEntity">
        select * from product_cal_loan_hand_charge where `year` = (select max(`year`) from product_cal_loan_hand_charge) order by range_low
    </select>

    <!-- 查询扣款手续费表详情 -->
    <select id="getDeductMoneyHandChargeDetail" resultType="com.gwmfc.entity.ProductCalDeductMoneyHandChargeEntity">
        select * from product_cal_deduct_money_hand_charge where `year` = (select max(`year`) from product_cal_deduct_money_hand_charge) order by range_low
    </select>
</mapper>