package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductCalDataExportEntity
 * @Description product_cal_data_export
 * @Date 2024/1/31 13:07
 */
@Data
@TableName("product_cal_data_export")
public class ProductCalDataExportEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "export_code", comment = "导出编码")
    private String exportCode;

    @TableFieldMapping(value = "export_name", comment = "导出名称")
    private String exportName;

    @TableFieldMapping(value = "export_class", comment = "导出实体类")
    private String exportClass;

    @TableFieldMapping(value = "export_desc", comment = "导出描述")
    private String exportDesc;

    @TableFieldMapping(value = "create_time", comment = "创建时间")
    private Date createTime;

    @TableFieldMapping(value = "create_user", comment = "创建用户")
    private String createUser;

    @TableFieldMapping(value = "update_time", comment = "更新时间")
    private Date updateTime;

    @TableFieldMapping(value = "update_user", comment = "更新用户")
    private String updateUser;
}
