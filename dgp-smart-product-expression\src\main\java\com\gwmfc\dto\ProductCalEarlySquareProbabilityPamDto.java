package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalEarlySquareProbabilityPamDto
 * @Description TODO
 * @Date 2024/7/24 13:57
 */
@Data
@ApiModel(value = "提前结清概率计算参数")
public class ProductCalEarlySquareProbabilityPamDto {
    @ApiModelProperty("激活日期起")
    private String activationDateStart;

    @ApiModelProperty("激活日期止")
    private String activationDateEnd;

    @ApiModelProperty("业务类型 0-本品  1-全品新车  2-二手车")
    private Integer businessType;

    @ApiModelProperty("业务类型 0-本品  1-全品新车  2-二手车")
    private String businessTypeStr;

    @ApiModelProperty("提前结清概率分类")
    private List<String> earlySquareProbabilityClassificationList;

    @ApiModelProperty("首付比例区间")
    private List<String> downPaymentRatioRangeList;

    @ApiModelProperty("赛道")
    private List<String> trackList;

    @ApiModelProperty("车辆类型")
    private List<String> vehicleTypeList;

    @ApiModelProperty("品牌")
    private List<String> brandNameList;

    @ApiModelProperty("产品分类概率使用")
    private List<String> productClassificationProbabilityList;

    @ApiModelProperty("产品细分")
    private List<String> productSegmentationList;

    @ApiModelProperty("客户利率（%）")
    private Double customerInterestRate;

    @ApiModelProperty("实际利率（%）")
    private Double actualInterestRate;

    @ApiModelProperty("是否贴息")
    private String ifDisInsterst;

    @ApiModelProperty("期限")
    private Integer timeLimit;
}
