package com.gwmfc.constant;

/**
 * <AUTHOR>
 * @Classname ProductCalBussType
 * @Description 业务类型
 * @Date 2023/9/22 14:26
 */
public enum ProductCalBussTypeEnum {
    /**
     * 本品
     */
    BP_CAR(0),

    /**
     * 全品新车
     */
    QP_NEW_CAR(1),

    /**
     * 二手车
     */
    SECOND_CAR(2);

    private final Integer type;

    ProductCalBussTypeEnum(Integer type) {
        this.type = type;
    }

    public Integer type() {
        return type;
    }

    public static ProductCalBussTypeEnum getProductCalBussTypeEnumByType(Integer type) {
        for (ProductCalBussTypeEnum productCalBussTypeEnum : ProductCalBussTypeEnum.values()) {
            if (productCalBussTypeEnum.type().equals(type)) {
                return productCalBussTypeEnum;
            }
        }
        return null;
    }
}
