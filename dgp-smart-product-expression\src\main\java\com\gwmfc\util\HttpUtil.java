package com.gwmfc.util;

import com.gwmfc.bo.CpcaautoBo;
import com.gwmfc.bo.PageContent;
import com.gwmfc.bo.RequestBo;
import com.gwmfc.entity.CpcaautoCallRecordEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023年08月16日 13:51
 */
@Slf4j
@RefreshScope
@Component
public class HttpUtil {
    private static Integer recallNum = 1;

    public CpcaautoBo analysisPictureGet(String url) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            // 创建httpget.
            HttpGet httpget = new HttpGet(url);
            // 执行get请求.
            CloseableHttpResponse response = httpClient.execute(httpget);
            try {
                HttpEntity entity = response.getEntity();
                log.info("picture url:{}", url);
                log.info("Response content length: {}", entity.getContentLength());

                // 打印响应状态
                if (response.getStatusLine().getStatusCode() == 200 && entity != null) {
                    String entityStr = EntityUtils.toString(entity);
                    log.info("Response content: {}", entityStr);
                    CpcaautoBo cpcaautoBo = GsonUtil.gsonToBean(entityStr, CpcaautoBo.class);
                    if (cpcaautoBo.getCode() == 200) {
                        cpcaautoBo.setRecallNum(recallNum);
                        return cpcaautoBo;
                    } else {
                        if (recallNum < 5) {
                            recallNum++;
                            analysisPictureGet(url);
                        } else {
                            cpcaautoBo.setRecallNum(recallNum);
                            return cpcaautoBo;
                        }
                        return null;
                    }
                } else {
                    if (recallNum < 5) {
                        recallNum++;
                        analysisPictureGet(url);
                    } else {
                        CpcaautoBo cpcaautoBo = new CpcaautoBo();
                        cpcaautoBo.setCode(response.getStatusLine().getStatusCode());
                        cpcaautoBo.setRecallNum(recallNum);
                        return cpcaautoBo;
                    }
                    return null;
                }
            } finally {
                response.close();
            }
        } catch (Exception e) {
            log.error("Exception:{}，{}",url,e);
            if (recallNum < 5) {
                recallNum++;
                analysisPictureGet(url);
            } else {
                CpcaautoBo cpcaautoBo = new CpcaautoBo();
                cpcaautoBo.setCode(404);
                cpcaautoBo.setRecallNum(recallNum);
                return cpcaautoBo;
            }
        } finally {
            // 关闭连接,释放资源
            try {
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public CpcaautoBo analysisPicturePost(String url,String tableName, CpcaautoCallRecordEntity cpcaautoCallRecordEntity) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            // 创建httpget.
            HttpPost httpPost = new HttpPost(url);
            // 执行get请求.
            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");

            // 设置请求体
            RequestBo requestBo = new RequestBo();
            requestBo.setTableName(tableName);
            PageContent pageContent = new PageContent();
            pageContent.setArticleName(cpcaautoCallRecordEntity.getPageTitle());
            pageContent.setImageUrls(GsonUtil.jsonToList(cpcaautoCallRecordEntity.getPictureUrlList(),String.class));
            pageContent.setReqTime(cpcaautoCallRecordEntity.getCallTime());
            pageContent.setReqUserName(cpcaautoCallRecordEntity.getCreateUser());
            requestBo.setPageContent(pageContent);
            StringEntity entity = new StringEntity(GsonUtil.toJson(requestBo), "UTF-8");
            httpPost.setEntity(entity);

            // 执行请求并获取响应
            CloseableHttpResponse response = httpClient.execute(httpPost);

            try {
                // 处理响应结果
                HttpEntity responseEntity = response.getEntity();
                log.info("picture url:{}", url);
                log.info("Response content length: {}", entity.getContentLength());

                // 打印响应状态
                if (response.getStatusLine().getStatusCode() == 200 && entity != null) {
                    String entityStr = EntityUtils.toString(responseEntity, "UTF-8");
                    log.info("request content: {}", GsonUtil.toJson(requestBo));
                    log.info("Response content: {}", entityStr);

                    CpcaautoBo cpcaautoBo = GsonUtil.gsonToBean(entityStr, CpcaautoBo.class);
                    if (cpcaautoBo.getCode() == 200) {
                        cpcaautoBo.setRecallNum(recallNum);
                        return cpcaautoBo;
                    } else {
                        if (recallNum < 5) {
                            recallNum++;
                            analysisPicturePost(url, tableName, cpcaautoCallRecordEntity);
                        } else {
                            cpcaautoBo.setRecallNum(recallNum);
                            return cpcaautoBo;
                        }
                        return cpcaautoBo;
                    }
                } else {
                    CpcaautoBo cpcaautoBo = new CpcaautoBo();
                    cpcaautoBo.setCode(response.getStatusLine().getStatusCode());
                    if (recallNum < 5) {
                        recallNum++;
                        analysisPicturePost(url, tableName, cpcaautoCallRecordEntity);
                    } else {
                        cpcaautoBo.setRecallNum(recallNum);
                        return cpcaautoBo;
                    }
                    return cpcaautoBo;
                }
            } finally {
                response.close();
            }
        } catch (Exception e) {
            log.error("Exception:{}，{}",url,e);
            if (recallNum < 5) {
                recallNum++;
                analysisPictureGet(url);
            } else {
                CpcaautoBo cpcaautoBo = new CpcaautoBo();
                cpcaautoBo.setCode(404);
                cpcaautoBo.setRecallNum(recallNum);
                return cpcaautoBo;
            }
        } finally {
            // 关闭连接,释放资源
            try {
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

}
