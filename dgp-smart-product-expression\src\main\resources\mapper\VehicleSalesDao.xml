<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.VehicleSalesDao">

    <select id="selectByType" resultType="java.util.LinkedHashMap">
        select
        <foreach collection="columnList" item="column" separator=",">
            ${column}
        </foreach>
        from ${tableName}
        <where>
            <if test="conditionParams != null and conditionParams.size > 0">
                <foreach collection="conditionParams" item="value" index="key">
                    <if test="value != '' and value != null">
                        <choose>
                            <when test="key == 'start_date'">
                                <choose>
                                    <when test="tableName == 'used_car_weekly_trading'">
                                        and start_date &gt;= #{value}
                                    </when>
                                    <otherwise>
                                        and data_date &gt;= #{value}
                                    </otherwise>
                                </choose>
                            </when>
                            <when test="key == 'end_date'">
                                <choose>
                                    <when test="tableName == 'used_car_weekly_trading'">
                                        and start_date &lt;= #{value}
                                    </when>
                                    <otherwise>
                                        and data_date &lt;= #{value}
                                    </otherwise>
                                </choose>
                            </when>
                            <otherwise>
                                and ${key} = #{value}
                            </otherwise>
                        </choose>
                    </if>
                </foreach>
            </if>
        </where>
        <choose>
            <when test="tableName == 'used_car_weekly_trading'">
                order by start_date desc
            </when>
            <otherwise>
                order by data_date desc
            </otherwise>
        </choose>
    </select>
</mapper>
