package com.gwmfc.controller;

import com.alibaba.nacos.common.utils.StringUtils;
import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.GlobalFormBusinessDto;
import com.gwmfc.dto.UsedCarQueryDto;
import com.gwmfc.entity.ProductDataMartEntity;
import com.gwmfc.entity.data.UsedCarMonthlyTradingEntity;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.service.GlobalFormBusinessService;
import com.gwmfc.service.ProductDataMartService;
import com.gwmfc.service.UsedCarMonthlyTradingService;
import com.gwmfc.service.VehicleSalesService;
import com.gwmfc.util.BusinessEnum;
import com.gwmfc.util.PageForm;
import com.gwmfc.dto.VehicleExcelQueryDto;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.util.Result;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gwmfc.util.StatusCodeEnum.SUCCESS;


/**
 * <AUTHOR>
 * @create 2023-10-31 09:57
 */
@Api(value = "二手车月度交易量接口", tags = "二手车月度交易量接口")
@RestController
@RequestMapping("/usedCarMonthlyTrading")
public class UsedCarMonthlyTradingController {

    @Resource
    private UsedCarMonthlyTradingService usedCarMonthlyTradingService;

    @Resource
    private VehicleSalesService vehicleSalesService;
    @Resource
    private GlobalFormBusinessService globalFormBusinessService;
    @Resource
    private ProductDataMartService productDataMartService;

    /**
     * 列表查询接口
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "列表查询接口", produces = "application/json")
    @PostMapping("/list")
    public Result list(@RequestBody PageForm<UsedCarQueryDto> pageForm) {
        Integer type = pageForm.getParam().getType();
        if (type.equals(6)) {
            ProductDataMartEntity productDataMartEntity = productDataMartService.getProductDataMartEntityByTableName("price_range_trading_volume");
            PageForm<GlobalFormBusinessDto> pageSel = new PageForm<>(pageForm.getCurrent(), pageForm.getSize());
            GlobalFormBusinessDto globalFormBusinessDto = new GlobalFormBusinessDto();
            globalFormBusinessDto.setTableName("price_range_trading_volume");
            Map<String, Object> map = new HashMap<>();
            if (!StringUtils.isEmpty(pageForm.getParam().getStartDate())) {
                map.put("start_data_date", pageForm.getParam().getStartDate().concat("-01"));
            }
            if (!StringUtils.isEmpty(pageForm.getParam().getEndDate())) {
                map.put("end_data_date", pageForm.getParam().getEndDate().concat("-02"));
            }
            globalFormBusinessDto.setUpdateFrequency(productDataMartEntity.getUpdateFrequency());
            globalFormBusinessDto.setConditionParams(map);
            pageSel.setParam(globalFormBusinessDto);
            try {
                IPage<Map<String, Object>> dataVolume = globalFormBusinessService.listByPage(pageSel);
                return Result.ok(dataVolume.getRecords(), dataVolume.getTotal());
            } catch (IOException e) {
                throw new SystemRuntimeException(BusinessEnum.ERROR);
            }
        } else {
            IPage<UsedCarMonthlyTradingEntity> data = usedCarMonthlyTradingService.page(pageForm.getParam(), pageForm.getCurrent(), pageForm.getSize());
            return Result.ok(data.getRecords(), data.getTotal());
        }
    }

    /**
     * 根据id查询单个数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "根据id查询单个数据", produces = "application/json")
    @GetMapping("/{id}")
    public Result getOne(@PathVariable("id") Long id) {
        Result result = new Result();
        UsedCarMonthlyTradingEntity usedCarMonthlyTradingEntity = usedCarMonthlyTradingService.getOne(id);
        result.setData(usedCarMonthlyTradingEntity);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }


    /**
     * 批量保存数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "批量保存数据", produces = "application/json")
    @PostMapping("/batchSave")
    public Result batchSave(@RequestBody List<UsedCarMonthlyTradingEntity> list) {
        if (usedCarMonthlyTradingService.batchSave(list)) {
            return Result.ok("保存成功");
        } else {
            return Result.error("保存失败");
        }
    }

    /**
     * 删除
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", produces = "application/json")
    @PostMapping("/remove")
    public Result remove(@RequestParam("id") Long id) {
        Integer count = usedCarMonthlyTradingService.remove(id);
        if (count == 0) {
            return Result.error("删除失败");
        }
        return Result.ok();
    }

    /**
     * 更新数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "更新数据", produces = "application/json")
    @PostMapping("/update")
    public Result update(@RequestBody UsedCarMonthlyTradingEntity usedCarMonthlyTradingEntity, @CurrentUser User user) {
        usedCarMonthlyTradingEntity.setUpdateUser(user.getUserName());
        usedCarMonthlyTradingEntity.setUpdateTime(LocalDateTime.now());
        Integer count = usedCarMonthlyTradingService.update(usedCarMonthlyTradingEntity);
        if (count == 0) {
            return Result.error("更新失败");
        }
        return Result.ok();
    }

    @ApiOperation("导出Excel")
    @PostMapping("/export")
    public void export(@RequestBody @Valid VehicleExcelQueryDto queryDto, HttpServletResponse response) {
        vehicleSalesService.export(queryDto, response);
    }
}

