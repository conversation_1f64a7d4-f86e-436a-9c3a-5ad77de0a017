package com.gwmfc.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.vo.DdFpFinProductParamVo;
import com.gwmfc.entity.data.DdFpFinProductEntity;
import com.gwmfc.vo.DdFpFinProductVo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface DdFpFinProductDao extends BaseMapper<DdFpFinProductEntity>{

    /**
     * 分页查询
     * @param page
     * @param param
     * @return
     */
    IPage<DdFpFinProductVo> listPage(IPage<DdFpFinProductVo> page, DdFpFinProductParamVo param);
}
