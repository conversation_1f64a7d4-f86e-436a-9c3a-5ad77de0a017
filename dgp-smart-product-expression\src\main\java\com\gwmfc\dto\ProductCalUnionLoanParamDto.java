package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalUnionLoanParamDto
 * @Description TODO
 * @Date 2024/3/22 11:16
 */
@Data
@ApiModel(value = "联合贷参数")
public class ProductCalUnionLoanParamDto {
    @ApiModelProperty("银行贷款比例(%)")
    private Double bankLoanRatio;

    @ApiModelProperty("银行结算利率(%)")
    private Double bankSettleRate;

    @ApiModelProperty("产品推送率(%)")
    private Double productPushRate;

    @ApiModelProperty("通过率(%)")
    private Double passingRate;

    @ApiModelProperty("银行贷款金额")
    private Double bankLoanMoney;

    @ApiModelProperty("我司贷款金额")
    private Double ourLoanMoney;

    @ApiModelProperty("银行利息收入")
    private Double bankInterestIncom;

    @ApiModelProperty("我司利息收入")
    private Double ourInterestIncom;

    @ApiModelProperty("综合Irr我司系数")
    private Double integrativeIrrOursFactor;
}
