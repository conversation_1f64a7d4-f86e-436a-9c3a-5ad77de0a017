package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.dao.AdjustCalResultHistoryDao;
import com.gwmfc.dao.ProductProposalDao;
import com.gwmfc.dao.ProductProposalTemplateDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.AdjustCalResultHistoryDto;
import com.gwmfc.dto.ProductProposalTemplateDto;
import com.gwmfc.dto.ProductProposalTemplateGroupDto;
import com.gwmfc.entity.AdjustCalResultHistoryEntity;
import com.gwmfc.entity.ProductProposalTemplateEntity;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.PageForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023年10月18日 9:46
 */
@Service
@Slf4j
public class ProductProposalTemplateService extends ServiceImpl<ProductProposalTemplateDao, ProductProposalTemplateEntity> {
    @Resource
    private ProductProposalTemplateDao productProposalTemplateDao;
    @Resource
    private ProductProposalDao productProposalDao;
    @Resource
    private AdjustCalResultHistoryDao adjustCalResultHistoryDao;

    public List<ProductProposalTemplateEntity> selectTemplate(PageForm<ProductProposalTemplateDto> productElementTemplateDtoPageForm) {
        ProductProposalTemplateDto param = productElementTemplateDtoPageForm.getParam();
        List<ProductProposalTemplateEntity> productElementTemplateEntityList = productProposalTemplateDao.selectTemplateName(new Page<>(productElementTemplateDtoPageForm.getCurrent(), productElementTemplateDtoPageForm.getSize()), param.getName(), param.getBusinessType(),
                param.getCreateUser(), param.getCreateTime());
        return productElementTemplateEntityList;
    }

    public void saveTemplate(@Valid List<ProductProposalTemplateDto> productProposalTemplateDtoList, String templateName, Integer businessType, User user) {
        LocalDateTime localDateTime = LocalDateTime.now();
        productProposalTemplateDtoList.forEach(productElementTemplateDto -> {
            ProductProposalTemplateEntity productProposalTemplateEntity = new ProductProposalTemplateEntity();
            BeanUtils.copyProperties(productElementTemplateDto, productProposalTemplateEntity);
            productProposalTemplateEntity.setName(templateName);
            productProposalTemplateEntity.setBusinessType(businessType);
            productProposalTemplateEntity.setCreateTime(localDateTime);
            productProposalTemplateEntity.setCreateUser(user.getUserName());
            productProposalTemplateEntity.setSelfDefinedParameter(GsonUtil.toJson(productElementTemplateDto.getSelfDefinedParameter()));
            if (null != productElementTemplateDto.getSalesForecast()) {
                productProposalTemplateEntity.setSalesForecast(GsonUtil.toJson(productElementTemplateDto.getSalesForecast()));
            }
            if(productElementTemplateDto.getProductCalEarlySquareParamSubDto() != null){
                productProposalTemplateEntity.setProductCalEarlySquareParamSubDto(GsonUtil.toJson(productElementTemplateDto.getProductCalEarlySquareParamSubDto()));
            }
            if(productElementTemplateDto.getProductCalRepaymentMethodParamDto() != null){
                productProposalTemplateEntity.setProductCalRepaymentMethodParamDto(GsonUtil.toJson(productElementTemplateDto.getProductCalRepaymentMethodParamDto()));
            }
            baseMapper.insert(productProposalTemplateEntity);
        });
    }

    public Integer deleteTemplate(String templateName) {
        ProductProposalTemplateEntity productProposalTemplateEntity = new ProductProposalTemplateEntity();
        productProposalTemplateEntity.setName(templateName);
        QueryWrapper queryWrapper = new QueryWrapper<>(productProposalTemplateEntity);
        return productProposalTemplateDao.delete(queryWrapper);
    }

    public Integer selectTemplateCount(String templateName, Integer businessType, String createUser, LocalDateTime createTime) {
        Integer templateCount = productProposalTemplateDao.selectTemplateCount(templateName, businessType, createUser, createTime);
        return templateCount;
    }

    public Integer selectTemplateCountByName(String templateName) {
        Integer templateCount = productProposalTemplateDao.selectTemplateCountByName(templateName);
        return templateCount;
    }

    public List<ProductProposalTemplateEntity> selectTemplateDetail(String name) {
        return productProposalTemplateDao.selectTemplateDetail(name);
    }

    public Integer adjustCalResultHistory(AdjustCalResultHistoryDto adjustCalResultHistoryDto, User user) {
        AdjustCalResultHistoryEntity adjustCalResultHistorySel = new AdjustCalResultHistoryEntity();
        adjustCalResultHistorySel.setProductProposalId(adjustCalResultHistoryDto.getProductProposalId());
        adjustCalResultHistorySel.setCurrentTimeSeqNo(adjustCalResultHistoryDto.getCurrentTimeSeqNo());
        QueryWrapper queryWrapper = new QueryWrapper<>(adjustCalResultHistorySel);
        if (adjustCalResultHistoryDao.selectList(queryWrapper).isEmpty()) {
            String productProposalTemplateGroupJson = productProposalDao.selectById(adjustCalResultHistoryDto.getProductProposalId()).getProductProposalTemplateGroupJson();
            if (StringUtils.isNotEmpty(productProposalTemplateGroupJson)) {
                GsonUtil.jsonToList(productProposalTemplateGroupJson, ProductProposalTemplateGroupDto.class)
                        .stream().forEach(productProposalTemplateGroupDto -> {
                            List<ProductProposalTemplateDto> productProposalTemplateDtoList = productProposalTemplateGroupDto.getProductProposalTemplateList().stream().filter(productProposalTemplateDto -> productProposalTemplateDto.getCurrentTimeSeqNo().equals(adjustCalResultHistoryDto.getCurrentTimeSeqNo())).collect(Collectors.toList());

                            if (productProposalTemplateDtoList != null && productProposalTemplateDtoList.size() != 0) {
                                AdjustCalResultHistoryEntity adjustCalResultHistoryOld = new AdjustCalResultHistoryEntity();
                                ProductProposalTemplateDto productProposalTemplateDto = productProposalTemplateDtoList.get(0);
                                BeanUtils.copyProperties(productProposalTemplateDto, adjustCalResultHistoryOld);
                                if(productProposalTemplateDto.getProductCalUnionLoanParamDto() != null){
                                    adjustCalResultHistoryOld.setProductCalUnionLoanParamDto(GsonUtil.toJson(productProposalTemplateDto.getProductCalUnionLoanParamDto()));
                                }
                                if(productProposalTemplateDto.getProductCalEarlySquareParamSubDto() != null){
                                    adjustCalResultHistoryOld.setProductCalEarlySquareParamSubDto(GsonUtil.toJson(productProposalTemplateDto.getProductCalEarlySquareParamSubDto()));
                                }
                                adjustCalResultHistoryOld.setChangeTime(LocalDateTime.now());
                                adjustCalResultHistoryOld.setChangeUser(user.getUserName());
                                adjustCalResultHistoryOld.setProductProposalGroupUuid(productProposalTemplateGroupDto.getUuid());
                                adjustCalResultHistoryOld.setProductProposalId(adjustCalResultHistoryDto.getProductProposalId());
                                adjustCalResultHistoryDao.insert(adjustCalResultHistoryOld);
                            }
                        });
            }
        }
        AdjustCalResultHistoryEntity adjustCalResultHistoryEntity = new AdjustCalResultHistoryEntity();
        BeanUtils.copyProperties(adjustCalResultHistoryDto, adjustCalResultHistoryEntity);

        if(adjustCalResultHistoryDto.getProductCalUnionLoanParamDto() != null){
            adjustCalResultHistoryEntity.setProductCalUnionLoanParamDto(GsonUtil.toJson(adjustCalResultHistoryDto.getProductCalUnionLoanParamDto()));
        }

        if(adjustCalResultHistoryDto.getProductCalEarlySquareParamSubDto() != null){
            adjustCalResultHistoryEntity.setProductCalEarlySquareParamSubDto(GsonUtil.toJson(adjustCalResultHistoryDto.getProductCalEarlySquareParamSubDto()));
        }
        adjustCalResultHistoryEntity.setChangeTime(LocalDateTime.now());
        adjustCalResultHistoryEntity.setChangeUser(user.getUserName());
        return adjustCalResultHistoryDao.insert(adjustCalResultHistoryEntity);
    }

    public IPage selectAdjustCalResult(PageForm<AdjustCalResultHistoryDto> pageForm) {
        AdjustCalResultHistoryEntity adjustCalResultHistoryEntity = new AdjustCalResultHistoryEntity();
        adjustCalResultHistoryEntity.setCurrentTimeSeqNo(pageForm.getParam().getCurrentTimeSeqNo());
        adjustCalResultHistoryEntity.setProductProposalId(pageForm.getParam().getProductProposalId());
        LambdaQueryWrapper<AdjustCalResultHistoryEntity> queryWrapper = new LambdaQueryWrapper<>(adjustCalResultHistoryEntity);
        queryWrapper.orderByDesc(AdjustCalResultHistoryEntity::getChangeTime);
        IPage page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page(pageForm.getCurrent(), pageForm.getSize());
        return adjustCalResultHistoryDao.selectPage(page, queryWrapper);
    }
}
