package com.gwmfc.service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gwmfc.bo.UploadFileBo;
import com.gwmfc.dao.*;
import com.gwmfc.domain.User;
import com.gwmfc.dto.CompeteInfoBatchDto;
import com.gwmfc.dto.CompeteInfoDto;
import com.gwmfc.dto.CompeteInfoQueryDto;
import com.gwmfc.entity.*;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @Classname CompeteInfoCollectionService
 * @Description TODO
 * @Date 2024/12/11 14:55
 */
@Service
@Slf4j
public class CompeteInfoCollectionService {

    @Autowired
    private CompeteInfoBatchDao competeInfoBatchDao;

    @Autowired
    private CompeteInfoBankDao competeInfoBankDao;

    @Autowired
    private CompeteInfoAfcMustDao competeInfoAfcMustDao;

    @Autowired
    private CompeteInfoAfcOptionalDao competeInfoAfcOptionalDao;

    @Autowired
    private DfChlSupDInvestorManageDao dfChlSupDInvestorManageDao;

    @Resource
    private FtpService ftpService;

    @Value("${attachment-dir}")
    private String attachmentDir;

    @Transactional(rollbackFor = Exception.class)
    public Result saveCompeteInfo(CompeteInfoDto competeInfoDto, List<MultipartFile> attachmentList, User user){
        Result result = new Result();
        try{
            log.info("竞对信息保存开始...");
            List<CompeteInfoAfcMustEntity> competeInfoAfcMustEntityList = competeInfoDto.getCompeteInfoAfcMustEntityList();
            List<CompeteInfoAfcOptionalEntity> competeInfoAfcOptionalEntityList = competeInfoDto.getCompeteInfoAfcOptionalEntityList();
            List<CompeteInfoBankEntity> competeInfoBankEntityList = competeInfoDto.getCompeteInfoBankEntityList();

            Date now = new Date();

            CompeteInfoBatchEntity competeInfoBatchEntity = new CompeteInfoBatchEntity();
            competeInfoBatchEntity.setAttachment(GsonUtil.toJson(ftpService.uploadFileToFtp(attachmentDir, attachmentList, user.getName())));
            competeInfoBatchEntity.setStaffNo(user.getStaffNo());
            competeInfoBatchEntity.setCreateTime(now);
            competeInfoBatchEntity.setCreateUser(user.getUserName());
            if(competeInfoBankEntityList != null && competeInfoBankEntityList.size()>0){
                competeInfoBatchEntity.setDataType("bank");
                competeInfoBatchDao.insert(competeInfoBatchEntity);

                for(CompeteInfoBankEntity competeInfoBankEntity : competeInfoBankEntityList){
                    competeInfoBankEntity.setBatchId(competeInfoBatchEntity.getId());
                    competeInfoBankEntity.setStaffNo(user.getStaffNo());
                    competeInfoBankEntity.setCreateTime(now);
                    competeInfoBankEntity.setCreateUser(user.getUserName());
                    competeInfoBankDao.insert(competeInfoBankEntity);
                }
            }else{
                competeInfoBatchEntity.setDataType("afc");
                competeInfoBatchDao.insert(competeInfoBatchEntity);
                if(competeInfoAfcMustEntityList != null && competeInfoAfcMustEntityList.size()>0){
                    for(CompeteInfoAfcMustEntity competeInfoAfcMustEntity : competeInfoAfcMustEntityList){
                        competeInfoAfcMustEntity.setBatchId(competeInfoBatchEntity.getId());
                        competeInfoAfcMustEntity.setStaffNo(user.getStaffNo());
                        competeInfoAfcMustEntity.setCreateTime(now);
                        competeInfoAfcMustEntity.setCreateUser(user.getUserName());
                        competeInfoAfcMustDao.insert(competeInfoAfcMustEntity);
                    }
                }
                if(competeInfoAfcOptionalEntityList != null && competeInfoAfcOptionalEntityList.size()>0){
                    for(CompeteInfoAfcOptionalEntity competeInfoAfcOptionalEntity : competeInfoAfcOptionalEntityList){
                        if(!(StringUtils.isBlank(competeInfoAfcOptionalEntity.getCarFinance()) && StringUtils.isBlank(competeInfoAfcOptionalEntity.getAdmittanceCriterion()) &&
                                StringUtils.isBlank(competeInfoAfcOptionalEntity.getDownpaymentsRatio()) && StringUtils.isBlank(competeInfoAfcOptionalEntity.getMaxLoanRatio()) &&
                                StringUtils.isBlank(competeInfoAfcOptionalEntity.getAppendLoanPolicy()) && StringUtils.isBlank(competeInfoAfcOptionalEntity.getFreeMortgagePolicy()) &&
                                StringUtils.isBlank(competeInfoAfcOptionalEntity.getDealerExamineDimension()))){
                            competeInfoAfcOptionalEntity.setBatchId(competeInfoBatchEntity.getId());
                            competeInfoAfcOptionalEntity.setStaffNo(user.getStaffNo());
                            competeInfoAfcOptionalEntity.setCreateTime(now);
                            competeInfoAfcOptionalEntity.setCreateUser(user.getUserName());
                            competeInfoAfcOptionalDao.insert(competeInfoAfcOptionalEntity);
                        }
                    }
                }
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            log.info("竞对信息保存结束...");
        }catch (Exception e){
            log.error("保存竞对信息失败:{}", e);
            throw new RuntimeException("保存竞对信息失败");
        }
        return result;
    }

    public Result getCompeteInfo(CompeteInfoQueryDto competeInfoQueryDto){
        Result result = new Result();
        try{
            log.info("查询{}类型竞对信息开始...", competeInfoQueryDto.getType());
            String dataType = competeInfoQueryDto.getType();
            if("bank".equals(dataType)){
                /**
                 * 查询银行竞对信息
                 */
                if(competeInfoQueryDto.getCompeteInfoBankEntity() != null){
                    CompeteInfoBankEntity competeInfoBankEntity = competeInfoQueryDto.getCompeteInfoBankEntity();
                    competeInfoBankEntity.setStaffNo(competeInfoQueryDto.getUser().getStaffNo());
                    competeInfoBankEntity.setCreateUser(competeInfoQueryDto.getUser().getUserName());
                    QueryWrapper queryWrapper = new QueryWrapper(competeInfoBankEntity);
                    queryWrapper.orderByDesc("create_time");

                    List<CompeteInfoBankEntity> competeInfoBankEntityList = competeInfoBankDao.selectList(queryWrapper);
                    if(competeInfoBankEntityList != null && competeInfoBankEntityList.size()>0){
                        result.setData(competeInfoBankEntityList.get(0));
                    }else{
                        result.setData("");
                    }
                }
            }else if("afcMust".equals(dataType)){
                /**
                 * 查询afc必填竞对信息
                 */
                if(competeInfoQueryDto.getCompeteInfoAfcMustEntity() != null){
                    CompeteInfoAfcMustEntity competeInfoAfcMustEntity = competeInfoQueryDto.getCompeteInfoAfcMustEntity();
                    competeInfoAfcMustEntity.setStaffNo(competeInfoQueryDto.getUser().getStaffNo());
                    competeInfoAfcMustEntity.setCreateUser(competeInfoQueryDto.getUser().getUserName());
                    QueryWrapper queryWrapper = new QueryWrapper(competeInfoAfcMustEntity);
                    queryWrapper.orderByDesc("create_time");

                    List<CompeteInfoAfcMustEntity> competeInfoAfcMustEntityList = competeInfoAfcMustDao.selectList(queryWrapper);
                    if(competeInfoAfcMustEntityList != null && competeInfoAfcMustEntityList.size()>0){
                        result.setData(competeInfoAfcMustEntityList.get(0));
                    }else{
                        result.setData("");
                    }
                }
            }else{
                /**
                 * 查询afc选填竞对信息
                 */
                CompeteInfoAfcOptionalEntity competeInfoAfcOptionalEntity = competeInfoQueryDto.getCompeteInfoAfcOptionalEntity();
                competeInfoAfcOptionalEntity.setStaffNo(competeInfoQueryDto.getUser().getStaffNo());
                competeInfoAfcOptionalEntity.setCreateUser(competeInfoQueryDto.getUser().getUserName());
                QueryWrapper queryWrapper = new QueryWrapper(competeInfoAfcOptionalEntity);
                queryWrapper.orderByDesc("create_time");

                List<CompeteInfoAfcOptionalEntity> competeInfoAfcOptionalEntityList = competeInfoAfcOptionalDao.selectList(queryWrapper);
                if(competeInfoAfcOptionalEntityList != null && competeInfoAfcOptionalEntityList.size()>0){
                    result.setData(competeInfoAfcOptionalEntityList.get(0));
                }else{
                    result.setData("");
                }
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            log.info("查询{}类型竞对信息结束...", competeInfoQueryDto.getType());
        }catch (Exception e){
            log.error("获取竞对信息失败:{}", e);
            result.setCode(FAIL.getCode());
            result.setMessage("获取竞对信息失败");
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public Result updateCompeteInfo(CompeteInfoDto competeInfoDto, List<MultipartFile> attachmentList, User user){
        /**
         * 更新操作，将新旧都存在的更新 新不存在的删除 旧的不存在的插入
         */
        Result result = new Result();
        try {
            log.info("更新竞对信息开始...");
            Date now = new Date();

            CompeteInfoBatchDto competeInfoBatchDto = competeInfoDto.getCompeteInfoBatchDto();
            List<UploadFileBo> attachmentBoList = competeInfoBatchDto.getAttachmentList();
            for (UploadFileBo uploadFileBo : attachmentBoList) {
                String filePath = uploadFileBo.getFilePath();
                ftpService.deleteFile(filePath);
            }
            CompeteInfoBatchEntity competeInfoBatchEntity = new CompeteInfoBatchEntity();
            competeInfoBatchEntity.setId(competeInfoBatchDto.getId());
            competeInfoBatchEntity.setAttachment(GsonUtil.toJson(ftpService.uploadFileToFtp(attachmentDir, attachmentList, user.getName())));
            competeInfoBatchEntity.setCreateTime(competeInfoBatchDto.getCreateTime());
            competeInfoBatchEntity.setCreateUser(competeInfoBatchDto.getCreateUser());
            competeInfoBatchEntity.setUpdateTime(now);
            competeInfoBatchEntity.setUpdateUser(user.getUserName());

            List<CompeteInfoAfcMustEntity> competeInfoAfcMustEntityList = competeInfoDto.getCompeteInfoAfcMustEntityList();
            List<CompeteInfoAfcOptionalEntity> competeInfoAfcOptionalEntityList = competeInfoDto.getCompeteInfoAfcOptionalEntityList();
            List<CompeteInfoBankEntity> competeInfoBankEntityList = competeInfoDto.getCompeteInfoBankEntityList();

            /**
             * 修改竞对信息   先删除原ftp存储文件  再重新上传新文件
             */
            if(competeInfoBankEntityList != null && competeInfoBankEntityList.size()>0){
                competeInfoBatchEntity.setDataType("bank");

                /**
                 * 通过batchId查所有的银行竞对信息
                 */
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("batch_id",competeInfoBatchEntity.getId());
                List<CompeteInfoBankEntity> oldCompeteInfoBankEntityList = competeInfoBankDao.selectList(queryWrapper);
                for (CompeteInfoBankEntity competeInfoBankEntity : competeInfoBankEntityList) {
                    boolean flag = oldCompeteInfoBankEntityList.stream().anyMatch(oldCompeteInfoBankEntity -> oldCompeteInfoBankEntity.getId().equals(competeInfoBankEntity.getId()));
                    if(flag){
                        competeInfoBankEntity.setUpdateTime(now);
                        competeInfoBankEntity.setUpdateUser(user.getUserName());
                        competeInfoBankDao.updateById(competeInfoBankEntity);
                    }else{
                        competeInfoBankEntity.setBatchId(competeInfoBatchEntity.getId());
                        competeInfoBankEntity.setCreateTime(now);
                        competeInfoBankEntity.setCreateUser(user.getUserName());
                        competeInfoBankDao.insert(competeInfoBankEntity);
                    }
                }

                for (CompeteInfoBankEntity competeInfoBankEntity : oldCompeteInfoBankEntityList) {
                    if(!competeInfoBankEntityList.stream().anyMatch(competeInfoBankEntity1 -> competeInfoBankEntity1.getId().equals(competeInfoBankEntity.getId()))){
                        competeInfoBankDao.deleteById(competeInfoBankEntity.getId());
                    }
                }

            }else{
                competeInfoBatchEntity.setDataType("afc");
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("batch_id",competeInfoBatchEntity.getId());
                List<CompeteInfoAfcMustEntity> oldCompeteInfoAfcMustEntityList = competeInfoAfcMustDao.selectList(queryWrapper);
                List<CompeteInfoAfcOptionalEntity> oldCompeteInfoAfcOptionalEntityList = competeInfoAfcOptionalDao.selectList(queryWrapper);

                if(competeInfoAfcMustEntityList != null && competeInfoAfcMustEntityList.size()>0){
                    for (CompeteInfoAfcMustEntity competeInfoAfcMustEntity : competeInfoAfcMustEntityList) {
                        boolean flag = oldCompeteInfoAfcMustEntityList.stream().anyMatch(oldCompeteInfoAfcMustEntity -> oldCompeteInfoAfcMustEntity.getId().equals(competeInfoAfcMustEntity.getId()));
                        if(flag){
                            competeInfoAfcMustEntity.setUpdateTime(now);
                            competeInfoAfcMustEntity.setUpdateUser(user.getUserName());
                            competeInfoAfcMustDao.updateById(competeInfoAfcMustEntity);
                        }else{
                            competeInfoAfcMustEntity.setBatchId(competeInfoBatchEntity.getId());
                            competeInfoAfcMustEntity.setCreateTime(now);
                            competeInfoAfcMustEntity.setCreateUser(user.getUserName());
                            competeInfoAfcMustDao.insert(competeInfoAfcMustEntity);
                        }
                    }

                    for (CompeteInfoAfcMustEntity competeInfoAfcMustEntity : oldCompeteInfoAfcMustEntityList) {
                        if(!competeInfoAfcMustEntityList.stream().anyMatch(competeInfoAfcMustEntity1 -> competeInfoAfcMustEntity1.getId().equals(competeInfoAfcMustEntity.getId()))){
                            competeInfoAfcMustDao.deleteById(competeInfoAfcMustEntity.getId());
                        }
                    }

                    for (CompeteInfoAfcOptionalEntity competeInfoAfcOptionalEntity : competeInfoAfcOptionalEntityList) {
                        boolean flag = oldCompeteInfoAfcOptionalEntityList.stream().anyMatch(oldCompeteInfoAfcOptionalEntity -> oldCompeteInfoAfcOptionalEntity.getId().equals(competeInfoAfcOptionalEntity.getId()));
                        if(flag){
                            competeInfoAfcOptionalEntity.setUpdateTime(now);
                            competeInfoAfcOptionalEntity.setUpdateUser(user.getUserName());
                            competeInfoAfcOptionalDao.updateById(competeInfoAfcOptionalEntity);
                       }else{
                            competeInfoAfcOptionalEntity.setBatchId(competeInfoBatchEntity.getId());
                            competeInfoAfcOptionalEntity.setCreateTime(now);
                            competeInfoAfcOptionalEntity.setCreateUser(user.getUserName());
                            competeInfoAfcOptionalDao.insert(competeInfoAfcOptionalEntity);
                        }
                    }
                    for (CompeteInfoAfcOptionalEntity competeInfoAfcOptionalEntity : oldCompeteInfoAfcOptionalEntityList) {
                        if(!competeInfoAfcOptionalEntityList.stream().anyMatch(competeInfoAfcOptionalEntity1 -> competeInfoAfcOptionalEntity1.getId().equals(competeInfoAfcOptionalEntity.getId()))){
                            competeInfoAfcOptionalDao.deleteById(competeInfoAfcOptionalEntity.getId());
                        }
                    }
                }
            }

            competeInfoBatchDao.updateById(competeInfoBatchEntity);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            log.info("更新竞对信息结束...");
        }catch(Exception e){
            log.error("修改竞对信息失败:{}", e);
            throw new RuntimeException("修改竞对信息失败");
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public Result deleteCompeteInfo(List<Long> competeInfoIdList){
        Result result = new Result();
        try{
            log.info("删除批次Id为：{}竞对信息开始...", competeInfoIdList);
            for (Long batchId : competeInfoIdList) {
                /**
                 * 分别删除batch表、bank表、afc_must表、afc_optional表
                 */
                competeInfoBatchDao.deleteById(batchId);
                competeInfoBankDao.delete(new QueryWrapper<CompeteInfoBankEntity>().eq("batch_id",batchId));
                competeInfoAfcMustDao.delete(new QueryWrapper<CompeteInfoAfcMustEntity>().eq("batch_id",batchId));
                competeInfoAfcOptionalDao.delete(new QueryWrapper<CompeteInfoAfcOptionalEntity>().eq("batch_id",batchId));
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            log.info("删除批次Id为：{}竞对信息结束...", competeInfoIdList);
        }catch(Exception e){
            log.error("删除竞对信息失败:{}", e);
            throw  new RuntimeException("删除竞对信息失败");
        }
        return result;
    }

    public Result getCompeteInfoDetail(Long batchId){
        Result result = new Result();
        try{
            log.info("获取竞对信息批次{}详情开始", batchId);
            CompeteInfoBatchEntity competeInfoBatchEntity = competeInfoBatchDao.selectById(batchId);
            List<CompeteInfoBankEntity> competeInfoBankEntityList = competeInfoBankDao.selectList(new QueryWrapper<CompeteInfoBankEntity>().eq("batch_id",batchId));
            List<CompeteInfoAfcMustEntity> competeInfoAfcMustEntityList = competeInfoAfcMustDao.selectList(new QueryWrapper<CompeteInfoAfcMustEntity>().eq("batch_id",batchId));
            List<CompeteInfoAfcOptionalEntity> competeInfoAfcOptionalEntityList = competeInfoAfcOptionalDao.selectList(new QueryWrapper<CompeteInfoAfcOptionalEntity>().eq("batch_id",batchId));

            CompeteInfoDto competeInfoDto = new CompeteInfoDto();
            CompeteInfoBatchDto competeInfoBatchDto = new CompeteInfoBatchDto();
            competeInfoBatchDto.setId(competeInfoBatchEntity.getId());
            competeInfoBatchDto.setDataType(competeInfoBatchEntity.getDataType());
            competeInfoBatchDto.setAttachmentList(GsonUtil.jsonToList(competeInfoBatchEntity.getAttachment(),UploadFileBo.class));
            competeInfoBatchDto.setCreateTime(competeInfoBatchEntity.getCreateTime());
            competeInfoBatchDto.setCreateUser(competeInfoBatchEntity.getCreateUser());
            competeInfoBatchDto.setUpdateTime(competeInfoBatchEntity.getUpdateTime());
            competeInfoBatchDto.setUpdateUser(competeInfoBatchEntity.getUpdateUser());
            competeInfoDto.setCompeteInfoBatchDto(competeInfoBatchDto);

            competeInfoDto.setCompeteInfoBankEntityList(competeInfoBankEntityList);
            competeInfoDto.setCompeteInfoAfcMustEntityList(competeInfoAfcMustEntityList);
            competeInfoDto.setCompeteInfoAfcOptionalEntityList(competeInfoAfcOptionalEntityList);
            result.setData(competeInfoDto);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            log.info("获取竞对信息批次{}详情结束", batchId);
        }catch (Exception e){
            log.error("获取竞对信息详情失败:{}", e);
            result.setCode(FAIL.getCode());
            result.setMessage("获取竞对信息详情失败");
        }
        return result;
    }

    public Result getCompeteInfoBatchList(CompeteInfoBatchDto competeInfoBatchDto,Integer current, Integer size){
        Result result = new Result();
        try{
            log.info("获取竞对信息批次列表开始");
            IPage<CompeteInfoBatchEntity> page = new Page<>(current, size);
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.orderByDesc("id");
            if (competeInfoBatchDto != null && !StringUtils.isBlank(competeInfoBatchDto.getDataType())) {
                String dataType = competeInfoBatchDto.getDataType();
                queryWrapper.like("data_type", dataType);
            }
            IPage<CompeteInfoBatchEntity> competeInfoBatchEntityIPage = competeInfoBatchDao.selectPage(page, queryWrapper);
            result.setData(competeInfoBatchEntityIPage.getRecords());
            result.setTotal(competeInfoBatchEntityIPage.getTotal());
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            log.info("获取竞对信息批次列表结束");
        }catch (Exception e){
            log.error("获取竞对信息批次列表失败:{}", e);
            result.setCode(FAIL.getCode());
            result.setMessage("获取竞对信息批次列表失败");
        }
        return result;
    }

    public Result getAllGroupInfoByUserInfo(String userName){
        Result result = new Result();
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        try{
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("regional_manager",userName);
            List<DfChlSupDInvestorManageEntity> groupList = dfChlSupDInvestorManageDao.selectList(queryWrapper);
            if(groupList != null && groupList.size()>0){
                List<String> groupNameList = groupList.stream().map(DfChlSupDInvestorManageEntity::getClique).distinct().collect(Collectors.toList());
                result.setData(groupNameList);
            }
        }catch (Exception e){
            log.error("获取集团列表失败:{}", e);
            result.setCode(FAIL.getCode());
            result.setMessage("获取集团列表失败");
        }
        return result;
    }
}
