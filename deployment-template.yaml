apiVersion: apps/v1
kind: Deployment
metadata:
  name: #appName
  namespace: #env
spec:
  replicas: #replica
  revisionHistoryLimit: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: #appName
  template:
    metadata:
      labels:
        app: #appName
    spec:
      imagePullSecrets:
        - name: dgp-docker-secret
      containers:
        - name: #appName
          image: #image
          resources:
            requests:
              memory: 500Mi
              cpu: 10m
            limits:
              memory: #limitMem
              cpu: #limitCpu
          ports:
            - name:
              containerPort: #port
              protocol: TCP
          env:
            - name: "NACOS_SERVER"
              value: #NACOS_SERVER
            - name: k8s_logs_#env-#appName
              value: /opt/#appName/logs/*.log
            - name: k8s_logs_#env-#appName_format
              value: "json"
            - name: k8s_logs_#env-#appName_tags
              value: "topic=#env-#appName"
          volumeMounts:
            - name: applog
              mountPath: /opt/#appName/logs
          livenessProbe:
            httpGet:
              path: "/actuator/health"
              port:  #port
            initialDelaySeconds: 3
            periodSeconds: 30
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 5
          readinessProbe:
            httpGet:
              path: "/actuator/health/readiness"
              port:  #port
            initialDelaySeconds: 3
            periodSeconds: 30
            successThreshold: 1
            failureThreshold: 5
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - >-
                    wget 'http://localhost:#port/nacos/deregister' && sleep 90 && PID=`pidof java` && kill -SIGTERM $PID && while pidof java > /dev/stdout; do sleep 1; done;
      terminationGracePeriodSeconds: 120
      volumes:
        - name: applog
          emptyDir: {}
