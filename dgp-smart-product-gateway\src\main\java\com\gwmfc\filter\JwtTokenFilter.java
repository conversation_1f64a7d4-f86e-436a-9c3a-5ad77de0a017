package com.gwmfc.filter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gwmfc.config.WhiteUrlMapping;

import com.gwmfc.util.Result;
import com.gwmfc.vo.Authentication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.netty.ByteBufFlux;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
@RefreshScope
public class JwtTokenFilter implements GlobalFilter, Ordered {


    public Pattern staticResource = Pattern.compile("js|css|ico|html|htm");

    private String servicePrefix = "/gw-sso/";

    @Autowired
    private WhiteUrlMapping whiteUrlMapping;


    @Autowired
    private WebClient.Builder webClientBuilder;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        log.info("filter jwt token and verify ");
        /**
         *
         * 白名单过滤
         */

        ServerHttpRequest request = exchange.getRequest();
        String path = request.getPath().toString();
        boolean urlByPass = isUrlByPass(path);
        log.info("bypass url :{},{}",path,urlByPass);
        if (urlByPass) {
            return chain.filter(exchange);
        }
        String token = null;
        if (null != exchange.getRequest().getHeaders()) {
            if (null != exchange.getRequest().getHeaders().get(HttpHeaders.AUTHORIZATION)) {
                token = exchange.getRequest().getHeaders().get(HttpHeaders.AUTHORIZATION).get(0);
            }
        }

        log.info("auth-gateway token:{}",token);
        Authentication authentication = new Authentication();
        authentication.setJwtToken(token);
        return webClientBuilder.build().method(HttpMethod.POST).uri("http://auth-gateway/")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization",token)
                .retrieve()
                .bodyToMono(Result.class)
                .flatMap(s -> {
                    if (HttpStatus.OK.value() == s.getCode()) {
                        return chain.filter(exchange);
                    } else {
                        ServerHttpResponse response = exchange.getResponse();
                        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
                        return response.writeAndFlushWith(Flux.just(ByteBufFlux.just(response.bufferFactory().wrap(getWrapData(s)))));
                    }
                });
    }


    @Override
    public int getOrder() {
        return -10;
    }


    private byte[] getWrapData(Result result) {
        try {
            return new ObjectMapper().writeValueAsString(result).getBytes();
        } catch (JsonProcessingException e) {
            log.error("serializable result error {}", e);
        }
        return "".getBytes();
    }


    /**
     * filter white url list
     *
     * @param path
     * @return
     */
    private boolean isUrlByPass(String path) {
        //judge white url list
        List<String> whiteUrlList = whiteUrlMapping.getWhiteUrlList();
        boolean isWhiteUrlPassed = whiteUrlList.stream().anyMatch(path::contains);

        Matcher matcher = staticResource.matcher(path);
        if(matcher.find()){
            return  true;
        }
        if (isWhiteUrlPassed) {
            return true;
        } else {
            // judge black url list
            Map<String, String> blackUrlMaps = whiteUrlMapping.getBlackUrlMaps();
            Set<String> whiteServices = blackUrlMaps.keySet();
            boolean whiteListContains = whiteServices.stream().anyMatch(path::contains);
            if (whiteListContains) {
                for (String key : whiteServices) {
                    if (path.contains(servicePrefix + key)) {
                        String exceptedUrlForCurrentService = blackUrlMaps.get(key);
                        return Arrays.stream(exceptedUrlForCurrentService.split(",")).noneMatch(path::endsWith);
                    }
                }
            }
            return false;
        }
    }

    /**
     * judge url is ssoUrl
     * @param path
     * @return
     */
    private boolean isSsoByPass(String path){
        List<String> ssoUrlList = whiteUrlMapping.getSsoUrlList();
        return ssoUrlList.stream().anyMatch(path::contains);

    }
}
