package com.gwmfc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.DataProcessingManageDto;
import com.gwmfc.entity.DataProcessingManageEntity;
import com.gwmfc.service.DataProcessingManageService;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static com.gwmfc.util.StatusCodeEnum.SUCCESS;


/**
 * <AUTHOR>
 * @create 2023-11-20 16:58
 */
@Api(value = "数据加工管理接口", tags = "数据加工管理接口")
@RestController
@RequestMapping("/dataProcessingManage")
public class DataProcessingManageController {

    @Resource
    DataProcessingManageService dataProcessingManageService;

    /**
     * 列表查询接口
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "列表查询接口", produces = "application/json")
    @PostMapping("/list")
    public Result list(@RequestBody PageForm<DataProcessingManageEntity> page) {
        IPage data = dataProcessingManageService.page(page.getParam(), page.getCurrent(), page.getSize());
        return Result.ok(data.getRecords(), data.getTotal());
    }

    /**
     * 根据id查询单个数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "根据id查询单个数据", produces = "application/json")
    @GetMapping("/{id}")
    public Result getOne(@PathVariable("id") Long id) {
        Result result = new Result();
        DataProcessingManageDto dataProcessingManageDto = dataProcessingManageService.getOne(id);
        result.setData(dataProcessingManageDto);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    /**
     * 更新数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "更新数据", produces = "application/json")
    @PostMapping("/update")
    public Result update(@RequestBody DataProcessingManageDto dataProcessingManageDto, @CurrentUser User user) {
        dataProcessingManageDto.setUpdateUser(user.getUserName());
        dataProcessingManageDto.setUpdateTime(LocalDateTime.now());
        Integer count = dataProcessingManageService.update(dataProcessingManageDto);
        if (count == 0) {
            return Result.error("更新失败");
        }
        return Result.ok();
    }

    /**
     * 保存数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "保存数据", produces = "application/json")
    @PostMapping("/save")
    public Result save(@RequestBody DataProcessingManageDto dataProcessingManageDto, @CurrentUser User user) {
        dataProcessingManageDto.setCreateUser(user.getUserName());
        dataProcessingManageDto.setCreateTime(LocalDateTime.now());
        dataProcessingManageService.save(dataProcessingManageDto);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", produces = "application/json")
    @PostMapping("/remove")
    public Result remove(@RequestParam("id") Long id) {
        Integer count = dataProcessingManageService.remove(id);
        if (count == 0) {
            return Result.error("删除失败");
        }
        return Result.ok();
    }

    /**
     * 批量删除
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "批量删除", produces = "application/json")
    @PostMapping("/batchRemove")
    public Result batchRemove(@RequestBody List<Long> idList) {
        dataProcessingManageService.batchRemove(idList);
        return Result.ok();
    }


}

