package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024年02月05日 13:31
 */
@Data
@ApiModel(value = "irr结果保存")
@TableName("irr_cal_result")
public class IrrCalResultEntity {
    @ApiModelProperty("主键id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "business_type", comment = "业务类型")
    private Integer businessType;
    @TableFieldMapping(value = "product_proposal_name", comment = "产品提案名称")
    private String productProposalName;

    @TableFieldMapping(value = "product_proposal_group_uuid", comment = "产品提案组uuid")
    private String productProposalGroupUuid;

    @TableFieldMapping(value = "weighted_between_groups_or_not", comment = "是否组间加权")
    private Integer weightedBetweenGroupsOrNot;

    @TableFieldMapping(value = "weighted_or_not", comment = "是否组内加权计算")
    private Integer weightedOrNot;

    @TableFieldMapping(value = "product_proposal_id", comment = "提案id")
    private Long productProposalId;

    @TableFieldMapping(value = "brand", comment = "品牌")
    private String brand;
    @TableFieldMapping(value = "loan_amount", comment = "贷额")
    private Double loanAmount;
    @TableFieldMapping(value = "payment_days", comment = "贷期")
    private Double paymentDays;

    @TableFieldMapping(value = "customer_interest_rate", comment = "客户利率（%）")
    private Double customerInterestRate;
    @TableFieldMapping(value = "settled_rate", comment = "结算利率（%）")
    private Double settledRate;
    @TableFieldMapping(value = "repayment_mode", comment = "还款方式(后期改枚举)")
    private Integer repaymentMode;
    @TableFieldMapping(value = "repayment_mode_explain", comment = "还款方式说明")
    private String repaymentModeExplain;
    @TableFieldMapping(value = "contracts_proportion", comment = "合同占比（%）")
    private Double contractsProportion;

    //风险
    @TableFieldMapping(value = "risk_loss_rate", comment = "风险损失率（%）")
    private Double riskLossRate;

    @TableFieldMapping(value = "base_commission_ratio", comment = "基础服务费（%）")
    private Double baseCommissionRatio;
    @TableFieldMapping(value = "ladder_bonus_ratio", comment = "阶梯奖金比例（%）")
    private Double ladderBonusRatio;
    @TableFieldMapping(value = "promotion_bonus_proportion", comment = "促销奖金比例（%）")
    private Double promotionBonusProportion;

    //政策
    @TableFieldMapping(value = "irr", comment = "IRR")
    private Double irr;
    @TableFieldMapping(value = "roa", comment = "ROA")
    private Double roa;
    @TableFieldMapping(value = "net_margin", comment = "净利润")
    private Double netMargin;
    @TableFieldMapping(value = "thousand_income", comment = "万元收益")
    private Double thousandIncome;
    @TableFieldMapping(value = "weighted_irr", comment = "加权IRR")
    private Double weightedIrr;
    @TableFieldMapping(value = "irr_template_id", comment = "irr模板ID")
    private Double irrTemplateId;


    //基础信息
    @TableFieldMapping(value = "personal_reward", comment = "个人奖励")
    private String personalReward;

    @TableFieldMapping(value = "cal_time", comment = "测算时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime calTime;

    @TableFieldMapping(value = "cal_user", comment = "测算人")
    private String calUser;

    @TableFieldMapping(value = "product_cal_repayment_method_param_dto", comment = "还款方式")
    private String productCalRepaymentMethodParamDto;
    @TableFieldMapping(value = "vehicle_type", comment = "车辆类型")
    private String vehicleType;

    @TableFieldMapping(value = "buss_rebeat", comment = "商务返利")
    private Double bussRebeat;
}
