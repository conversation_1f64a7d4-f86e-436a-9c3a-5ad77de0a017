package com.gwmfc.schedule;

import com.gwmfc.service.MonthlyUsedCarTransactionVolumeService;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Calendar;

/**
 * <AUTHOR>
 * @date 2024年03月22日 10:52
 */
@Component
@Slf4j
public class PriceRangeTradingVolumeMonthSchedule {
    @Resource
    private MonthlyUsedCarTransactionVolumeService monthlyUsedCarTransactionVolumeService;

    @Scheduled(cron = "0 10 0 * * ?")
    @SchedulerLock(name = "catchPriceRangeTradingVolumeMonth", lockAtMostFor = "PT1H", lockAtLeastFor = "PT1H")
    public void catchPriceRangeTradingVolumeMonth() {
        int year = Calendar.getInstance().get(Calendar.YEAR);
        Calendar calendar = Calendar.getInstance();
        int month = calendar.get(Calendar.MONTH) - 1;
        if (month == -1) {
            year = year - 1;
            month = 11;
        }else if(month == 0){
            year = year - 1;
            month = 12;
        }
        String[] tableNameArray = {"price_range_trading_volume"};
        int finalYear = year;
        String monthStr;
        if (month < 10) {
            monthStr = "0".concat(String.valueOf(month));
        } else {
            monthStr = String.valueOf(month);
        }
        Arrays.asList(tableNameArray).forEach(tableName -> {
            try {
                monthlyUsedCarTransactionVolumeService.catchMonthlyUsedCarTransactionVolume(String.valueOf(finalYear),monthStr,tableName, "Scheduled");
            } catch (Exception e) {
                log.error("{}",e);
            }
        });
        log.info("catchMonthlyUsedCarTransactionVolume:{},{}",finalYear,month);
    }
}
