package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalProfitDetailDto
 * @Description TODO
 * @Date 2023/12/13 9:16
 */
@Data
@ApiModel(value = "利润详情dto")
public class ProductCalProfitDetailDto {
    @ApiModelProperty("投放月")
    private Integer putMonth;
    @ApiModelProperty("年月")
    private String startMonth;
    @ApiModelProperty("单量")
    private Double amount;
    @ApiModelProperty("还款日")
    private String dueDate;
    @ApiModelProperty("还款年")
    private String dueYear;
    @ApiModelProperty("利润")
    private Double profitMoney;
}
