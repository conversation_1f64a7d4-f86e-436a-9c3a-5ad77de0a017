package com.gwmfc.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname IrrBatchCalTemplateDto
 * @Description TODO
 * @Date 2024/3/18 14:08
 */
@Data
@ExcelIgnoreUnannotated
public class IrrBatchCalTemplateDto {
    @ExcelProperty("融资类型")
    String financingType;

    @ExcelProperty("产品分类")
    String productClassification;

    @ExcelProperty("贷额")
    Double loanAmount;

    @ExcelProperty("贷期")
    Integer paymentDays;

    @ExcelProperty("客户利率(%)")
    Double customerInterestRate;

    @ExcelProperty("结算利率(%)")
    Double settledRate;

    @ExcelProperty("风险损失率(%)")
    Double riskLossRate;

    @ExcelProperty("基础服务费(%)")
    Double baseCommissionRatio;

    @ExcelProperty("阶梯奖励(%)")
    Double ladderBonusRatio;

    @ExcelProperty("促销奖励(%)")
    Double promotionBonusProportion;

    @ExcelProperty("个人奖励")
    Double personalReward;

    @ExcelProperty("合同占比(%)")
    Double contractsProportion;
}
