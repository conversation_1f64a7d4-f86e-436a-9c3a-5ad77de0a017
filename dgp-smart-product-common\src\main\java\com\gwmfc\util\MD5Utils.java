package com.gwmfc.util;


import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;

import java.io.UnsupportedEncodingException;


@Slf4j
public class MD5Utils {

	private static final String SALT = "1qazxsw2";


	public static String encrypt(String password){
		String md5Password=null;
		String data = SALT+"/"+password;
		try {
			md5Password = DigestUtils.md5DigestAsHex(data.getBytes("utf-8"));
		}catch (UnsupportedEncodingException e){
			log.error("md5 digest exception {}",e);
		}
		return md5Password;
	}


}
