package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.service.CpcaautoService;
import com.gwmfc.service.GroupDataService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Classname GroupDataController
 * @Description 处理集团数据
 * @Date 2023/10/19 13:56
 */
@Api(tags = "集团数据处理")
@RestController
@RequestMapping("/groupdata")
public class GroupDataController {

    @Resource
    private GroupDataService groupDataService;

    /**
     * 处理集团数据文件名为月度 或者 周度应该处理的文件名
     *
     * @return
     */
    @ApiOperation("处理集团数据文件名")
    @GetMapping("/updateGroupFileName/{fileType}/{date}")
    public Integer updateGroupFileName(@PathVariable("fileType") String fileType, @PathVariable("date") String date) {
        return groupDataService.updateGroupFileName(fileType,date);
    }
}
