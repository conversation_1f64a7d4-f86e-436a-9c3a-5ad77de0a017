package com.gwmfc.service;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.dao.ProductBusinessLineDao;
import com.gwmfc.dao.ProductLineDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.*;
import com.gwmfc.entity.ProductBusinessLineEntity;
import com.gwmfc.entity.ProductCalBasicInfoEntity;
import com.gwmfc.entity.ProductCalDataExportEntity;
import com.gwmfc.entity.ProductLineEntity;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @Classname ProductMatrixService
 * @Description TODO
 * @Date 2024/8/12 9:38
 */
@Service
@Slf4j
@RefreshScope
public class ProductMatrixService extends ServiceImpl<ProductBusinessLineDao, ProductBusinessLineEntity> {

    @Autowired
    private ProductBusinessLineDao productBusinessLineDao;

    @Autowired
    private ProductLineDao productLineDao;

    @Autowired
    private ProductLineService productLineService;

    @Resource(name = "prestoKafkaTemplate")
    private JdbcTemplate prestoTemplate;

    @Value("${product-bussiness-line-upSql}")
    private String bussLineSql;

    public Result saveBusinessLine(ProductBusinessLineDto productBusinessLineDto, User user){
        log.info("save businessLine info start");
        Result result = new Result();
        try{
            ProductBusinessLineEntity productBusinessLineEntity = new ProductBusinessLineEntity();
            BeanUtils.copyProperties(productBusinessLineDto, productBusinessLineEntity);
            if(productBusinessLineDto.getState() == 1){
                /**
                 * 如果有业务条线有效，则增加生效日期
                 */
                productBusinessLineEntity.setEffectiveDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

            }
            productBusinessLineEntity.setCreateUser(user.getName());
            productBusinessLineEntity.setCreateTime(new Date());
            productBusinessLineEntity.setUpdateTime(new Date());
            productBusinessLineEntity.setUpdateUser(user.getName());
            productBusinessLineDao.insert(productBusinessLineEntity);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch(Exception e){
            log.error("保存业务条线信息异常", e);
            result.setCode(FAIL.getCode());
            result.setMessage("保存业务条线信息异常");
        }
        return result;
    }

    public Result updateBusinessLine(ProductBusinessLineEntity productBusinessLineEntity, User user){
        log.info("update businessLine info start");
        Result result = new Result();
        try{
            ProductBusinessLineEntity productBusinessLineEntityOld = productBusinessLineDao.selectById(productBusinessLineEntity.getId());
            productBusinessLineEntityOld.setUpdateUser(user.getName());
            productBusinessLineEntityOld.setUpdateTime(new Date());
            productBusinessLineEntityOld.setDescription(productBusinessLineEntity.getDescription());
            productBusinessLineEntityOld.setBussType(productBusinessLineEntity.getBussType());
            productBusinessLineEntityOld.setTrack(productBusinessLineEntity.getTrack());
            productBusinessLineEntityOld.setSubdivision(productBusinessLineEntity.getSubdivision());

            if(!productBusinessLineEntityOld.getState().equals(productBusinessLineEntity.getState())){
                if(productBusinessLineEntity.getState() == 0){
                    productBusinessLineEntityOld.setState(productBusinessLineEntity.getState());
                    productBusinessLineEntityOld.setExpiryDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                }else{
                    productBusinessLineEntityOld.setState(productBusinessLineEntity.getState());
                    productBusinessLineEntityOld.setEffectiveDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    productBusinessLineEntityOld.setExpiryDate(null);
                }
            }
            productBusinessLineDao.updateById(productBusinessLineEntityOld);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("更新业务条线信息异常",e);
            result.setCode(FAIL.getCode());
            result.setMessage("更新业务条线信息异常");
        }
        return result;
    }

    public Result deleteBusinessLine(Integer id){
        log.info("delete businessLine info start");
        Result result = new Result();
        try{
            productBusinessLineDao.deleteById(id);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("删除业务条线异常",e);
            result.setCode(FAIL.getCode());
            result.setMessage("删除业务条线异常");
        }
        return result;
    }

    public Result getBusinessLineDetail(Integer id){
        log.info("get businessLine detail start");
        Result result = new Result();
        try{
            ProductBusinessLineEntity productBusinessLineEntity = productBusinessLineDao.selectById(id);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productBusinessLineEntity);
        }catch (Exception e){
            log.error("获取业务条线详情异常",e);
            result.setCode(FAIL.getCode());
            result.setMessage("获取业务条线详情异常");
        }
        return result;
    }

    public Result getBusinessLineList(ProductBusinessLineDto productBusinessLineDto, Integer current, Integer size){
        log.info("get businessLine list start");
        Result result = new Result();
        try{
            IPage<ProductBusinessLineEntity> page = new Page<>(current, size);
            ProductBusinessLineEntity productBusinessLineEntity = new ProductBusinessLineEntity();
            if(productBusinessLineDto != null){
                BeanUtils.copyProperties(productBusinessLineDto, productBusinessLineEntity);
            }

            QueryWrapper queryWrapper = new QueryWrapper(productBusinessLineEntity);
            if(StringUtils.hasLength(productBusinessLineEntity.getBussType())){
                String bussType = productBusinessLineEntity.getBussType();
                productBusinessLineEntity.setBussType(null);
                queryWrapper.like("buss_type", bussType);
            }

            if(StringUtils.hasLength(productBusinessLineEntity.getTrack())){
                String track = productBusinessLineEntity.getTrack();
                productBusinessLineEntity.setTrack(null);
                queryWrapper.like("track", track);
            }
            queryWrapper.orderByDesc("update_time");

            IPage<ProductBusinessLineEntity> iPage = productBusinessLineDao.selectPage(page,queryWrapper);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(iPage.getRecords());
            result.setTotal(iPage.getTotal());
        }catch (Exception e){
            log.error("获取业务条线列表异常",e);
            result.setCode(FAIL.getCode());
            result.setMessage("获取业务条线列表异常");
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public Result oneClickUpdateBusinessLine(User user){
        Result result = new Result();
        /**
         * 先删除库里的存量数据
         */
        QueryWrapper<ProductBusinessLineEntity> queryWrapper = new QueryWrapper<ProductBusinessLineEntity>();
        log.info("业务条线一键更新-删除历史数据");
        remove(queryWrapper);

        log.info("业务条线一键更新-查询数仓数据sql:{}",bussLineSql);
        SqlRowSet resultSet=prestoTemplate.queryForRowSet(bussLineSql);

        List<ProductBusinessLineEntity> productBusinessLineEntityList = new ArrayList<>();
        while (resultSet.next()){
            ProductBusinessLineEntity productBusinessLineEntity = new ProductBusinessLineEntity();
            productBusinessLineEntity.setBussType(resultSet.getString("business_type"));
            productBusinessLineEntity.setTrack(resultSet.getString("vehicle_type"));
            productBusinessLineEntity.setSubdivision(resultSet.getString("vehicle_type_detail"));
            productBusinessLineEntity.setState(1);
            productBusinessLineEntity.setEffectiveDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            productBusinessLineEntity.setCreateUser(user.getName());
            productBusinessLineEntity.setCreateTime(new Date());
            productBusinessLineEntity.setUpdateTime(new Date());
            productBusinessLineEntity.setUpdateUser(user.getName());
            productBusinessLineEntityList.add(productBusinessLineEntity);
        }

        log.info("业务条线一键更新-保存数仓数据");
        saveBatch(productBusinessLineEntityList);

        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    public Result getProductLineList(ProductLineDto productLineDto, Integer current, Integer size){
        log.info("get productLine list start");
        Result result = new Result();
        try{
            IPage<ProductLineEntity> page = new Page<>(current, size);
            ProductLineEntity productLineEntity = new ProductLineEntity();
            if(productLineDto != null){
                BeanUtils.copyProperties(productLineDto, productLineEntity);
            }

            QueryWrapper queryWrapper = new QueryWrapper(productLineEntity);
            if(StringUtils.hasLength(productLineEntity.getBussType())){
                String bussType = productLineEntity.getBussType();
                productLineEntity.setBussType(null);
                queryWrapper.like("buss_type", bussType);
            }

            if(StringUtils.hasLength(productLineEntity.getProductClassification())){
                String productClassification = productLineEntity.getProductClassification();
                productLineEntity.setProductClassification(null);
                queryWrapper.like("product_classification", productClassification);
            }
            queryWrapper.orderByDesc("update_time");

            IPage<ProductLineEntity> iPage = productLineDao.selectPage(page,queryWrapper);

            List<ProductLineDto> productLineDtoList = new ArrayList<>();

            if(iPage.getRecords().size() > 0){
                List<ProductLineEntity> productLineEntityList = iPage.getRecords();
                for(ProductLineEntity productLineEntity1 : productLineEntityList){
                    ProductLineDto productLineDto1 = new ProductLineDto();
                    BeanUtils.copyProperties(productLineEntity1, productLineDto1);
                    productLineDto1.setTrack(GsonUtil.jsonToList(productLineEntity1.getTrack(), String.class));
                    productLineDto1.setSubdivision(GsonUtil.jsonToList(productLineEntity1.getSubdivision(), String.class));
                    productLineDtoList.add(productLineDto1);
                }
            }

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productLineDtoList);
            result.setTotal(iPage.getTotal());
        }catch (Exception e){
            log.error("获取产品条线列表异常",e);
            result.setCode(FAIL.getCode());
            result.setMessage("获取产品条线列表异常");
        }
        return result;
    }

    public Result saveProductLine(ProductLineDto productLineDto, @CurrentUser User user){
        log.info("save productLine info start");
        Result result = new Result();
        try{
            ProductLineEntity productLineEntity = new ProductLineEntity();
            BeanUtils.copyProperties(productLineDto, productLineEntity);
            if(productLineDto.getState() == 1){
                /**
                 * 如果有产品条线有效，则增加生效日期
                 */
                productLineEntity.setEffectiveDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

            }
            productLineEntity.setTrack(GsonUtil.toJson(productLineDto.getTrack()));
            productLineEntity.setSubdivision(GsonUtil.toJson(productLineDto.getSubdivision()));
            productLineEntity.setCreateUser(user.getName());
            productLineEntity.setCreateTime(new Date());
            productLineEntity.setUpdateTime(new Date());
            productLineEntity.setUpdateUser(user.getName());
            productLineDao.insert(productLineEntity);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch(Exception e){
            log.error("保存产品条线信息异常", e);
            result.setCode(FAIL.getCode());
            result.setMessage("保存产品条线信息异常");
        }
        return result;
    }

    public Result updateProductLine(ProductLineDto productLineDto, User user){
        log.info("update productLine info start");
        Result result = new Result();
        try{
            ProductLineEntity productLineEntityOld = productLineDao.selectById(productLineDto.getId());
            productLineEntityOld.setUpdateUser(user.getName());
            productLineEntityOld.setUpdateTime(new Date());
            productLineEntityOld.setBussType(productLineDto.getBussType());
            productLineEntityOld.setTrack(GsonUtil.toJson(productLineDto.getTrack()));
            productLineEntityOld.setSubdivision(GsonUtil.toJson(productLineDto.getSubdivision()));
            productLineEntityOld.setProductClassification(productLineDto.getProductClassification());
            productLineEntityOld.setProductSecondaryClassification(productLineDto.getProductSecondaryClassification());
            productLineEntityOld.setProductPosition(productLineDto.getProductPosition());
            productLineEntityOld.setProductPositionDescription(productLineDto.getProductPositionDescription());
            productLineEntityOld.setProductProgramDescription(productLineDto.getProductProgramDescription());

            if(!productLineEntityOld.getState().equals(productLineDto.getState())){
                if(productLineDto.getState() == 0){
                    productLineEntityOld.setState(productLineDto.getState());
                    productLineEntityOld.setExpiryDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                }else{
                    productLineEntityOld.setState(productLineDto.getState());
                    productLineEntityOld.setEffectiveDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    productLineEntityOld.setExpiryDate(null);
                }
            }
            productLineDao.updateById(productLineEntityOld);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("更新产品条线信息异常",e);
            result.setCode(FAIL.getCode());
            result.setMessage("更新产品条线信息异常");
        }
        return result;
    }

    public Result deleteProductLine(Integer id){
        log.info("delete productLine info start");
        Result result = new Result();
        try{
            productLineDao.deleteById(id);
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("删除产品条线异常",e);
            result.setCode(FAIL.getCode());
            result.setMessage("删除产品条线异常");
        }
        return result;
    }

    public Result getProductLineDetail(Integer id){
        log.info("get productLine detail start");
        Result result = new Result();
        try{
            ProductLineEntity productLineEntity = productLineDao.selectById(id);
            ProductLineDto productLineDto = new ProductLineDto();
            BeanUtils.copyProperties(productLineEntity,productLineDto);
            productLineDto.setTrack(GsonUtil.jsonToList(productLineEntity.getTrack(), String.class));
            productLineDto.setSubdivision(GsonUtil.jsonToList(productLineEntity.getSubdivision(),String.class));
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productLineDto);
        }catch (Exception e){
            log.error("获取产品条线详情异常",e);
            result.setCode(FAIL.getCode());
            result.setMessage("获取产品条线详情异常");
        }
        return result;
    }

    public Result getBusinessLineSort(){
        Result result = new Result();
        try{
            List<String> bussTypes = productBusinessLineDao.getBussTypes();
            Map<String,List<Map<String,List<String>>>> map = new HashMap<>();
            if(bussTypes != null && bussTypes.size()>0){
                for (String bussType : bussTypes) {
                    List<Map<String,List<String>>> list = new ArrayList<>();
                    List<String> trackByBussTypes = productBusinessLineDao.getTrackByBussType(bussType);
                    if(trackByBussTypes != null && trackByBussTypes.size()>0){
                        Map<String,List<String>> s = new HashMap<>();
                        for (String trackByBussType : trackByBussTypes) {
                            List<String> subdivisionByBussTypeAndTracks = productBusinessLineDao.getSubdivisionByBussTypeAndTrack(bussType, trackByBussType);
                            s.put(trackByBussType,subdivisionByBussTypeAndTracks);
                        }
                        list.add(s);
                    }
                    map.put(bussType,list);
                }
            }

            log.info("结果为：{}",GsonUtil.toJson(map));
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(GsonUtil.toJson(map));
        }catch (Exception e){
            log.error("获取业务线排序异常",e);
            result.setCode(FAIL.getCode());
            result.setMessage("获取业务线排序异常");
        }
        return result;
    }

    public Result exportCalData(String bussType,HttpServletResponse response){
        Result result = new Result();
        try {
            String fileName = "产品矩阵数据导出";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            fileName = fileName+".xlsx";

            String classStr = "com.gwmfc.dto.ExportProductMatrixDto";
            Class classExport = Class.forName(classStr);

            QueryWrapper queryWrapper = new QueryWrapper();
            if(StringUtils.hasText(bussType)){
                queryWrapper.eq("buss_type",bussType);
            }
            List<ProductLineEntity> productLineEntityList = productLineDao.selectList(queryWrapper);
            List<ExportProductMatrixDto> exportProductMatrixDtoList = new ArrayList<>();
            if(productLineEntityList != null && productLineEntityList.size()>0){
                int count = 1;
                for (ProductLineEntity productLineEntity : productLineEntityList) {
                    ExportProductMatrixDto exportProductMatrixDto = new ExportProductMatrixDto();
                    exportProductMatrixDto.setNum(count);
                    BeanUtils.copyProperties(productLineEntity,exportProductMatrixDto);

                    String track = productLineEntity.getTrack();
                    List<String> trackList = GsonUtil.jsonToList(track, String.class);
                    String subdivision = productLineEntity.getSubdivision();
                    List<String> subdivisionList = GsonUtil.jsonToList(subdivision, String.class);

                    exportProductMatrixDto.setTrack(String.join(",",trackList));
                    exportProductMatrixDto.setSubdivision(String.join(",",subdivisionList));

                    exportProductMatrixDtoList.add(exportProductMatrixDto);
                    count++;
                }
            }

            OutputStream outputStream = response.getOutputStream();
            ExcelWriter excelWriter = EasyExcelFactory.write(outputStream).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(1, "Sheet1").head(classExport)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();
            excelWriter.write(exportProductMatrixDtoList, writeSheet);


            response.setCharacterEncoding("utf-8");
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);

//            EasyExcel.write(writerOutputStream, ExportEarlySquareProbabilityDto.class).sheet("sheet1").doWrite(exportEarlySquareProbabilityDtos);
            excelWriter.finish();
            outputStream.flush();
        } catch (Exception e) {
            log.error("exportCalData error",e);
        }
        return result;
    }
}
