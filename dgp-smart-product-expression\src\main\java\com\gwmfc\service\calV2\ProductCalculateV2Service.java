package com.gwmfc.service.calV2;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.dozermapper.core.DozerBeanMapperBuilder;
import com.github.dozermapper.core.Mapper;
import com.google.common.base.CaseFormat;
import com.gwmfc.constant.EarlySquareRatioRulePamEnum;
import com.gwmfc.constant.ProductCalRepaymentMethodEnum;
import com.gwmfc.dao.*;
import com.gwmfc.dao.calV2.ProductCalBaseTemplateV2Dao;
import com.gwmfc.dto.*;
import com.gwmfc.entity.*;
import com.gwmfc.entity.calV2.ProductCalBaseTemplateV2Entity;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.service.ProductProposalCalService;
import com.gwmfc.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;
import static java.lang.Double.NaN;

/**
 * <AUTHOR>
 * @Classname ProductCalculateService
 * @Description 计算产品相关指标
 * @Date 2023/9/14 9:14
 */
@Service
@Slf4j
@RefreshScope
public class ProductCalculateV2Service {

    @Resource(name = "prestoKafkaTemplate")
    private JdbcTemplate prestoTemplate;

    @Autowired
    private ProductCalBaseTemplateV2Dao productCalBaseTemplateV2Dao;

    @Autowired
    private ProductCalDao productCalDao;

    @Autowired
    private ProductCalEarlySquareProbabilityDao productCalEarlySquareProbabilityDao;

    @Autowired
    private ProductCalBasicInfoDao productCalBasicInfoDao;

    @Autowired
    private ProductCalEarlySquareParamSubDao productCalEarlySquareParamSubDao;

    @Autowired
    private ProductProposalCalService productProposalCalService;

    @Value("${bpProbabilitySql}")
    private String bpProbabilitySql;

    @Value("${qpProbabilitySql}")
    private String qpProbabilitySql;

    @Value("${escProbabilitySql}")
    private String escProbabilitySql;

    /**
     * 综合考虑联合贷计算IRR
     * @param productCalculateDto
     * @return
     */
    public Result calculateAllUnionParam(ProductCalculateDto productCalculateDto){
        if(productCalculateDto.getProductCalUnionLoanParamDto()!=null && productCalculateDto.getProductCalUnionLoanParamDto().getBankLoanRatio()!=null){
            Mapper mapper = DozerBeanMapperBuilder.buildDefault();
            ProductCalculateDto productCalculateDtoOurs = mapper.map(productCalculateDto,ProductCalculateDto.class);
            Result result = calculateAllParam(productCalculateDto);
            /**
             * 如果返回结果不是200，直接返回失败
             */
            if(!SUCCESS.getCode().equals(result.getCode())){
                return result;
            }

            productCalculateDtoOurs.setProductCalUnionLoanParamDto(null);
            Result ourAllLoanIrr = calculateAllParam(productCalculateDtoOurs);
            if(!SUCCESS.getCode().equals(ourAllLoanIrr.getCode())){
                return ourAllLoanIrr;
            }

            ProductCalculateDto productCalculateDtoUnion = (ProductCalculateDto)result.getData();
            ProductCalculateDto productCalculateDtoOurAllLoan = (ProductCalculateDto)ourAllLoanIrr.getData();

            productCalculateDtoUnion.getProductCalResultInfoDto().setResultOurFullLoanIrr(productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultIrr());
            productCalculateDtoUnion.getProductCalResultInfoDto().setResultOurFullLoanMarginalProfit(productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultMarginalProfit());
            productCalculateDtoUnion.getProductCalResultInfoDto().setResultOurFullLoanNetProfitMoney(productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultNetProfitMoney());
            productCalculateDtoUnion.getProductCalResultInfoDto().setResultOurFullLoanXirr(productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultXirr());
            productCalculateDtoUnion.getProductCalResultInfoDto().setResultOurFullLoanRoa(productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultRoa());

            /**
             * 计算综合IRR、综合净利润、综合边际利润
             */
            QueryWrapper<ProductCalBaseTemplateV2Entity> queryWrapper = new QueryWrapper();
            queryWrapper.eq("buss_type",7777).eq("pam_type","5");
            List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2EntityList = productCalBaseTemplateV2Dao.selectList(queryWrapper);
            for (ProductCalBaseTemplateV2Entity productCalBaseTemplateV2Entity : productCalBaseTemplateV2EntityList) {
                String formula = productCalBaseTemplateV2Entity.getFormula();
                formula = formula.replaceAll("@zjzRoa@",productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultRoa().toString());
                formula = formula.replaceAll("@zjzXirr@",productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultXirr().toString());
                formula = formula.replaceAll("@zjzIrr@",productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultIrr().toString());
                formula = formula.replaceAll("@productPushRate@",productCalculateDto.getProductCalUnionLoanParamDto().getProductPushRate().toString());
                formula = formula.replaceAll("@passingRate@",productCalculateDto.getProductCalUnionLoanParamDto().getPassingRate().toString());
                formula = formula.replaceAll("@bankLoanRatio@",productCalculateDto.getProductCalUnionLoanParamDto().getBankLoanRatio().toString());
                formula = formula.replaceAll("@lhdIrr@",productCalculateDtoUnion.getProductCalResultInfoDto().getResultIrr().toString());
                formula = formula.replaceAll("@zjzNetProfitMoney@",productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultNetProfitMoney().toString());
                formula = formula.replaceAll("@lhdNetProfitMoney@",productCalculateDtoUnion.getProductCalResultInfoDto().getResultNetProfitMoney().toString());
                formula = formula.replaceAll("@zjzMarginalProfit@",productCalculateDtoOurAllLoan.getProductCalResultInfoDto().getResultMarginalProfit().toString());
                formula = formula.replaceAll("@lhdMarginalProfit@",productCalculateDtoUnion.getProductCalResultInfoDto().getResultMarginalProfit().toString());
                formula = formula.replaceAll("@lhdXirr@",productCalculateDtoUnion.getProductCalResultInfoDto().getResultXirr().toString());
                formula = formula.replaceAll("@lhdRoa@",productCalculateDtoUnion.getProductCalResultInfoDto().getResultRoa().toString());
                Map map = new HashMap();
                Double paramValue = (Double) AviatorUtils.calculateValue(map, formula);
                Double paramValueNew = dealScale(productCalBaseTemplateV2Entity.getScaleValue(), paramValue);
                if("integrative_irr".equals(productCalBaseTemplateV2Entity.getItem())){
                    productCalculateDtoUnion.getProductCalResultInfoDto().setResultIntegrativeIrr(paramValueNew);
                }else if("integrative_net_profit_money".equals(productCalBaseTemplateV2Entity.getItem())){
                    productCalculateDtoUnion.getProductCalResultInfoDto().setResultIntegrativeNetProfitMoney(paramValueNew);
                }else if("integrative_xirr".equals(productCalBaseTemplateV2Entity.getItem())){
                    productCalculateDtoUnion.getProductCalResultInfoDto().setResultIntegrativeXirr(paramValueNew);
                }else if("integrative_roa".equals(productCalBaseTemplateV2Entity.getItem())){
                    productCalculateDtoUnion.getProductCalResultInfoDto().setResultIntegrativeRoa(paramValueNew);
                }else{
                    productCalculateDtoUnion.getProductCalResultInfoDto().setResultIntegrativeMarginalProfit(paramValueNew);
                }
            }
            result.setData(productCalculateDtoUnion);
            return result;
        }else{
            return calculateAllParam(productCalculateDto);
        }
    }

    /**
     * 指标测算
     * @param productCalculateDto
     * @return
     */
    public Result calculateAllParam(ProductCalculateDto productCalculateDto) {
        log.info("calculateAllParam start");
        long startTime = System.currentTimeMillis();
        Result result = new Result();
        /**
         * 查询对应的基础模板信息
         */
        ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
        List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities = getCalTemplates(productCalBasicInfoDto);

        /**
         * 首先校验所有必传参数是否为null，如果为null，返回错误提示
         */
        result = checkMustParam(productCalculateDto,productCalBaseTemplateV2Entities);
        if(result.getCode() != 200){
            return result;
        }


        /**
         * 解析参数
         */

        Map<String,Object> paramMap = new HashMap();
        ProductCalBasicParamDto productCalBasicParamDto = productCalculateDto.getProductCalBasicParamDto();
        ProductCalTaxFeeParamDto productCalTaxFeeParamDto = productCalculateDto.getProductCalTaxFeeParamDto();
        ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto = productCalculateDto.getProductCalRepaymentMethodParamDto();
        ProductCalEarlySquareParamDto productCalEarlySquareParamDto = productCalculateDto.getProductCalEarlySquareParamDto();
        ProductCalculateEarlySquareDto  productCalculateEarlySquareDto = productCalculateDto.getProductCalculateEarlySquareDto();
        ProductCalUnionLoanParamDto productCalUnionLoanParamDto = productCalculateDto.getProductCalUnionLoanParamDto();
        ProductCalProfitDetailDto productCalProfitDetailDto = productCalculateDto.getProductCalProfitDetailDto();

        boolean parseFlag = parseParams(paramMap,productCalBasicParamDto,productCalTaxFeeParamDto,productCalRepaymentMethodParamDto,productCalEarlySquareParamDto,productCalculateEarlySquareDto,productCalProfitDetailDto,productCalUnionLoanParamDto,productCalBasicInfoDto);

        if(parseFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("解析参数失败");
            return result;
        }

        /**
         * 计算前置方法
         */
        Result beforeCalResult = beforeCalDeal(productCalBaseTemplateV2Entities,paramMap,productCalculateDto);
        if(beforeCalResult.getCode() != 200){
            return beforeCalResult;
        }

        /**
         * 按期数计算提前结清参数赋值，放到 前置处理方法后，因为等本等息，本金等比分段贷，前置方法中会用到dealArrayOrMap计算贴息，需要期限为全部期限
         */
        if(productCalculateEarlySquareDto != null){
            paramMap.put("earlySquareTimeLimit",productCalculateEarlySquareDto.getEarlySquareTimeLimit());
        }

        /**
         * 计算基本参数
         */
        boolean basicFlag = calulateBasicParam(paramMap,productCalBaseTemplateV2Entities,productCalBasicParamDto,productCalBasicInfoDto);

        Double actualLoanMoneyValue = productCalBasicParamDto.getActualLoanMoney();

        if(basicFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("计算基本参数失败");
            return result;
        }


        /**
         * 计算税费参数
         */
        boolean taxFeeFlag = calulateTaxFeeParam(paramMap,productCalBaseTemplateV2Entities,productCalTaxFeeParamDto,productCalBasicInfoDto);

        if(taxFeeFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("计算税费参数失败");
            return result;
        }

        if(productCalRepaymentMethodParamDto != null){
            /**
             * 计算还款方式参数
             */
            boolean repaymentMethodFlag = calulateRepaymentMethodParam(paramMap,productCalBaseTemplateV2Entities,productCalRepaymentMethodParamDto,productCalBasicInfoDto);

            if(repaymentMethodFlag == false){
                result.setCode(FAIL.getCode());
                result.setMessage("计算还款方式参数失败");
                return result;
            }
        }

        /**
         * 计算提前结清参数
         */
        if(productCalEarlySquareParamDto != null){
            boolean earlySquareFlag = calulateEarlySquareParam(productCalBasicInfoDto,productCalBasicParamDto,productCalEarlySquareParamDto);

            if(earlySquareFlag == false){
                result.setCode(FAIL.getCode());
                result.setMessage("计算提前结清参数失败");
                return result;
            }
        }

        /**
         * 计算联合贷参数
         */
        boolean isUnionFlag = false;
        if(productCalUnionLoanParamDto != null && productCalUnionLoanParamDto.getBankLoanRatio() != null){
            isUnionFlag = true;
            boolean unionLoanFlag = calulateUnionLoanParam(paramMap,productCalBaseTemplateV2Entities,productCalUnionLoanParamDto,productCalBasicInfoDto,productCalculateDto);
            if(unionLoanFlag == false){
                result.setCode(FAIL.getCode());
                result.setMessage("计算联合贷参数失败");
                return result;
            }
        }

        /**
         * 计算现金流
         */
        boolean cashStremFlag = calulateCashStrem(paramMap,productCalBaseTemplateV2Entities,productCalculateDto,actualLoanMoneyValue,isUnionFlag);

        if(cashStremFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("计算现金流失败");
            return result;
        }

        /**
         * 计算后置方法
         */
        Result afterCalResult = afterCalDeal(paramMap,productCalBaseTemplateV2Entities,productCalculateDto,isUnionFlag);

        if(afterCalResult.getCode() != 200){
            return afterCalResult;
        }

        /**
         * 计算返回结果
         */
        boolean resultInfoFlag = calulateResultInfo(paramMap,productCalBaseTemplateV2Entities,productCalculateDto,isUnionFlag);

        if(resultInfoFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("计算返回结果失败");
            return result;
        }

        /**
         * 处理小数百分比
         */
        boolean scalePercentFlag = dealScalePercent(productCalculateDto,productCalBaseTemplateV2Entities);

        if(scalePercentFlag == false){
            result.setCode(FAIL.getCode());
            result.setMessage("处理百分比小数失败");
            return result;
        }

        result.setData(productCalculateDto);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());

        long endTime = System.currentTimeMillis();
        log.info("calculateAllParam cost time: "+(endTime - startTime)+"ms");
        log.info("calculateAllParam end");
        return result;
    }

    public List<ProductCalBaseTemplateV2Entity> getCalTemplates(ProductCalBasicInfoDto productCalBasicInfoDto){
        QueryWrapper<ProductCalBaseTemplateV2Entity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("buss_type",productCalBasicInfoDto.getBusinessType());
        queryWrapper.or((qw) -> qw.isNull("buss_type").eq("repayment_method",productCalBasicInfoDto.getRepaymentMethod()));

        List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities = productCalBaseTemplateV2Dao.selectList(queryWrapper);
        productCalBaseTemplateV2Entities = productCalBaseTemplateV2Entities.stream().map(productCalBaseTemplateV2Entity -> {
            productCalBaseTemplateV2Entity.setItem(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalBaseTemplateV2Entity.getItem()));
            return productCalBaseTemplateV2Entity;
        }).collect(Collectors.toList());
        return productCalBaseTemplateV2Entities;
    }

    /**
     * 校验必输项
     * @param productCalculateDto
     * @param productCalBaseTemplateV2Entities
     * @return
     */
    public Result checkMustParam(ProductCalculateDto productCalculateDto,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities){
        Result result = new Result();
        log.info("checkMustParam start");
        long startTime = System.currentTimeMillis();
        /**
         * 校验基本参数
         */
        ProductCalBasicParamDto productCalBasicParamDto = productCalculateDto.getProductCalBasicParamDto();
        Map<Object, Object> basicParamMap = GsonUtil.jsonToMap(GsonUtil.toJson(productCalBasicParamDto));
        List<ProductCalBaseTemplateV2Entity> basicParamBaseTemplateList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
            return "0".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getMust());
        }).collect(Collectors.toList());
        result = checkExist(basicParamMap,basicParamBaseTemplateList);
        if(result.getCode() != 200){
            return result;
        }

        /**
         * 校验税费参数
         */
        ProductCalTaxFeeParamDto productCalTaxFeeParamDto = productCalculateDto.getProductCalTaxFeeParamDto();
        Map<Object, Object> taxFeeParamMap = GsonUtil.jsonToMap(GsonUtil.toJson(productCalTaxFeeParamDto));
        List<ProductCalBaseTemplateV2Entity> taxFeeParamBaseTemplateList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
            return "1".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getMust());
        }).collect(Collectors.toList());
        result = checkExist(taxFeeParamMap,taxFeeParamBaseTemplateList);
        if(result.getCode() != 200){
            return result;
        }
        /**
         * 校验还款方式参数
         */
        ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto = productCalculateDto.getProductCalRepaymentMethodParamDto();
        Map<Object, Object> repaymentMethodParamMap = GsonUtil.jsonToMap(GsonUtil.toJson(productCalRepaymentMethodParamDto));
        List<ProductCalBaseTemplateV2Entity> repaymentMethodParamBaseTemplateList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
            return "2".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getMust());
        }).collect(Collectors.toList());
        result = checkExist(repaymentMethodParamMap,repaymentMethodParamBaseTemplateList);
        if(result.getCode() != 200){
            return result;
        }

        Double actualLoanMoney = productCalBasicParamDto.getActualLoanMoney();
        Double packageCarPrice = productCalRepaymentMethodParamDto.getPackageCarPrice();
        Double downpaymentsRatio = productCalRepaymentMethodParamDto.getDownpaymentsRatio();

        if(actualLoanMoney == null && (packageCarPrice == null || downpaymentsRatio == null)){
            result.setCode(FAIL.getCode());
            result.setMessage("(实际放款额)和(打包车价&首付比例)选一必填");
            return result;
        }

        /**
         * 校验提前结清参数
         */
        ProductCalEarlySquareParamDto productCalEarlySquareParamDto = productCalculateDto.getProductCalEarlySquareParamDto();
        Map<Object, Object> earlySquareParamMap = GsonUtil.jsonToMap(GsonUtil.toJson(productCalEarlySquareParamDto));
        List<ProductCalBaseTemplateV2Entity> earlySquareParamBaseTemplateList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
            return "3".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getMust());
        }).collect(Collectors.toList());
        result = checkExist(earlySquareParamMap,earlySquareParamBaseTemplateList);
        if(result.getCode() != 200){
            return result;
        }

        if(productCalculateDto.getProductCalculateEarlySquareDto()!=null){
            if(productCalEarlySquareParamDto == null){
                result.setCode(FAIL.getCode());
                result.setMessage("提前结清irr测算模板提前结清参数不可为空");
                return result;
            }
        }

        /**
         * 校验联合贷参数
         */
        ProductCalUnionLoanParamDto productCalUnionLoanParamDto = productCalculateDto.getProductCalUnionLoanParamDto();
        Map<Object, Object> unionLoanParamMap = GsonUtil.jsonToMap(GsonUtil.toJson(productCalUnionLoanParamDto));
        List<ProductCalBaseTemplateV2Entity> unionLoanParamBaseTemplateList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
            return "6".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getMust());
        }).collect(Collectors.toList());
        result = checkExist(unionLoanParamMap,unionLoanParamBaseTemplateList);
        if(result.getCode() != 200){
            return result;
        }

        if(productCalUnionLoanParamDto != null && productCalUnionLoanParamDto.getBankLoanRatio() != null){
            QueryWrapper<ProductCalBaseTemplateV2Entity> queryWrapper = new QueryWrapper();
            queryWrapper.eq("buss_type",7777);
            queryWrapper.eq("repayment_method",productCalculateDto.getProductCalBasicInfoDto().getRepaymentMethod());
            List<ProductCalBaseTemplateV2Entity> unionLoanBaseTemplateEntities = productCalBaseTemplateV2Dao.selectList(queryWrapper);
            if(unionLoanBaseTemplateEntities == null || unionLoanBaseTemplateEntities.size() == 0){
                result.setCode(FAIL.getCode());
                result.setMessage("该还款方式暂不支持联合贷");
                return result;
            }
        }

        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        long endTime = System.currentTimeMillis();
        log.info("checkMustParam cost time: "+(endTime - startTime)+"ms");
        log.info("checkMustParam end");
        return result;

    }

    /**
     * 校验参数是否存在
     * @param map
     * @param productCalBaseTemplateV2EntityList
     * @return
     */
    public Result checkExist(Map<Object, Object> map, List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2EntityList){
        Result result = new Result();
        for (ProductCalBaseTemplateV2Entity productCalBaseTemplateV2Entity : productCalBaseTemplateV2EntityList) {
            String item = productCalBaseTemplateV2Entity.getItem();
            if(map.get(item) == null){
                result.setCode(FAIL.getCode());
                result.setMessage("参数："+productCalBaseTemplateV2Entity.getItemDesc()+"为必传，请核查");
                result.setData(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, item));
                return result;
            }
        }
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    public boolean parseParams(Map<String,Object> paramMap,ProductCalBasicParamDto productCalBasicParamDto,ProductCalTaxFeeParamDto productCalTaxFeeParamDto,ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto,ProductCalEarlySquareParamDto productCalEarlySquareParamDto,ProductCalculateEarlySquareDto productCalculateEarlySquareDto,ProductCalProfitDetailDto productCalProfitDetailDto,ProductCalUnionLoanParamDto productCalUnionLoanParamDto,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            log.info("parseParams start");
            /**
             * 阶梯贷修改还款方式参数中月供上升比例为对应得到相反数
             */

            if(ProductCalRepaymentMethodEnum.JTD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                productCalRepaymentMethodParamDto.setDecreaseInMonthlyPaymentRatio(-1*productCalRepaymentMethodParamDto.getDecreaseInMonthlyPaymentRatio());
            }

            paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalBasicParamDto)));
            paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalTaxFeeParamDto)));
            if(productCalRepaymentMethodParamDto != null){
                paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalRepaymentMethodParamDto)));
            }
            if(productCalEarlySquareParamDto != null){
                paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalEarlySquareParamDto)));
            }
            if(productCalculateEarlySquareDto != null){
                paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalculateEarlySquareDto)));
                paramMap.put("earlySquareTimeLimit",null);
            }
            paramMap.put("timeLimit",productCalBasicParamDto.getTimeLimit());

            /**
             * 利润测算参数添加
             */

            if(productCalProfitDetailDto != null){
                paramMap.put("startMonth",productCalProfitDetailDto.getStartMonth());
            }
            /**
             * 联合贷参数
             */
            if(productCalUnionLoanParamDto != null){
                paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalUnionLoanParamDto)));
            }
            log.info("parseParams end");
            return true;
        }catch(Exception e){
            log.error("parseParams error",e);
            return false;
        }
    }

    public Result beforeCalDeal(List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,Map<String,Object> paramMap,ProductCalculateDto productCalculateDto){
        Result result = new Result();
        try{
            log.info("beforeCalDeal start");
            long startTime = System.currentTimeMillis();
            ProductCalBasicParamDto productCalBasicParamDto = productCalculateDto.getProductCalBasicParamDto();
            ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();

            /**
             * 计算现值总和
             */
            if (ProductCalRepaymentMethodEnum.DEBJ.type().equals(productCalBasicInfoDto.getRepaymentMethod()) || ProductCalRepaymentMethodEnum.JTD.type().equals(productCalBasicInfoDto.getRepaymentMethod())) {
                boolean flag = false;

                flag = calPresentValueSum(paramMap, productCalBaseTemplateV2Entities, productCalculateDto, productCalBasicInfoDto.getRepaymentMethod());


                if (flag == false) {
                    result.setCode(FAIL.getCode());
                    result.setMessage("计算现值总和失败");
                    return result;
                }
            }

            /**
             * 处理动态分段贷特殊公式：还款期次、还款比例、还款额、月供、贴息、现金流收入、首月现金流收入、现金流还款计划、首月现金流还款计划、现金流应收客户利息、首月现金流应收客户利息
             */
            ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto = productCalculateDto.getProductCalRepaymentMethodParamDto();
            if(ProductCalRepaymentMethodEnum.FDD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                boolean dealFddFormulaFlag = dealFddFormula(paramMap,productCalBaseTemplateV2Entities,productCalRepaymentMethodParamDto);
                if(dealFddFormulaFlag == false){
                    result.setCode(FAIL.getCode());
                    result.setMessage("处理分段贷失败");
                    return result;
                }
            }

            /**
             * 如果是等本等息，前置处理贴息
             */
            if(ProductCalRepaymentMethodEnum.DBDX.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                boolean dealDbdxFlag = dealDbdxFormula(paramMap,productCalBaseTemplateV2Entities,productCalculateDto);
                if(dealDbdxFlag == false){
                    result.setCode(FAIL.getCode());
                    result.setMessage("处理等本等息失败");
                    return result;
                }
            }

            if(ProductCalRepaymentMethodEnum.BJDBFDD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                boolean dealBjdbfddFlag = dealBjdbfddFormula(paramMap,productCalBaseTemplateV2Entities,productCalculateDto);
                if(dealBjdbfddFlag == false){
                    result.setCode(FAIL.getCode());
                    result.setMessage("处理本金等比分段贷失败");
                    return result;
                }
            }

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            long endTime = System.currentTimeMillis();
            log.info("beforeCalDeal cost time: "+(endTime - startTime)+"ms");
            log.info("beforeCalDeal end");
        }catch(Exception e){
            log.error("beforeCalDeal error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("计算前置方法处理失败");
        }
        return result;
    }

    public void dealEarlyFormula(ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity,Integer subsectionLendNum){
        StringBuffer stringBufferFir = new StringBuffer("");
        StringBuffer stringBufferSec = new StringBuffer("");
        StringBuffer stringBufferkh = new StringBuffer("");
        String formula = ProductCalBaseTemplateV2Entity.getFormula();
        String[] split = formula.split("~");
        for (int i = 1; i <= subsectionLendNum; i++) {
            if("0".equals(ProductCalBaseTemplateV2Entity.getPamType())){
                String replaFir = split[1];
                replaFir = replaFir.replaceAll("Numtime",String.valueOf(i));
                replaFir = replaFir.replaceAll("Numj1",String.valueOf(i-1));
                if(i != subsectionLendNum){
                    stringBufferFir.append(replaFir+",");
                }else{
                    stringBufferFir.append(replaFir);
                }
                String replaSec = split[3];
                replaSec = replaSec.replaceAll("Numtime",String.valueOf(i));
                replaSec = replaSec.replaceAll("Numj1",String.valueOf(i-1));
                if(i != subsectionLendNum){
                    stringBufferSec.append(replaSec+",");
                }else{
                    stringBufferSec.append(replaSec);
                }
            }else{
                String strRepl = split[1];
                strRepl = strRepl.replaceAll("Numtime",String.valueOf(i));
                stringBufferFir.append(strRepl+",");
                stringBufferkh.append(")");
            }
        }
        if("0".equals(ProductCalBaseTemplateV2Entity.getPamType())){
            formula = formula.replace("~"+split[1]+"~",stringBufferFir.toString()).replace("~"+split[3]+"~",stringBufferSec.toString());
        }else{
            formula = formula.replace("~"+split[1]+"~",stringBufferFir.toString());
            formula = formula+stringBufferkh.toString();
        }
        ProductCalBaseTemplateV2Entity.setFormula(formula);
    }

    private boolean calPresentValueSum(Map<String, Object> paramMap, List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities, ProductCalculateDto productCalculateDto, Integer repaymentMethod) {
        Integer timeLimit = (Integer)paramMap.get("timeLimit");
        Mapper mapper = DozerBeanMapperBuilder.buildDefault();
        List<ProductCalBaseTemplateV2Entity> cashStreamTemplateEntitiesBak = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
            return "4".equals(ProductCalBaseTemplateV2Entity.getPamType());
        }).map(ProductCalBaseTemplateV2Entity -> {
            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityBak = mapper.map(ProductCalBaseTemplateV2Entity,ProductCalBaseTemplateV2Entity.class);
            return ProductCalBaseTemplateV2EntityBak;
        }).collect(Collectors.toList());
        List<Double> presentValueList = new ArrayList<>();
        List<Double> postFloatingDiscountRateList = new ArrayList<>();

        if (ProductCalRepaymentMethodEnum.JTD.type().equals(repaymentMethod)) {
            for (int i = 0; i <= timeLimit; i++) {
                /**
                 * 对于现金流计算，每次都要重新对现金流公式赋值
                 */
                Integer month = i;

                ProductCalCashStreamDto productCalCashStreamDto = new ProductCalCashStreamDto();
                productCalCashStreamDto.setMonth(month);

                paramMap.put("month", month);

                Map<String, Object> map = new HashMap<>();
                ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
                /**
                 * 计算首月信息
                 */
                if (month == 1) {

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityDiscountRate = cashStreamTemplateEntitiesBak.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "discountRate".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto, productCalBaseTemplateV2Entities, ProductCalBaseTemplateV2EntityDiscountRate, paramMap, map, month, productCalBasicInfoDto);

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityPostFloatingDiscountRate = cashStreamTemplateEntitiesBak.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "postFloatingDiscountRate".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    postFloatingDiscountRateList.add(dealFormula(productCalCashStreamDto, productCalBaseTemplateV2Entities, ProductCalBaseTemplateV2EntityPostFloatingDiscountRate, paramMap, map, month, productCalBasicInfoDto));
                }

                if (month >= 2) {
                    List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2EntitiesCashStream = productCalBaseTemplateV2Entities.stream().map(ProductCalBaseTemplateV2Entity -> {
                        ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityBak = mapper.map(ProductCalBaseTemplateV2Entity, ProductCalBaseTemplateV2Entity.class);
                        return ProductCalBaseTemplateV2EntityBak;
                    }).collect(Collectors.toList());

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityDiscountRate = productCalBaseTemplateV2EntitiesCashStream.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "discountRate".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto, productCalBaseTemplateV2Entities, ProductCalBaseTemplateV2EntityDiscountRate, paramMap, map, month, productCalBasicInfoDto);

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityPostFloatingDiscountRate = productCalBaseTemplateV2EntitiesCashStream.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "postFloatingDiscountRate".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    postFloatingDiscountRateList.add(dealFormula(productCalCashStreamDto, productCalBaseTemplateV2Entities, ProductCalBaseTemplateV2EntityPostFloatingDiscountRate, paramMap, map, month, productCalBasicInfoDto));
                }
            }
            paramMap.put("postFloatingDiscountRateSum", postFloatingDiscountRateList.stream().mapToDouble(Double::doubleValue).sum());
        }

        for(int i=0;i<=timeLimit;i++){
            /**
             * 对于现金流计算，每次都要重新对现金流公式赋值
             */
            Integer month = i;

            ProductCalCashStreamDto productCalCashStreamDto = new ProductCalCashStreamDto();
            productCalCashStreamDto.setMonth(month);

            paramMap.put("month",month);

            Map<String,Object> map = new HashMap<>();
            ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
            /**
             * 计算首月信息
             */
            if(month == 1){
                /**
                 * 计算首月利息支出
                 */
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityPresentValueFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "presentValueFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                presentValueList.add(dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityPresentValueFirst,paramMap,map,month,productCalBasicInfoDto));
            }

            if(month >= 2){
                //重新赋值一遍公式 不然会一直是数字
                List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2EntitiesCashStream = productCalBaseTemplateV2Entities.stream().map(ProductCalBaseTemplateV2Entity -> {
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityBak = mapper.map(ProductCalBaseTemplateV2Entity,ProductCalBaseTemplateV2Entity.class);
                    return ProductCalBaseTemplateV2EntityBak;
                }).collect(Collectors.toList());

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityPresentValue = productCalBaseTemplateV2EntitiesCashStream.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "presentValue".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                presentValueList.add(dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityPresentValue,paramMap,map,month,productCalBasicInfoDto));
            }
        }
        paramMap.put("presentValueSum",presentValueList.stream().mapToDouble(Double::doubleValue).sum());
        return true;
    }

    public boolean dealFddFormula(Map<String, Object> paramMap,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto){
        try{
            /**
             * 动态分段贷笔数
             */
            List<ProductCalSubsectionLendDto> productCalSubsectionLendDtoList = productCalRepaymentMethodParamDto.getProductCalSubsectionLendDtoList();
            Integer subsectionLendNum = productCalSubsectionLendDtoList.size();
            paramMap.put("subRepaymentRatio0",0);
            paramMap.put("subRepaymentTimeLimit0",0);
            paramMap.put("subRepaymentMoney0",0);
            for (ProductCalSubsectionLendDto productCalSubsectionLendDto : productCalSubsectionLendDtoList) {
                /**
                 * 动态分段贷第几笔
                 */
                Integer subRepaymentCount = productCalSubsectionLendDto.getSubRepaymentCount();
                /**
                 * 动态分段贷期次
                 */
                Integer subRepaymentTimeLimit = productCalSubsectionLendDto.getSubRepaymentTimeLimit();
                /**
                 * 动态分段贷比例
                 */
                Double subRepaymentRatio = productCalSubsectionLendDto.getSubRepaymentRatio();

                paramMap.put("subRepaymentTimeLimit"+subRepaymentCount,subRepaymentTimeLimit);
                paramMap.put("subRepaymentRatio"+subRepaymentCount,subRepaymentRatio);

                ProductCalBaseTemplateV2Entity subRepaymentMoneyBaseTemplateEntity = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "subRepaymentMoneyNumtime".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                String formula = subRepaymentMoneyBaseTemplateEntity.getFormula();
                formula = formula.replace("Numtime",String.valueOf(subRepaymentCount));

                formula = formula.replaceAll("@actualLoanMoney@",String.valueOf(paramMap.get("actualLoanMoney")));
                formula = formula.replaceAll("@downpaymentsRatio@",String.valueOf(paramMap.get("downpaymentsRatio")));
                formula = formula.replaceAll("@packageCarPrice@",String.valueOf(paramMap.get("packageCarPrice")));
                formula = formula.replaceAll("@subRepaymentRatio"+subRepaymentCount+"@",String.valueOf(paramMap.get("subRepaymentRatio"+subRepaymentCount)));

                Map<String,Object> mapRepl = new HashMap<>();
                productCalSubsectionLendDto.setSubRepaymentMoney((Double)AviatorUtils.calculateValue(mapRepl,formula));
            }
            /**
             * 处理月供、贴息、现金流收入、首月现金流收入、现金流还款计划、首月现金流还款计划、现金流应收客户利息、首月现金流应收客户利息公式
             */
            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : productCalBaseTemplateV2Entities) {
                if("monthPayment".equals(ProductCalBaseTemplateV2Entity.getItem())||"subInterest".equals(ProductCalBaseTemplateV2Entity.getItem())||"cashStreamIncom".equals(ProductCalBaseTemplateV2Entity.getItem())||"cashStreamIncomFirst".equals(ProductCalBaseTemplateV2Entity.getItem())
                        ||"cashStreamRepaymentPlan".equals(ProductCalBaseTemplateV2Entity.getItem())||"cashStreamRepaymentPlanFirst".equals(ProductCalBaseTemplateV2Entity.getItem())){
                    dealEarlyFormula(ProductCalBaseTemplateV2Entity,subsectionLendNum);
                }
            }
            return true;
        }catch(Exception e){
            log.error("dealFddFormula error",e);
            return false;
        }
    }

    public boolean dealDbdxFormula(Map<String, Object> paramMap,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalculateDto productCalculateDto){
        try{
            /**
             * 等本等息首先计算月供、实际利率本金
             */
            Integer timeLimit = productCalculateDto.getProductCalBasicParamDto().getTimeLimit();
            ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
            Map<String,Object> map = new HashMap<>();
            for (int i = 0;i<=timeLimit;i++){
                ProductCalCashStreamDto productCalCashStreamDto = new ProductCalCashStreamDto();
                paramMap.put("month",i);
                /**
                 * 计算零月现金流实际利率本机
                 */
                if(i == 0){
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityActualInterestPrincipalZero = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamActualInterestPrincipalZero".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityActualInterestPrincipalZero,paramMap,map,i,productCalBasicInfoDto);
                    paramMap.put("0cashStreamMonthPayment",productCalCashStreamDto.getCashStreamActualInterestPrincipal());
                }else if(i == 1){
                    /**
                     * 计算每个月的实际利率本金
                     */
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityActualInterestPrincipalFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamActualInterestPrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityActualInterestPrincipalFirst,paramMap,map,i,productCalBasicInfoDto);

                    /**
                     * 计算月供
                     */
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityMonthPaymentFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamMonthPaymentFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityMonthPaymentFirst,paramMap,map,i,productCalBasicInfoDto);

                }else{
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityActualInterestPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamActualInterestPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityActualInterestPrincipal,paramMap,map,i,productCalBasicInfoDto);

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityMonthPayment = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamMonthPayment".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityMonthPayment,paramMap,map,i,productCalBasicInfoDto);
                }
            }

            /**
             * 计算贴息
             */
            ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntitysubInterest = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "subInterest".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();

            String formula = productCalBaseTemplateV2EntitysubInterest.getFormula();
            String[] split = formula.split("~");
            List<String> arrOrMapList = Arrays.stream(split).filter(x -> {
                return x.startsWith("cashStream") || x.startsWith("repl") || x.startsWith("dueDate");
            }).collect(Collectors.toList());

            int count = 1;
            paramMap.put("beforeCalDeal",true);
            for (String arrorMapStr : arrOrMapList){
                formula = dealArrayOrMap(count,formula,arrorMapStr,paramMap,map,productCalBaseTemplateV2EntitysubInterest,productCalBaseTemplateV2Entities,false);
                count++;
            }
            productCalBaseTemplateV2EntitysubInterest.setFormula(formula);
            dealFormula(productCalculateDto.getProductCalBasicParamDto(),productCalBaseTemplateV2Entities,productCalBaseTemplateV2EntitysubInterest,paramMap,map,null,productCalBasicInfoDto);
            productCalBaseTemplateV2EntitysubInterest.setFormula(productCalculateDto.getProductCalBasicParamDto().getSubInterest().toString());
//            dealFormula(productCalculateDto.getProductCalBasicParamDto(),productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntitysubInterest,paramMap,map,null,productCalBasicInfoDto);

        }catch(Exception e){
            log.error("dealDbdxFormula error",e);
            return false;
        }
        return true;
    }

    public boolean dealBjdbfddFormula(Map<String, Object> paramMap,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalculateDto productCalculateDto){
        //获取本金等比分段贷动态还款方式信息
        List<ProductCalEqualPrincipalDto> productCalEqualPrincipalDtoList = productCalculateDto.getProductCalRepaymentMethodParamDto().getProductCalEqualPrincipalDtoList();
        if(productCalEqualPrincipalDtoList == null || productCalEqualPrincipalDtoList.size() == 0){
            log.info("本金等比分段贷阶段数为0，数据有误");
            return false;
        }

        paramMap.put("equal_principal_ratio0",0);
        paramMap.put("equalPrincipalStageNum",productCalEqualPrincipalDtoList.size());

        BigDecimal bigDecimalStart = BigDecimal.valueOf(0);
        BigDecimal equalPrincipalRatioNumtimeJ1Sum = bigDecimalStart;
        BigDecimal equalPrincipalRatioNumtimeSum = bigDecimalStart;
        Map<String,Object> mapRepl = new HashMap<>();

        for (ProductCalEqualPrincipalDto productCalEqualPrincipalDto : productCalEqualPrincipalDtoList) {
            //本金等比分段贷本金比例
            Double equalPrincipalRatio = productCalEqualPrincipalDto.getEqualPrincipalRatio();
            paramMap.put("equal_principal_ratio"+productCalEqualPrincipalDto.getEqualPrincipalStage(),equalPrincipalRatio);

            equalPrincipalRatioNumtimeSum = equalPrincipalRatioNumtimeSum.add(BigDecimal.valueOf(equalPrincipalRatio).divide(BigDecimal.valueOf(100)));

            ProductCalBaseTemplateV2Entity euqalPrincipalMonthPaymentNumtimeBaseTemplateEntity = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "euqalPrincipalMonthPaymentNumtime".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            String formula = euqalPrincipalMonthPaymentNumtimeBaseTemplateEntity.getFormula();
            formula = formula.replace("@equalPrincipalRatioNumtimeJ1Sum@",String.valueOf(equalPrincipalRatioNumtimeJ1Sum.doubleValue()));
            formula = formula.replace("@equalPrincipalRatioNumtimeSum@",String.valueOf(equalPrincipalRatioNumtimeSum.doubleValue()));
            formula = formula.replace("@customerInterestRate@",String.valueOf(productCalculateDto.getProductCalBasicParamDto().getCustomerInterestRate()));
            formula = formula.replace("@timeLimit@",String.valueOf(productCalculateDto.getProductCalBasicParamDto().getTimeLimit()));
            formula = formula.replace("@equalPrincipalStageNum@",String.valueOf(productCalEqualPrincipalDtoList.size()));
            formula = formula.replaceAll("@actualLoanMoney@",String.valueOf(productCalculateDto.getProductCalBasicParamDto().getActualLoanMoney()));

            //计算每阶段月供
            Double monthPayment = (Double)AviatorUtils.calculateValue(mapRepl,formula);
            paramMap.put("euqalPrincipalMonthPayment"+productCalEqualPrincipalDto.getEqualPrincipalStage(),monthPayment);
            productCalEqualPrincipalDto.setEqualPrincipalMonthPayment(dealScale(2,monthPayment));
            equalPrincipalRatioNumtimeJ1Sum = equalPrincipalRatioNumtimeJ1Sum.add(BigDecimal.valueOf(equalPrincipalRatio).divide(BigDecimal.valueOf(100)));
        }
        Integer timeLimit = productCalculateDto.getProductCalBasicParamDto().getTimeLimit();
        Map<String,Object> map = new HashMap<>();

        for (int i = 0;i<=timeLimit;i++){
            paramMap.put("month",i);

            if(i == 0){
                paramMap.put("0cashStreamMonthPayment",-productCalculateDto.getProductCalBasicParamDto().getActualLoanMoney());
            }else if(i == 1){
                paramMap.put("1cashStreamMonthPayment",paramMap.get("euqalPrincipalMonthPayment1"));
            }else{
                Integer monthStage = (productCalEqualPrincipalDtoList.size())-((productCalculateDto.getProductCalBasicParamDto().getTimeLimit()-i)/(productCalculateDto.getProductCalBasicParamDto().getTimeLimit()/productCalEqualPrincipalDtoList.size()));
                paramMap.put(i+"cashStreamMonthPayment",paramMap.get("euqalPrincipalMonthPayment"+monthStage));
            }
        }

        /**
         * 计算贴息
         */
        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntitysubInterest = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
            return "subInterest".equals(ProductCalBaseTemplateV2Entity.getItem());
        }).findFirst().get();

        String formula = productCalBaseTemplateV2EntitysubInterest.getFormula();
        String[] split = formula.split("~");
        List<String> arrOrMapList = Arrays.stream(split).filter(x -> {
            return x.startsWith("cashStream") || x.startsWith("repl") || x.startsWith("dueDate");
        }).collect(Collectors.toList());

        int count = 1;
        paramMap.put("beforeCalDeal",true);
        for (String arrorMapStr : arrOrMapList){
            formula = dealArrayOrMap(count,formula,arrorMapStr,paramMap,map,productCalBaseTemplateV2EntitysubInterest,productCalBaseTemplateV2Entities,false);
            count++;
        }
        productCalBaseTemplateV2EntitysubInterest.setFormula(formula);
        dealFormula(productCalculateDto.getProductCalBasicParamDto(),productCalBaseTemplateV2Entities,productCalBaseTemplateV2EntitysubInterest,paramMap,map,null,productCalculateDto.getProductCalBasicInfoDto());
        productCalBaseTemplateV2EntitysubInterest.setFormula(productCalculateDto.getProductCalBasicParamDto().getSubInterest().toString());
        return true;
    }

    public boolean dealUnionLoanFormula(Map<String, Object> paramMap,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalculateDto productCalculateDto){
        /**
         * 判断是否按期数计算提前结清
         */
        ProductCalculateEarlySquareDto productCalculateEarlySquareDto = productCalculateDto.getProductCalculateEarlySquareDto();

        QueryWrapper<ProductCalBaseTemplateV2Entity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("buss_type",7777).eq("repayment_method",productCalculateDto.getProductCalBasicInfoDto().getRepaymentMethod());
        List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2EntitiesUnionLoan = productCalBaseTemplateV2Dao.selectList(queryWrapper);
        productCalBaseTemplateV2EntitiesUnionLoan = productCalBaseTemplateV2EntitiesUnionLoan.stream().map(productCalBaseTemplateV2Entity -> {
            productCalBaseTemplateV2Entity.setItem(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, productCalBaseTemplateV2Entity.getItem()));
            return productCalBaseTemplateV2Entity;
        }).collect(Collectors.toList());

        Integer timeLimit = productCalculateDto.getProductCalBasicParamDto().getTimeLimit();
        Map<String,Object> map = new HashMap<>();

        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntityUnionLoanBankCurrentInterest = productCalBaseTemplateV2EntitiesUnionLoan.stream().filter(ProductCalBaseTemplateV2Entity -> {
            return "cashStreamUnionLoanBankCurrentInterest".equals(ProductCalBaseTemplateV2Entity.getItem());
        }).findFirst().get();

        String formula = productCalBaseTemplateV2EntityUnionLoanBankCurrentInterest.getFormula();
        formula = formula.replace("@customerInterestRate@",String.valueOf(productCalculateDto.getProductCalBasicParamDto().getCustomerInterestRate()));
        formula = formula.replace("@timeLimit@",String.valueOf(productCalculateDto.getProductCalBasicParamDto().getTimeLimit()));
        formula = formula.replace("#bankLoanMoney#",String.valueOf(paramMap.get("bankLoanMoney")));

        BigDecimal bankCustomerInterest = BigDecimal.valueOf(0);
        Map<String,Object> mapRepl = new HashMap<>();

        for (int i = 1;i<=timeLimit;i++){
            String newFormula = formula.replace("@month@",String.valueOf(i));
            Double currentInterest = (Double)AviatorUtils.calculateValue(mapRepl,newFormula);
            bankCustomerInterest = bankCustomerInterest.add(BigDecimal.valueOf(currentInterest));

            if(productCalculateEarlySquareDto!=null && i == productCalculateDto.getProductCalculateEarlySquareDto().getEarlySquareTimeLimit()){
                break;
            }
        }
        paramMap.put("bankCustomerInterest",bankCustomerInterest.doubleValue());
        return true;
    }


    /**
     * 计算基本参数
     * @param paramMap
     * @param productCalBaseTemplateV2Entities
     * @param productCalBasicParamDto
     * @return
     */
    public boolean calulateBasicParam(Map<String,Object> paramMap,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalBasicParamDto productCalBasicParamDto,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            log.info("calulateBasicParam start");
            long startTime = System.currentTimeMillis();
            Map<String,Object> map = new HashMap<>();

            Double actualLoanMoneyValue = productCalBasicParamDto.getActualLoanMoney();
            /**
             * 实际放款额特殊需要先计算
             */
            if(!ProductCalRepaymentMethodEnum.DEBJ.type().equals(productCalBasicInfoDto.getRepaymentMethod())) {
                ProductCalBaseTemplateV2Entity actualLoanMoneyBaseTemplateEntity = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "actualLoanMoney".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                if (actualLoanMoneyValue == null) {
                    dealFormula(productCalBasicParamDto, productCalBaseTemplateV2Entities, actualLoanMoneyBaseTemplateEntity, paramMap, map, null, productCalBasicInfoDto);
                    paramMap.put("actualLoanMoney", actualLoanMoneyBaseTemplateEntity.getFormula());
                    actualLoanMoneyValue = productCalBasicParamDto.getActualLoanMoney();
                }
            }


            /**
             * 计算基本公式参数
             */
            List<ProductCalBaseTemplateV2Entity> basicFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "0".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : basicFormularList) {
                dealFormula(productCalBasicParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalBasicInfoDto);
            }

            /**
             * 计算公式套公式参数
             */
            List<ProductCalBaseTemplateV2Entity> formularAndFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "0".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "2".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : formularAndFormularList) {
                if("actualLoanMoney".equals(ProductCalBaseTemplateV2Entity.getItem())){
                    continue;
                }
                dealFormula(productCalBasicParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalBasicInfoDto);
            }
            long endTime = System.currentTimeMillis();
            log.info("calulateBasicParam cost time: "+(endTime - startTime)+"ms");
            log.info("calulateBasicParam end");
        }catch(Exception e){
            log.error("calulateBasicParam error",e);
            return false;
        }

        return true;
    }

    /**
     * 计算税费参数
     * @param paramMap
     * @param productCalBaseTemplateV2Entities
     * @param productCalTaxFeeParamDto
     * @return
     */
    public boolean calulateTaxFeeParam(Map<String,Object> paramMap,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalTaxFeeParamDto productCalTaxFeeParamDto,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            log.info("calulateTaxFeeParam start");
            long startTime = System.currentTimeMillis();
            Map<String,Object> map = new HashMap<>();
            /**
             * 计算基本公式参数
             */
            List<ProductCalBaseTemplateV2Entity> basicFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "1".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : basicFormularList) {
                dealFormula(productCalTaxFeeParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalBasicInfoDto);
            }

            /**
             * 计算公式套公式参数
             */
            List<ProductCalBaseTemplateV2Entity> formularAndFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "1".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "2".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : formularAndFormularList) {
                dealFormula(productCalTaxFeeParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalBasicInfoDto);
            }
            paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalTaxFeeParamDto)));

            /**
             * 特殊处理进项税抵扣比例和销项税比例，根据是否是农户贷，重新计算进项税抵扣比例和销项税比例，但是该值至用作计算，不覆盖原值
             */
            Double inputTaxDeductionRatio = productCalTaxFeeParamDto.getInputTaxDeductionRatio();
            Double valueAddedTaxRatio = productCalTaxFeeParamDto.getValueAddedTaxRatio();

            List<ProductCalBaseTemplateV2Entity> inputTaxAndOutputTaxFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "inputTaxDeductionRatio".equals(ProductCalBaseTemplateV2Entity.getItem()) || "valueAddedTaxRatio".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : inputTaxAndOutputTaxFormularList) {
                dealFormula(productCalTaxFeeParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalBasicInfoDto);
            }

            paramMap.put("inputTaxDeductionRatio",productCalTaxFeeParamDto.getInputTaxDeductionRatio());
            paramMap.put("valueAddedTaxRatio",productCalTaxFeeParamDto.getValueAddedTaxRatio());
            productCalTaxFeeParamDto.setInputTaxDeductionRatio(inputTaxDeductionRatio);
            productCalTaxFeeParamDto.setValueAddedTaxRatio(valueAddedTaxRatio);


            long endTime = System.currentTimeMillis();
            log.info("calulateTaxFeeParam cost time: "+(endTime - startTime)+"ms");
            log.info("calulateTaxFeeParam end");
        }catch(Exception e){
            log.error("calulateBasicParam error",e);
            return false;
        }

        return true;
    }

    /**
     * 计算还款方式参数
     * @param paramMap
     * @param productCalBaseTemplateV2Entities
     * @param productCalRepaymentMethodParamDto
     * @return
     */
    public boolean calulateRepaymentMethodParam(Map<String,Object> paramMap,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            log.info("calulateRepaymentMethodParam start");
            long startTime = System.currentTimeMillis();

            Map<String,Object> map = new HashMap<>();
            /**
             * 计算基本公式参数
             */
            List<ProductCalBaseTemplateV2Entity> basicFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "2".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : basicFormularList) {
                dealFormula(productCalRepaymentMethodParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalBasicInfoDto);
            }
            paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalRepaymentMethodParamDto)));
            long endTime = System.currentTimeMillis();
            log.info("calulateRepaymentMethodParam cost time: "+(endTime - startTime)+"ms");
            log.info("calulateRepaymentMethodParam end");
        }catch(Exception e){
            log.error("calulateRepaymentMethodParam error",e);
            return false;
        }
        return true;
    }

    /**
     * 计算提前结清参数
     * @param productCalEarlySquareParamDto
     * @return
     */
    public boolean calulateEarlySquareParam(ProductCalBasicInfoDto productCalBasicInfoDto,ProductCalBasicParamDto productCalBasicParamDto,ProductCalEarlySquareParamDto productCalEarlySquareParamDto){
        /**
         * 计算提前结清概率
         */
        try{
            log.info("calulateEarlySquareParam start");
            Result earlySquareProbabilityInfo = getEarlySquareProbabilityInfo(productCalBasicInfoDto.getBusinessType(), productCalBasicParamDto.getCustomerInterestRate(), productCalBasicParamDto.getActualInterestRate(), productCalBasicParamDto.getTimeLimit());
            List<ProductCalEarlySquareProbabilityEntity> earlySquareProbabilityEntityList = (List<ProductCalEarlySquareProbabilityEntity>)earlySquareProbabilityInfo.getData();
            if(earlySquareProbabilityEntityList != null && earlySquareProbabilityEntityList.size()>0){
                Double earlySquareProbability = earlySquareProbabilityEntityList.stream().filter(productCalEarlySquareProbabilityEntity -> {
                    return !productCalEarlySquareProbabilityEntity.getPayoutRentalId().equals(earlySquareProbabilityEntityList.size());
                }).mapToDouble(ProductCalEarlySquareProbabilityEntity::getPayoutProbability).sum();
                productCalEarlySquareParamDto.setEarlySquareProbability(earlySquareProbability);
            }

            log.info("calulateEarlySquareParam end");
        }catch(Exception e){
            log.error("calulateEarlySquareParam error",e);
            return false;
        }
        return true;
    }

    /**
     * 计算联合贷参数
     * @param paramMap
     * @param productCalBaseTemplateV2Entities
     * @param productCalUnionLoanParamDto
     * @param productCalBasicInfoDto
     * @return
     */
    public boolean calulateUnionLoanParam(Map<String,Object> paramMap,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalUnionLoanParamDto productCalUnionLoanParamDto,ProductCalBasicInfoDto productCalBasicInfoDto,ProductCalculateDto productCalculateDto){
        try{
            log.info("calulateUnionLoanParam start");
            long startTime = System.currentTimeMillis();

            Map<String,Object> map = new HashMap<>();
            /**
             * 计算基本公式参数
             */
            List<ProductCalBaseTemplateV2Entity> basicFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "6".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : basicFormularList) {
                dealFormula(productCalUnionLoanParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalBasicInfoDto);
            }
            paramMap.putAll(GsonUtil.jsonToMap(GsonUtil.toJson(productCalUnionLoanParamDto)));

            /**
             * 联合贷银行客户利息
             */
            boolean dealUnionLoanFlag = dealUnionLoanFormula(paramMap,productCalBaseTemplateV2Entities,productCalculateDto);
            if(dealUnionLoanFlag == false){
                log.info("联合贷银行客户利息处理失败");
                return false;
            }

            long endTime = System.currentTimeMillis();
            log.info("calulateUnionLoanParam cost time: "+(endTime - startTime)+"ms");
            log.info("calulateUnionLoanParam end");
        }catch(Exception e){
            log.error("calulateUnionLoanParam error",e);
            return false;
        }
        return true;
    }

    /**
     * 计算现金流
     * @param paramMap
     * @param productCalBaseTemplateV2Entities
     * @param productCalculateDto
     * @return
     */
    public boolean calulateCashStrem(Map<String,Object> paramMap,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities, ProductCalculateDto productCalculateDto,Double actualLoanMoneyValue,boolean isUnionFlag){
        try{
            log.info("calulateCashStrem start");
            long startTime = System.currentTimeMillis();

            ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
            /**
             * 将现金流模板公式保存下来，以后不用每次都重新查库，直接用模板中的内容就可以，增大计算效率
             */

            Mapper mapper = DozerBeanMapperBuilder.buildDefault();
            List<ProductCalBaseTemplateV2Entity> cashStreamTemplateEntitiesBak = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "4".equals(ProductCalBaseTemplateV2Entity.getPamType());
            }).map(ProductCalBaseTemplateV2Entity -> {
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityBak = mapper.map(ProductCalBaseTemplateV2Entity,ProductCalBaseTemplateV2Entity.class);
                return ProductCalBaseTemplateV2EntityBak;
            }).collect(Collectors.toList());

            /**
             * 如果拨备率和管理费分摊系数不为空的时候计算利润
             */
            List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateProfitCalEntityListBak = new ArrayList<>();
            if(productCalculateDto.getProductCalBasicParamDto().getProvisionRate() != null && productCalculateDto.getProductCalBasicParamDto().getManageFeeShareCoefficient() != null){
                QueryWrapper<ProductCalBaseTemplateV2Entity> queryWrapper = new QueryWrapper();
                queryWrapper.eq("buss_type",8888).eq("repayment_method",productCalBasicInfoDto.getRepaymentMethod());
                productCalBaseTemplateProfitCalEntityListBak = productCalBaseTemplateV2Dao.selectList(queryWrapper);
                productCalBaseTemplateProfitCalEntityListBak = productCalBaseTemplateProfitCalEntityListBak.stream().map(ProductCalBaseTemplateV2Entity -> {
                    ProductCalBaseTemplateV2Entity.setItem(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, ProductCalBaseTemplateV2Entity.getItem()));
                    return ProductCalBaseTemplateV2Entity;
                }).collect(Collectors.toList());
                boolean dealProfitTemplateFlag = dealProfitTemplate(productCalBaseTemplateProfitCalEntityListBak,productCalBaseTemplateV2Entities,mapper);
                if(dealProfitTemplateFlag == false){
                    log.info("处理利润公式失败");
                    return false;
                }
            }
            Integer timeLimit = (Integer)paramMap.get("timeLimit");
            /**
             * 如果联合贷参数不为空，需要处理联合贷参数
             */
            List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateUnionLoanEntityListBak = new ArrayList<>();
            if(isUnionFlag){
                QueryWrapper<ProductCalBaseTemplateV2Entity> queryWrapper = new QueryWrapper();
                queryWrapper.eq("buss_type",7777).eq("repayment_method",productCalBasicInfoDto.getRepaymentMethod()).eq("pam_type","4");
                productCalBaseTemplateUnionLoanEntityListBak = productCalBaseTemplateV2Dao.selectList(queryWrapper);
                productCalBaseTemplateUnionLoanEntityListBak = productCalBaseTemplateUnionLoanEntityListBak.stream().map(ProductCalBaseTemplateV2Entity -> {
                    ProductCalBaseTemplateV2Entity.setItem(CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, ProductCalBaseTemplateV2Entity.getItem()));
                    return ProductCalBaseTemplateV2Entity;
                }).collect(Collectors.toList());
                boolean dealUnionLoanTemplateFlag = dealUnionLoanTemplate(productCalBaseTemplateUnionLoanEntityListBak,productCalBaseTemplateV2Entities,mapper);
                if(dealUnionLoanTemplateFlag == false){
                    log.info("处理联合贷公式失败");
                    return false;
                }
                timeLimit = timeLimit+1;
            }

            List<ProductCalCashStreamDto> productCalCashStreamDtoList = new ArrayList<>();

            /**
             * todo 后续增加是提前结清条件校验
             */
//            boolean isEarlySquareFlag = productCalculateDto.getProductCalEarlySquareParamSubDto().getIsEarlySquareFlag();
            boolean isEarlySquareFlag = true;
            /**
             * 查询提前结清概率表
             */
            List<ProductCalEarlySquareProbabilityDto> productCalEarlySquareProbabilityDtoList = new ArrayList<>();
            List<ProductCalEarlySquareBasicCommissionRebateRatioDto> productCalEarlySquareBasicCommissionRebateRatioDtoList = new ArrayList<>();
            List<ProductCalEarlySquareHandChargeRatioDto> productCalEarlySquareHandChargeRatioDtoList = new ArrayList<>();

            if(isEarlySquareFlag){
                productCalEarlySquareProbabilityDtoList = productCalculateDto.getProductCalEarlySquareParamSubDto().getProductCalEarlySquareProbabilityDtoList();
                if(productCalEarlySquareProbabilityDtoList == null || productCalEarlySquareProbabilityDtoList.size() == 0){
                    log.info("提前结清概率信息为空");
                    return false;
                }

                productCalEarlySquareBasicCommissionRebateRatioDtoList = productCalculateDto.getProductCalEarlySquareParamSubDto().getProductCalEarlySquareBasicCommissionRebateRatioDtoList();
                if(productCalEarlySquareBasicCommissionRebateRatioDtoList == null || productCalEarlySquareBasicCommissionRebateRatioDtoList.size() == 0){
                    log.info("提前结清服务费扣返比例为空");
                    return false;
                }

                productCalEarlySquareHandChargeRatioDtoList = productCalculateDto.getProductCalEarlySquareParamSubDto().getProductCalEarlySquareHandChargeRatioDtoList();
                if(productCalEarlySquareHandChargeRatioDtoList == null || productCalEarlySquareHandChargeRatioDtoList.size() == 0){
                    log.info("提前结清手续费比例为空");
                    return false;
                }
                double asDouble = productCalEarlySquareProbabilityDtoList.stream().filter(productCalEarlySquareProbabilityDto -> {
                    return productCalEarlySquareProbabilityDto.getPayoutRentalId() < (Integer)paramMap.get("timeLimit");
                }).mapToDouble(ProductCalEarlySquareProbabilityDto::getPayoutProbability).max().getAsDouble();
                paramMap.put("maxEarlySquareProbability",asDouble);
            }else{
                paramMap.put("maxEarlySquareProbability",0);
            }



            for(int i=0;i<=timeLimit;i++){
                Integer month = i;
                boolean earlyDealFlag = false;
                if(!isUnionFlag && productCalculateDto.getProductCalculateEarlySquareDto()!=null){
                    List<ProductCalEarlySquareBasicCommissionRebateRatioDto> productCalEarlySquareBasicCommissionRebateRatioDtoListDeal = productCalculateDto.getProductCalEarlySquareParamSubDto().getProductCalEarlySquareBasicCommissionRebateRatioDtoList();
                    Optional<ProductCalEarlySquareBasicCommissionRebateRatioDto> first = productCalEarlySquareBasicCommissionRebateRatioDtoListDeal.stream().filter(productCalEarlySquareBasicCommissionRebateRatioDto -> (productCalculateDto.getProductCalculateEarlySquareDto().getEarlySquareTimeLimit()).equals(productCalEarlySquareBasicCommissionRebateRatioDto.getPayoutRentalId())).findFirst();
                    if(first.isPresent()){
                        ProductCalEarlySquareBasicCommissionRebateRatioDto productCalEarlySquareBasicCommissionRebateRatioDto = first.get();
                        if(productCalEarlySquareBasicCommissionRebateRatioDto.getBasicCommissionRebateRatio() != 0.0){
                            earlyDealFlag = true;
                        }
                    }
                }

                Integer timeLimitReal = (Integer)paramMap.get("timeLimit");

                if(productCalculateDto.getProductCalculateEarlySquareDto()!=null){
                    timeLimitReal = productCalculateDto.getProductCalculateEarlySquareDto().getEarlySquareTimeLimit();
                }
                /**
                 * 当期提前结清概率、上一期提前结清概率、前i-1期不提前结清概率赋值
                 */
                if(!isUnionFlag && earlyDealFlag && i>timeLimitReal){
                    if(productCalEarlySquareBasicCommissionRebateRatioDtoList.size()>=i-1){
                        paramMap.put("upBasicCommissionRebateRatio",productCalEarlySquareBasicCommissionRebateRatioDtoList.get(i-2).getBasicCommissionRebateRatio());
                    }else{
                        paramMap.put("upBasicCommissionRebateRatio",0);
                    }
                    paramMap.put("upEarlySquareProbability",productCalEarlySquareProbabilityDtoList.get(i-2).getPayoutProbability());
                    paramMap.put("noEarlySquareProbability",0);
                } else if(isUnionFlag && i>timeLimitReal){
                    paramMap.put("upNoEarlySquareProbability",paramMap.get("noEarlySquareProbability"));
                    if(productCalculateDto.getProductCalculateEarlySquareDto()!=null){
                        paramMap.put("upEarlySquareProbability",1);
                        if(productCalEarlySquareBasicCommissionRebateRatioDtoList.size()>=i-1){
                            paramMap.put("upBasicCommissionRebateRatio",productCalEarlySquareBasicCommissionRebateRatioDtoList.get(i-2).getBasicCommissionRebateRatio());
                        }else{
                            paramMap.put("upBasicCommissionRebateRatio",0);
                        }
                    }
                }else{
                    if(isEarlySquareFlag){
                        if(i == 0){
                            paramMap.put("currentEarlySquareProbability",0);
                            paramMap.put("upEarlySquareProbability",0);
                            paramMap.put("noEarlySquareProbability",1);
                        }else{
                            /**
                             * 联合贷使用
                             */
                            if(isUnionFlag){
                                paramMap.put("upNoEarlySquareProbability",paramMap.get("noEarlySquareProbability"));
                            }

                            /**
                             * 赋值提前结清手续费比例，提前结清服务费扣返比例
                             */
                            if(productCalEarlySquareBasicCommissionRebateRatioDtoList.size()>=i){
                                paramMap.put("basicCommissionRebateRatio",productCalEarlySquareBasicCommissionRebateRatioDtoList.get(i-1).getBasicCommissionRebateRatio());
                            }else{
                                paramMap.put("basicCommissionRebateRatio",0);
                            }

                            if(productCalEarlySquareHandChargeRatioDtoList.size()>=i){
                                paramMap.put("earlySquareHandChargeRatio",productCalEarlySquareHandChargeRatioDtoList.get(i-1).getEarlySquareHandChargeRatio());
                            }else{
                                paramMap.put("earlySquareHandChargeRatio",0);
                            }

                            paramMap.put("currentEarlySquareProbability",productCalEarlySquareProbabilityDtoList.get(i-1).getPayoutProbability());
                            if(i == 1){
                                paramMap.put("upEarlySquareProbability",0);
                                paramMap.put("noEarlySquareProbability",1);
                                paramMap.put("nEarlySquareProbability",paramMap.get("currentEarlySquareProbability"));
                            }else{
                                if(productCalEarlySquareBasicCommissionRebateRatioDtoList.size()>=i-1){
                                    paramMap.put("upBasicCommissionRebateRatio",productCalEarlySquareBasicCommissionRebateRatioDtoList.get(i-2).getBasicCommissionRebateRatio());
                                }else{
                                    paramMap.put("upBasicCommissionRebateRatio",0);
                                }
                                paramMap.put("upEarlySquareProbability",productCalEarlySquareProbabilityDtoList.get(i-2).getPayoutProbability());

                                Integer currentMonth = i-1;
                                double sum = productCalEarlySquareProbabilityDtoList.stream().filter(productCalEarlySquareProbabilityDto -> {
                                    return productCalEarlySquareProbabilityDto.getPayoutRentalId() <= currentMonth;
                                }).mapToDouble(ProductCalEarlySquareProbabilityDto::getPayoutProbability).sum();
                                paramMap.put("noEarlySquareProbability",1-sum);
                                paramMap.put("nEarlySquareProbability",sum+(Double) paramMap.get("currentEarlySquareProbability"));
                            }
                        }
                    }else{
                        if(i == timeLimitReal){
                            paramMap.put("currentEarlySquareProbability",1);
                        }else{
                            paramMap.put("currentEarlySquareProbability",0);
                        }
                        paramMap.put("upEarlySquareProbability",0);
                        paramMap.put("earlySquareHandChargeRatio",0);
                        paramMap.put("upBasicCommissionRebateRatio",0);
                        paramMap.put("noEarlySquareProbability",1);
                    }
                }


                /**
                 * 对于现金流计算，每次都要重新对现金流公式赋值
                 */

                Map<String,Object> map = new HashMap<>();
                ProductCalCashStreamDto productCalCashStreamDto = new ProductCalCashStreamDto();
                productCalCashStreamDto.setMonth(month);
                paramMap.put("month",month);

                if(month > 2){
                    productCalBaseTemplateV2Entities = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return !"4".equals(ProductCalBaseTemplateV2Entity.getPamType());
                    }).collect(Collectors.toList());
                    List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2EntitiesCashStream = cashStreamTemplateEntitiesBak.stream().map(ProductCalBaseTemplateV2Entity -> {
                        ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityBak = mapper.map(ProductCalBaseTemplateV2Entity,ProductCalBaseTemplateV2Entity.class);
                        return ProductCalBaseTemplateV2EntityBak;
                    }).collect(Collectors.toList());
                    productCalBaseTemplateV2Entities.addAll(productCalBaseTemplateV2EntitiesCashStream);

                    if(productCalculateDto.getProductCalBasicParamDto().getProvisionRate() != null && productCalculateDto.getProductCalBasicParamDto().getManageFeeShareCoefficient() != null){
                        List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateProfitCalEntityList = productCalBaseTemplateProfitCalEntityListBak.stream().map(ProductCalBaseTemplateV2Entity -> {
                            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityBak = mapper.map(ProductCalBaseTemplateV2Entity,ProductCalBaseTemplateV2Entity.class);
                            return ProductCalBaseTemplateV2EntityBak;
                        }).collect(Collectors.toList());
                        productCalBaseTemplateV2Entities.addAll(productCalBaseTemplateProfitCalEntityList);
                    }

                    if(isUnionFlag){
                        dealUnionLoanTemplate(productCalBaseTemplateUnionLoanEntityListBak,productCalBaseTemplateV2Entities,mapper);
                    }
                }

                /**
                 * 处理现金流日期
                 */
                boolean dealDateFlag = dealCashStreamDate(month,paramMap,productCalCashStreamDto);
                if(dealDateFlag == false){
                    log.info("处理现金流日期失败");
                    return false;
                }

                /**
                 * 计算通用公式
                 */
                boolean calNormalFormulaFlag = calNormalFormula(month,actualLoanMoneyValue,productCalBaseTemplateV2Entities,paramMap,map,productCalCashStreamDto,productCalBasicInfoDto);
                if(calNormalFormulaFlag == false){
                    log.info("计算现金流通用公式失败");
                    return false;
                }

                /**
                 * 计算零月信息
                 */
                if(month == 0){
                    boolean zeroFlag = calZeroSpecialFormula(month,actualLoanMoneyValue,productCalBaseTemplateV2Entities,productCalCashStreamDto,paramMap,map,productCalBasicInfoDto);
                    if(zeroFlag == false){
                        log.info("计算现金流0月特殊公式失败");
                        return false;
                    }
                }

                /**
                 * 如果是联合贷且提前结清，并且月数等于提前结清月数，则本金为上月的剩余本金
                 */
                if(isUnionFlag && productCalculateDto.getProductCalculateEarlySquareDto()!=null && month.equals(productCalculateDto.getProductCalculateEarlySquareDto().getEarlySquareTimeLimit())){

                    if(month>1){
                        productCalCashStreamDto.setCashStreamPrincipal((Double) paramMap.get("cashStreamRemainPrincipalUp"));
                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2Entity = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2Entity.setFormula(productCalCashStreamDto.getCashStreamPrincipal().toString());

                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntityBankPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamUnionLoanBankPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2EntityBankPrincipal.setFormula((String)paramMap.get("cashStreamUnionLoanBankRemainPrincipalUp"));

                        dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,productCalBaseTemplateV2EntityBankPrincipal,paramMap,map,month,productCalBasicInfoDto);


                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntityUnionLoanOurPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamUnionLoanOurPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2EntityUnionLoanOurPrincipal.setFormula((String)paramMap.get("cashSteramUnionLoanOurRemainPrincipalUp"));

                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntityUnionLoanEarlySquareOurMonthPayment = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamUnionLoanEarlySquareOurMonthPayment".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2EntityUnionLoanEarlySquareOurMonthPayment.setFormula((String)paramMap.get("cashSteramUnionLoanOurRemainPrincipalUp")+"+"+productCalCashStreamDto.getCashStreamUnionLoanOursInterest());


                        /**
                         * 等额本息联合贷 提前结清 月供特殊处理
                         */
                        Double cashStreamReceivableCustomerInterest = productCalCashStreamDto.getCashStreamReceivableCustomerInterest();
                        Double cashStreamRemainPrincipalUp = productCalCashStreamDtoList.stream().filter(productCalCashStreamDtoa -> productCalCashStreamDtoa.getMonth() == (month-1)).collect(Collectors.toList()).get(0).getCashStreamRemainPrincipal();

                        BigDecimal add = BigDecimal.valueOf(cashStreamReceivableCustomerInterest).add(BigDecimal.valueOf(cashStreamRemainPrincipalUp));
                        productCalCashStreamDto.setCashStreamMonthPayment(add.doubleValue());
                        productCalCashStreamDto.setCashStreamEarlySquareMonthPayment(add.doubleValue());

                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntityMonthPayment = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamMonthPayment".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2EntityMonthPayment.setFormula(productCalCashStreamDto.getCashStreamMonthPayment().toString());

                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntityMonthPaymentEarly = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamEarlySquareMonthPayment".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2EntityMonthPaymentEarly.setFormula(productCalCashStreamDto.getCashStreamMonthPayment().toString());
                    }else if(month == 1){
                        productCalCashStreamDto.setCashStreamPrincipal((Double) paramMap.get("actualLoanMoney"));
                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2Entity = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamPrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2Entity.setFormula(productCalCashStreamDto.getCashStreamPrincipal().toString());

                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntityBankPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamUnionLoanBankPrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2EntityBankPrincipal.setFormula(((Double)paramMap.get("bankLoanMoney")).toString());


                        ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanOurRemainPrincipalZero = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashSteramUnionLoanOurRemainPrincipalZero".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();

                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntityUnionLoanOurPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamUnionLoanOurPrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2EntityUnionLoanOurPrincipal.setFormula(ProductCalBaseTemplateV2EntityUnionLoanOurRemainPrincipalZero.getFormula());

                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntityUnionLoanEarlySquareOurMonthPayment = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamUnionLoanEarlySquareOurMonthPaymentFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2EntityUnionLoanEarlySquareOurMonthPayment.setFormula(ProductCalBaseTemplateV2EntityUnionLoanOurRemainPrincipalZero.getFormula()+"+"+productCalCashStreamDto.getCashStreamUnionLoanOursInterest());


                        /**
                         * 等额本息联合贷 提前结清 月供特殊处理
                         */
                        Double cashStreamReceivableCustomerInterest = productCalCashStreamDto.getCashStreamReceivableCustomerInterest();
                        Double cashStreamRemainPrincipalUp = productCalCashStreamDtoList.stream().filter(productCalCashStreamDtoa -> productCalCashStreamDtoa.getMonth() == (month-1)).collect(Collectors.toList()).get(0).getCashStreamRemainPrincipal();

                        BigDecimal add = BigDecimal.valueOf(cashStreamReceivableCustomerInterest).add(BigDecimal.valueOf(cashStreamRemainPrincipalUp));
                        productCalCashStreamDto.setCashStreamMonthPayment(add.doubleValue());
                        productCalCashStreamDto.setCashStreamEarlySquareMonthPayment(add.doubleValue());

                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntityMonthPayment = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamMonthPaymentFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2EntityMonthPayment.setFormula(productCalCashStreamDto.getCashStreamMonthPayment().toString());

                        ProductCalBaseTemplateV2Entity productCalBaseTemplateV2EntityMonthPaymentEarly = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                            return "cashStreamEarlySquareMonthPaymentFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                        }).findFirst().get();
                        productCalBaseTemplateV2EntityMonthPaymentEarly.setFormula(productCalCashStreamDto.getCashStreamMonthPayment().toString());
                    }

                }

                /**
                 * 计算首月信息
                 */
                if(month == 1){
                    boolean firstFlag = calFirstSpecialFormula(month,actualLoanMoneyValue,productCalBaseTemplateV2Entities,productCalCashStreamDto,paramMap,map,productCalBasicInfoDto,productCalculateDto.getProductCalBasicParamDto(),isUnionFlag);
                    if(firstFlag == false){
                        log.info("计算现金流首月特殊公式失败");
                        return false;
                    }
                }

                /**
                 * 计算2月及之后信息
                 */
                if(month >= 2){
                    boolean firstFlag = calSecondAndMoreFormula(month,productCalBaseTemplateV2Entities,productCalCashStreamDto,paramMap,map,productCalBasicInfoDto,productCalculateDto.getProductCalBasicParamDto(),cashStreamTemplateEntitiesBak,productCalBaseTemplateProfitCalEntityListBak,mapper,isUnionFlag,productCalculateDto.getProductCalculateEarlySquareDto(),earlyDealFlag);
                    if(firstFlag == false){
                        log.info("计算现金流大于等于2月公式失败");
                        return false;
                    }
                }

                productCalCashStreamDtoList.add(productCalCashStreamDto);
                productCalculateDto.setProductCalCashStreamDtoList(productCalCashStreamDtoList);

                /**
                 * 如果是提前结清，并且月数等于提前结清月数，则跳出循环
                 */
                if(isUnionFlag && productCalculateDto.getProductCalculateEarlySquareDto()!=null && month.equals(productCalculateDto.getProductCalculateEarlySquareDto().getEarlySquareTimeLimit())){
                    /**
                     * 如果是提前结清，且为最后提前结清月份，则单独处理返回的现金流月供
                     */
                    /**
                     * 当期应收客户利息
                     */
                    Double cashStreamReceivableCustomerInterest = productCalCashStreamDto.getCashStreamReceivableCustomerInterest();
                    Double cashStreamRemainPrincipalUp = productCalCashStreamDtoList.stream().filter(productCalCashStreamDtoa -> productCalCashStreamDtoa.getMonth() == (month-1)).collect(Collectors.toList()).get(0).getCashStreamRemainPrincipal();

                    BigDecimal add = BigDecimal.valueOf(cashStreamReceivableCustomerInterest).add(BigDecimal.valueOf(cashStreamRemainPrincipalUp));
                    productCalCashStreamDto.setCashStreamMonthPayment(add.doubleValue());
                }

                if(productCalculateDto.getProductCalculateEarlySquareDto()!=null && isUnionFlag && month.equals(productCalculateDto.getProductCalculateEarlySquareDto().getEarlySquareTimeLimit() + 1)){
                    break;
                }else if(productCalculateDto.getProductCalculateEarlySquareDto()!=null && !isUnionFlag && !earlyDealFlag && month.equals(productCalculateDto.getProductCalculateEarlySquareDto().getEarlySquareTimeLimit())){
                    break;
                }else if(productCalculateDto.getProductCalculateEarlySquareDto()!=null && !isUnionFlag && earlyDealFlag && month.equals(productCalculateDto.getProductCalculateEarlySquareDto().getEarlySquareTimeLimit() + 1)){
                    break;
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("calulateCashStream end");
        }catch(Exception e){
            log.error("calulateCashStream error：{}",e);
            return false;
        }
        return true;

    }

    public boolean dealProfitTemplate(List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateProfitCalEntityListBak,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,Mapper mapper){
        try{
            List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateProfitCalEntityList = productCalBaseTemplateProfitCalEntityListBak.stream().map(ProductCalBaseTemplateV2Entity -> {
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityBak = mapper.map(ProductCalBaseTemplateV2Entity,ProductCalBaseTemplateV2Entity.class);
                return ProductCalBaseTemplateV2EntityBak;
            }).collect(Collectors.toList());
            productCalBaseTemplateV2Entities.addAll(productCalBaseTemplateProfitCalEntityList);
            return true;
        }catch(Exception e){
            log.error("dealProfitTemplate error",e);
            return false;
        }
    }

    public boolean dealUnionLoanTemplate(List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateUnionLoanEntityListBak,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,Mapper mapper){
        try{
            List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateUnionLoanEntityList = productCalBaseTemplateUnionLoanEntityListBak.stream().map(ProductCalBaseTemplateV2Entity -> {
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityBak = mapper.map(ProductCalBaseTemplateV2Entity,ProductCalBaseTemplateV2Entity.class);
                return ProductCalBaseTemplateV2EntityBak;
            }).collect(Collectors.toList());
            for (ProductCalBaseTemplateV2Entity productCalBaseTemplateEarlySquareEntity : productCalBaseTemplateUnionLoanEntityList) {
                boolean flag = true;
                String item = productCalBaseTemplateEarlySquareEntity.getItem();
                for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : productCalBaseTemplateV2Entities) {
                    if(ProductCalBaseTemplateV2Entity.getItem().equals(item)){
                        ProductCalBaseTemplateV2Entity.setDataType(productCalBaseTemplateEarlySquareEntity.getDataType());
                        ProductCalBaseTemplateV2Entity.setFormula(productCalBaseTemplateEarlySquareEntity.getFormula());
                        flag = false;
                    }
                }
                if(flag){
                    productCalBaseTemplateV2Entities.add(productCalBaseTemplateEarlySquareEntity);
                }
            }

            return true;
        }catch(Exception e){
            log.error("dealUnionLoanTemplate error",e);
            return false;
        }
    }

    public boolean dealCashStreamDate(Integer month,Map<String,Object> paramMap,ProductCalCashStreamDto productCalCashStreamDto){
        try{
            /**
             * 获取日期  2017-10-15 格式
             */
            String activationDate = "";
            if(paramMap.get("startMonth")!=null){
                String startMonth = (String)paramMap.get("startMonth");
                activationDate = startMonth+"-15";
            }else{
                activationDate = LocalDate.of(LocalDate.now().getYear(), LocalDate.now().getMonth(), 15).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            paramMap.put("activationDate",activationDate);
            productCalCashStreamDto.setCashStreamDate(LocalDate.parse(activationDate).plusMonths(month).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

            paramMap.put(month+"dueDate",productCalCashStreamDto.getCashStreamDate());
            return true;
        }catch(Exception e){
            log.error("dealDate error",e);
            return false;
        }
    }

    public boolean calNormalFormula(Integer month,Double actualLoanMoneyValue,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,Map<String,Object> paramMap,Map<String,Object> map,ProductCalCashStreamDto productCalCashStreamDto,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            /**
             * 计算基本公式参数
             */
            List<ProductCalBaseTemplateV2Entity> basicFormularList;
            if(month == 0){
                Double fixedCost = (Double) paramMap.get("fixedCost");
                productCalCashStreamDto.setCashStreamOtherChangeCost(fixedCost);
                productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamOtherChangeCostZero".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get().setFormula(fixedCost.toString());
                /**
                 * 查询中台放款手续费信息
                 */
                List<ProductCalLoanHandChargeEntity> productCalLoanHandChargeEntityList = productCalDao.getLoanHandCharge(actualLoanMoneyValue);
                Double loanHandCharge = productCalLoanHandChargeEntityList.stream().mapToDouble(productCalLoanHandChargeEntity -> {
                    return productCalLoanHandChargeEntity.getHandCharge() * productCalLoanHandChargeEntity.getLoanProbability();
                }).sum();
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityLoanHandChargeZero = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamLoanHandChargeZero".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                Double loanHandChargeNew = dealScale(ProductCalBaseTemplateV2EntityLoanHandChargeZero.getScaleValue(),loanHandCharge);
                productCalCashStreamDto.setCashStreamLoanHandCharge(Double.valueOf("-"+loanHandChargeNew));
                productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamLoanHandChargeZero".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get().setFormula("-"+loanHandChargeNew);
                paramMap.put(month+"CashStreamLoanHandCharge","-"+loanHandChargeNew);


                basicFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "4".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getDataType()) && ProductCalBaseTemplateV2Entity.getItem().endsWith("Zero");
                }).collect(Collectors.toList());
            }else if(month == 1){
                basicFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "4".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getDataType()) && ProductCalBaseTemplateV2Entity.getItem().endsWith("First");
                }).collect(Collectors.toList());
            }else{
                basicFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "4".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "1".equals(ProductCalBaseTemplateV2Entity.getDataType()) && !ProductCalBaseTemplateV2Entity.getItem().endsWith("Zero") && !ProductCalBaseTemplateV2Entity.getItem().endsWith("First");
                }).collect(Collectors.toList());
            }
            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : basicFormularList) {
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,month,productCalBasicInfoDto);
            }

            /**
             * 计算公式套公式
             */
            List<ProductCalBaseTemplateV2Entity> formularAndFormularList = new ArrayList<>();
            if(month == 0){
                formularAndFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "4".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "2".equals(ProductCalBaseTemplateV2Entity.getDataType()) && ProductCalBaseTemplateV2Entity.getItem().endsWith("Zero");
                }).collect(Collectors.toList());
            }else if(month == 1){
                formularAndFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "4".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "2".equals(ProductCalBaseTemplateV2Entity.getDataType()) && ProductCalBaseTemplateV2Entity.getItem().endsWith("First");
                }).collect(Collectors.toList());
            }else{
                if(!ProductCalRepaymentMethodEnum.WKD.equals(productCalBasicInfoDto.getRepaymentMethod())){
                    formularAndFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "4".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "2".equals(ProductCalBaseTemplateV2Entity.getDataType()) && !ProductCalBaseTemplateV2Entity.getItem().endsWith("Zero") && !ProductCalBaseTemplateV2Entity.getItem().endsWith("First");
                    }).collect(Collectors.toList());
                }
            }
            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : formularAndFormularList) {
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,month,productCalBasicInfoDto);
            }
            return true;
        }catch(Exception e){
            log.error("calNormalFormula",e);
            return false;
        }
    }

    public boolean calZeroSpecialFormula(Integer month,Double actualLoanMoneyValue,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalCashStreamDto productCalCashStreamDto,Map<String,Object> paramMap,Map<String,Object> map,ProductCalBasicInfoDto productCalBasicInfoDto){
        try{
            /**
             * 计算零月现金流支出
             */
            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityExpendZero = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamRemainMoneyZero".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityExpendZero,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 计算零月现金流
             */
            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityCashStreamZero = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamZero".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityCashStreamZero,paramMap,map,month,productCalBasicInfoDto);
            return true;
        }catch(Exception e){
            log.error("calZeroSpecialFormula error",e);
            return false;
        }
    }

    public boolean calFirstSpecialFormula(Integer month,Double actualLoanMoneyValue,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalCashStreamDto productCalCashStreamDto,Map<String,Object> paramMap,Map<String,Object> map,ProductCalBasicInfoDto productCalBasicInfoDto,ProductCalBasicParamDto productCalBasicParamDto,boolean isUnionFlag){
        try{
            /**
             * 本金等比分段贷需要对月供特殊从处理
             */
            if(ProductCalRepaymentMethodEnum.BJDBFDD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityMonthPaymentFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamMonthPaymentFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityMonthPaymentFirst,paramMap,map,month,productCalBasicInfoDto);

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityReceivableCustomerInterestFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamReceivableCustomerInterestFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityReceivableCustomerInterestFirst,paramMap,map,month,productCalBasicInfoDto);

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityPrincipalFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamPrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityPrincipalFirst,paramMap,map,month,productCalBasicInfoDto);
            }

            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityRemainPrincipalZero = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamRemainPrincipalZero".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            paramMap.put("cashStreamRemainPrincipalUp",ProductCalBaseTemplateV2EntityRemainPrincipalZero.getFormula());

            /**
             * 计算首月剩余本金
             */
            Double remainPrincipal = 0.0;

            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityRemainPrincipalFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamRemainPrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            Double paramValueRemainPrincipal = dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityRemainPrincipalFirst,paramMap,map,month,productCalBasicInfoDto);
            remainPrincipal = paramValueRemainPrincipal;

            if(ProductCalRepaymentMethodEnum.FDD.type().equals(productCalBasicInfoDto.getRepaymentMethod())) {
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityActualInterestFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamReceivableCustomerInterestFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto, productCalBaseTemplateV2Entities, ProductCalBaseTemplateV2EntityActualInterestFirst, paramMap, map, month, productCalBasicInfoDto);

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityPrincipalFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamPrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto, productCalBaseTemplateV2Entities, ProductCalBaseTemplateV2EntityPrincipalFirst, paramMap, map, month, productCalBasicInfoDto);
            }

            if(isUnionFlag){
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanBankRemainPrincipalZero = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamUnionLoanBankRemainPrincipalZero".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                paramMap.put("cashStreamUnionLoanBankRemainPrincipalUp",ProductCalBaseTemplateV2EntityUnionLoanBankRemainPrincipalZero.getFormula());

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanBankRemainPrincipalFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamUnionLoanBankRemainPrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityUnionLoanBankRemainPrincipalFirst,paramMap,map,month,productCalBasicInfoDto);
                paramMap.put("cashStreamUnionLoanBankRemainPrincipalUp",ProductCalBaseTemplateV2EntityUnionLoanBankRemainPrincipalFirst.getFormula());

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityEarlySquareBankPrincipalFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamUnionLoanEarlySquareBankPrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityEarlySquareBankPrincipalFirst,paramMap,map,month,productCalBasicInfoDto);

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityReceivableCustomerInterestFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamUnionLoanBankInterestHandChargeFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityReceivableCustomerInterestFirst,paramMap,map,month,productCalBasicInfoDto);

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanOurPrincipalFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamUnionLoanOurPrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityUnionLoanOurPrincipalFirst,paramMap,map,month,productCalBasicInfoDto);

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanOurRemainPrincipalZero = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashSteramUnionLoanOurRemainPrincipalZero".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                paramMap.put("cashSteramUnionLoanOurRemainPrincipalUp",ProductCalBaseTemplateV2EntityUnionLoanOurRemainPrincipalZero.getFormula());

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanOurRemainPrincipalFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashSteramUnionLoanOurRemainPrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityUnionLoanOurRemainPrincipalFirst,paramMap,map,month,productCalBasicInfoDto);
                paramMap.put("cashSteramUnionLoanOurRemainPrincipalUp",ProductCalBaseTemplateV2EntityUnionLoanOurRemainPrincipalFirst.getFormula());

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanEarlySquareOurHandChargeFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamUnionLoanEarlySquareOurHandChargeFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityUnionLoanEarlySquareOurHandChargeFirst,paramMap,map,month,productCalBasicInfoDto);

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanEarlySquareOurMonthPaymentFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamUnionLoanEarlySquareOurMonthPaymentFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityUnionLoanEarlySquareOurMonthPaymentFirst,paramMap,map,month,productCalBasicInfoDto);
            }


            /**
             * 计算首月剩余金额
             */
            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityRemainMoneyZero = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamRemainMoneyZero".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            paramMap.put("cashStreamRemainMoneyUp",ProductCalBaseTemplateV2EntityRemainMoneyZero.getFormula());

            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityRemainMoneyFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamRemainMoneyFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            Double paramValueRemainMoney = dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityRemainMoneyFirst,paramMap,map,month,productCalBasicInfoDto);
            paramMap.put("cashStreamRemainMoneyUp",paramValueRemainMoney);

            /**
             * 计算首月利息支出
             */
            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityInterestExpendFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamInterestExpendFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityInterestExpendFirst,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 计算首月现金流提前结清手续费
             */
            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityEarlySquareHandChargeFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamEarlySquareHandChargeFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityEarlySquareHandChargeFirst,paramMap,map,month,productCalBasicInfoDto);


            /**
             * 计算首月扣款手续费
             */
            List<ProductCalDeductMoneyHandChargeEntity> productCalDeductMoneyHandChargeEntityList = productCalDao.getDeductMoneyHandCharge(actualLoanMoneyValue);
            Double deductHandCharge = productCalDeductMoneyHandChargeEntityList.stream().mapToDouble(productCalDeductMoneyHandChargeEntity -> {
                return productCalDeductMoneyHandChargeEntity.getHandCharge() * productCalDeductMoneyHandChargeEntity.getDeductProbability() * productCalDeductMoneyHandChargeEntity.getProportion();
            }).sum();
            paramMap.put("deductHandCharge",deductHandCharge);

            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityDeductMoneyFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamDeductMoneyHandChargeFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityDeductMoneyFirst,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 现金流提前结清月供，提前结清本金
             */
            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityEarlySquarePrincipalFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamEarlySquarePrincipalFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityEarlySquarePrincipalFirst,paramMap,map,month,productCalBasicInfoDto);

            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityEarlySquareMonthPaymentFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamEarlySquareMonthPaymentFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityEarlySquareMonthPaymentFirst,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 计算首月现金流流转税
             */
            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityTurnoverTaxFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamTurnoverTaxFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityTurnoverTaxFirst,paramMap,map,month,productCalBasicInfoDto);

            /**
             * 计算首月现金流
             */
            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityCashStreamFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityCashStreamFirst,paramMap,map,month,productCalBasicInfoDto);


            /**
             * 计算首月利息收入
             */
            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityInterestIncomFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "cashStreamInterestIncomFirst".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityInterestIncomFirst,paramMap,map,month,productCalBasicInfoDto);

            paramMap.put("cashStreamRemainPrincipalUp",remainPrincipal);
            return true;
        }catch(Exception e){
            log.error("calFirstSpecialFormula",e);
            return false;
        }
    }

    public boolean calSecondAndMoreFormula(Integer month,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalCashStreamDto productCalCashStreamDto,Map<String,Object> paramMap,Map<String,Object> map,ProductCalBasicInfoDto productCalBasicInfoDto,ProductCalBasicParamDto productCalBasicParamDto,List<ProductCalBaseTemplateV2Entity> cashStreamTemplateEntitiesBak,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateProfitCalEntityListBak,Mapper mapper,boolean isUnionFlag,ProductCalculateEarlySquareDto productCalculateEarlySquareDto,boolean earlyDealFlag){
        try{
            /**
             * 本金等比分段贷需要对月供特殊从处理
             */
            if(earlyDealFlag && month.equals(productCalculateEarlySquareDto.getEarlySquareTimeLimit() + 1)){

                /**
                 * 标识，后期计算IRR需要
                 */
                paramMap.put("earlyDealFlag","1");
                productCalCashStreamDto.setCashStreamInterestIncom(0.0);
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityInterestIncom = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamInterestIncom".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                ProductCalBaseTemplateV2EntityInterestIncom.setFormula("0.0");

                productCalCashStreamDto.setCashStreamEarlySquareHandCharge(0.0);
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityEarlySquareHandCharge = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamEarlySquareHandCharge".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                ProductCalBaseTemplateV2EntityEarlySquareHandCharge.setFormula("0.0");

                productCalCashStreamDto.setCashStreamDeductMoneyHandCharge(0.0);
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityDeductMoneyHandCharge = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamDeductMoneyHandCharge".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                ProductCalBaseTemplateV2EntityDeductMoneyHandCharge.setFormula("0.0");

                productCalCashStreamDto.setCashStreamCollectionFeeTotal(0.0);
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityCollectionFeeTotal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamCollectionFeeTotal".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                ProductCalBaseTemplateV2EntityCollectionFeeTotal.setFormula("0.0");

                productCalCashStreamDto.setCashStreamEarlySquarePrincipal(0.0);
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityEarlySquarePrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamEarlySquarePrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                ProductCalBaseTemplateV2EntityEarlySquarePrincipal.setFormula("0.0");

                /**
                 * 计算现金流流转税
                 */
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityTurnoverTax = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamTurnoverTax".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityTurnoverTax,paramMap,map,month,productCalBasicInfoDto);

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityCashStream = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStream".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityCashStream,paramMap,map,month,productCalBasicInfoDto);
            }else{
                if(ProductCalRepaymentMethodEnum.BJDBFDD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityMonthPayment = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamMonthPayment".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();

                    Integer monthStage = ((Integer)paramMap.get("equalPrincipalStageNum"))-((productCalBasicParamDto.getTimeLimit()-month)/(productCalBasicParamDto.getTimeLimit()/(Integer)paramMap.get("equalPrincipalStageNum")));
                    ProductCalBaseTemplateV2EntityMonthPayment.setFormula(ProductCalBaseTemplateV2EntityMonthPayment.getFormula().replace("Numtime",monthStage.toString()));
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityMonthPayment,paramMap,map,month,productCalBasicInfoDto);

                    /**
                     * 重新计算外包催收费用和催收费用合计
                     */
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityOutsourcingCollectionFee = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamOutsourcingCollectionFee".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityOutsourcingCollectionFee,paramMap,map,month,productCalBasicInfoDto);

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityCollectionFeeTotal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamCollectionFeeTotal".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityCollectionFeeTotal,paramMap,map,month,productCalBasicInfoDto);


                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityReceivableCustomerInterestFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamReceivableCustomerInterest".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityReceivableCustomerInterestFirst,paramMap,map,month,productCalBasicInfoDto);

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityPrincipalFirst = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityPrincipalFirst,paramMap,map,month,productCalBasicInfoDto);
                }

                /**
                 * 对于尾款贷需要单独处理当期利息 并且在计算出当期利息后计算剩余的  现金流信息
                 */
                if(ProductCalRepaymentMethodEnum.WKD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityReceivableCustomerInterest = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamReceivableCustomerInterest".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityReceivableCustomerInterest,paramMap,map,month,productCalBasicInfoDto);

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityPrincipal,paramMap,map,month,productCalBasicInfoDto);

                    List<ProductCalBaseTemplateV2Entity> formularAndFormularList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "4".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "2".equals(ProductCalBaseTemplateV2Entity.getDataType()) && !ProductCalBaseTemplateV2Entity.getItem().endsWith("Zero") && !ProductCalBaseTemplateV2Entity.getItem().endsWith("First");
                    }).collect(Collectors.toList());
                    for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : formularAndFormularList) {
                        dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,month,productCalBasicInfoDto);
                    }
                }

                /**
                 * 计算剩余本金
                 */
                Double remainPrincipal = 0.0;
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityRemainPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamRemainPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                Double paramValueRemainPrincipal = dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityRemainPrincipal,paramMap,map,month,productCalBasicInfoDto);
                remainPrincipal = paramValueRemainPrincipal;

                /**
                 * 计算首月实际利息
                 */
                if(ProductCalRepaymentMethodEnum.FDD.type().equals(productCalBasicInfoDto.getRepaymentMethod())) {
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityActualInterest = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamReceivableCustomerInterest".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto, productCalBaseTemplateV2Entities, ProductCalBaseTemplateV2EntityActualInterest, paramMap, map, month, productCalBasicInfoDto);

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto, productCalBaseTemplateV2Entities, ProductCalBaseTemplateV2EntityPrincipal, paramMap, map, month, productCalBasicInfoDto);
                }

                if(isUnionFlag){
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanBankRemainPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamUnionLoanBankRemainPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityUnionLoanBankRemainPrincipal,paramMap,map,month,productCalBasicInfoDto);
                    paramMap.put("cashStreamUnionLoanBankRemainPrincipalUp",ProductCalBaseTemplateV2EntityUnionLoanBankRemainPrincipal.getFormula());

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityEarlySquareBankPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamUnionLoanEarlySquareBankPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityEarlySquareBankPrincipal,paramMap,map,month,productCalBasicInfoDto);

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityReceivableCustomerInterest = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamUnionLoanBankInterestHandCharge".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityReceivableCustomerInterest,paramMap,map,month,productCalBasicInfoDto);

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanOurPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamUnionLoanOurPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityUnionLoanOurPrincipal,paramMap,map,month,productCalBasicInfoDto);

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanOurRemainPrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashSteramUnionLoanOurRemainPrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityUnionLoanOurRemainPrincipal,paramMap,map,month,productCalBasicInfoDto);
                    paramMap.put("cashSteramUnionLoanOurRemainPrincipalUp",ProductCalBaseTemplateV2EntityUnionLoanOurRemainPrincipal.getFormula());

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanEarlySquareOurHandCharge = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamUnionLoanEarlySquareOurHandCharge".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityUnionLoanEarlySquareOurHandCharge,paramMap,map,month,productCalBasicInfoDto);

                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityUnionLoanEarlySquareOurMonthPayment = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamUnionLoanEarlySquareOurMonthPayment".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityUnionLoanEarlySquareOurMonthPayment,paramMap,map,month,productCalBasicInfoDto);
                }

                /**
                 * 计算首月剩余金额
                 */

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityRemainMoney = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamRemainMoney".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                Double paramValueRemainMoney = dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityRemainMoney,paramMap,map,month,productCalBasicInfoDto);
                paramMap.put("cashStreamRemainMoneyUp",paramValueRemainMoney);

                /**
                 * 计算首月利息支出
                 */
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityInterestExpend = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamInterestExpend".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityInterestExpend,paramMap,map,month,productCalBasicInfoDto);

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityDeductMoney = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamDeductMoneyHandCharge".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityDeductMoney,paramMap,map,month,productCalBasicInfoDto);

                /**
                 * 现金流提前结清月供，提前结清本金
                 */
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityEarlySquarePrincipal = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamEarlySquarePrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityEarlySquarePrincipal,paramMap,map,month,productCalBasicInfoDto);

                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityEarlySquareMonthPayment = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamEarlySquareMonthPayment".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityEarlySquareMonthPayment,paramMap,map,month,productCalBasicInfoDto);

                /**
                 * 计算首月现金流提前结清手续费
                 */
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityEarlySquareHandCharge = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamEarlySquareHandCharge".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityEarlySquareHandCharge,paramMap,map,month,productCalBasicInfoDto);


                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityInterestIncom = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamInterestIncom".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityInterestIncom,paramMap,map,month,productCalBasicInfoDto);

                if(isUnionFlag && productCalculateEarlySquareDto!=null && month.equals(productCalculateEarlySquareDto.getEarlySquareTimeLimit() + 1)){
                    productCalCashStreamDto.setCashStreamDeductMoneyHandCharge(0.0);
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityDeductMoneyHandCharge = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamDeductMoneyHandCharge".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    ProductCalBaseTemplateV2EntityDeductMoneyHandCharge.setFormula("0.0");

                    productCalCashStreamDto.setCashStreamEarlySquareHandCharge(0.0);
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityHandCharge = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamEarlySquareHandCharge".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    ProductCalBaseTemplateV2EntityHandCharge.setFormula("0.0");


                    productCalCashStreamDto.setCashStreamInterestIncom(0.0);
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityCashStreamInterestIncom = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamInterestIncom".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    ProductCalBaseTemplateV2EntityCashStreamInterestIncom.setFormula("0.0");

                    productCalCashStreamDto.setCashStreamUnionLoanBankInterestHandCharge(0.0);
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityCashStreamUnionLoanBankInterestHandCharge = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamUnionLoanBankInterestHandCharge".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    ProductCalBaseTemplateV2EntityCashStreamUnionLoanBankInterestHandCharge.setFormula("0.0");

                    productCalCashStreamDto.setCashStreamEarlySquarePrincipal(0.0);
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityEarlySquarePrincipalNew = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamEarlySquarePrincipal".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    ProductCalBaseTemplateV2EntityEarlySquarePrincipalNew.setFormula("0.0");

                    productCalCashStreamDto.setCashStreamRemainMoney(0.0);
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityRemainMoneyNew = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamRemainMoney".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    ProductCalBaseTemplateV2EntityRemainMoneyNew.setFormula("0.0");

                    productCalCashStreamDto.setCashStreamInterestExpend(0.0);
                    ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityInterestExpendNew = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                        return "cashStreamInterestExpend".equals(ProductCalBaseTemplateV2Entity.getItem());
                    }).findFirst().get();
                    ProductCalBaseTemplateV2EntityInterestExpendNew.setFormula("0.0");
                    paramMap.put(month+"cashStreamInterestExpend",0.0);
                }

                /**
                 * 计算现金流流转税
                 */
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityTurnoverTax = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStreamTurnoverTax".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityTurnoverTax,paramMap,map,month,productCalBasicInfoDto);

                /**
                 * 计算首月现金流
                 */
                ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityCashStream = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                    return "cashStream".equals(ProductCalBaseTemplateV2Entity.getItem());
                }).findFirst().get();
                dealFormula(productCalCashStreamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityCashStream,paramMap,map,month,productCalBasicInfoDto);

                paramMap.put("cashStreamRemainPrincipalUp",remainPrincipal);
            }
            return true;
        }catch(Exception e){
            log.error("calSecondAndMoreFormula",e);
            return false;
        }
    }

    public Result afterCalDeal(Map<String,Object> paramMap,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalculateDto productCalculateDto,boolean isUnionFlag){
        Result result = new Result();
        try{
            log.info("afterCalDeal start");
            long startTime = System.currentTimeMillis();
            Map<String,Object> map = new HashMap<>();

            boolean basicPamFlag = basicPamAfterCal(productCalBaseTemplateV2Entities,productCalculateDto,paramMap,map,isUnionFlag);
            if(basicPamFlag == false){
                result.setCode(FAIL.getCode());
                result.setMessage("基本参数后置方法处理失败");
                return result;
            }

            boolean taxFeePamFlag = taxFeePamAfterCal(productCalBaseTemplateV2Entities,productCalculateDto,paramMap,map,isUnionFlag);
            if(taxFeePamFlag == false){
                result.setCode(FAIL.getCode());
                result.setMessage("税费参数后置方法处理失败");
                return result;
            }

            if(isUnionFlag){
                boolean unionFamFlag = unionLoanPamAfterCal(productCalBaseTemplateV2Entities,productCalculateDto,paramMap,map);
                if(unionFamFlag == false){
                    result.setCode(FAIL.getCode());
                    result.setMessage("联合贷参数后置方法处理失败");
                    return result;
                }
            }

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            long endTime = System.currentTimeMillis();
            log.info("afterCalDeal cost time: "+(endTime - startTime)+"ms");
            log.info("afterCalDeal end");
        }catch(Exception e){
            log.error("afterCalDeal error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("计算后置方法失败");
        }
        return result;
    }

    public boolean basicPamAfterCal(List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalculateDto productCalculateDto,Map<String,Object> paramMap,Map<String,Object> map, boolean isUnionLoanFlag){
        try{
            /**
             * 计算基本参数剩余参数
             */
            List<ProductCalBaseTemplateV2Entity> basicRemainBaseTemplateEntityList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "0".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "5".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());
            ProductCalBasicParamDto productCalBasicParamDto = productCalculateDto.getProductCalBasicParamDto();
            ProductCalCashStreamDto productCalCashStreamDtoZero = productCalculateDto.getProductCalCashStreamDtoList().stream().filter(x -> {
                return x.getMonth() == 0;
            }).findFirst().get();

            ProductCalCashStreamDto productCalCashStreamDtoFirst = productCalculateDto.getProductCalCashStreamDtoList().stream().filter(x -> {
                return x.getMonth() == 1;
            }).findFirst().get();
            productCalBasicParamDto.setLoanHandCharge(productCalCashStreamDtoZero.getCashStreamLoanHandCharge());
            productCalBasicParamDto.setDeductMoneyHandCharge(productCalCashStreamDtoFirst.getCashStreamDeductMoneyHandCharge());

            /**
             * 计算总利息
             */
            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : basicRemainBaseTemplateEntityList) {

                String formula = ProductCalBaseTemplateV2Entity.getFormula();
                String[] split = formula.split("~");
                List<String> arrOrMapList = Arrays.stream(split).filter(x -> {
                    return x.startsWith("cashStream");
                }).collect(Collectors.toList());

                int count = 1;
                for (String arrorMapStr : arrOrMapList){
                    formula = dealArrayOrMap(count,formula,arrorMapStr,paramMap,map,ProductCalBaseTemplateV2Entity,productCalBaseTemplateV2Entities,isUnionLoanFlag);
                    count++;
                }
                ProductCalBaseTemplateV2Entity.setFormula(formula);
                dealFormula(productCalBasicParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalculateDto.getProductCalBasicInfoDto());
            }
            return true;
        }catch(Exception e){
            log.error("basicPamAfterCal error",e);
            return false;
        }
    }

    public boolean taxFeePamAfterCal(List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalculateDto productCalculateDto,Map<String,Object> paramMap,Map<String,Object> map,boolean isUnionLoanFlag){
        try{
            /**
             * 计算税费参数剩余参数
             */
            ProductCalTaxFeeParamDto productCalTaxFeeParamDto = productCalculateDto.getProductCalTaxFeeParamDto();

            List<ProductCalBaseTemplateV2Entity> taxFeeRemainArrBaseTemplateEntityList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "1".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "5".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : taxFeeRemainArrBaseTemplateEntityList) {

                String formula = ProductCalBaseTemplateV2Entity.getFormula();
                String[] split = formula.split("~");
                List<String> arrOrMapList = Arrays.stream(split).filter(x -> {
                    return x.startsWith("cashStream") || x.startsWith("repl");
                }).collect(Collectors.toList());

                int count = 1;
                for (String arrorMapStr : arrOrMapList){
                    formula = dealArrayOrMap(count,formula,arrorMapStr,paramMap,map,ProductCalBaseTemplateV2Entity,productCalBaseTemplateV2Entities,isUnionLoanFlag);
                    count++;
                }
                ProductCalBaseTemplateV2Entity.setFormula(formula);
                dealFormula(productCalTaxFeeParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalculateDto.getProductCalBasicInfoDto());

            }

            List<ProductCalBaseTemplateV2Entity> taxFeeRemainBaseTemplateEntityList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "1".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "4".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());


            /**
             * 先计算费额
             */

            List<ProductCalBaseTemplateV2Entity> ProductCalBaseTemplateV2EntityMoneyList= taxFeeRemainBaseTemplateEntityList.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "valueAddedTax".equals(ProductCalBaseTemplateV2Entity.getItem()) || "educationFeeAppend".equals(ProductCalBaseTemplateV2Entity.getItem()) || "badness".equals(ProductCalBaseTemplateV2Entity.getItem()) || "totalCost".equals(ProductCalBaseTemplateV2Entity.getItem()) || "netProfitMoney".equals(ProductCalBaseTemplateV2Entity.getItem()) || "incomeTaxCost".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : ProductCalBaseTemplateV2EntityMoneyList) {
                dealFormula(productCalTaxFeeParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalculateDto.getProductCalBasicInfoDto());
            }

            /**
             * 计算费率相关参数
             */

            List<ProductCalBaseTemplateV2Entity> ProductCalBaseTemplateV2EntityRateList= taxFeeRemainBaseTemplateEntityList.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "stampTaxFeeRate".equals(ProductCalBaseTemplateV2Entity.getItem()) || "otherChangeFeeRate".equals(ProductCalBaseTemplateV2Entity.getItem()) || "incomeTaxRate".equals(ProductCalBaseTemplateV2Entity.getItem()) || "interestExpendFeeRate".equals(ProductCalBaseTemplateV2Entity.getItem()) || "educationFeeAppendFeeRate".equals(ProductCalBaseTemplateV2Entity.getItem()) || "badnessFeeRate".equals(ProductCalBaseTemplateV2Entity.getItem()) || "serviceFeeRate".equals(ProductCalBaseTemplateV2Entity.getItem()) || "valueAddedTaxFeeRate".equals(ProductCalBaseTemplateV2Entity.getItem()) || "fixedExpendFeeRate".equals(ProductCalBaseTemplateV2Entity.getItem()) || "totalFeeRate".equals(ProductCalBaseTemplateV2Entity.getItem()) || "profitRate".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : ProductCalBaseTemplateV2EntityRateList) {
                dealFormula(productCalTaxFeeParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalculateDto.getProductCalBasicInfoDto());
            }
            return true;
        }catch(Exception e){
            log.error("taxFeePamAfterCal error",e);
            return false;
        }
    }

    public boolean unionLoanPamAfterCal(List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalculateDto productCalculateDto,Map<String,Object> paramMap,Map<String,Object> map){
        try{
            ProductCalUnionLoanParamDto productCalUnionLoanParamDto = productCalculateDto.getProductCalUnionLoanParamDto();
            List<ProductCalBaseTemplateV2Entity> unionLoanRemainArrBaseTemplateEntityList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "6".equals(ProductCalBaseTemplateV2Entity.getPamType()) && "5".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : unionLoanRemainArrBaseTemplateEntityList) {

                String formula = ProductCalBaseTemplateV2Entity.getFormula();
                String[] split = formula.split("~");
                List<String> arrOrMapList = Arrays.stream(split).filter(x -> {
                    return x.startsWith("cashStream") || x.startsWith("repl");
                }).collect(Collectors.toList());

                int count = 1;
                for (String arrorMapStr : arrOrMapList){
                    formula = dealArrayOrMap(count,formula,arrorMapStr,paramMap,map,ProductCalBaseTemplateV2Entity,productCalBaseTemplateV2Entities,true);
                    count++;
                }
                ProductCalBaseTemplateV2Entity.setFormula(formula);
                dealFormula(productCalUnionLoanParamDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalculateDto.getProductCalBasicInfoDto());
            }

            return true;
        }catch(Exception e){
            log.error("unionLoanPamAfterCal error",e);
            return false;
        }
    }

    /**
     * 计算结果信息
     * @param paramMap
     * @param productCalBaseTemplateV2Entities
     * @param productCalculateDto
     * @return
     */
    public boolean calulateResultInfo(Map<String,Object> paramMap,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalculateDto productCalculateDto,boolean isUnionLoanFlag){
        try{
            log.info("calulateResultInfo start");
            ProductCalBasicInfoDto productCalBasicInfoDto = productCalculateDto.getProductCalBasicInfoDto();
            long startTime = System.currentTimeMillis();
            Map<String,Object> map = new HashMap<>();

            ProductCalResultInfoDto productCalResultInfoDto = new ProductCalResultInfoDto();
            /**
             * 计算结果信息
             */
            List<ProductCalBaseTemplateV2Entity> resultInfoBaseTemplateEntityList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "5".equals(ProductCalBaseTemplateV2Entity.getPamType());
            }).collect(Collectors.toList());

            /**
             * 计算公式套公式
             */
            List<ProductCalBaseTemplateV2Entity> formulaAndForBaseTemplateEntityList = resultInfoBaseTemplateEntityList.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "2".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : formulaAndForBaseTemplateEntityList) {
                dealFormula(productCalResultInfoDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalBasicInfoDto);

            }

            /**
             * 计算数组公式
             */
            List<ProductCalBaseTemplateV2Entity> arrOrMapBaseTemplateEntityList = resultInfoBaseTemplateEntityList.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "5".equals(ProductCalBaseTemplateV2Entity.getDataType());
            }).collect(Collectors.toList());

            for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : arrOrMapBaseTemplateEntityList) {
                String formula = ProductCalBaseTemplateV2Entity.getFormula();
                String[] split = formula.split("~");
                List<String> arrOrMapList = Arrays.stream(split).filter(x -> {
                    return x.startsWith("cashStream") || x.startsWith("repl") || x.startsWith("dueDate");
                }).collect(Collectors.toList());

                int count = 1;
                for (String arrorMapStr : arrOrMapList){
                    formula = dealArrayOrMap(count,formula,arrorMapStr,paramMap,map,ProductCalBaseTemplateV2Entity,productCalBaseTemplateV2Entities,isUnionLoanFlag);
                    count++;
                }
                ProductCalBaseTemplateV2Entity.setFormula(formula);
                dealFormula(productCalResultInfoDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity,paramMap,map,null,productCalBasicInfoDto);
            }
            productCalculateDto.setProductCalResultInfoDto(productCalResultInfoDto);

            if(ProductCalRepaymentMethodEnum.JTD.type().equals(productCalBasicInfoDto.getRepaymentMethod())){
                productCalculateDto.getProductCalRepaymentMethodParamDto().setDecreaseInMonthlyPaymentRatio(-1*productCalculateDto.getProductCalRepaymentMethodParamDto().getDecreaseInMonthlyPaymentRatio());
            }

            /**
             * 计算ROA
             */
            /**
             * 计算现金流流转税
             */
            ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2EntityRoa = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return "resultRoa".equals(ProductCalBaseTemplateV2Entity.getItem());
            }).findFirst().get();
            dealFormula(productCalResultInfoDto,productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2EntityRoa,paramMap,map,null,productCalBasicInfoDto);

            long endTime = System.currentTimeMillis();
            log.info("calulateResultInfo cost time: "+(endTime - startTime)+"ms");
            log.info("calulateResultInfo end");
        }catch(Exception e){
            log.error("计算返回结果信息失败",e);
            return false;
        }
        return true;
    }

    public boolean dealScalePercent(ProductCalculateDto productCalculateDto,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities){
        try{
            log.info("dealScalePercent start");
            long startTime = System.currentTimeMillis();
            List<String> scalePercentList = productCalBaseTemplateV2Entities.stream().filter(ProductCalBaseTemplateV2Entity -> {
                return 1==ProductCalBaseTemplateV2Entity.getNeedPersentDel();
            }).map(ProductCalBaseTemplateV2Entity::getItem).collect(Collectors.toList());

            ProductCalBasicParamDto productCalBasicParamDto = productCalculateDto.getProductCalBasicParamDto();

            if(productCalBasicParamDto != null){
                dealFieldValuePercent(productCalBasicParamDto,scalePercentList);
            }

            ProductCalTaxFeeParamDto productCalTaxFeeParamDto = productCalculateDto.getProductCalTaxFeeParamDto();

            if(productCalTaxFeeParamDto != null){
                dealFieldValuePercent(productCalTaxFeeParamDto,scalePercentList);
            }

            ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto = productCalculateDto.getProductCalRepaymentMethodParamDto();

            if(productCalRepaymentMethodParamDto != null){
                dealFieldValuePercent(productCalRepaymentMethodParamDto,scalePercentList);
            }

            ProductCalResultInfoDto productCalResultInfoDto = productCalculateDto.getProductCalResultInfoDto();

            if(productCalResultInfoDto != null){
                dealFieldValuePercent(productCalResultInfoDto,scalePercentList);
            }
            long endTime = System.currentTimeMillis();
            log.info("dealScalePercent cost time: "+(endTime - startTime)+"ms");
            log.info("dealScalePercent end");
        }catch(Exception e){
            log.error("dealScalePercent error",e);
            return false;
        }
        return true;
    }

    public void dealFieldValuePercent(Object object,List<String> scalePercentList) throws Exception {
        Field[] declaredFields = object.getClass().getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            if(scalePercentList.contains(declaredField.getName())){
                Double value = null;
                if(declaredField.get(object) != null){
                    value = BigDecimal.valueOf((Double)declaredField.get(object)).multiply(BigDecimal.valueOf(100)).doubleValue();
                }
                assgnFieldWithCalValue(object,declaredField.getName(),value);
            }
        }
    }

    /**
     * 处理数组或者map
     * @param count
     * @param formula
     * @param arrorMapStr
     * @param paramMap
     * @param map
     * @param ProductCalBaseTemplateV2Entity
     * @param productCalBaseTemplateV2Entities
     * @return
     */
    public String dealArrayOrMap(int count, String formula, String arrorMapStr, Map<String,Object> paramMap, Map<String,Object> map, ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity, List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities, boolean isUnionLoanFlag){
        Integer timeLimit;
        if(paramMap.get("earlySquareTimeLimit")!=null){
            timeLimit = (Integer) paramMap.get("earlySquareTimeLimit");
        }else{
            timeLimit = (Integer) paramMap.get("timeLimit");
        }
        if(isUnionLoanFlag){
            timeLimit = timeLimit + 1;
        }

        if(paramMap.get("earlyDealFlag")!=null){
            timeLimit = timeLimit + 1;
        }

        if(formula.indexOf("array(~"+arrorMapStr+"~)") != -1){
            StringBuffer stringBuffer = new StringBuffer("");
            for(int i=0; i<=timeLimit; i++){
                if(paramMap.get(i+arrorMapStr) != null){
                    if(i!=timeLimit && paramMap.get((i+1)+arrorMapStr) != null){
                        stringBuffer.append(paramMap.get(i+arrorMapStr)+",");
                    }else {
                        stringBuffer.append(paramMap.get(i+arrorMapStr));
                    }

                }
            }
            formula = formula.replaceAll("array\\(~"+arrorMapStr+"~\\)",stringBuffer.toString());
        }else if(formula.indexOf("mapDouble(~"+arrorMapStr+"~)") != -1){
            formula = formula.replaceAll("mapDouble\\(~"+arrorMapStr+"~\\)",arrorMapStr);
            List<Double> valueList = new ArrayList<>();
            for(int i=0; i<=timeLimit; i++){
                if(paramMap.get(i+arrorMapStr) != null){
                    if(i == 0){
                        if(!"resultBeforeTaxIncome".equals(ProductCalBaseTemplateV2Entity.getItem())){
                            valueList.add((Double) paramMap.get(i+arrorMapStr));
                        }
                    }else{
                        valueList.add((Double) paramMap.get(i+arrorMapStr));
                    }
                }
            }
            double[] doubles = new double[valueList.size()];
            for (int i=0; i<valueList.size(); i++){
                doubles[i] = valueList.get(i);
            }
            map.put(arrorMapStr,doubles);
        }else if(formula.indexOf("mapString(~"+arrorMapStr+"~)") != -1){
            formula = formula.replaceAll("mapString\\(~"+arrorMapStr+"~\\)",arrorMapStr);
            List<String> valueList = new ArrayList<>();
            for(int i=0; i<=timeLimit; i++){
                if(paramMap.get(i+arrorMapStr) != null){
                    valueList.add((String)paramMap.get(i+arrorMapStr));
                }
            }
            String[] strings = new String[valueList.size()];
            for (int i=0; i<valueList.size(); i++){
                strings[i] = valueList.get(i);
            }
            map.put(arrorMapStr,strings);
        }else if(formula.indexOf("~"+arrorMapStr+"~") != -1){
            String formulaNew = arrorMapStr;
            arrorMapStr = arrorMapStr.replaceAll("\\(","\\\\(");
            arrorMapStr = arrorMapStr.replaceAll("\\)","\\\\)");
            formula = formula.replaceAll("~"+arrorMapStr+"~","value"+count);
            String formulaNoRepl = formulaNew.replace("repl","");
            while(true){
                formulaNoRepl = getFinalFormula(formulaNoRepl,productCalBaseTemplateV2Entities);
                if(formulaNoRepl.indexOf("#") == -1){
                    break;
                }
            }
            for (String param : paramMap.keySet()) {
                if(formulaNoRepl.indexOf(param) != -1){
                    formulaNoRepl = formulaNoRepl.replaceAll("@"+param+"@",String.valueOf(paramMap.get(param)));
                }
            }
            Map<String,Object> mapRepl = new HashMap<>();
            map.put("value"+count,(Double)AviatorUtils.calculateValue(mapRepl,formulaNoRepl));
        }

        return formula;
    }

    /**
     * 获取最终不带#公式
     * @param formula
     * @param ProductCalBaseTemplateV2EntityList
     * @return
     */
    public String getFinalFormula(String formula, List<ProductCalBaseTemplateV2Entity> ProductCalBaseTemplateV2EntityList){
        for (ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity : ProductCalBaseTemplateV2EntityList) {
            if(formula.indexOf("#"+ProductCalBaseTemplateV2Entity.getItem()+"#")!=-1){
                formula = formula.replaceAll("#"+ProductCalBaseTemplateV2Entity.getItem()+"#",ProductCalBaseTemplateV2Entity.getFormula());
            }
        }
        return formula;
    }

    /**
     * 使用反射方法，为对象的属性赋值
     * @param object
     * @param fieldName
     * @param value
     */
    public void assgnFieldWithCalValue(Object object,String fieldName,Double value) throws Exception {
        Field field = object.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(object,value);
    }

    /**
     * 处理小数部分
     * @param scaleValue
     * @param paramValue
     * @return
     */
    public Double dealScale(Integer scaleValue, Double paramValue){
        Double paramValueNew;
        if(scaleValue!=null){
            paramValueNew = BigDecimal.valueOf(paramValue).setScale(scaleValue, BigDecimal.ROUND_HALF_UP).doubleValue();
        }else{
            paramValueNew = BigDecimal.valueOf(paramValue).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
        return paramValueNew;
    }

    /**
     * 计算公式
     * @param productCalBaseTemplateV2Entities
     * @param ProductCalBaseTemplateV2Entity
     * @param paramMap
     * @param map
     * @return
     */
    public Double dealFormula(Object object,List<ProductCalBaseTemplateV2Entity> productCalBaseTemplateV2Entities,ProductCalBaseTemplateV2Entity ProductCalBaseTemplateV2Entity,Map<String,Object> paramMap,Map<String,Object> map,Integer month,ProductCalBasicInfoDto productCalBasicInfoDto) {
        Double paramValue = 0.0;
        String formula = ProductCalBaseTemplateV2Entity.getFormula();

        String formulaOld = "";
        while(true){
            formula = getFinalFormula(formula,productCalBaseTemplateV2Entities);
            if(formula.indexOf("#") == -1){
                break;
            }
            if(formulaOld.equals(formula)){
                break;
            }else{
                formulaOld = formula;
            }
        }

        if(formula.indexOf("#") != -1){
            log.error("条目："+ProductCalBaseTemplateV2Entity.getItem()+"的公式："+formula+"含有未替换完成公式#?#");
            throw new SystemRuntimeException("条目："+ProductCalBaseTemplateV2Entity.getItem()+"的公式："+formula+"含有未替换完成公式#?#");
        }

        /**
         * 第二部计算值
         */
        for (String param : paramMap.keySet()) {
            if(formula.indexOf(param) != -1){
                if("activationDate".equals(param)){
                    formula = formula.replaceAll("@"+param+"@","'"+String.valueOf(paramMap.get(param))+"'");
                }else{
                    formula = formula.replaceAll("@"+param+"@",String.valueOf(paramMap.get(param)));
                }
            }
        }

        if(formula.indexOf("@") == -1){
            ProductCalBaseTemplateV2Entity.setFormula(formula);
            try {
                paramValue = (Double) AviatorUtils.calculateValue(map, formula);
            } catch (Exception e) {
                log.error("报错条目：{}，报错公式：{}\n{}",ProductCalBaseTemplateV2Entity.getItem(),formula,e);
            }
            if (paramValue.equals(NaN)) {
                log.error("报错条目：{}，报错公式NaN：{}\n{}",ProductCalBaseTemplateV2Entity.getItem(),formula);
            }
            Double paramValueNew = dealScale(ProductCalBaseTemplateV2Entity.getScaleValue(), paramValue);
            String item = ProductCalBaseTemplateV2Entity.getItem();
            if(month != null){
                if(month == 0){
                    if(item.endsWith("Zero")){
                        item = item.substring(0,item.length()-4);
                    }
                }else if(month == 1){
                    if(item.endsWith("First")){
                        item = item.substring(0,item.length()-5);
                    }
                }
            }
            if(!(paramValueNew == 0 && ProductCalRepaymentMethodEnum.DEBX.type().equals(productCalBasicInfoDto.getRepaymentMethod()) && (ProductCalRepaymentMethodEnum.DEBJ.type().equals(productCalBasicInfoDto.getRepaymentMethod())) && "cashStreamRemainPrincipal".equals(item))){
                if(month != null){
                    paramMap.put(month+item,paramValue);
                }
                try {
                    assgnFieldWithCalValue(object,item,paramValueNew);
                } catch (Exception e) {
                    log.error("assgnFieldWithCalValue error:{}",e);
                }
            }
        } else {
            log.info("公式中的参数未替换完毕！"+formula);
        }
        return paramValue;
    }

    /**
     * 获取提前结清概率信息
     * @return
     */
    public Result getEarlySquareProbabilityInfo(Integer businessType,Double customerInterestRate,Double actualInterestRate,Integer timeLimit){
        Result result = new Result();
        QueryWrapper queryWrapperEarlySquareProbability = new QueryWrapper();
        String bussType = businessType == 0?"本品":(businessType == 1?"全品新车":"全品二手车");
        queryWrapperEarlySquareProbability.eq("business_type",bussType);

        String ifDisInsterst = customerInterestRate.equals(actualInterestRate)==true?"非贴息":"贴息";
        queryWrapperEarlySquareProbability.eq("if_dis_insterst",ifDisInsterst);

        queryWrapperEarlySquareProbability.eq("contract_trm",timeLimit);

        queryWrapperEarlySquareProbability.orderByAsc("payout_rental_id");

        List<ProductCalEarlySquareProbabilityEntity> productCalEarlySquareProbabilityEntityList = productCalEarlySquareProbabilityDao.selectList(queryWrapperEarlySquareProbability);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        result.setData(productCalEarlySquareProbabilityEntityList);
        return result;
    }

    public Result calEarlySquareProbabilityAndRatio(ProductCalculateDto productCalculateDto,String source){
        Result result = new Result();
        try{
            if(!"template".equals(source)){
                Integer isUnionLoan = null;
                ProductCalUnionLoanParamDto productCalUnionLoanParamDto = productCalculateDto.getProductCalUnionLoanParamDto();
                if(productCalUnionLoanParamDto != null && productCalUnionLoanParamDto.getBankLoanRatio() != null){
                    isUnionLoan = 1;
                }
                productProposalCalService.replaceProductCalculateDto(productCalculateDto,isUnionLoan);
            }

            Mapper mapper = DozerBeanMapperBuilder.buildDefault();
            ProductCalEarlySquareParamSubDto productCalEarlySquareParamSubDto = mapper.map(productCalculateDto.getProductCalEarlySquareParamSubDto(),ProductCalEarlySquareParamSubDto.class);

            /**
             * 由于计算提前结清手续费比例，扣返比例需要剩余本金支持，所以需要提前计算现金流。又因为现有计算现金流流程需要手续费比例和扣返比例，所以计算现金流前先将手续费比例，扣返比例，提前
             * 还清概率置值为0
             */
            dealZeroEarlySquare(productCalculateDto);
            if(productCalculateDto.getProductCalBasicParamDto().getIsFarmerLoan() == null){
                productCalculateDto.getProductCalBasicParamDto().setIsFarmerLoan(0);
            }
            /**
             * 损失率不影响剩余本金的计算，批量和提案中反算风险损失的时候不会提供损失率，但是计算扣返比例和手续费比例又需要损失率，所以判断如果为空给默认值 0.1（0.0计算现金流可能存在失败情况）
             */
            if(productCalculateDto.getProductCalBasicParamDto().getLossRate() == null){
                productCalculateDto.getProductCalBasicParamDto().setLossRate(0.1);
            }
            Result resultPre = calculateAllUnionParam(productCalculateDto);
            if(resultPre.getCode() != 200){
                log.info("计算剩余本金失败,{}",resultPre.getMessage());
                result.setCode(FAIL.getCode());
                result.setMessage("计算剩余本金失败,"+resultPre.getMessage());
                return result;
            }
            ProductCalculateDto productCalculateDtoAft = (ProductCalculateDto)resultPre.getData();
            List<ProductCalCashStreamDto> productCalCashStreamDtoList = productCalculateDtoAft.getProductCalCashStreamDtoList();

            /**
             * 先使用presto查询hive获取提前结清概率集合
             */
            try{
                log.info("计算提前结清概率");
                ProductCalEarlySquareProbabilityPamDto productCalEarlySquareProbabilityPamDto = productCalEarlySquareParamSubDto.getProductCalEarlySquareProbabilityPamDto();
                String probabilitySql = "";
                String bussTypeStr = productCalEarlySquareProbabilityPamDto.getBusinessType() == 0?"本品":(productCalEarlySquareProbabilityPamDto.getBusinessType() == 1?"全品新车":"全品二手车");
                productCalEarlySquareProbabilityPamDto.setBusinessTypeStr(bussTypeStr);


                List<String> oriEarlySquareProbabilityClassificationList = new ArrayList<>();
                if(productCalEarlySquareProbabilityPamDto.getEarlySquareProbabilityClassificationList() != null){
                    oriEarlySquareProbabilityClassificationList.addAll(productCalEarlySquareProbabilityPamDto.getEarlySquareProbabilityClassificationList());
                }

                Integer oriTimeLimit = productCalEarlySquareProbabilityPamDto.getTimeLimit();
                if("本品".equals(bussTypeStr)){
                    probabilitySql = bpProbabilitySql;
                    /**
                     * 经营复盘时因表现期不完整标息-低利率、标息-新标息暂时参考标息-常规，低息-增益暂时参考低息-常规
                     */
                    productCalEarlySquareProbabilityPamDto.getEarlySquareProbabilityClassificationList().replaceAll(s -> s.replaceAll("标息-低利率标息","标息-常规标息"));
                    productCalEarlySquareProbabilityPamDto.getEarlySquareProbabilityClassificationList().replaceAll(s -> s.replaceAll("标息-新标息","标息-常规标息"));
                    productCalEarlySquareProbabilityPamDto.getEarlySquareProbabilityClassificationList().replaceAll(s -> s.replaceAll("低息-增益系列","低息-常规低息"));
                }else if("全品新车".equals(bussTypeStr)){
                    probabilitySql = qpProbabilitySql;
                }else{
                    probabilitySql = escProbabilitySql;
                    /**
                     * 二手车48期与60期使用36期合同补充，36-47/59期提前结清概率置0
                     */
                    if(productCalEarlySquareProbabilityPamDto.getTimeLimit().equals(48) || productCalEarlySquareProbabilityPamDto.getTimeLimit().equals(60)){
                        productCalEarlySquareProbabilityPamDto.setTimeLimit(36);
                    }
                }

                String ifDisInsterst = "";
                if(productCalEarlySquareProbabilityPamDto.getCustomerInterestRate().equals(productCalEarlySquareProbabilityPamDto.getActualInterestRate())){
                    ifDisInsterst = "非贴息";
                }else{
                    ifDisInsterst = "贴息";
                }
                productCalEarlySquareProbabilityPamDto.setIfDisInsterst(ifDisInsterst);

                for (Field field : productCalEarlySquareProbabilityPamDto.getClass().getDeclaredFields()) {
                    field.setAccessible(true);

                    if(probabilitySql.indexOf("@"+field.getName()+"@") != -1){
                        if("timeLimit".equals(field.getName())){
                            probabilitySql = probabilitySql.replaceAll("@"+field.getName()+"@",field.get(productCalEarlySquareProbabilityPamDto).toString());
                        }else{
                            probabilitySql = probabilitySql.replaceAll("@"+field.getName()+"@","'"+field.get(productCalEarlySquareProbabilityPamDto).toString()+"'");
                        }
                    }else if(probabilitySql.indexOf("#"+field.getName()+"#") != -1){
                        List list = (List)field.get(productCalEarlySquareProbabilityPamDto);
                        String listFileValue = list.stream().map(o->"'"+o.toString()+"'").collect(Collectors.joining(",")).toString();
                        probabilitySql = probabilitySql.replaceAll("#"+field.getName()+"#",listFileValue);
                    }
                }

                List<ProductCalEarlySquareProbabilityDto> productCalEarlySquareProbabilityDtoList = new ArrayList<>();
                log.info("probabilitySql:{}",probabilitySql);
                SqlRowSet resultSet=prestoTemplate.queryForRowSet(probabilitySql);
                int count = 1;
                while (resultSet.next()){
                    ProductCalEarlySquareProbabilityDto productCalEarlySquareProbabilityDto = new ProductCalEarlySquareProbabilityDto();
                    productCalEarlySquareProbabilityDto.setPayoutRentalId(resultSet.getInt("rental_id"));
                    productCalEarlySquareProbabilityDto.setPayoutProbability(resultSet.getDouble("payout_probability"));
                    productCalEarlySquareProbabilityDtoList.add(productCalEarlySquareProbabilityDto);
                    count++;
                }
                productCalEarlySquareProbabilityDtoList.remove(0);
                productCalEarlySquareParamSubDto.setProductCalEarlySquareProbabilityDtoList(productCalEarlySquareProbabilityDtoList);


                /**
                 * 因为表现期不完整导致的替换，在此处还原
                 */
                if("本品".equals(bussTypeStr)){
                    productCalEarlySquareProbabilityPamDto.setEarlySquareProbabilityClassificationList(oriEarlySquareProbabilityClassificationList);
                }else if ("全品二手车".equals(bussTypeStr)) {
                    productCalEarlySquareProbabilityPamDto.setTimeLimit(oriTimeLimit);
                    if(productCalEarlySquareProbabilityDtoList.size() < oriTimeLimit){
                        List<ProductCalEarlySquareProbabilityDto> productCalEarlySquareProbabilityDtoListZero = new ArrayList<>();
                        for(int i = productCalEarlySquareProbabilityDtoList.size()+1; i <= oriTimeLimit; i++){
                            ProductCalEarlySquareProbabilityDto productCalEarlySquareProbabilityDto = new ProductCalEarlySquareProbabilityDto();
                            if(i == oriTimeLimit){
                                productCalEarlySquareProbabilityDto.setPayoutRentalId(i);
                                productCalEarlySquareProbabilityDto.setPayoutProbability(productCalEarlySquareProbabilityDtoList.get(productCalEarlySquareProbabilityDtoList.size()-1).getPayoutProbability());
                                productCalEarlySquareProbabilityDtoList.get(productCalEarlySquareProbabilityDtoList.size()-1).setPayoutProbability(0.0);
                            }else{
                                productCalEarlySquareProbabilityDto.setPayoutRentalId(i);
                                productCalEarlySquareProbabilityDto.setPayoutProbability(0.0);
                            }
                            productCalEarlySquareProbabilityDtoListZero.add(productCalEarlySquareProbabilityDto);
                        }
                        productCalEarlySquareParamSubDto.getProductCalEarlySquareProbabilityDtoList().addAll(productCalEarlySquareProbabilityDtoListZero);
                    }
                }
            }catch(Exception e){
                log.error("获取提前结清概率信息失败！",e);
                productCalEarlySquareParamSubDto.setProductCalEarlySquareProbabilityDtoList(null);
            }


            /**
             * 处理手续费比例和佣金扣返比例
             */
            Map<String, Object> variables = new HashMap<>();
            ProductCalEarlySquareHandChargeAndCommissionRebatePamDto productCalEarlySquareHandChargeAndCommissionRebatePamDto = productCalEarlySquareParamSubDto.getProductCalEarlySquareHandChargeAndCommissionRebatePamDto();
            try{
                log.info("计算提前结清佣金扣返比例");
                if(productCalEarlySquareHandChargeAndCommissionRebatePamDto.getBusinessType() == 1){
                    String isCorpToCorpStr = productCalEarlySquareHandChargeAndCommissionRebatePamDto.getIsCorpToCorp() == 1?"是":"否";
                    productCalEarlySquareHandChargeAndCommissionRebatePamDto.setIsCorpToCorpStr(isCorpToCorpStr);
                }

                for (Field declaredField : productCalEarlySquareHandChargeAndCommissionRebatePamDto.getClass().getDeclaredFields()) {
                    declaredField.setAccessible(true);
                    variables.put(declaredField.getName(), declaredField.get(productCalEarlySquareHandChargeAndCommissionRebatePamDto));
                }

                variables.put("actualLoanMoney", productCalculateDto.getProductCalBasicParamDto().getActualLoanMoney());
                variables.put("dealerBasicCommissionRatio",BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getDealerBasicCommissionRatio()).divide(BigDecimal.valueOf(100)).doubleValue());
                variables.put("dealerLadderBonusRatio",BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getDealerLadderBonusRatio()).divide(BigDecimal.valueOf(100)).doubleValue());
                variables.put("dealerSaleBonusRatio",BigDecimal.valueOf(productCalculateDto.getProductCalBasicParamDto().getDealerSaleBonusRatio()).divide(BigDecimal.valueOf(100)).doubleValue());

                /**
                 * 使用佣金扣返比例规则，计算每期的佣金扣返比例
                 */
                String basicCommissionRebateRatioRule = productCalEarlySquareParamSubDto.getBasicCommissionRebateRatioRule();
                for (EarlySquareRatioRulePamEnum pamEnum : EarlySquareRatioRulePamEnum.values()) {
                    basicCommissionRebateRatioRule = basicCommissionRebateRatioRule.replaceAll(pamEnum.type(), pamEnum.value());
                }

                basicCommissionRebateRatioRule = basicCommissionRebateRatioRule.replaceAll("\n"," ");

                List<ProductCalEarlySquareBasicCommissionRebateRatioDto> productCalEarlySquareBasicCommissionRebateRatioDtoList = new ArrayList<>();
                for (int i = 1; i <=productCalEarlySquareHandChargeAndCommissionRebatePamDto.getTimeLimit(); i++) {
                    variables.put("remainPrincipal",productCalCashStreamDtoList.get(i).getCashStreamRemainPrincipal());
                    variables.put("month",i);
                    Object ratio = RuleExecutorUtil.executeRule(basicCommissionRebateRatioRule,variables);
                    ProductCalEarlySquareBasicCommissionRebateRatioDto productCalEarlySquareBasicCommissionRebateRatioDto = new ProductCalEarlySquareBasicCommissionRebateRatioDto();
                    productCalEarlySquareBasicCommissionRebateRatioDto.setPayoutRentalId(i);

                    Double douRatio = 0.0;
                    if(ratio != null){
                        if(ratio instanceof BigDecimal){
                            douRatio = ((BigDecimal)ratio).doubleValue();
                        }else if(ratio instanceof Double){
                            douRatio = (Double)ratio;
                        }else if(ratio instanceof Integer){
                            douRatio = ((Integer)ratio).doubleValue();
                        }
                        if(douRatio != 0.0){
                            douRatio = dealScale(6,douRatio);
                        }
                        productCalEarlySquareBasicCommissionRebateRatioDto.setBasicCommissionRebateRatio(douRatio);
                        productCalEarlySquareBasicCommissionRebateRatioDtoList.add(productCalEarlySquareBasicCommissionRebateRatioDto);
                    }else{
                        break;
                    }
                }
                productCalEarlySquareParamSubDto.setProductCalEarlySquareBasicCommissionRebateRatioDtoList(productCalEarlySquareBasicCommissionRebateRatioDtoList);
            }catch(Exception e){
                log.error("计算提前结清佣金扣返比例失败",e);
                productCalEarlySquareParamSubDto.setProductCalEarlySquareBasicCommissionRebateRatioDtoList(null);
            }


            /**
             * 使用提前结清手续费比例规则，计算每期的提前结清手续费比例
             */
            try{
                log.info("计算提前结清手续费比例");
                String earlySquareHandChargeRatioRule = productCalEarlySquareParamSubDto.getEarlySquareHandChargeRatioRule();
                for (EarlySquareRatioRulePamEnum pamEnum : EarlySquareRatioRulePamEnum.values()) {
                    earlySquareHandChargeRatioRule = earlySquareHandChargeRatioRule.replaceAll(pamEnum.type(), pamEnum.value());
                }

                earlySquareHandChargeRatioRule = earlySquareHandChargeRatioRule.replaceAll("\n"," ");

                List<ProductCalEarlySquareHandChargeRatioDto> productCalEarlySquareHandChargeRatioDtoList = new ArrayList<>();
                for (int i = 1; i <=productCalEarlySquareHandChargeAndCommissionRebatePamDto.getTimeLimit(); i++) {
                    variables.put("remainPrincipal",productCalCashStreamDtoList.get(i).getCashStreamRemainPrincipal());
                    variables.put("month",i);
                    Object ratio = RuleExecutorUtil.executeRule(earlySquareHandChargeRatioRule,variables);
                    ProductCalEarlySquareHandChargeRatioDto productCalEarlySquareHandChargeRatioDto = new ProductCalEarlySquareHandChargeRatioDto();
                    productCalEarlySquareHandChargeRatioDto.setPayoutRentalId(i);

                    Double douRatio = 0.0;
                    if(ratio != null){
                        if(ratio instanceof BigDecimal){
                            douRatio = ((BigDecimal)ratio).doubleValue();
                        }else if(ratio instanceof Double){
                            douRatio = (Double)ratio;
                        }else if(ratio instanceof Integer){
                            douRatio = ((Integer)ratio).doubleValue();
                        }
                        if(douRatio != 0.0){
                            douRatio = dealScale(6,douRatio);
                        }
                        productCalEarlySquareHandChargeRatioDto.setEarlySquareHandChargeRatio(douRatio);
                        productCalEarlySquareHandChargeRatioDtoList.add(productCalEarlySquareHandChargeRatioDto);
                    }else{
                        break;
                    }
                }
                productCalEarlySquareParamSubDto.setProductCalEarlySquareHandChargeRatioDtoList(productCalEarlySquareHandChargeRatioDtoList);
            }catch(Exception e){
                log.error("计算手续费比例失败",e);
                productCalEarlySquareParamSubDto.setProductCalEarlySquareHandChargeRatioDtoList(null);
            }
            result.setCode(SUCCESS.getCode());
            result.setData(productCalEarlySquareParamSubDto);

        }catch(Exception e){
            log.error("calEarlySquareProbabilityAndRatio error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("计算提前结清参数失败");
        }
        return result;
    }

    public void dealZeroEarlySquare(ProductCalculateDto productCalculateDto){
        ProductCalEarlySquareParamSubDto productCalEarlySquareParamSubDto = new ProductCalEarlySquareParamSubDto();
        List<ProductCalEarlySquareProbabilityDto> productCalEarlySquareProbabilityDtoList = new ArrayList<>();
        List<ProductCalEarlySquareBasicCommissionRebateRatioDto> productCalEarlySquareBasicCommissionRebateRatioDtoList = new ArrayList<>();
        List<ProductCalEarlySquareHandChargeRatioDto> productCalEarlySquareHandChargeRatioDtoList = new ArrayList<>();
        Integer limtTime = productCalculateDto.getProductCalBasicParamDto().getTimeLimit();

        for (int i = 1; i <=limtTime; i++) {
            ProductCalEarlySquareProbabilityDto productCalEarlySquareProbabilityDto = new ProductCalEarlySquareProbabilityDto();
            productCalEarlySquareProbabilityDto.setPayoutRentalId(i);
            if(i!=limtTime){
                productCalEarlySquareProbabilityDto.setPayoutProbability(0.0);
            }else{
                productCalEarlySquareProbabilityDto.setPayoutProbability(1.0);
            }
            productCalEarlySquareProbabilityDtoList.add(productCalEarlySquareProbabilityDto);

            ProductCalEarlySquareBasicCommissionRebateRatioDto productCalEarlySquareBasicCommissionRebateRatioDto = new ProductCalEarlySquareBasicCommissionRebateRatioDto();
            productCalEarlySquareBasicCommissionRebateRatioDto.setPayoutRentalId(i);
            productCalEarlySquareBasicCommissionRebateRatioDto.setBasicCommissionRebateRatio(0.0);
            productCalEarlySquareBasicCommissionRebateRatioDtoList.add(productCalEarlySquareBasicCommissionRebateRatioDto);

            ProductCalEarlySquareHandChargeRatioDto productCalEarlySquareHandChargeRatioDto = new ProductCalEarlySquareHandChargeRatioDto();
            productCalEarlySquareHandChargeRatioDto.setPayoutRentalId(i);
            productCalEarlySquareHandChargeRatioDto.setEarlySquareHandChargeRatio(0.0);
            productCalEarlySquareHandChargeRatioDtoList.add(productCalEarlySquareHandChargeRatioDto);
        }

        productCalEarlySquareParamSubDto.setProductCalEarlySquareProbabilityDtoList(productCalEarlySquareProbabilityDtoList);
        productCalEarlySquareParamSubDto.setProductCalEarlySquareHandChargeRatioDtoList(productCalEarlySquareHandChargeRatioDtoList);
        productCalEarlySquareParamSubDto.setProductCalEarlySquareBasicCommissionRebateRatioDtoList(productCalEarlySquareBasicCommissionRebateRatioDtoList);
        productCalculateDto.setProductCalEarlySquareParamSubDto(productCalEarlySquareParamSubDto);
    }

    /**
     * 计算每期的提前结清信息
     * @param productCalculateDto
     * @return
     */
    public Result calculateEarlySquare(ProductCalculateDto productCalculateDto){
        log.info("计算提前结清irr开始");
        long startTime = System.currentTimeMillis();
        Result result = new Result();
        try{
            Integer timeLimit = productCalculateDto.getProductCalBasicParamDto().getTimeLimit();
            List<ProductCalEarlySquareResultInfoDto> productCalEarlySquareResultInfoDtoList = new ArrayList<>();

            for (int i = 1; i <=timeLimit; i++) {

                List<ProductCalEarlySquareProbabilityDto> productCalEarlySquareProbabilityDtoList = new ArrayList<>();
                for (int j = 1; j <= i; j++) {
                    ProductCalEarlySquareProbabilityDto productCalEarlySquareProbabilityDto = new ProductCalEarlySquareProbabilityDto();
                    if(j!=i){
                        productCalEarlySquareProbabilityDto.setPayoutRentalId(j);
                        productCalEarlySquareProbabilityDto.setPayoutProbability(0.0);
                    }else{
                        productCalEarlySquareProbabilityDto.setPayoutRentalId(j);
                        productCalEarlySquareProbabilityDto.setPayoutProbability(1.0);
                    }
                    productCalEarlySquareProbabilityDtoList.add(productCalEarlySquareProbabilityDto);
                }

                productCalculateDto.getProductCalEarlySquareParamSubDto().setProductCalEarlySquareProbabilityDtoList(productCalEarlySquareProbabilityDtoList);

                ProductCalculateEarlySquareDto productCalculateEarlySquareDto = new ProductCalculateEarlySquareDto();
                productCalculateEarlySquareDto.setEarlySquareTimeLimit(i);
                productCalculateDto.setProductCalculateEarlySquareDto(productCalculateEarlySquareDto);

                Result earlyResult = calculateAllUnionParam(productCalculateDto);
                if(earlyResult.getCode()!=200){
                    result.setCode(FAIL.getCode());
                    result.setMessage("计算提前结清期限："+i+"异常："+earlyResult.getMessage());
                    return result;
                }else{
                    ProductCalculateDto productCalculateDtoResult = (ProductCalculateDto)earlyResult.getData();
                    ProductCalEarlySquareResultInfoDto productCalEarlySquareResultInfoDto = new ProductCalEarlySquareResultInfoDto();
                    productCalEarlySquareResultInfoDto.setTime(i);
                    if(productCalculateDto.getProductCalUnionLoanParamDto() != null && productCalculateDto.getProductCalUnionLoanParamDto().getBankLoanRatio() != null){
                        productCalEarlySquareResultInfoDto.setResultIrr(productCalculateDtoResult.getProductCalResultInfoDto().getResultIntegrativeIrr());
                        productCalEarlySquareResultInfoDto.setResultXirr(productCalculateDtoResult.getProductCalResultInfoDto().getResultIntegrativeXirr());
                        productCalEarlySquareResultInfoDto.setResultRoa(productCalculateDtoResult.getProductCalResultInfoDto().getResultIntegrativeRoa());
                        productCalEarlySquareResultInfoDto.setResultNetProfitMoney(productCalculateDtoResult.getProductCalResultInfoDto().getResultIntegrativeNetProfitMoney());
                        productCalEarlySquareResultInfoDto.setResultMarginalProfit(productCalculateDtoResult.getProductCalResultInfoDto().getResultIntegrativeMarginalProfit());
                        productCalEarlySquareResultInfoDto.setProductCalCashStreamDtoList(productCalculateDtoResult.getProductCalCashStreamDtoList());
                    }else{
                        productCalEarlySquareResultInfoDto.setResultIrr(productCalculateDtoResult.getProductCalResultInfoDto().getResultIrr());
                        productCalEarlySquareResultInfoDto.setResultXirr(productCalculateDtoResult.getProductCalResultInfoDto().getResultXirr());
                        productCalEarlySquareResultInfoDto.setResultRoa(productCalculateDtoResult.getProductCalResultInfoDto().getResultRoa());
                        productCalEarlySquareResultInfoDto.setResultMarginalProfit(productCalculateDtoResult.getProductCalResultInfoDto().getResultMarginalProfit());
                        productCalEarlySquareResultInfoDto.setResultNetProfitMoney(productCalculateDtoResult.getProductCalResultInfoDto().getResultNetProfitMoney());
                        productCalEarlySquareResultInfoDto.setProductCalCashStreamDtoList(productCalculateDtoResult.getProductCalCashStreamDtoList());
                    }
                    productCalEarlySquareResultInfoDtoList.add(productCalEarlySquareResultInfoDto);
                }
            }

            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            result.setData(productCalEarlySquareResultInfoDtoList);
            log.info("计算提前结清irr结束,耗时：{}s",(System.currentTimeMillis()-startTime)/1000);
        }catch (Exception e){
            log.error("calculateEarlySquare error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("计算提前结清信息失败");
        }
        return result;
    }

    /**
     * 获取提前结清详情信息
     * @param bussType
     * @param repaymentMethod
     * @return
     */
    public Result getEarlySquarePamSub(Integer bussType,Integer repaymentMethod){
        Result result = new Result();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("business_type",bussType);
        queryWrapper.eq("repayment_method",repaymentMethod);
        queryWrapper.eq("consider_early_square",1);
        queryWrapper.eq("state",1);
        queryWrapper.orderByDesc("create_time");

        long querryTemplateStartTime = System.currentTimeMillis();
        List<ProductCalBasicInfoEntity> productCalBasicInfoEntityList = productCalBasicInfoDao.selectList(queryWrapper);
        log.info("querryTemplate cost time: "+(System.currentTimeMillis()-querryTemplateStartTime)+"ms");

        if(productCalBasicInfoEntityList == null || productCalBasicInfoEntityList.size() == 0){
            result.setCode(FAIL.getCode());
            result.setMessage("该业务类型，还款方式没有对应IRR测算模板");
            return result;
        }

        /**
         * 基本信息
         */
        ProductCalBasicInfoEntity productCalBasicInfoEntity = productCalBasicInfoEntityList.get(0);


        /**
         * 查询提前结清详情
         */
        QueryWrapper queryWrapperEarlySquareSub = new QueryWrapper();
        queryWrapperEarlySquareSub.eq("basic_info_id",productCalBasicInfoEntity.getId());
        ProductCalEarlySquareParamSubEntity productCalEarlySquareParamSubEntity = productCalEarlySquareParamSubDao.selectOne(queryWrapperEarlySquareSub);
        ProductCalEarlySquareParamSubDto productCalEarlySquareParamSubDto = new ProductCalEarlySquareParamSubDto();
        if(productCalEarlySquareParamSubEntity != null){
            BeanUtils.copyProperties(productCalEarlySquareParamSubEntity,productCalEarlySquareParamSubDto);
            productCalEarlySquareParamSubDto.setProductCalEarlySquareProbabilityPamDto(GsonUtil.gsonToBean(productCalEarlySquareParamSubEntity.getProbabilityPam(), ProductCalEarlySquareProbabilityPamDto.class));
            productCalEarlySquareParamSubDto.setProductCalEarlySquareHandChargeAndCommissionRebatePamDto(GsonUtil.gsonToBean(productCalEarlySquareParamSubEntity.getHandChargeAndCommissionRebatePam(), ProductCalEarlySquareHandChargeAndCommissionRebatePamDto.class));

            productCalEarlySquareParamSubDto.setProductCalEarlySquareProbabilityDtoList(GsonUtil.jsonToList(productCalEarlySquareParamSubEntity.getEarlySquareProbability(), ProductCalEarlySquareProbabilityDto.class));
            productCalEarlySquareParamSubDto.setProductCalEarlySquareBasicCommissionRebateRatioDtoList(GsonUtil.jsonToList(productCalEarlySquareParamSubEntity.getEarlySquareBasicCommissionRebateRatio(), ProductCalEarlySquareBasicCommissionRebateRatioDto.class));
            productCalEarlySquareParamSubDto.setProductCalEarlySquareHandChargeRatioDtoList(GsonUtil.jsonToList(productCalEarlySquareParamSubEntity.getEarlySquareHandChargeRatio(), ProductCalEarlySquareHandChargeRatioDto.class));
        }

        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        result.setData(productCalEarlySquareParamSubDto);
        return result;
    }
}