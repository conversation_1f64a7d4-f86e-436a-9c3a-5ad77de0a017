package com.gwmfc.util;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import freemarker.template.utility.StringUtil;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class GsonUtil {

    private static Gson gson = null;

    private GsonUtil(){};

    static {
        gson = new GsonBuilder().disableHtmlEscaping().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
    }

    public static String toJson(Object object) {
        String json = null;
        if (gson != null && object != null) {
            json = gson.toJson(object);
        }
        return json;
    }

    public static <T> T gsonToBean(String gsonString, Class<T> cls) {
        T t = null;
        if (gson != null) {
            t = gson.fromJson(gsonString, cls);
        }
        return t;
    }


    public static <T> List<T> jsonToList(String json, Class<T> cls) {
        List<T> list = new ArrayList<T>();
        JsonArray array = new JsonParser().parse(json).getAsJsonArray();
        for (final JsonElement elem : array) {
            list.add(gson.fromJson(elem, cls));
        }
        return list;
    }

    public static <T, V> Map<T, V> jsonToMap(String json) {
        return gson.fromJson(json, Map.class);
    }


    public static Map<String, Object> flatJsonKey(String baseKey, String json) {
        JsonParser parser = new JsonParser();
        JsonElement tree = parser.parse(json);
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.hasText(json) && GsonUtil.isJsonArray(json)) {
            JsonArray jsonArray = jsonToArray(json);
            jsonArray.forEach(jsonElement -> {
                if (jsonElement.isJsonObject()) {
                    map.putAll(gson.fromJson(flatten(jsonElement.getAsJsonObject(), new JsonObject(), baseKey), new TypeToken<Map<String, String>>() {
                    }.getType()));
                } else {
                    map.put(baseKey,"[" + jsonElement.getAsString() + "]");
                }
            });
        } else {
            JsonObject jo = (JsonObject) tree;
            map.putAll(gson.fromJson(flatten(jo, new JsonObject(), baseKey), new TypeToken<Map<String, String>>() {}.getType()));
        }
        return map;
    }


    private static JsonObject flatten(JsonObject object, JsonObject flattened, String father) {
        //flatten递归函数实现对多层json的扁平化处理解析，第三个形参仅仅用来保留外层的键并在之后进行拼接
        if (flattened == null) {
            flattened = new JsonObject();
        }
        for (Map.Entry entry : object.entrySet()) {
            String midFather = entry.getKey().toString();
            String tmp = father;
            JsonElement tmpVa = (JsonElement) entry.getValue();
            try {
                if (tmpVa.isJsonObject()) {
                    //检测到多层json的时候进行递归处理   当前层键与之前外层键进行拼接
                    tmp = tmp + "." + midFather;
                    flatten(object.getAsJsonObject(entry.getKey().toString()), flattened, tmp);
                } else if (tmpVa.isJsonArray()){
                    //当前层的值没有嵌套json键值对，直接将键值对添加到flattened中
                    tmp = tmp + "." + midFather + "[0]";
                    JsonArray jsonArray = object.getAsJsonArray(entry.getKey().toString());

                    if(jsonArray.size()>0) {
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JsonObject jsonObject = (JsonObject) jsonArray.get(i);
                            flatten(jsonObject, flattened, tmp);
                        }
                    }
                } else {
                    //当前层的值没有嵌套json键值对，直接将键值对添加到flattened中
                    String nowKey = father + "." + entry.getKey().toString();
                    JsonNull jsonNull = new JsonNull();
                    if (!jsonNull.equals(entry.getValue())) {
                        flattened.add(nowKey, ((JsonElement) entry.getValue()));
                    } else {
                        JsonPrimitive jsonPrimitive = new JsonPrimitive("it's null");
                        entry.setValue(jsonPrimitive);
                        flattened.add(nowKey,((JsonElement) entry.getValue()));
                    }
                }
            } catch (JsonIOException e) {
                System.out.println(e);
            }
        }
        return flattened;
    }

    public static boolean isJson(String json) {
        JsonElement jsonElement;
        try {
            jsonElement = new JsonParser().parse(json);
        } catch (Exception e) {
            return false;
        }
        if (jsonElement == null) {
            return false;
        }
        if (!jsonElement.isJsonObject()) {
            return false;
        }
        return true;
    }

    public static boolean isJsonArray(String json) {
        JsonElement jsonElement;
        try {
            jsonElement = new JsonParser().parse(json);
        } catch (Exception e) {
            return false;
        }
        if (jsonElement == null) {
            return false;
        }
        if (!jsonElement.isJsonArray()) {
            return false;
        }
        return true;
    }

    public static JsonArray jsonToArray(String originValStr) {
        JsonElement jsonElement;
        try {
            jsonElement = new JsonParser().parse(originValStr);
            return jsonElement.getAsJsonArray();
        } catch (Exception e) {

        }
        return null;
    }
}
