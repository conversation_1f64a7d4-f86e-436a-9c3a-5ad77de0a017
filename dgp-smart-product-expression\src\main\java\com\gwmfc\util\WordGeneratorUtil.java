package com.gwmfc.util;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import java.io.*;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname WordGeneratorUtil
 * @Description 生成word文档
 * @Date 2023/10/31 16:14
 */
public class WordGeneratorUtil {
    /**
     * 生成doc文件
     * @param dataMap word中需要展示的动态数据
     * @param templateName word模板名称
     * @param output 输出流
     * @return
     * @throws IOException
     * @throws TemplateException
     */
    public static void createDoc(Map<String, Object> dataMap, String templateName, OutputStream output) throws TemplateException, IOException {
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_23);
        configuration.setDefaultEncoding("UTF-8");
        configuration.setClassicCompatible(true);
        configuration.setClassForTemplateLoading(WordGeneratorUtil.class,"/ftl/");
        Template template = configuration.getTemplate(templateName);
        //如果文件要保存
//        File outFile = new File("D://文件路径名称.doc");
//        Writer out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile), "utf-8"), 10240);
        //将模板和数据模型合并生成文件
        Writer out = new BufferedWriter(new OutputStreamWriter(output,"utf-8"), 10240);
        //生成文件
        template.process(dataMap, out);
        //关闭流
        out.flush();
        out.close();
    }
}
