package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.gwmfc.bo.UploadFileBo;
import com.gwmfc.domain.User;
import com.gwmfc.properties.FtpProperties;
import com.gwmfc.util.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class FtpService {

    @Autowired
    private FtpProperties ftpProperties;

    /**
     * 获取ftp连接
     * @return
     */
    public FTPClient getConnection() {
        FTPClient ftpClient = new FTPClient();
        // 设置连接超时时间 30秒
        ftpClient.setConnectTimeout(1000 * 30);
        // 设置ftp字符集
        ftpClient.setControlEncoding("utf-8");
        // 设置被动模式，文件传输端口设置,否则文件上传不成功，也不报错
        ftpClient.enterLocalPassiveMode();
        try {
            // 定义返回的状态码
            int replyCode;
            // 连接ftp(当前项目所部署的服务器和ftp服务器之间可以相互通讯，表示连接成功)
            ftpClient.connect(ftpProperties.getHostname(), ftpProperties.getPort());
            // 输入账号和密码进行登录
            ftpClient.login(ftpProperties.getUsername(), ftpProperties.getPassword());
            // 接受状态码(如果成功，返回230，如果失败返回503)
            replyCode = ftpClient.getReplyCode();
            // 根据状态码检测ftp的连接，调用isPositiveCompletion(reply)-->如果连接成功返回true，否则返回false
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                // 说明连接失败，需要断开连接
                log.error("[ftp] [connection error] [code:{} host:{} port:{}]", replyCode, ftpProperties.getHostname(), ftpProperties.getPort());
                ftpClient.disconnect();
                return null;
            }
        } catch (IOException e) {
            log.error("", e);
            return null;
        }
        return ftpClient;
    }

    /**
     * 检查并创建文件夹
     * @param ftpClient
     * @param filePath
     */
    public void checkOrCreateDir(FTPClient ftpClient, String filePath) throws IOException {
        String[] dirs = filePath.split("/");
        for (String dir : dirs) {
            if (StringUtils.isEmpty(dir)) {
                continue;
            }
            if (!ftpClient.changeWorkingDirectory(dir)) {
                ftpClient.makeDirectory(dir);
                ftpClient.changeWorkingDirectory(dir);
            }
        }
    }

    /**
     * 上传文件
     * @param inputStream
     * @param fileName
     * @param filePath
     * @return
     */
    public boolean uploadFile(InputStream inputStream, String fileName, String filePath) {
        FTPClient ftpClient = getConnection();

        boolean result = false;

        if (ftpClient == null) {
            return result;
        }
        try {
            // 设置文件传输模式为二进制，可以保证传输的内容不会被改变
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            ftpClient.enterLocalPassiveMode();
            checkOrCreateDir(ftpClient, filePath);
            result = ftpClient.storeFile(fileName, inputStream);
            inputStream.close();
            ftpClient.logout();
        } catch (IOException e) {
            log.error("", e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException e) {
                    log.error("", e);
                }
            }
        }
        return result;
    }

    /**
     * 下载文件
     * @param filePath
     * @param outputStream
     */
    public void downloadFile(String filePath, OutputStream outputStream) {
        FTPClient ftpClient = getConnection();
        try {
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            ftpClient.enterLocalPassiveMode();
            String simpleFileName = findFileDir(ftpClient, filePath);
            ftpClient.retrieveFile(simpleFileName, outputStream);
            outputStream.flush();
            ftpClient.logout();
        } catch (Exception e) {
            log.error("", e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException e) {
                    log.error("", e);
                }
            }
        }
    }

    /**
     * 下载
     * @param filePath
     * @return
     */
    public InputStream downloadFile(String filePath) {
        FTPClient ftpClient = getConnection();
        InputStream inputStream = null;
        try {
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            ftpClient.enterLocalPassiveMode();
            String simpleFileName = findFileDir(ftpClient, filePath);
            inputStream = ftpClient.retrieveFileStream(simpleFileName);
            ftpClient.logout();
        } catch (Exception e) {
            log.error("", e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException e) {
                    log.error("", e);
                }
            }
        }
        return inputStream;
    }

    /**
     * 找到文件存储位置
     * @param ftpClient
     * @param filePath
     * @return
     * @throws IOException
     */
    private String findFileDir(FTPClient ftpClient, String filePath) throws IOException {
        String simpleFileName = filePath.substring(filePath.lastIndexOf("/") + 1);
        String fileDir = filePath.substring(0, filePath.lastIndexOf("/"));
        if (StringUtils.isNotEmpty(fileDir)) {
            String[] dirs = fileDir.split("/");
            for (String dir : dirs) {
                if (StringUtils.isNotEmpty(dir)) {
                    ftpClient.changeWorkingDirectory(dir);
                }
            }
        }
        return simpleFileName;
    }

    /**
     * 删除文件
     * @param filePath
     * @return
     */
    public boolean deleteFile(String filePath) {
        FTPClient ftpClient = getConnection();
        boolean result = false;
        try {
            String simpleFileName = findFileDir(ftpClient, filePath);
            result = ftpClient.deleteFile(simpleFileName);
            ftpClient.logout();
            return result;
        } catch (Exception e) {
            log.error("", e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException ioe) {
                    log.error("", ioe);
                }
            }
        }
        return result;
    }


    /**
     * 更新文件
     *
     * @param remainFileList
     * @param fileList
     * @param user
     * @return
     */
    public List<UploadFileBo> uploadFileToFtp(String dir, List<UploadFileBo> remainFileList, List<MultipartFile> fileList, User user) {
        fileList.forEach(file -> {
            String originalFileName = file.getOriginalFilename();
            long startTime = System.currentTimeMillis();
            Assert.hasLength(originalFileName, "文件名不能为空");
            String fileSuffix = originalFileName.substring(originalFileName.lastIndexOf('.'));
            String newFileName = UUID.randomUUID() + "-" + System.currentTimeMillis() + fileSuffix;
            String fileDir = dir + "/" + TimeUtils.getToday();
            boolean result = false;
            try {
                result = this.uploadFile(file.getInputStream(), newFileName, fileDir);
            } catch (IOException e) {
                log.error("[replenish data] [file upload result error],{}", e.getMessage());
            }
            if (result) {
                log.info("[replenish data] [file upload end] [upload time:{}]", System.currentTimeMillis() - startTime);
            } else {
                log.info("[replenish data] [file upload result error]");
            }
            UploadFileBo uploadFileBo = new UploadFileBo();
            uploadFileBo.setFileName(originalFileName);
            uploadFileBo.setFilePath(fileDir + "/" + newFileName);
            uploadFileBo.setCreateTime(LocalDateTime.now());
            uploadFileBo.setCreateUser(user.getUserName());
            remainFileList.forEach(remain -> {
                if (remain.getFileName().equals(originalFileName)) {
                    uploadFileBo.setOverview(remain.getOverview());
                    BeanUtils.copyProperties(uploadFileBo,remain);
                }
            });
        });
        return remainFileList;
    }

    public List<UploadFileBo> uploadFileToFtp(String dir, List<MultipartFile> fileList, String userName) {
        if (fileList !=null && !fileList.isEmpty()) {
            List uploadList = new ArrayList<>(fileList.size());
            fileList.forEach(file -> {
                String originalFileName = file.getOriginalFilename();
                long startTime = System.currentTimeMillis();
                Assert.hasLength(originalFileName, "文件名不能为空");
                String fileSuffix = originalFileName.substring(originalFileName.lastIndexOf('.'));
                String newFileName = UUID.randomUUID() + "-" + System.currentTimeMillis() + fileSuffix;
                String fileDir = dir + "/" + TimeUtils.getToday();
                boolean result = false;
                try {
                    result = this.uploadFile(file.getInputStream(), newFileName, fileDir);
                } catch (IOException e) {
                    log.error("[replenish data] [file upload result error],{}", e.getMessage());
                }
                if (result) {
                    log.info("[replenish data] [file upload end] [upload time:{}]", System.currentTimeMillis() - startTime);
                } else {
                    log.info("[replenish data] [file upload result error]");
                }
                UploadFileBo uploadFileBo = new UploadFileBo();
                uploadFileBo.setFileName(originalFileName);
                uploadFileBo.setFilePath(fileDir + "/" + newFileName);
                uploadFileBo.setCreateTime(LocalDateTime.now());
                uploadFileBo.setCreateUser(userName);
                uploadList.add(uploadFileBo);
            });
            return uploadList;
        }
        return null;
    }
}
