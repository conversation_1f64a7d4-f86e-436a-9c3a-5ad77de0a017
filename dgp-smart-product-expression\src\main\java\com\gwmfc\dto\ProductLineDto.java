package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductLineDto
 * @Description TODO
 * @Date 2024/8/29 15:52
 */
@Data
@ApiModel(value = "产品条线")
public class ProductLineDto {

    @ApiModelProperty("主键id")
    private Long id;

    @ApiModelProperty("业务类型")
    private String bussType;

    @ApiModelProperty("赛道")
    private List<String> track;

    @ApiModelProperty("细分领域")
    private List<String> subdivision;

    @ApiModelProperty("产品分类")
    private String productClassification;

    @ApiModelProperty("产品二级分类")
    private String productSecondaryClassification;

    @ApiModelProperty("产品定位")
    private String productPosition;

    @ApiModelProperty("产品定位描述")
    private String productPositionDescription;

    @ApiModelProperty("产品方案描述")
    private String productProgramDescription;

    @ApiModelProperty("状态")
    private Integer state;

    @ApiModelProperty("生效开始日期")
    private String effectiveDate;

    @ApiModelProperty("有效截止日期")
    private String expiryDate;

    @ApiModelProperty("创建人")
    private String createUser;
}
