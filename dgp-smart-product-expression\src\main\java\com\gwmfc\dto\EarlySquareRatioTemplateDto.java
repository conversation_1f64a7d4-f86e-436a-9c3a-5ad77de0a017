package com.gwmfc.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname EarlySquareRatioTemplateDto
 * @Description TODO
 * @Date 2024/7/4 16:57
 */
@Data
@ExcelIgnoreUnannotated
public class EarlySquareRatioTemplateDto {
    @ExcelProperty("提前结清期次")
    Integer payoutRentalId;

    @ExcelProperty("提前结清概率")
    Double payoutProbability;

    @ExcelProperty("基础服务费扣返比例(%)")
    Double basicCommissionRebateRatio;

    @ExcelProperty("手续费比例(%)")
    Double earlySquareHandChargeRatio;
}
