package com.gwmfc.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.SocketException;

/**
 * <AUTHOR> jay
 * @Classname FTPUtil
 * @Description FTPUtil描述
 * @Date 2021/07/20
 */
@Slf4j
public class FtpUtil {

    private FtpUtil() {
        throw new IllegalStateException("FtpUtil");
    }

    private static final String CONTEXT = "context";
    /**
     * connect FTP server
     *
     * @param address  FTP server IP address
     * @param port     FTP server port
     * @param username
     * @param password
     * @return
     * @throws Exception
     */
    public static FTPClient connectFtpServer(String address, int port, String username, String password) {
        FTPClient ftpClient = new FTPClient();
        try {
            // set encoding for file transmission
            ftpClient.setControlEncoding("utf-8");
            // connect ftp server
            ftpClient.connect(address, port);
            // login ftp server
            ftpClient.login(username, password);
            // set file type for file transmission
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            int replyCode = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                ftpClient.disconnect();
                log.error("FTP connection haven't been established，authentication failed!");
                return null;
            } else {
                log.info("FTP connect successfully");
                ftpClient.enterLocalPassiveMode();
                return ftpClient;
            }
        } catch (SocketException e) {
            log.error("IP of FTP may be incorrect, please fix");
            return null;
        } catch (IOException e) {
            log.error("PORT of FTP may be incorrect, please fix");
            return null;
        }
    }

    /**
     * update file to remote ftp server
     *
     * @param ftpClient
     * @param directory   destination directory
     * @param fileName    remote file name
     * @param inputStream
     */
    public static void uploadFile(FTPClient ftpClient, String directory, String fileName, InputStream inputStream) {
        try {
            boolean isDirectoryExisted = ftpClient.changeWorkingDirectory(directory);
            log.info("isDirectoryExisted ================" + isDirectoryExisted);
            if (!isDirectoryExisted) {
                ftpClient.makeDirectory(directory);
                ftpClient.changeWorkingDirectory(directory);
            }
            ftpClient.storeFile(fileName, inputStream);
        } catch (IOException e) {
            log.error("IOException======================");
            log.error(e.getMessage());
            log.error(CONTEXT, e);
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.error(CONTEXT, e);
            }
        }
    }

    /**
     * download file from ftp server
     *
     * @param ftpClient
     * @param filePath  remote file path in ftp server
     * @return
     */
    public static byte[] downloadFile(FTPClient ftpClient, String filePath) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            FTPFile[] ftpFiles = ftpClient.listFiles(filePath);
            FTPFile ftpFile = null;
            if (ftpFiles.length >= 1) {
                ftpFile = ftpFiles[0];
            } else {
                return null;
            }
            String directory = filePath.substring(0, filePath.lastIndexOf("/"));
            ftpClient.changeWorkingDirectory(directory);
            ftpClient.retrieveFile(ftpFile.getName(), outputStream);
            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error(CONTEXT, e);
            return null;
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error(CONTEXT, e);
            }
        }
    }

    /**
     * remove file from ftp server
     *
     * @param ftpClient
     * @param filePath
     */
    public static void removeFile(FTPClient ftpClient, String filePath) {
        try {
            ftpClient.deleteFile(filePath);
        } catch (IOException e) {
            log.error(CONTEXT, e);
        }
    }

    /**
     * close ftp connection
     *
     * @param ftpClient
     * @return
     */
    public static FTPClient closeFtpConnect(FTPClient ftpClient) {
        try {
            if (ftpClient != null && ftpClient.isConnected()) {
                ftpClient.abort();
                ftpClient.disconnect();
            }
        } catch (IOException e) {
            log.error(CONTEXT, e);
        }
        return ftpClient;
    }
}
