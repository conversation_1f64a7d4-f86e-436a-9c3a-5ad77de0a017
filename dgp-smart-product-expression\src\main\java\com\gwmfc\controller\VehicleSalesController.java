package com.gwmfc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.VehicleSalesDto;
import com.gwmfc.dto.VehicleSalesQueryDto;
import com.gwmfc.entity.data.VehicleSalesEntity;
import com.gwmfc.service.VehicleSalesService;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import com.gwmfc.dto.VehicleExcelQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 商用车销量
 *
 * @Date: 2023/11/7
 * @Author: zhangxinyu
 */

@Slf4j
@Api(tags = "商用车销量")
@RequestMapping("/vehicle/sales")
@RestController
public class VehicleSalesController {

    @Resource
    private VehicleSalesService vehicleSalesService;

    @ApiOperation("商用车销量列表查询")
    @PostMapping("/list")
    public Result list(@RequestBody PageForm<VehicleSalesQueryDto> pageForm) {
        IPage<VehicleSalesEntity> pages = vehicleSalesService.listPage(pageForm.getParam(), pageForm.getCurrent(), pageForm.getSize());
        return Result.ok(pages.getRecords(), pages.getTotal());
    }

    @ApiOperation("根据id获取商用车销量详情")
    @GetMapping("/get/{id}")
    public Result getVehicleSalesById(@PathVariable Integer id) {
        VehicleSalesEntity vehicleSales = vehicleSalesService.getVehicleSalesById(id);
        return Result.ok().data(vehicleSales);
    }

    @ApiOperation("批量保存商用车销量数据")
    @PostMapping("/batchSave")
    public Result batchSaveVehicleSales(@RequestBody List<VehicleSalesEntity> vehicleSalesEntityList) {
        if (vehicleSalesService.batchSaveVehicleSales(vehicleSalesEntityList)) {
            return Result.ok("保存成功");
        } else {
            return Result.error("保存失败");
        }
    }

    @ApiOperation("保存商用车销量数据")
    @PostMapping("/save")
    public Result saveVehicleSales(@RequestBody VehicleSalesDto vehicleSalesDto, @CurrentUser User user) {
        if (vehicleSalesService.saveVehicleSales(vehicleSalesDto, user)) {
            return Result.ok("保存成功");
        } else {
            return Result.error("保存失败");
        }
    }

    @ApiOperation("根据id删除商用车销量数据")
    @PostMapping("/remove")
    public Result delVehicleSalesById(@RequestParam Integer id) {
        if (vehicleSalesService.delVehicleSalesById(id)) {
            return Result.ok("删除成功");
        } else {
            return Result.error("该记录不存在");
        }
    }

    @ApiOperation("手动更新")
    @GetMapping("/update")
    public Result updateData() {
        return GsonUtil.gsonToBean(vehicleSalesService.updateData(), Result.class);
    }

    @ApiOperation("导出Excel")
    @PostMapping("/export")
    public void export(@RequestBody @Valid VehicleExcelQueryDto queryDto, HttpServletResponse response) {
        vehicleSalesService.export(queryDto, response);
    }
}
