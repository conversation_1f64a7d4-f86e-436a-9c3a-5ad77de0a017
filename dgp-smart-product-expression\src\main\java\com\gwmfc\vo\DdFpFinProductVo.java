package com.gwmfc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023年10月11日 9:38
 */
@Data
@ApiModel(value = "产品列表")
public class DdFpFinProductVo {

    @ApiModelProperty("产品id")
    private String finProductId;

    @ApiModelProperty("产品code")
    private String finProductCode;

    @ApiModelProperty("源系统")
    private String srcSys;

    @ApiModelProperty("金融产品名称")
    private String finProductName;

    @ApiModelProperty("利率计算方式描述")
    private String rateCalTypeDsc;

    @ApiModelProperty("最大期数")
    private Integer maxTerm;

    @ApiModelProperty("最小期数")
    private Integer minTerm;

    @ApiModelProperty("最大利率")
    private BigDecimal maxRate;

    @ApiModelProperty("最小利率")
    private BigDecimal minRate;

    @ApiModelProperty("最低首付比例")
    private BigDecimal minPayPct;

    @ApiModelProperty("尾款比例")
    private BigDecimal tailPayPct;

    @ApiModelProperty("最小贷款额")
    private BigDecimal minLoanLimit;

    @ApiModelProperty("最大贷款额")
    private BigDecimal maxLoanLimit;

    @ApiModelProperty("首付比例范围")
    private String firstPayRange;

    @ApiModelProperty("尾款比例范围")
    private String tailPayRange;

    @ApiModelProperty("产品开始日期")
    private String proBeginDate;

    @ApiModelProperty("产品结束日期")
    private String proEndDate;

    @ApiModelProperty("状态描述")
    private String statusDsc;

    @ApiModelProperty("是否机构贷产品")
    private String isOrgan;

    @ApiModelProperty("BOSS车辆类型")
    private String bossCarType;

    @ApiModelProperty("ACS车辆类型")
    private String acsCarType;

    @ApiModelProperty("厂商贴息比例")
    private BigDecimal dealerDiscountPct;

    @ApiModelProperty("厂商最高贴息额")
    private BigDecimal dealerDiscountAmount;

    @ApiModelProperty("产品提案id")
    private String productProposalId;

    @ApiModelProperty("产品提案名称")
    private String productProposalName;

    @ApiModelProperty("产品提案时间")
    private String productProposalCreateTime;
}
