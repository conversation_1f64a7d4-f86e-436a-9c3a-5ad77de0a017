package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ConsumerConfidenceIndexEntity
 * @Description 消费者信息指数实体类
 * @Date 2024/10/31 17:15
 */
@Data
@TableName("east_money_consumer_confidence_index")
@ExcelIgnoreUnannotated
public class EastMoneyConsumerConfidenceIndexEntity extends BaseEntity{
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期")@TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("消费者信心指数指数值")
    @TableFieldMapping(value = "consumers_faith_index", comment = "消费者信心指数指数值")
    private String consumersFaithIndex;

    @ExcelProperty("消费者信心指数同比增长")
    @TableFieldMapping(value = "faith_index_same", comment = "消费者信心指数同比增长")
    private String faithIndexSame;

    @ExcelProperty("消费者信心指数环比增长")
    @TableFieldMapping(value = "faith_index_sequential", comment = "消费者信心指数环比增长")
    private String faithIndexSequential;

    @ExcelProperty("消费者满意指数指数值")
    @TableFieldMapping(value = "consumers_astis_index", comment = "消费者满意指数指数值")
    private String consumersAstisIndex;

    @ExcelProperty("消费者满意指数同比增长")
    @TableFieldMapping(value = "astis_index_same", comment = "消费者满意指数同比增长")
    private String astisIndexSame;

    @ExcelProperty("消费者满意指数环比增长")
    @TableFieldMapping(value = "astis_index_sequential", comment = "消费者满意指数环比增长")
    private String astisIndexSequential;

    @ExcelProperty("消费者预期指数指数值")
    @TableFieldMapping(value = "consumers_expect_index", comment = "消费者预期指数指数值")
    private String consumersExpectIndex;

    @ExcelProperty("消费者预期指数同比增长")
    @TableFieldMapping(value = "expect_index_same", comment = "消费者预期指数同比增长")
    private String expectIndexSame;

    @ExcelProperty("消费者预期指数环比增长")
    @TableFieldMapping(value = "expect_index_sequential", comment = "消费者预期指数环比增长")
    private String expectIndexSequential;

    @ExcelProperty("网页地址")
    @TableFieldMapping(value = "page_url", comment = "网页地址")
    private String pageUrl;
}
