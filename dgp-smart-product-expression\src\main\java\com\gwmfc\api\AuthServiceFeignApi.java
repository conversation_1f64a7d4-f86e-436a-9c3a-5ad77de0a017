package com.gwmfc.api;


import com.gwmfc.dto.UserDto;
import com.gwmfc.util.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient("auth-service")
public interface AuthServiceFeignApi {

    /**
     * 获取字段列表
     * @param username 更新信息
     * @return List 字段列表
     */
    @GetMapping("/user/getUserByUserName")
    Result<UserDto> getUserByUserName(@RequestParam  String username);

    /**
     * 获取用户信息
     * @param staffNo 工号
     * @return UserDto 用户信息
     */
    @GetMapping("/user/getUserByStaffNo")
    Result<UserDto> getUserByStaffNo(@RequestParam("staffNo") String staffNo);
}
