package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.service.GrossDomesticProductService;
import com.gwmfc.service.LogisticsIndexService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024年03月07日 17:50
 */
@Api(tags = "物流指数获取")
@RestController
@RequestMapping("/logistics/index")
public class LogisticsIndexController {
    @Resource
    private LogisticsIndexService logisticsIndexService;

    /**
     * 爬取月度销量排名
     *
     * @return
     */
    @ApiOperation("物流指数获取")
    @GetMapping("/catchLogisticsIndex")
    public Result catchLogisticsIndex(@RequestParam String tableName, @CurrentUser User user) {
        logisticsIndexService.catchLogisticsIndex(tableName, user.getUserName());
        return Result.ok();
    }

    /**
     * 爬取月度销量排名
     *
     * @return
     */
    @ApiOperation("物流PMI指数获取")
    @GetMapping("/catchManufacturingPmiIndexDownload")
    public Result catchManufacturingPmiIndexDownload(@RequestParam String year, @RequestParam String month, @CurrentUser User user) {
        logisticsIndexService.manufacturingPmiIndexDownload(year, month, user.getUserName());
        return Result.ok();
    }
}
