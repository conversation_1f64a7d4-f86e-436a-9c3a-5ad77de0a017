package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.UsedCarQueryDto;
import com.gwmfc.entity.data.UsedCarWeeklyTradingEntity;
import com.gwmfc.service.UsedCarWeeklyTradingService;
import com.gwmfc.service.VehicleSalesService;
import com.gwmfc.util.PageForm;
import com.gwmfc.dto.VehicleExcelQueryDto;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.util.Result;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

import static com.gwmfc.util.StatusCodeEnum.SUCCESS;


/**
 * <AUTHOR>
 * @create 2023-10-31 09:57
 */
@Api(value = "二手车周度交易量接口", tags = "二手车周度交易量接口")
@RestController
@RequestMapping("/usedCarWeeklyTrading")
public class UsedCarWeeklyTradingController {

    @Resource
    private UsedCarWeeklyTradingService usedCarWeeklyTradingService;

    @Resource
    private VehicleSalesService vehicleSalesService;

    /**
     * 列表查询接口
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "列表查询接口", produces = "application/json")
    @PostMapping("/list")
    public Result list(@RequestBody PageForm<UsedCarQueryDto> pageForm) {
        IPage<UsedCarWeeklyTradingEntity> data = usedCarWeeklyTradingService.page(pageForm.getParam(), pageForm.getCurrent(), pageForm.getSize());
        return Result.ok(data.getRecords(), data.getTotal());
    }

    /**
     * 根据id查询单个数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "根据id查询单个数据", produces = "application/json")
    @GetMapping("/{id}")
    public Result getOne(@PathVariable("id") Long id) {
        Result result = new Result();
        UsedCarWeeklyTradingEntity usedCarWeeklyTradingEntity = usedCarWeeklyTradingService.getOne(id);
        result.setData(usedCarWeeklyTradingEntity);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    /**
     * 保存数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "批量保存数据", produces = "application/json")
    @PostMapping("/batchSave")
    public Result batchSave(@RequestBody List<UsedCarWeeklyTradingEntity> list) {
        if (usedCarWeeklyTradingService.batchSave(list)) {
            return Result.ok("保存成功");
        } else {
            return Result.error("保存失败");
        }
    }

    /**
     * 删除
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", produces = "application/json")
    @PostMapping("/remove")
    public Result remove(@RequestParam("id") Long id) {
        Integer count = usedCarWeeklyTradingService.remove(id);
        if (count == 0) {
            return Result.error("删除失败");
        }
        return Result.ok();
    }

    /**
     * 更新数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "更新数据", produces = "application/json")
    @PostMapping("/update")
    public Result update(@RequestBody UsedCarWeeklyTradingEntity usedCarWeeklyTrading, @CurrentUser User user) {
        usedCarWeeklyTrading.setUpdateUser(user.getUserName());
        usedCarWeeklyTrading.setUpdateTime(LocalDateTime.now());
        Integer count = usedCarWeeklyTradingService.update(usedCarWeeklyTrading);
        if (count == 0) {
            return Result.error("更新失败");
        }
        return Result.ok();
    }

    @ApiOperation("导出Excel")
    @PostMapping("/export")
    public void export(@RequestBody @Valid VehicleExcelQueryDto queryDto, HttpServletResponse response) {
        vehicleSalesService.export(queryDto, response);
    }
}

