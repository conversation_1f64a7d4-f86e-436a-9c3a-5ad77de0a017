package com.gwmfc.util;

import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.Mustache;
import com.github.mustachejava.MustacheFactory;

import java.io.StringWriter;

/**
 * <AUTHOR> jay
 * @Classname MustacheGenerator
 * @Description MustacheGenerator描述
 * @Date 2021/05/18
 */
public class MustacheGenerator {

    private MustacheGenerator() {
        throw new IllegalStateException("MustacheGenerator");
    }

    public static String generateTemplate(String template, Object product) {
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile("mustache/" + template + ".mustache");
        StringWriter writer = new StringWriter();
        mustache.execute(writer, product);
        return writer.toString();
    }
}
