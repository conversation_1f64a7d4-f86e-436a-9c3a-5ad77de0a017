package com.gwmfc.dto;

import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalEarlySquareProbabilityDto
 * @Description TODO
 * @Date 2024/6/25 13:53
 */
@Data
@ApiModel(value = "提前结清概率")
public class ProductCalEarlySquareProbabilityDto {
    @ApiModelProperty("业务类型")
    private String businessType;

    @ApiModelProperty("是否贴息")
    private String ifDisInsterst;

    @ApiModelProperty("合同期数")
    private Integer contractTrm;

    @ApiModelProperty("结清期次")
    private Integer payoutRentalId;

    @ApiModelProperty("结清概率")
    private Double payoutProbability;
}
