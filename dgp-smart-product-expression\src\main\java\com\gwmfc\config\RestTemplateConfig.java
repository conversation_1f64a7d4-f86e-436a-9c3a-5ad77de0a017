package com.gwmfc.config;

import org.apache.http.HttpHost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.SocketAddress;
import java.security.cert.X509Certificate;

/**
 * <AUTHOR> jay
 * @Classname RestTemplateConfig
 * @Description RestTemplateConfig描述
 * @Date 2021/05/13
 */
@Configuration
@ConditionalOnClass(ProxyConfig.class)
public class RestTemplateConfig {

    @Autowired
    private ProxyConfig proxyConfig;

    @Bean
    public SimpleClientHttpRequestFactory httpClientFactory() {
        SimpleClientHttpRequestFactory httpRequestFactory = new SimpleClientHttpRequestFactory();
        httpRequestFactory.setReadTimeout(30000);
        httpRequestFactory.setConnectTimeout(30000);
        if (proxyConfig.getEnabled()) {
            SocketAddress address = new InetSocketAddress(proxyConfig.getHost(), proxyConfig.getPort());
            Proxy proxy = new Proxy(Proxy.Type.HTTP, address);
            httpRequestFactory.setProxy(proxy);
        }

        return httpRequestFactory;
    }

    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate(generateHttpRequestFactory());

        return restTemplate;
    }

    private HttpComponentsClientHttpRequestFactory generateHttpRequestFactory() {
        try {
            TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;
            // 创建一个不进行SSL证书校验的SSLContext
            SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
            SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext,new NoopHostnameVerifier());
            HttpClientBuilder builder = HttpClients.custom();
            if (proxyConfig.getEnabled()) {
                HttpHost hostProxy = new HttpHost(proxyConfig.getHost(),proxyConfig.getPort());
                builder.setProxy(hostProxy);
            }
            CloseableHttpClient httpClient = builder
                    .setSSLSocketFactory(csf)
                    .setSSLHostnameVerifier(new NoopHostnameVerifier())
                    .build();

            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
            requestFactory.setConnectionRequestTimeout(3000);
            requestFactory.setConnectTimeout(3000);
            requestFactory.setReadTimeout(6000);
            requestFactory.setHttpClient(httpClient);
            return requestFactory;
        } catch (Exception e) {
            throw new RuntimeException("创建HttpsRestTemplate失败", e);
        }
    }
}
