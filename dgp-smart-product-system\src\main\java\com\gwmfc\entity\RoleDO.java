package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
@TableName("sys_role")
public class RoleDO {

	@TableId(type = IdType.AUTO)
	private Long roleId;

	@TableField("role_name")
	private String roleName;

	@TableField("role_sign")
	private String roleSign;

	@TableField("remark")
	private String remark;

	@TableField("user_id_create")
	private Long userIdCreate;

	@TableField("gmt_create")
	private Timestamp gmtCreate;

	@TableField("gmt_modified")
	private Timestamp gmtModified;



}
