package com.gwmfc.exception;

import com.auth0.jwt.exceptions.TokenExpiredException;
import com.gwmfc.util.Result;
import com.gwmfc.util.StatusCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.sql.SQLIntegrityConstraintViolationException;

/**
 * <AUTHOR> jay
 * @Classname GlobalExceptionHandler
 * @Description Cheetah接口
 * @Date 2021/4/12
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    private static final String EXCEPTION_LOG = "Exception: {}";

    /**
     * 数据库重复key异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = {SQLIntegrityConstraintViolationException.class})
    public Result exceptionHandler(HttpServletRequest req, SQLIntegrityConstraintViolationException e) {
        log.error("duplicate key in db: {}", e);
        return Result.error(StatusCodeEnum.DUPLICATE_KEY.getCode(), StatusCodeEnum.DUPLICATE_KEY.getDesc());
    }

    /**
     * 处理其他异常
     *
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    public Result exceptionHandler(HttpServletRequest req, Exception e) {
        log.error(EXCEPTION_LOG, e);
        return Result.error(StatusCodeEnum.FAIL.getCode(), StatusCodeEnum.FAIL.getDesc());
    }


    @ExceptionHandler(value = TokenExpiredException.class)
    public Result exceptionHandler(HttpServletRequest req, TokenExpiredException e) {
        log.error(EXCEPTION_LOG, e);
        return Result.error(StatusCodeEnum.TOKEN_EXPIR.getCode(), StatusCodeEnum.TOKEN_EXPIR.getDesc());
    }


}
