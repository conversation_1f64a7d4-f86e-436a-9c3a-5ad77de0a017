<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.ProductProposalApprovalDao">
    <update id="dealAllTagForProductProposal">
        update
            `product_proposal_approval_status`
        set
            `deal_tag` = true,revocation = true
        where
            product_proposal_id = #{proposalId}
    </update>

    <delete id="deleteProductProposalApproveHistory">
        delete from product_proposal_approval_status where product_proposal_id = #{productProposalId}
    </delete>

    <select id="queryApprovalStatus" resultType="com.gwmfc.entity.ProductProposalApprovalStatusEntity">
        select * from product_proposal_approval_status where product_proposal_id = #{productProposalId}
        <choose>
            <when test="approveType == 0">

            </when>
            <when test="approveType != null and approveType != ''">
                and type = #{approveType}
            </when>
            <otherwise>
                and type is null
            </otherwise>
        </choose>
        <if test="status != null">
            and status = #{status}
        </if>
        and step = #{proposalStep} and deal_tag = false order by create_time DESC;
    </select>
    <select id="approvalHistory" resultType="com.gwmfc.entity.ProductProposalApprovalStatusEntity">
        select * from product_proposal_approval_status where product_proposal_id = #{productProposalId} and approve_status is not null
    </select>
    <select id="selectLatestCostAccountApprove"
            resultType="com.gwmfc.entity.ProductProposalApprovalStatusEntity">
        select * from product_proposal_approval_status where product_proposal_id = #{productProposalId} and approve_status = 1 and step = 'cost_accounting' order by create_time desc limit 1
    </select>

</mapper>