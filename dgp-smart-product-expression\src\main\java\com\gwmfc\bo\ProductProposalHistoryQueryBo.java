package com.gwmfc.bo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductProposalHistoryQueryBo
 * @Description TODO
 * @Date 2023/11/7 10:24
 */

/**
 * 查询市场宽表  逾期表 获取要查询出来的字段
 */
@Data
public class ProductProposalHistoryQueryBo {

    private Integer contractNum = 0;

    private Double sumVehiclePrice = 0.0;

    private Integer sumLoanTrm = 0;

    private Double sumTotalRate = 0.0;

    private Double sumCustomerRte = 0.0;

    private Double sumLoanAmt = 0.0;

    private Double sumPayPct = 0.0;

    private Double sumAddCreAmt = 0.0;

    private Double sumBadRemainPrincipalAmt = 0.0;

    private Double sumRemainPrincipalAmt = 0.0;

    private Double beforeHx18times30dRemainPrincipalAmt = 0.0;

    private Double beforeHx12times30dRemainPrincipalAmt = 0.0;

    private Double beforeHx30dRemainPrincipalAmt = 0.0;

    private Double afterHx18times30dRemainPrincipalAmt = 0.0;

    private Double afterHx12times30dRemainPrincipalAmt = 0.0;

    private Double afterHx30dRemainPrincipalAmt = 0.0;

    private Double sumLoss = 0.0;

    private Double sumMarginContribution = 0.0;

    private Double sumSaleProfit = 0.0;

    private Double sumRebeatRewPct = 0.0;
}
