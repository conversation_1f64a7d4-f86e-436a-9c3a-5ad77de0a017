package com.gwmfc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.bo.RecordBatchDeleteBo;
import com.gwmfc.bo.UploadFileBo;
import com.gwmfc.domain.User;
import com.gwmfc.dto.PolicyDto;
import com.gwmfc.entity.data.PolicyEntity;
import com.gwmfc.service.PolicyService;
import com.gwmfc.util.BeanListCopyUtil;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import com.gwmfc.util.TableUtils;
import com.gwmfc.vo.GlobalFormBusinessVo;
import com.gwmfc.vo.TableFieldDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @date 2023年08月21日 9:04
 */
@Api(tags = "政策")
@RestController
@RequestMapping("/policy")
public class PolicyController {
    @Resource
    private PolicyService policyService;

    /**
     * 详情查询接口
     *
     * @param
     * @return
     */
    @ApiOperation(value = "详情查询接口", produces = "application/json")
    @GetMapping("/detail")
    public Result<PolicyDto> detail(@RequestParam Long recordId) {
        Result<PolicyDto> result = new Result<>();
        PolicyDto dataSetBackRecordDto = policyService.detail(recordId);
        result.setData(dataSetBackRecordDto);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    /**
     * 新增
     * @param policyDto
     * @return MultipartFile file,
     *
     */
    @ApiOperation("新增")
    @PostMapping(value = "/add", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result add(@RequestPart("policyDto") PolicyDto policyDto, @RequestPart(name ="addFileList",required = false) List<MultipartFile> addFileList, @ApiIgnore @CurrentUser User user) {
        if (policyService.save(policyDto, addFileList, user) == 1) {
            return Result.ok();
        }
        return Result.error("政策数据保存失败！");
    }

    /**
     * 修改
     *
     * @return
     */
    @ApiOperation("修改")
    @PostMapping(value = "/update", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result update(@RequestPart("policyDto") PolicyDto policyDto, @RequestParam(required = false) List<MultipartFile> addFileList, @ApiIgnore @CurrentUser User user) {
        if (policyService.update(policyDto, addFileList, user) == 1) {
            return Result.ok();
        }
        return Result.error("政策数据更新失败！");
    }

    /**
     * 删除
     *
     * @return
     */
    @ApiOperation("单条删除")
    @GetMapping("/delete")
    public Result delete(@RequestParam Long recordId) {
        if (policyService.delete(recordId) == 1) {
            return Result.ok();
        }
        return Result.error("政策数据删除失败！");
    }

    /**
     * 删除
     *
     * @return
     */
    @ApiOperation("批量删除")
    @PostMapping("/deleteBatch")
    public Result deleteBatch(@RequestBody RecordBatchDeleteBo recordBatchDeleteBo) {
        policyService.deleteBatch(recordBatchDeleteBo.getRecordIdList());
        return Result.ok();
    }

    /**
     * 下载文件
     *
     * @param uploadFileBo
     * @return
     */
    @ApiOperation(value = "下载附件接口", produces = "application/json")
    @PostMapping("/downloadFile")
    public void downloadFile(@RequestBody UploadFileBo uploadFileBo, HttpServletResponse response) throws IOException {
        policyService.downloadFile(uploadFileBo, response);
    }

    @ApiOperation("列表查询")
    @PostMapping("/list")
    public Result selectByPage(@RequestBody @Valid PageForm<PolicyDto> pageForm) throws IOException {
        IPage<PolicyEntity> capitalEntityIPage = policyService.queryPage(pageForm);
        Map<String, TableFieldDetailVo> tableFieldMappings = new TableUtils().findTableBusFieldDetailMappings("policy");
        Result<GlobalFormBusinessVo> result = new Result<>();
        List<PolicyDto> productDataMartDtoList = BeanListCopyUtil.copyListProperties(capitalEntityIPage.getRecords(), PolicyDto::new);
        GlobalFormBusinessVo globalFormBusinessVo = new GlobalFormBusinessVo();
        globalFormBusinessVo.setTableHeaders(tableFieldMappings);
        globalFormBusinessVo.setTableData(productDataMartDtoList);
        result.setData(globalFormBusinessVo);
        result.setTotal(capitalEntityIPage.getTotal());
        result.setCode(200);
        return result;
    }
}
