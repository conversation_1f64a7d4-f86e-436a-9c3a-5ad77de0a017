package com.gwmfc.entity.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Classname UsedCarWeeklyTrading
 * @Description 二手车周交易量
 * @Date 2023/10/31
 * <AUTHOR> zhangke
 */
@Data
@ApiModel(value = "二手车周交易量")
@TableName("used_car_weekly_trading")
public class UsedCarWeeklyTradingEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("标题开始日期")
    private String titleStartDate;

    @ApiModelProperty("标题结束日期")
    private String titleEndDate;

    @ApiModelProperty("开始日期")
    private String startDate;

    @ApiModelProperty("结束日期")
    private String endDate;

    @ApiModelProperty("类型（1：总交易量，2.区域交易量）")
    private Integer type;

    @ApiModelProperty("区域")
    private String region;

    @ApiModelProperty("日均交易量")
    private String avgDailyTrading;

    @ApiModelProperty("图片地址")
    private String imgUrl;

    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @ApiModelProperty("修改人")
    private String updateUser;
}
