package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalResultRangeDto
 * @Description TODO
 * @Date 2023/10/17 8:46
 */
@Data
@ApiModel(value = "条件变动，范围值，计算结果信息")
public class ProductCalResultRangeDto extends ProductCalResultInfoDto{
    private String item;
    private String value;
    private Double contractProportion;
    private Double weightingIrr;
    private Long irrTemplateId;
}
