package com.gwmfc.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldMapping;
import com.gwmfc.bo.UploadFileBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年08月21日 13:31
 */
@Data
public class PolicyDto {
    private Long id;

    @ApiModelProperty("发布时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd")
    private String dataDate;

    @ApiModelProperty("发起部门")
    private String initiatingDepartment;

    @ApiModelProperty("政策概述")
    private String policyOverview;

    @ApiModelProperty("刪除附件")
    private List<UploadFileBo> delFileList;

    @ApiModelProperty("保留附件")
    private List<UploadFileBo> remainFileList;

    @ApiModelProperty("上传附件")
    private List<UploadFileBo> addFileList;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER,pattern = "yyyy-MM-dd HH:mm:ss")
    @TableFieldMapping(value = "create_time", comment = "数据时间戳",queryItem = true)
    @ExcelIgnore
    private Long createTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private String updateTime;
}
