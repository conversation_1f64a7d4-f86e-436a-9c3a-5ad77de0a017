package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname CompeteInfoAfcOptionalEntity
 * @Description TODO
 * @Date 2024/12/11 14:14
 */
@Data
@TableName("compete_info_afc_optional")
public class CompeteInfoAfcOptionalEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "batch_id")
    @ApiModelProperty("批次id")
    private Long batchId;

    @TableField(value = "car_finance")
    @ApiModelProperty("汽金")
    private String carFinance;

    @TableField("admittance_criterion")
    @ApiModelProperty("准入标准")
    private String admittanceCriterion;

    @TableField("downpayments_ratio")
    @ApiModelProperty("首付比例")
    private String downpaymentsRatio;

    @TableField("max_loan_ratio")
    @ApiModelProperty("最高贷款比例")
    private String maxLoanRatio;

    @TableField("append_loan_policy")
    @ApiModelProperty("附加贷政策")
    private String appendLoanPolicy;

    @TableField("free_mortgage_policy")
    @ApiModelProperty("免抵押政策")
    private String freeMortgagePolicy;

    @TableField("dealer_examine_dimension")
    @ApiModelProperty("经销商考核维度")
    private String dealerExamineDimension;

    @TableField(value = "staff_no")
    @ApiModelProperty("工号")
    private String staffNo;

    @TableField("create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField("create_user")
    @ApiModelProperty("创建用户")
    private String createUser;

    @TableField("update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @TableField("update_user")
    @ApiModelProperty("更新用户")
    private String updateUser;
}
