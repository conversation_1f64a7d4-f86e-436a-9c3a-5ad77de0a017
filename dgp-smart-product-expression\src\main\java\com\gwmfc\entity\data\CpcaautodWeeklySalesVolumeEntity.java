package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年03月19日 15:06
 */
@Data
@TableName("cpcaautod_weekly_sales_volume")
@ExcelIgnoreUnannotated
public class CpcaautodWeeklySalesVolumeEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("开始日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "开始日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("结束日期") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date_end", comment = "结束日期", queryItem = true)
    private String dataDateEnd;

    @ExcelProperty("数据开始日期")
    @TableFieldMapping(value = "start_date", comment = "数据开始日期")
    private String startDate;

    @ExcelProperty("数据结束日期")
    @TableFieldMapping(value = "end_date", comment = "数据结束日期", queryItem = true)
    private String endDate;

    @ExcelProperty("数据类型")
    @TableFieldMapping(value = "data_type", comment = "数据类型")
    private String dataType;

    @ExcelProperty("销量(万辆)")
    @TableFieldMapping(value = "sales_volume", comment = "销量(万辆)")
    private String salesVolume;

    @ExcelProperty("图片地址")
    @TableFieldMapping(value = "picture_url", comment = "图片地址")
    private String pictureUrl;
}
