package com.gwmfc;

import com.gwmfc.util.GsonUtil;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.OutputStreamWriter;
import java.net.InetSocketAddress;
import java.net.URL;
import java.net.URLConnection;
import java.net.Proxy;
/**
 * <AUTHOR>
 * @date 2024年02月23日 11:02
 */
@SpringBootTest
public class SslTest {

    public String getRequest(String url, int timeOut) throws Exception {
        URL u = new URL(url);
        if ("https".equalsIgnoreCase(u.getProtocol())) {
            SslUtils.ignoreSsl();
        }
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("files.uat.gwmfc.com", 8128));
        URLConnection connection = u.openConnection(proxy);
        connection.setConnectTimeout(timeOut);
        connection.setReadTimeout(timeOut);
        return IOUtils.toString(connection.getInputStream());
    }

    public String postRequest(String urlAddress, String args, int timeOut) throws Exception {
        URL url = new URL(urlAddress);
        if ("https".equalsIgnoreCase(url.getProtocol())) {
            SslUtils.ignoreSsl();
        }
        URLConnection u = url.openConnection();
        u.setDoInput(true);
        u.setDoOutput(true);
        u.setConnectTimeout(timeOut);
        u.setReadTimeout(timeOut);
        OutputStreamWriter osw = new OutputStreamWriter(u.getOutputStream(), "UTF-8");
        osw.write(args);
        osw.flush();
        osw.close();
        u.getOutputStream();
        return IOUtils.toString(u.getInputStream());
    }

    public static void main(String[] args) {
        try {
            SslTest st = new SslTest();
            String a = st.getRequest("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=%5B%5D&dfwds=%5B%7B%22wdcode%22%3A%22zb%22%2C%22valuecode%22%3A%22A010101%22%7D%5D&k1=1708647934882&h=1", 3000);
            System.out.println(a);
            ABo aBo = GsonUtil.gsonToBean(a,ABo.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
