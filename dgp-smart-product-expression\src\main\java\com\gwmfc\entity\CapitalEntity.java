package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@TableName(value = "capital",autoResultMap = true)
public class CapitalEntity {
  @TableId(type = IdType.AUTO)
  private Long id;
  private Integer source;
  private String capitalName;
  private String capitalProvince;
  private String capitalCity;
  private String dealerGroup;
  @TableField(typeHandler = JacksonTypeHandler.class)
  private List<Long> productRebateInformation;
//  @TableField(typeHandler = JacksonTypeHandler.class)
//  private List<Long> orderNumberInformation;
  @TableField(typeHandler = JacksonTypeHandler.class)
  private List<Long> otherInformation;
  private String attachment;
  @ApiModelProperty("状态")
  private Integer status;
  /**
   * 创建时间
   */
  @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime createTime;

  /**
   * 创建人
   */
  private String createUser;

  /**
   * 更新时间
   */
  @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime updateTime;

  /**
   * 更新人
   */
  private String updateUser;

  /**
   * 原始ID
   */
  private Long originId;
}
