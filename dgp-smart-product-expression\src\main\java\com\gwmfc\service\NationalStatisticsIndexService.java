package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gwmfc.constant.TotalRetailSalesIndexTypeEnum;
import com.gwmfc.dao.FixedAssetsInvestmentGrowthIndustryIndexDao;
import com.gwmfc.dao.TotalRetailSalesCarIndexDao;
import com.gwmfc.dao.TotalRetailSalesConsumptionIndexDao;
import com.gwmfc.dao.TotalRetailSalesIndexDao;
import com.gwmfc.domain.User;
import com.gwmfc.entity.data.FixedAssetsInvestmentGrowthIndustryIndexEntity;
import com.gwmfc.entity.data.TotalRetailSalesCarIndexEntity;
import com.gwmfc.entity.data.TotalRetailSalesConsumptionIndexEntity;
import com.gwmfc.entity.data.TotalRetailSalesIndexEntity;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Classname TotalRetailSalesService
 * @Description TODO
 * @Date 2024/11/13 13:45
 */
@Service
@Slf4j
@RefreshScope
public class NationalStatisticsIndexService {

    @Value("${dataCapture.nationalStatisticsUrl}")
    private String nationalStatisticsUrl;

    @Value("${dataCapture.nationalStatisticsPageUrl}")
    private String nationalStatisticsPageUrl;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TotalRetailSalesIndexDao totalRetailSalesIndexDao;

    @Autowired
    private TotalRetailSalesCarIndexDao totalRetailSalesCarIndexDao;

    @Autowired
    private TotalRetailSalesConsumptionIndexDao totalRetailSalesConsumptionIndexDao;

    @Autowired
    private FixedAssetsInvestmentGrowthIndustryIndexDao fixedAssetsInvestmentGrowthIndustryIndexDao;

    public Result nationalStatisticsIndexCapture(String type,String date, User user){
        try{
            log.info("处理国家统计数据-类型：{}，日期：{}开始",type,date);
            String dataDate;
            String dateStr;
            String year;
            String month;
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            if(user == null || StringUtils.isEmpty(user.getUserName())){
                LocalDate localDate = LocalDate.parse(date,dateTimeFormatter);
                LocalDate localDateBefore = localDate.minusMonths(1);
                year = String.valueOf(localDateBefore.getYear());
                month = String.valueOf(localDateBefore.getMonth().getValue());

                if(localDateBefore.getMonth().getValue() < 10){
                    dateStr = year.concat("0").concat(month);
                }else{
                    dateStr = year.concat(month);
                }

            }else{
                dateStr = date;
            }

            restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));

            Map<String,Object> map = new HashMap<>();


            map.put("dfwds","[{\"wdcode\":\"zb\",\"valuecode\":\""+type+"\"},{\"wdcode\":\"sj\",\"valuecode\":\""+dateStr+"\"}]");
            map.put("time",String.valueOf(System.currentTimeMillis()));
            String data = restTemplate.getForObject(nationalStatisticsUrl,String.class,map);

            Long createTime = Long.parseLong(DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now()));

            Map<String, Object> resultMap = GsonUtil.jsonToMap(data);
            Double returncode = (Double)resultMap.get("returncode");
            if(returncode == 200) {
                Map<String,String> codeNameMap = new HashMap<>();
                Map<String,String> codeUnitMap = new HashMap<>();
                Map<String,String> codeDataMap = new HashMap<>();
                Map<String,String> codeDateMap = new HashMap<>();
                Map<String,String> codeTypeMap = new HashMap<>();

                Map<String, Object> returndataMap = (Map<String, Object>)resultMap.get("returndata");
                List<Map<String, Object>> datanodesMapList = (List<Map<String, Object>>)returndataMap.get("datanodes");

                List<Map<String, Object>> wdnodesMapList = (List<Map<String, Object>>)returndataMap.get("wdnodes");
                if(wdnodesMapList != null && wdnodesMapList.size()>0){
                    for (Map<String, Object> wdnodeMap : wdnodesMapList) {
                        String wdcode = (String)wdnodeMap.get("wdcode");
                        if("zb".equals(wdcode)){
                            List<Map<String, Object>> nodeList = (List<Map<String, Object>>)wdnodeMap.get("nodes");
                            if(nodeList != null && nodeList.size()>0){
                                for (Map<String, Object> nodeMap : nodeList) {
                                    codeNameMap.put(nodeMap.get("code").toString(),nodeMap.get("name").toString());
                                    codeUnitMap.put(nodeMap.get("code").toString(),nodeMap.get("unit").toString());
                                }
                            }
                        }
                    }
                }

                if(datanodesMapList != null && datanodesMapList.size()>0){
                    for (Map<String, Object> datanodeMap : datanodesMapList) {
                        String code = (String)datanodeMap.get("code");

                        Map<String,Object> dataMap = (Map<String, Object>) datanodeMap.get("data");
                        boolean hasData = (boolean)dataMap.get("hasdata");
                        if(hasData){
                            codeDataMap.put(code,dataMap.get("data").toString());
                            List<Map<String,Object>> wdsMapList = (List<Map<String,Object>>) datanodeMap.get("wds");
                            if(wdsMapList != null && wdsMapList.size()>0){
                                for (Map<String, Object> wdsMap : wdsMapList) {
                                    if("zb".equals(wdsMap.get("wdcode"))){
                                        codeTypeMap.put(code,wdsMap.get("valuecode").toString());
                                    }else if("sj".equals(wdsMap.get("wdcode"))){
                                        codeDateMap.put(code,wdsMap.get("valuecode").toString());
                                    }
                                }
                            }
                        }
                    }
                }

                log.info("codeNameMap:{}",codeNameMap);
                log.info("codeDataMap:{}",codeDataMap);
                log.info("codeDateMap:{}",codeDateMap);
                log.info("codeTypeMap:{}",codeTypeMap);
                log.info("codeUnitMap:{}",codeUnitMap);

                if (TotalRetailSalesIndexTypeEnum.TOTAL_RETAIL_SALES.type().equals(type)) {
                    log.info("处理零售总额数据入库");
                    for (String codeStr : codeDataMap.keySet()) {
                        TotalRetailSalesIndexEntity totalRetailSalesIndexEntity = new TotalRetailSalesIndexEntity();
                        totalRetailSalesIndexEntity.setCode(codeStr);
                        totalRetailSalesIndexEntity.setIndexName(codeNameMap.get(codeTypeMap.get(codeStr))+"("+codeUnitMap.get(codeTypeMap.get(codeStr))+")");
                        totalRetailSalesIndexEntity.setIndexValue(codeDataMap.get(codeStr));
                        String dateData = codeDateMap.get(codeStr);
                        dataDate = dateData.substring(0,4)+"-"+dateData.substring(4,6);
                        totalRetailSalesIndexEntity.setDataDate(dataDate);
                        totalRetailSalesIndexEntity.setPageUrl(nationalStatisticsPageUrl.replace("{type}", type).replace("{time}", dateStr));
                        totalRetailSalesIndexEntity.setCreateTime(createTime);

                        QueryWrapper queryWrapper = new QueryWrapper();
                        queryWrapper.eq("code",codeStr);
                        TotalRetailSalesIndexEntity oriTotalRetailSalesIndexEntity = totalRetailSalesIndexDao.selectOne(queryWrapper);
                        if(oriTotalRetailSalesIndexEntity == null){
                            totalRetailSalesIndexDao.insert(totalRetailSalesIndexEntity);
                        }
                    }
                } else if (TotalRetailSalesIndexTypeEnum.TOTAL_RETAIL_SALES_CAR.type().equals(type)) {
                    log.info("处理零售总额-汽车类数据入库");
                    for (String codeStr : codeDataMap.keySet()) {
                        TotalRetailSalesCarIndexEntity totalRetailSalesCarIndexEntity = new TotalRetailSalesCarIndexEntity();
                        totalRetailSalesCarIndexEntity.setCode(codeStr);
                        totalRetailSalesCarIndexEntity.setIndexName(codeNameMap.get(codeTypeMap.get(codeStr))+"("+codeUnitMap.get(codeTypeMap.get(codeStr))+")");
                        totalRetailSalesCarIndexEntity.setIndexValue(codeDataMap.get(codeStr));
                        String dateData = codeDateMap.get(codeStr);
                        dataDate = dateData.substring(0,4)+"-"+dateData.substring(4,6);
                        totalRetailSalesCarIndexEntity.setDataDate(dataDate);
                        totalRetailSalesCarIndexEntity.setPageUrl(nationalStatisticsPageUrl.replace("{type}", type).replace("{time}", dateStr));
                        totalRetailSalesCarIndexEntity.setCreateTime(createTime);

                        QueryWrapper queryWrapper = new QueryWrapper();
                        queryWrapper.eq("code",codeStr);
                        TotalRetailSalesCarIndexEntity oriTotalRetailSalesCarIndexEntity = totalRetailSalesCarIndexDao.selectOne(queryWrapper);
                        if(oriTotalRetailSalesCarIndexEntity == null){
                            totalRetailSalesCarIndexDao.insert(totalRetailSalesCarIndexEntity);
                        }


                    }
                } else if (TotalRetailSalesIndexTypeEnum.TOTAL_RETAIL_SALES_CONSUMPTION.type().equals(type)) {
                    log.info("处理零售总额-消费分类数据入库");
                    for (String codeStr : codeDataMap.keySet()) {
                        TotalRetailSalesConsumptionIndexEntity totalRetailSalesConsumptionIndexEntity = new TotalRetailSalesConsumptionIndexEntity();
                        totalRetailSalesConsumptionIndexEntity.setCode(codeStr);
                        totalRetailSalesConsumptionIndexEntity.setIndexName(codeNameMap.get(codeTypeMap.get(codeStr))+"("+codeUnitMap.get(codeTypeMap.get(codeStr))+")");
                        totalRetailSalesConsumptionIndexEntity.setIndexValue(codeDataMap.get(codeStr));
                        String dateData = codeDateMap.get(codeStr);
                        dataDate = dateData.substring(0,4)+"-"+dateData.substring(4,6);
                        totalRetailSalesConsumptionIndexEntity.setDataDate(dataDate);
                        totalRetailSalesConsumptionIndexEntity.setPageUrl(nationalStatisticsPageUrl.replace("{type}", type).replace("{time}", dateStr));
                        totalRetailSalesConsumptionIndexEntity.setCreateTime(createTime);

                        QueryWrapper queryWrapper = new QueryWrapper();
                        queryWrapper.eq("code",codeStr);
                        TotalRetailSalesConsumptionIndexEntity oriTotalRetailSalesConsumptionIndexEntity = totalRetailSalesConsumptionIndexDao.selectOne(queryWrapper);
                        if(oriTotalRetailSalesConsumptionIndexEntity == null){
                            totalRetailSalesConsumptionIndexDao.insert(totalRetailSalesConsumptionIndexEntity);
                        }
                    }
                } else if (TotalRetailSalesIndexTypeEnum.FIXED_ASSETS_INVESTMENT_GROWTH_INDUSTRY.type().equals(type)){
                    log.info("处理固定资产投资增速-按行业分类数据入库");
                    for (String codeStr : codeDataMap.keySet()) {
                        FixedAssetsInvestmentGrowthIndustryIndexEntity fixedAssetsInvestmentGrowthIndustryIndexEntity = new FixedAssetsInvestmentGrowthIndustryIndexEntity();
                        fixedAssetsInvestmentGrowthIndustryIndexEntity.setCode(codeStr);
                        fixedAssetsInvestmentGrowthIndustryIndexEntity.setIndexName(codeNameMap.get(codeTypeMap.get(codeStr))+"("+codeUnitMap.get(codeTypeMap.get(codeStr))+")");
                        fixedAssetsInvestmentGrowthIndustryIndexEntity.setIndexValue(codeDataMap.get(codeStr));
                        String dateData = codeDateMap.get(codeStr);
                        dataDate = dateData.substring(0,4)+"-"+dateData.substring(4,6);
                        fixedAssetsInvestmentGrowthIndustryIndexEntity.setDataDate(dataDate);
                        fixedAssetsInvestmentGrowthIndustryIndexEntity.setPageUrl(nationalStatisticsPageUrl.replace("{type}", type).replace("{time}", dateStr));
                        fixedAssetsInvestmentGrowthIndustryIndexEntity.setCreateTime(createTime);

                        QueryWrapper queryWrapper = new QueryWrapper();
                        queryWrapper.eq("code",codeStr);
                        FixedAssetsInvestmentGrowthIndustryIndexEntity oriTotalRetailSalesConsumptionIndexEntity = fixedAssetsInvestmentGrowthIndustryIndexDao.selectOne(queryWrapper);
                        if(oriTotalRetailSalesConsumptionIndexEntity == null){
                            fixedAssetsInvestmentGrowthIndustryIndexDao.insert(fixedAssetsInvestmentGrowthIndustryIndexEntity);
                        }
                    }
                } else {
                    return Result.error("传入类型错误");
                }
                log.info("处理国家统计数据-类型：{}，日期：{}结束",type,date);
                return Result.ok();
            }else{
                return Result.error((String)resultMap.get("returndata"));
            }
        }catch(Exception e){
            log.error("处理国家统计数据失败",e);
            return Result.error("处理国家统计数据失败");
        }
    }
}
