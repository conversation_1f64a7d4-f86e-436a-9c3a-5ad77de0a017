package com.gwmfc;

import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.gwmfc.dao.GrossDomesticProductDao;
import com.gwmfc.dao.MonthConsumptionIndexDao;
import com.gwmfc.dao.YearConsumptionIndexDao;
import com.gwmfc.entity.data.GrossDomesticProductEntity;
import com.gwmfc.entity.data.MonthConsumptionIndexEntity;
import com.gwmfc.entity.data.YearConsumptionIndexEntity;
import com.gwmfc.service.DingDingService;
import com.gwmfc.service.GlobalFormBusinessService;
import com.gwmfc.util.GsonUtil;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2023年08月16日 13:24
 */
@SpringBootTest
public class CurlTest {
    @Value("${dingding-notify-users}")
    private String dingdingNotifyUsers;
    @Resource
    private DingDingService dingDingService;
    @Resource
    private GlobalFormBusinessService globalFormBusinessService;
    @Resource
    private MonthConsumptionIndexDao monthConsumptionIndexDao;
    @Resource
    private YearConsumptionIndexDao yearConsumptionIndexDao;
    @Resource
    private GrossDomesticProductDao grossDomesticProductDao;

    @Test
    public void curl() throws IOException {
        String[] command = {"curl", "-H", "User-Agent: curl/7.55.1", "-H", "Accept: */*", "http://10.16.16.224:8892/cpcatable_getdata?url=http://cpcaauto.com/admin/ewebeditor/uploadfile/20230609160624491.jpg"};

        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);

        Process process = processBuilder.start();

        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line;
        while ((line = reader.readLine()) != null) {
            System.out.println(line);
        }
    }

    /**
     * 发送 get请求
     */
//    @Test
//    public void get() {
//        List<String> userList = Arrays.asList(dingdingNotifyUsers.split(","));
//        LocalDateTime now = LocalDateTime.now();
//        userList.forEach(userId -> {
//            DingNotification dingNotification = DingNotification.builder()
//                    .content("sa")
//                    .applyTime(DateUtil.formatDateTime(now)).userId(userId).status(NOTIFICATION_PROCESSING)
//                    .accessToken(dingDingService.acquireAccountAccessToken()).build();
//            dingDingService.pushNotification(dingNotification,APPLY_TYPE_EXTRACTION);
//        });
//    }

    public static final String LIST_D = ".list_d";
    public static final String LI = "li";
    public static final String SPAN = "span";
    public static final String A = "a";
    public static final String HREF = "href";
    public static final String SRC = "src";
    public static final String READ_CONTENT = ".read_content";
    public static final String IMG = "img";

    @Resource
    private RestTemplate restTemplate;
    @Test
    public void logisticsIndexDownload() throws IOException {
        //http://www.clic.org.cn/search_3.jspx?q=%E7%94%B5%E5%95%86%E7%89%A9%E6%B5%81%E6%8C%87%E6%95%B0
        String url = "http://www.clic.org.cn/search.jspx?q=电商物流指数为";

        Document pageNumDocument = Jsoup.connect(url).get();
        String pageNumStr = pageNumDocument.select(".fy").select("span").get(0).text();
        Integer pageNum = Integer.parseInt(pageNumStr.substring(pageNumStr.indexOf("/")+1,pageNumStr.lastIndexOf("页")));

        Map resMap = new HashMap<>();
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (int i = 1; i <= pageNum; i++) {
            System.out.println(i);
            String selUrl = "http://www.clic.org.cn/search_" + i + ".jspx?q=电商物流指数为";
            Document documentListPage = Jsoup.connect(selUrl).get();
            Elements links = documentListPage.select(".lbbox").select("ul").select("li");
            for (Element link : links) {
//            if (link.select(SPAN).text().contains(year.concat("-").concat(month))) {
                String textNode = link.select("p").text();
//                System.out.println(textNode);
                if (textNode.indexOf("电商物流指数为") == -1 || textNode.indexOf("点") == -1 ) {
                    continue;
                }
                try {
                    String date = textNode.substring(textNode.indexOf("发布日期： ")+6);
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                    SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM");
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(format.parse(date));
                    calendar.add(Calendar.MONTH, -1);
                    String value = textNode.substring(textNode.indexOf("电商物流指数为")+"电商物流指数为".length(), textNode.indexOf("点")).replace("中国","");
//                    System.out.println(yearMonth + "\t" + value);
                    Date date1 = calendar.getTime();

                    resMap.put(format1.format(date1), value);
                } catch (Exception e) {
                    System.out.println(textNode);
                }
//            }
            }
        }
        resMap.forEach((k,v) -> {
            Map map = new HashMap<>();
            map.put("data_date", k);
            map.put("commerce_logistics_index_val", v);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            map.put("create_time", Long.parseLong(dateTimeFormatter.format(LocalDateTime.now())));
            dataList.add(map);
        });


        Set<String> columnSet = new HashSet<>();
        columnSet.add("data_date");
        columnSet.add("commerce_logistics_index_val");
        columnSet.add("create_time");

//        globalFormBusinessService.batchAddData("commerce_logistics_index", columnSet, dataList);
        System.out.println();
    }

    @Test
    public void logisticsIndexDownload2() throws IOException {
        //http://www.clic.org.cn/search_3.jspx?q=%E7%94%B5%E5%95%86%E7%89%A9%E6%B5%81%E6%8C%87%E6%95%B0
        String url = "http://www.clic.org.cn/search.jspx?q=物流业景气指数为";

        Document pageNumDocument = Jsoup.connect(url).get();
        String pageNumStr = pageNumDocument.select(".fy").select("span").get(0).text();
        Integer pageNum = Integer.parseInt(pageNumStr.substring(pageNumStr.indexOf("/")+1,pageNumStr.lastIndexOf("页")));

        Map resMap = new HashMap<>();
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (int i = 1; i <= pageNum; i++) {
            System.out.println(i);
            String selUrl = "http://www.clic.org.cn/search_" + i + ".jspx?q=物流业景气指数为";
            Document documentListPage = Jsoup.connect(selUrl).get();
            Elements links = documentListPage.select(".lbbox").select("ul").select("li");
            for (Element link : links) {
//            if (link.select(SPAN).text().contains(year.concat("-").concat(month))) {
                String textNode = link.select("p").text();
                if (textNode.indexOf("物流业景气指数为") == -1 || textNode.indexOf("%") == -1 || !(textNode.contains("年")&&textNode.contains("月"))) {
                    continue;
                }
//                System.out.println(textNode);
                try {
                    String date = textNode.substring(textNode.indexOf("发布日期： ")+6);
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                    SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM");
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(format.parse(date));
                    calendar.add(Calendar.MONTH, -1);
                    String value = textNode.substring(textNode.indexOf("中国物流业景气指数为")+"中国物流业景气指数为".length(), textNode.indexOf("%")).replace("中国","");
//                    System.out.println(yearMonth + "\t" + value);
                    Date date1 = calendar.getTime();

                    resMap.put(format1.format(date1), value);
                } catch (Exception e) {
                    System.out.println(textNode);
                }
//            }
            }
        }

        resMap.forEach((k,v) -> {
            Map map = new HashMap<>();
            map.put("data_date", k);
            map.put("logistics_industry_prosperity_index_val", v);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            map.put("create_time", Long.parseLong(dateTimeFormatter.format(LocalDateTime.now())));
            dataList.add(map);
        });

        Set<String> columnSet = new HashSet<>();
        columnSet.add("data_date");
        columnSet.add("logistics_industry_prosperity_index_val");
        columnSet.add("create_time");

        globalFormBusinessService.batchAddData("logistics_industry_prosperity_index", columnSet, dataList);
        System.out.println();
    }

    @Test
    public void logisticsIndexDownload3() throws IOException {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        //http://www.clic.org.cn/search_3.jspx?q=%E7%94%B5%E5%95%86%E7%89%A9%E6%B5%81%E6%8C%87%E6%95%B0
        // 中国仓储指数显示
        String url = "http://www.clic.org.cn/search.jspx?q=中国仓储指数为";

        Document pageNumDocument = Jsoup.connect(url).get();
        String pageNumStr = pageNumDocument.select(".fy").select("span").get(0).text();
        Integer pageNum = Integer.parseInt(pageNumStr.substring(pageNumStr.indexOf("/")+1,pageNumStr.lastIndexOf("页")));

        Map resMap = new HashMap<>();
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (int i = 1; i <= pageNum; i++) {
            String selUrl = "http://www.clic.org.cn/search_" + i + ".jspx?q=中国仓储指数为";
            Document documentListPage = Jsoup.connect(selUrl).get();
            Elements links = documentListPage.select(".lbbox").select("ul").select("li");
            for (Element link : links) {
//            if (link.select(SPAN).text().contains(year.concat("-").concat(month))) {
                String textNode = link.select("p").text();
                System.out.println(textNode);
                if (textNode.indexOf("中国仓储指数为") == -1 || textNode.indexOf("%") == -1) {
                    continue;
                }
                try {
                    String date = textNode.substring(textNode.indexOf("发布日期： ")+6);
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                    SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM");
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(format.parse(date));
                    calendar.add(Calendar.MONTH, -1);
                    String value = textNode.substring(textNode.indexOf("中国仓储指数为")+"中国仓储指数为".length(), textNode.indexOf("%")).replace("中国","");
                    Date date1 = calendar.getTime();

                    resMap.put(format1.format(date1), value);
                } catch (Exception e) {
                    System.out.println(textNode);
                }
            }
        }

        resMap.forEach((k,v) -> {
            Map map = new HashMap<>();
            map.put("data_date", k);
            map.put("storage_index_val", v);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            map.put("create_time", Long.parseLong(dateTimeFormatter.format(LocalDateTime.now())));
            dataList.add(map);
        });

        Set<String> columnSet = new HashSet<>();
        columnSet.add("data_date");
        columnSet.add("storage_index_val");
        columnSet.add("create_time");

        globalFormBusinessService.batchAddData("storage_index", columnSet, dataList);
        long endTime = System.currentTimeMillis(); // 记录结束时间
        long elapsedTime = endTime - startTime; // 计算运行时间
        System.out.println("程序运行时间：" + elapsedTime + "毫秒");
    }


    @Test
    public void monthDownload() {
        Map<String, String> map = new HashMap<>();
        // 添加键值对
//        map.put("setSameMonthLastYearConsumerPriceSubIndex", "A010101");
        map.put("setSamePeriodLastYearConsumerPriceSubIndex", "A010201");
        map.put("setLastMonthConsumerPriceSubIndex", "A010301");
        map.put("setSameMonthLastYearProducerPriceIndex", "A010801");
        map.put("setSamePeriodLastYearProducerPriceIndex", "A010804");
        map.put("setLastMonthProducerPriceIndex", "A010807");

        JsonArray jsonArray0 = new JsonArray();
        JsonObject jsonObject0 = new JsonObject();
        jsonObject0.addProperty("wdcode","zb");
        jsonObject0.addProperty("valuecode","A010101");
        jsonArray0.add(jsonObject0);
        String json0 = jsonArray0.toString();

        List<HttpMessageConverter<?>> messageConverters0 = restTemplate.getMessageConverters();
        for (int i = 0; i < messageConverters0.size(); i++) {
            HttpMessageConverter<?> httpMessageConverter = messageConverters0.get(i);
            if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
                messageConverters0.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
            }
        }
        ResponseEntity<String> strbody0=restTemplate.getForEntity("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds={dfwds}&k1=1710997576768&h=1",String.class,json0);
        ABo aBo0 = GsonUtil.gsonToBean(strbody0.getBody(),ABo.class);
        List<Datanode> datanodeList0 = aBo0.getReturndata().getDatanodes();
        Integer monthNum = aBo0.getReturndata().getWdnodes().get(1).getNodes().size();
        ArrayList arrayList1 = new ArrayList<MonthConsumptionIndexEntity>(monthNum);

        for(int i=0; i<monthNum; i++) {

            MonthConsumptionIndexEntity monthConsumptionIndexEntity = new MonthConsumptionIndexEntity();
            // 获取Person类的Class对象
            Class<?> clazz = monthConsumptionIndexEntity.getClass();
            Datanode datanode = datanodeList0.get(i);
            //判断该月在不在
            if (monthConsumptionIndexDao.selectDataDateExist(datanode.getWds().get(1).getValuecode()) != null) {
                break;
            }
            // 获取Person类的setName方法
            Method setNameMethod = null;
            Method setNameMethod1 = null;
            try {
                setNameMethod = clazz.getMethod("setSameMonthLastYearConsumerPriceSubIndex", String.class);
                // 调用setName方法设置name属性的值
                setNameMethod.invoke(monthConsumptionIndexEntity, datanode.getData().getData());

                setNameMethod1 = clazz.getMethod("setDataDate", String.class);
                // 调用setName方法设置name属性的值
                setNameMethod1.invoke(monthConsumptionIndexEntity, datanode.getWds().get(1).getValuecode());
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            arrayList1.add(monthConsumptionIndexEntity);
        }

        map.forEach((k,v) -> {

            JsonArray jsonArray = new JsonArray();
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("wdcode","zb");
            jsonObject.addProperty("valuecode",v);
            jsonArray.add(jsonObject);
            String json = jsonArray.toString();

            List<HttpMessageConverter<?>> messageConverters = restTemplate.getMessageConverters();
            for (int i = 0; i < messageConverters.size(); i++) {
                HttpMessageConverter<?> httpMessageConverter = messageConverters.get(i);
                if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
                    messageConverters.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
                }
            }
            ResponseEntity<String> strbody=restTemplate.getForEntity("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds={dfwds}&k1=1708571635859&h=1",String.class,json);
            ABo aBo = GsonUtil.gsonToBean(strbody.getBody(),ABo.class);
            List<Datanode> datanodeList = aBo.getReturndata().getDatanodes();

            for (int i = 0; i< arrayList1.size(); i++) {
                MonthConsumptionIndexEntity monthConsumptionIndexEntity = (MonthConsumptionIndexEntity) arrayList1.get(i);
                // 获取Person类的Class对象
                Class<?> clazz = monthConsumptionIndexEntity.getClass();
                Datanode datanode = datanodeList.get(i);
                // 获取Person类的setName方法
                Method setNameMethod = null;
                try {
                    if (datanode.getWds().get(1).getValuecode().equals(monthConsumptionIndexEntity.getDataDate())) {
                        setNameMethod = clazz.getMethod(k, String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod.invoke(monthConsumptionIndexEntity, datanode.getData().getData());
                    }
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }

        });
        System.out.println();
        arrayList1.forEach(monthConsumptionIndex -> {
            monthConsumptionIndexDao.insert((MonthConsumptionIndexEntity) monthConsumptionIndex);
        });
    }

    @Test
    public void yearDownload() {
        JsonArray jsonArray0 = new JsonArray();
        JsonObject jsonObject0 = new JsonObject();
        jsonObject0.addProperty("wdcode","zb");
        jsonObject0.addProperty("valuecode","A0901");
        jsonArray0.add(jsonObject0);
        String json0 = jsonArray0.toString();

        // 添加键值对
        List<String> property = new ArrayList<>();
        property.add("setConsumerPriceSubIndex");
        property.add("setUrbanConsumerPriceIndex");
        property.add("setRuralConsumerPriceIndex");
        property.add("setRetailPriceIndex");
        property.add("setProducerPriceIndex");
        property.add("setPurchasingPriceIndustrialProducerIndex");
        property.add("setFixedAssetInvestmentPriceIndex");

        List<HttpMessageConverter<?>> messageConverters0 = restTemplate.getMessageConverters();

        for (int i = 0; i < messageConverters0.size(); i++) {
            HttpMessageConverter<?> httpMessageConverter = messageConverters0.get(i);
            if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
                messageConverters0.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
            }
        }
        ResponseEntity<String> strbody0=restTemplate.getForEntity("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgnd&rowcode=sj&colcode=zb&wds=[]&dfwds={dfwds}&k1=1709544523690&h=1",String.class,json0);
        ABo aBo0 = GsonUtil.gsonToBean(strbody0.getBody(),ABo.class);
        List<Datanode> datanodeList0 = aBo0.getReturndata().getDatanodes();
        Integer monthNum = aBo0.getReturndata().getWdnodes().get(1).getNodes().size();
        ArrayList resArrayList = new ArrayList<YearConsumptionIndexEntity>(monthNum);
        for (int i=0;i<monthNum;i++) {
            resArrayList.add(new YearConsumptionIndexEntity());
        }

        // 集合分片
        List<List<Datanode>> newList = Lists.partition(datanodeList0, monthNum);
        // 打印分片集合
        AtomicInteger j = new AtomicInteger();
        newList.forEach(objectList -> {
            System.out.println("集合长度：" + objectList.size());
            for (int i=0;i<objectList.size();i++) {
                YearConsumptionIndexEntity monthConsumptionIndexEntity = (YearConsumptionIndexEntity) resArrayList.get(i);
                // 获取Person类的Class对象
                Class<?> clazz = monthConsumptionIndexEntity.getClass();
                Method setNameMethod = null;
                Method setNameMethod1 = null;
                try {
                    setNameMethod = clazz.getMethod(property.get(j.get()), String.class);
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
                Datanode datanode = objectList.get(i);

                try {
                    if (monthConsumptionIndexEntity.getDataDate() != null) {
                        if (datanode.getWds().get(1).getValuecode().equals(monthConsumptionIndexEntity.getDataDate())) {
                            // 调用setName方法设置name属性的值
                            setNameMethod.invoke(monthConsumptionIndexEntity, datanode.getData().getData());
                        }
                    }
                    if (monthConsumptionIndexEntity.getDataDate() == null) {
                        setNameMethod1 = clazz.getMethod("setDataDate", String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod1.invoke(monthConsumptionIndexEntity, datanode.getWds().get(1).getValuecode());
                        setNameMethod = clazz.getMethod(property.get(j.get()), String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod.invoke(monthConsumptionIndexEntity, datanode.getData().getData());
                    }
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }

            }
            j.getAndIncrement();
        });
        System.out.println();
        resArrayList.forEach(monthConsumptionIndex -> {
            yearConsumptionIndexDao.insert((YearConsumptionIndexEntity) monthConsumptionIndex);
        });
    }

    @Test
    public void gdpDownload() {
        JsonArray jsonArray0 = new JsonArray();
        JsonObject jsonObject0 = new JsonObject();
        jsonObject0.addProperty("wdcode","zb");
        jsonObject0.addProperty("valuecode","A0101");
        JsonObject jsonObject1 = new JsonObject();
        jsonObject1.addProperty("wdcode","sj");
        jsonObject1.addProperty("valuecode","LAST5");
        jsonArray0.add(jsonObject0);
        jsonArray0.add(jsonObject1);
        String json0 = jsonArray0.toString();

        // 添加键值对
        List<String> property = new ArrayList<>();

        property.add("setQuarterlyGrossDomesticProduct");
        property.add("setAccumulatedValueOfGrossDomesticProduct");
        property.add("setCurrentQuarterlyValueOfAddedValueOfPrimaryIndustry");
        property.add("setAccumulatedValueOfAddedValueOfPrimaryIndustry");
        property.add("setQuarterlyValueOfAddedValueOfSecondaryIndustry");
        property.add("setAccumulatedValueOfAddedValueOfSecondaryIndustry");

        property.add("setQuarterlyValueOfAddedValueOfTertiaryIndustry");
        property.add("setAccumulatedValueOfAddedValueOfTertiaryIndustry");

        property.add("setQuarterlyValueOfAddedValueOfAgriculture");
        property.add("setAccumulatedValueOfAddedValueOfAgriculture");

        property.add("setQuarterlyValueOfIndustrialAddedValue");
        property.add("setAccumulatedValueOfIndustrialAddedValue");

        property.add("setQuarterlyValueOfManufacturingAddedValue");
        property.add("setAccumulatedValueOfManufacturingAddedValue");

        property.add("setQuarterlyValueOfConstructionAddedValue");
        property.add("setAccumulatedValueOfConstructionAddedValue");

        property.add("setWholesaleRetailAddedValueForQuarter");
        property.add("setCumulativeValueAddedInWholesaleRetailTrade");

        property.add("setQuarterlyValueAddedOfTransportationStoragePostal");
        property.add("setAccumulatedValueAddedOfTransportationStoragePostal");

        property.add("setQuarterlyValueOfHotelAndCateringIndustryAddedValue");
        property.add("setAccumulatedValueOfHotelAndCateringIndustryAddedValue");

        property.add("setQuarterlyValueOfFinancialIndustryAddedValue");
        property.add("setAccumulatedValueOfFinancialIndustryAddedValue");

        property.add("setQuarterlyValueOfRealEstateAddedValue");
        property.add("setAccumulatedValueOfRealEstateAddedValue");

        property.add("setQuarterlyValueOfInformationTechnologyAddedValue");
        property.add("setAccumulatedValueOfInformationTechnologyAddedValue");

        property.add("setQuarterlyValueOfLeasingAndBusinessServiceAddedValue");
        property.add("setAccumulatedValueOfLeasingAndBusinessServiceAddedValue");

        property.add("setQuarterlyValueOfOtherIndustryAddedValue");
        property.add("setAccumulatedValueOfOtherIndustryAddedValue");

        List<HttpMessageConverter<?>> messageConverters0 = restTemplate.getMessageConverters();

        for (int i = 0; i < messageConverters0.size(); i++) {
            HttpMessageConverter<?> httpMessageConverter = messageConverters0.get(i);
            if (httpMessageConverter.getClass().equals(StringHttpMessageConverter.class)) {
                messageConverters0.set(i, new StringHttpMessageConverter(StandardCharsets.UTF_8));
            }
        }
        ResponseEntity<String> strbody0=restTemplate.getForEntity("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgjd&rowcode=zb&colcode=sj&wds=[]&dfwds={dfwds}&k1=1709690024653&h=1",String.class,json0);
        ABo aBo0 = GsonUtil.gsonToBean(strbody0.getBody(),ABo.class);
        List<Datanode> datanodeList0 = aBo0.getReturndata().getDatanodes();
        Integer monthNum = aBo0.getReturndata().getWdnodes().get(1).getNodes().size();
        ArrayList resArrayList = new ArrayList<GrossDomesticProductEntity>(monthNum);
        for (int i=0;i<monthNum;i++) {
            resArrayList.add(new GrossDomesticProductEntity());
        }

        // 集合分片
        List<List<Datanode>> newList = Lists.partition(datanodeList0, monthNum);
        // 打印分片集合
        AtomicInteger j = new AtomicInteger();
        newList.forEach(objectList -> {
            System.out.println("集合长度：" + objectList.size());
            for (int i=0;i<objectList.size();i++) {
                GrossDomesticProductEntity grossDomesticProductEntity = (GrossDomesticProductEntity) resArrayList.get(i);
                // 获取Person类的Class对象
                Class<?> clazz = grossDomesticProductEntity.getClass();
                Method setNameMethod = null;
                Method setNameMethod1 = null;
                try {
                    setNameMethod = clazz.getMethod(property.get(j.get()), String.class);
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                }
                Datanode datanode = objectList.get(i);

                try {
                    if (grossDomesticProductEntity.getDataDate() != null) {
                        if (datanode.getWds().get(1).getValuecode().equals(grossDomesticProductEntity.getDataDate())) {
                            // 调用setName方法设置name属性的值
                            setNameMethod.invoke(grossDomesticProductEntity, datanode.getData().getData());
                        }
                    }
                    if (grossDomesticProductEntity.getDataDate() == null) {
                        setNameMethod1 = clazz.getMethod("setDataDate", String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod1.invoke(grossDomesticProductEntity, datanode.getWds().get(1).getValuecode());
                        setNameMethod = clazz.getMethod(property.get(j.get()), String.class);
                        // 调用setName方法设置name属性的值
                        setNameMethod.invoke(grossDomesticProductEntity, datanode.getData().getData());
                    }
                } catch (NoSuchMethodException e) {
                    e.printStackTrace();
                } catch (InvocationTargetException e) {
                    e.printStackTrace();
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }

            }
            j.getAndIncrement();
        });
        resArrayList.forEach(grossDomesticProductEntity -> {
            grossDomesticProductDao.insert((GrossDomesticProductEntity) grossDomesticProductEntity);
        });
    }

    @Test
    public void a() throws UnsupportedEncodingException {
        String a = URLDecoder.decode("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds=[{\"wdcode\":\"zb\",\"valuecode\":\"A010101\"}]&k1=1708571635859&h=1","UTF-8");
        String a1 = URLDecoder.decode("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds=[{\"wdcode\":\"zb\",\"valuecode\":\"A010201\"}]&k1=1708571712667&h=1","UTF-8");
        String a2 = URLDecoder.decode("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds=[{\"wdcode\":\"zb\",\"valuecode\":\"A010301\"}]&k1=1708571828347&h=1","UTF-8");
        String a3 = URLDecoder.decode("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds=[{\"wdcode\":\"zb\",\"valuecode\":\"A010801\"}]&k1=1708571972500&h=1","UTF-8");
        String a4 = URLDecoder.decode("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds=[{\"wdcode\":\"zb\",\"valuecode\":\"A010804\"}]&k1=1708572076332&h=1","UTF-8");
        String a5 = URLDecoder.decode("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds=[{\"wdcode\":\"zb\",\"valuecode\":\"A010807\"}]&k1=1708572129823&h=1","UTF-8");
        String b = URLDecoder.decode("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgnd&rowcode=zb&colcode=sj&wds=[]&dfwds=[{\"wdcode\":\"zb\",\"valuecode\":\"A0901\"}]&k1=1708573682406&h=1","UTF-8");
        System.out.println(a);
        System.out.println(a1);
        System.out.println(a5);
        //https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds=[{"wdcode":"zb","valuecode":"A010807"}]&k1=1708572129823&h=1

    }

    @Test
    public void b() throws UnsupportedEncodingException {
        String a = URLDecoder.decode("https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgnd&rowcode=sj&colcode=zb&wds=%5B%5D&dfwds=%5B%5D&k1=1709170904188","UTF-8");
        String b = URLDecoder.decode("\n" +
                "https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgnd&rowcode=zb&colcode=sj&wds=%5B%5D&dfwds=%5B%7B%22wdcode%22%3A%22zb%22%2C%22valuecode%22%3A%22A0201%22%7D%5D&k1=1709170892660&h=1","UTF-8");

        System.out.println(a);
        System.out.println(b);
        //https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds=[{"wdcode":"zb","valuecode":"A010807"}]&k1=1708572129823&h=1

    }

    @Test
    public void year() throws UnsupportedEncodingException {
        String a = URLDecoder.decode("\n" +
                "\n" +
                "https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=%5B%5D&dfwds=%5B%7B%22wdcode%22%3A%22sj%22%2C%22valuecode%22%3A%22LAST24%22%7D%5D&k1=1710997576768","UTF-8");

        System.out.println(a);
        //https://data.stats.gov.cn/easyquery.htm?m=QueryData&dbcode=hgyd&rowcode=zb&colcode=sj&wds=[]&dfwds=[{"wdcode":"zb","valuecode":"A010807"}]&k1=1708572129823&h=1

    }
}
