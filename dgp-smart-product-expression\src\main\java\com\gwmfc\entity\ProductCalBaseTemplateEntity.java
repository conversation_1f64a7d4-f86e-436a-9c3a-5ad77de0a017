package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductCalBaseTemplateEntity
 * @Description product_cal_base_template
 * @Date 2023/9/19 17:05
 */
@Data
@TableName("product_cal_base_template")
public class ProductCalBaseTemplateEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "buss_type", comment = "业务类型")
    private Integer bussType;

    @TableFieldMapping(value = "repayment_method", comment = "还款方式")
    private Integer repaymentMethod;

    @TableFieldMapping(value = "item", comment = "条目")
    private String item;

    @TableFieldMapping(value = "item_desc", comment = "描述")
    private String itemDesc;

    @TableFieldMapping(value = "must", comment = "是否必须")
    private String must;

    @TableFieldMapping(value = "data_type", comment = "数据类型")
    private String dataType;

    @TableFieldMapping(value = "pam_type", comment = "参数类型")
    private String pamType;

    @TableFieldMapping(value = "formula", comment = "公式")
    private String formula;

    @TableFieldMapping(value = "scale_value", comment = "小数保留位数")
    private Integer scaleValue;

    @TableFieldMapping(value = "create_time", comment = "创建日期")
    private Date createTime;

    @TableFieldMapping(value = "create_user", comment = "创建人")
    private String createUser;

    @TableFieldMapping(value = "update_time", comment = "修改日期")
    private Date updateTime;

    @TableFieldMapping(value = "update_user", comment = "修改人")
    private String updateUser;

}
