package com.gwmfc.dto;

import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalEarlySquareParamSubDto
 * @Description TODO
 * @Date 2024/7/2 17:24
 */
@Data
@ApiModel(value = "提前结清参数附加")
public class ProductCalEarlySquareParamSubDto {
    @ApiModelProperty("是否考虑提前结清")
    private Boolean isEarlySquareFlag;

    @ApiModelProperty("默认产品分类")
    private String productClassification;

    @ApiModelProperty("默认融资类型")
    private String financingType;

    @ApiModelProperty("提前结清概率分类")
    private String earlySquareProbabilityClassification;

    @ApiModelProperty("代理商类型")
    private String agentType;

    @ApiModelProperty("赛道")
    private String track;

    @ApiModelProperty("是否总对总")
    private Integer isCorpToCorp;

    @ApiModelProperty("提前结清手续费和扣返比例计算参数")
    private ProductCalEarlySquareHandChargeAndCommissionRebatePamDto productCalEarlySquareHandChargeAndCommissionRebatePamDto;

    @ApiModelProperty("提前结清概率计算参数")
    private ProductCalEarlySquareProbabilityPamDto productCalEarlySquareProbabilityPamDto;

    @ApiModelProperty("基础佣金扣返比例规则")
    private String basicCommissionRebateRatioRule;

    @ApiModelProperty("提前结清手续费比例规则")
    private String earlySquareHandChargeRatioRule;

    @ApiModelProperty("提前结清概率")
    private List<ProductCalEarlySquareProbabilityDto> productCalEarlySquareProbabilityDtoList;

    @ApiModelProperty("提前结清基础服务费扣返比例")
    private List<ProductCalEarlySquareBasicCommissionRebateRatioDto> productCalEarlySquareBasicCommissionRebateRatioDtoList;

    @ApiModelProperty("提前结清手续费比例")
    private List<ProductCalEarlySquareHandChargeRatioDto> productCalEarlySquareHandChargeRatioDtoList;


}
