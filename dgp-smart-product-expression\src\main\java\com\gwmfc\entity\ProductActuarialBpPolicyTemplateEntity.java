package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductActuarialBpPolicyTemplateEntity
 * @Description TODO
 * @Date 2025/4/21 14:12
 */
@Data
@TableName("product_actuarial_bp_policy_template")
public class ProductActuarialBpPolicyTemplateEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "policy_template_name")
    @ApiModelProperty("政策模板名称")
    private String policyTemplateName;

    @TableField(value = "first_dimension_name")
    @ApiModelProperty("一级维度名称")
    private String firstDimensionName;

    @TableField(value = "first_dimension_content")
    @ApiModelProperty("一级维度内容")
    private String firstDimensionContent;

    @TableField(value = "second_dimension_name")
    @ApiModelProperty("二级维度名称")
    private String secondDimensionName;

    @TableField(value = "second_dimension_content")
    @ApiModelProperty("二级维度内容")
    private String secondDimensionContent;

    @TableField(value = "third_dimension_name")
    @ApiModelProperty("三级维度名称")
    private String thirdDimensionName;

    @TableField(value = "third_dimension_content")
    @ApiModelProperty("三级维度内容")
    private String thirdDimensionContent;

    @TableField(value = "forth_dimension_name")
    @ApiModelProperty("四级维度名称")
    private String forthDimensionName;

    @TableField(value = "forth_dimension_content")
    @ApiModelProperty("四级维度内容")
    private String forthDimensionContent;

    @TableField(value = "fifth_dimension_name")
    @ApiModelProperty("五级维度名称")
    private String fifthDimensionName;

    @TableField(value = "fifth_dimension_content")
    @ApiModelProperty("五级维度内容")
    private String fifthDimensionContent;

    @TableField(value = "sixth_dimension_name")
    @ApiModelProperty("六级维度名称")
    private String sixthDimensionName;

    @TableField(value = "sixth_dimension_content")
    @ApiModelProperty("六级维度内容")
    private String sixthDimensionContent;

    @TableField(value = "status")
    @ApiModelProperty("状态")
    private Integer status;

    @TableField(value = "effective_time")
    @ApiModelProperty("生效时间")
    private Date effectiveTime;

    @TableField(value = "end_time")
    @ApiModelProperty("结束时间")
    private Date endTime;

    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField(value = "create_user")
    @ApiModelProperty("创建人")
    private String createUser;

    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @TableField(value = "update_user")
    @ApiModelProperty("更新人")
    private String updateUser;
}
