package com.gwmfc.service;

import com.gwmfc.entity.data.EastMoneyConsumerConfidenceIndexEntity;
import com.gwmfc.entity.data.EastMoneyNewCreditIndexEntity;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.OkHttpUtils;
import com.gwmfc.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @Classname ProductDataCaptureService
 * @Description TODO
 * @Date 2024/10/31 15:46
 */
@Service
@Slf4j
@RefreshScope
public class EastMoneyNetIndexService {

    @Value("${dataCapture.consumerConfidenceIndexUrl}")
    private String consumerConfidenceIndexUrl;

    @Value("${dataCapture.consumerConfidenceIndexPageUrl}")
    private String consumerConfidenceIndexPageUrl;

    @Value("${dataCapture.creditIncreaseDataUrl}")
    private String creditIncreaseDataUrl;

    @Value("${dataCapture.creditIncreaseDataPageUrl}")
    private String creditIncreaseDataPageUrl;

    @Autowired
    private EastMoneyConsumerConfidenceIndexService eastMoneyConsumerConfidenceIndexService;

    @Autowired
    private EastMoneyNewCreditIndexService eastMoneyNewCreditIndexService;

    @Autowired
    private RestTemplate restTemplate;

    public Result consumerConfidenceIndexCapture() {
        Result result = new Result();
        try {
            restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
            Map<String,Object> map = new HashMap<>();
            map.put("time",String.valueOf(System.currentTimeMillis()));
            String data = restTemplate.getForObject(consumerConfidenceIndexUrl, String.class,map);

            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            Long createTime = Long.parseLong(dateTimeFormatter.format(LocalDateTime.now()));
            Map<String, Object> dataMap = GsonUtil.jsonToMap(data);
            boolean success = (boolean)dataMap.get("success");
            if(success){
                List<EastMoneyConsumerConfidenceIndexEntity> eastMoneyConsumerConfidenceIndexEntityList = new ArrayList<>();
                Map<String, Object> resultMap = (Map<String, Object>) dataMap.get("result");
                List<Map<String,Object>> list = (List<Map<String,Object>>)resultMap.get("data");
                if(list != null && list.size()>0){
                    /**
                     * 先清理表中的旧数据，再将新数据保存
                     */
                    eastMoneyConsumerConfidenceIndexService.remove(null);

                    for (Map<String, Object> dataResult : list) {
                        EastMoneyConsumerConfidenceIndexEntity eastMoneyConsumerConfidenceIndexEntity = new EastMoneyConsumerConfidenceIndexEntity();
                        eastMoneyConsumerConfidenceIndexEntity.setDataDate(dataResult.get("REPORT_DATE").toString().substring(0,7));
                        eastMoneyConsumerConfidenceIndexEntity.setConsumersFaithIndex(dataResult.get("CONSUMERS_FAITH_INDEX").toString());
                        eastMoneyConsumerConfidenceIndexEntity.setFaithIndexSame(dataResult.get("FAITH_INDEX_SAME").toString());
                        eastMoneyConsumerConfidenceIndexEntity.setFaithIndexSequential(dataResult.get("FAITH_INDEX_SEQUENTIAL").toString());
                        eastMoneyConsumerConfidenceIndexEntity.setConsumersAstisIndex(dataResult.get("CONSUMERS_ASTIS_INDEX").toString());
                        eastMoneyConsumerConfidenceIndexEntity.setAstisIndexSame(dataResult.get("ASTIS_INDEX_SAME").toString());
                        eastMoneyConsumerConfidenceIndexEntity.setAstisIndexSequential(dataResult.get("ASTIS_INDEX_SEQUENTIAL").toString());
                        eastMoneyConsumerConfidenceIndexEntity.setConsumersExpectIndex(dataResult.get("CONSUMERS_EXPECT_INDEX").toString());
                        eastMoneyConsumerConfidenceIndexEntity.setExpectIndexSame(dataResult.get("EXPECT_INDEX_SAME").toString());
                        eastMoneyConsumerConfidenceIndexEntity.setExpectIndexSequential(dataResult.get("EXPECT_INDEX_SEQUENTIAL").toString());
                        eastMoneyConsumerConfidenceIndexEntity.setPageUrl(consumerConfidenceIndexPageUrl);
                        eastMoneyConsumerConfidenceIndexEntity.setCreateTime(createTime);
                        eastMoneyConsumerConfidenceIndexEntityList.add(eastMoneyConsumerConfidenceIndexEntity);
                    }
                }
                eastMoneyConsumerConfidenceIndexService.saveBatch(eastMoneyConsumerConfidenceIndexEntityList);
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch(Exception e){
            log.error("getConsumerConfidenceIndex error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("getConsumerConfidenceIndex error");
        }
        return result;
    }

    public Result newCreditIndexCapture(){
        Result result = new Result();
        try{
            restTemplate.getMessageConverters().add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
            Map<String,Object> map = new HashMap<>();
            map.put("time",String.valueOf(System.currentTimeMillis()));
            String data = restTemplate.getForObject(creditIncreaseDataUrl, String.class,map);

            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
            Long createTime = Long.parseLong(dateTimeFormatter.format(LocalDateTime.now()));
            Map<String, Object> dataMap = GsonUtil.jsonToMap(data);
            boolean success = (boolean)dataMap.get("success");
            if(success){
                List<EastMoneyNewCreditIndexEntity> eastMoneyNewCreditIndexEntityList = new ArrayList<>();
                Map<String, Object> resultMap = (Map<String, Object>) dataMap.get("result");
                List<Map<String,Object>> list = (List<Map<String,Object>>)resultMap.get("data");
                if(list != null && list.size()>0){
                    /**
                     * 先清理旧的数据，再保存新数据
                     */
                    eastMoneyNewCreditIndexService.remove(null);

                    for (Map<String, Object> dataResult : list) {
                        EastMoneyNewCreditIndexEntity eastMoneyNewCreditIndexEntity = new EastMoneyNewCreditIndexEntity();
                        eastMoneyNewCreditIndexEntity.setDataDate(dataResult.get("REPORT_DATE").toString().substring(0,7));
                        eastMoneyNewCreditIndexEntity.setRmbLoan(dataResult.get("RMB_LOAN").toString());
                        eastMoneyNewCreditIndexEntity.setRmbLoanSame(dataResult.get("RMB_LOAN_SAME").toString());
                        eastMoneyNewCreditIndexEntity.setRmbLoanSequential(dataResult.get("RMB_LOAN_SEQUENTIAL").toString());
                        eastMoneyNewCreditIndexEntity.setRmbLoanAccumulate(dataResult.get("RMB_LOAN_ACCUMULATE").toString());
                        eastMoneyNewCreditIndexEntity.setLoanAccumulateSame(dataResult.get("LOAN_ACCUMULATE_SAME").toString());
                        eastMoneyNewCreditIndexEntity.setPageUrl(creditIncreaseDataPageUrl);
                        eastMoneyNewCreditIndexEntity.setCreateTime(createTime);
                        eastMoneyNewCreditIndexEntityList.add(eastMoneyNewCreditIndexEntity);
                    }
                }
                eastMoneyNewCreditIndexService.saveBatch(eastMoneyNewCreditIndexEntityList);
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
        }catch (Exception e){
            log.error("getNewCreditIndex error",e);
            result.setCode(FAIL.getCode());
            result.setMessage("getNewCreditIndex error");
        }
        return result;
    }
}
