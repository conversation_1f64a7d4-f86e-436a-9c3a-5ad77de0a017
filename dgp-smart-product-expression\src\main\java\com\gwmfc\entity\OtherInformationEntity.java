package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023年09月07日 13:04
 */
@Data
@TableName("other_information")
public class OtherInformationEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("对私奖励")
    private String privateReward;
    @ApiModelProperty("活动礼品")
    private String eventGifts;
    @ApiModelProperty("是否免抵押")
    private Boolean mortgageFreeOrNot;
    @ApiModelProperty("抵押机构")
    private String mortgageAgency;
    @ApiModelProperty("准入门槛")
    private String coefficientOfTenThousand;

    @ApiModelProperty("审批通过率")
    private String approvalRate;
    @ApiModelProperty("放款速度")
    private String rateOfLending;
    @ApiModelProperty("发票抵扣")
    private String invoiceDeduction;
    @ApiModelProperty("驻店情况")
    private String residentSituation;
    @ApiModelProperty("其他")
    private String other;
    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("对接方式(银行员工、银行sp)")
    private String dockingMode;
    @ApiModelProperty("贷款模式(消费贷、信用卡)")
    private String loansMode;
    @ApiModelProperty("还款方式(等本等息、等额本息)")
    private Integer repaymentMode;
    @ApiModelProperty("可长贷短还(是、否)")
    private Boolean canLongLoanShortRepayment;
}
