package com.gwmfc.schedule;

import com.gwmfc.service.GrossDomesticProductService;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023年08月14日 10:14
 */
@Component
@Slf4j
public class GrossDomesticProductQuarterSchedule {
    @Resource
    private GrossDomesticProductService grossDomesticProductService;

    @Scheduled(cron = "0 0 0 * * ?")
    @SchedulerLock(name = "catchGrossDomesticProductQuarter", lockAtMostFor = "PT1H", lockAtLeastFor = "PT1H")
    public void catchGrossDomesticProductQuarter() {
        grossDomesticProductService.catchGrossDomesticProductQuarter(1, "Scheduled");
        log.info("catchMonthlySalesRanking:{}","Scheduled");
    }

}