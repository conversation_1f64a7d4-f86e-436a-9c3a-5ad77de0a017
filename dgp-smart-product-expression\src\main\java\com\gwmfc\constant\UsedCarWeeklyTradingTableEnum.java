package com.gwmfc.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * 流通协会二手车周销量
 *
 * @Date: 2024/01/31
 * @Author: zhang<PERSON>yu
 */

@Getter
@AllArgsConstructor
public enum UsedCarWeeklyTradingTableEnum {

    TRADING_DATA {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("title_start_date", "标题开始时间");
            headMap.put("title_end_date", "标题结束时间");
            headMap.put("start_date", "开始日期");
            headMap.put("end_date", "结束日期");
            headMap.put("avg_daily_trading", "日均交易量（万辆）");
            headMap.put("img_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    },
    REGION_TRADING_DATA {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("title_start_date", "标题开始时间");
            headMap.put("title_end_date", "标题结束时间");
            headMap.put("start_date", "开始日期");
            headMap.put("end_date", "结束日期");
            headMap.put("region", "区域");
            headMap.put("avg_daily_trading", "日均交易量（万辆）");
            headMap.put("img_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    },
    ;

    public abstract void getHeader(Map<String, String> headMap);
}
