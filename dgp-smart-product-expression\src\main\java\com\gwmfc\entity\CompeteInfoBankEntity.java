package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname CompeteInfoBankEntity
 * @Description TODO
 * @Date 2024/12/11 14:13
 */
@Data
@TableName("compete_info_bank")
public class CompeteInfoBankEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "batch_id")
    @ApiModelProperty("批次id")
    private Long batchId;

    @TableField(value = "bank")
    @ApiModelProperty("银行")
    private String bank;

    @TableField(value = "group_name")
    @ApiModelProperty("集团")
    private String groupName;

    @TableField(value = "buss_type")
    @ApiModelProperty("业务类型")
    private String bussType;

    @TableField(value = "loan_term")
    @ApiModelProperty("期数")
    private String loanTerm;

    @TableField(value = "fee_rate")
    @ApiModelProperty("费率")
    private String feeRate;

    @TableField(value = "commission_rebate")
    @ApiModelProperty("返佣")
    private String commissionRebate;

    @TableField(value = "prepayment_term")
    @ApiModelProperty("提前还款期次")
    private String prepaymentTerm;

    @TableField(value = "dedit_ratio")
    @ApiModelProperty("违约金比例")
    private String deditRatio;

    @TableField(value = "max_loan_ratio")
    @ApiModelProperty("最高贷款比例")
    private String maxLoanRatio;

    @TableField(value = "service_fee_manage")
    @ApiModelProperty("服务费管控")
    private String serviceFeeManage;

    @TableField(value = "other")
    @ApiModelProperty("其他")
    private String other;

    @TableField(value = "staff_no")
    @ApiModelProperty("工号")
    private String staffNo;

    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField(value = "create_user")
    @ApiModelProperty("创建用户")
    private String createUser;

    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @TableField(value = "update_user")
    @ApiModelProperty("更新用户")
    private String updateUser;

}
