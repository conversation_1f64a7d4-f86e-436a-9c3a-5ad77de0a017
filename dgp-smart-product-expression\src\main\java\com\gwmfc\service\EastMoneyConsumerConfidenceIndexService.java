package com.gwmfc.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.dao.EastMoneyConsumerConfidenceIndexDao;
import com.gwmfc.entity.data.EastMoneyConsumerConfidenceIndexEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Classname ConsumerConfidenceIndexService
 * @Description TODO
 * @Date 2024/11/1 9:33
 */
@Service
@Slf4j
@RefreshScope
public class EastMoneyConsumerConfidenceIndexService extends ServiceImpl<EastMoneyConsumerConfidenceIndexDao, EastMoneyConsumerConfidenceIndexEntity> {
}
