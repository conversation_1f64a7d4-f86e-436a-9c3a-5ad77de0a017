package com.gwmfc.resolver;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.jwt.JwtTokenResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * <AUTHOR>
 * @Classname CurrentUserMethodArgumentResolver
 * @Description 解析http request 中用户信息
 * @Date 2021/4/12 15:06
 */
@Slf4j
public class CurrentUserMethodArgumentResolver implements HandlerMethodArgumentResolver , JwtTokenResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        if (parameter.getParameterType().isAssignableFrom(User.class) && parameter.hasParameterAnnotation(CurrentUser.class)) {
            return true;
        }
        return false;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        String token = webRequest.getHeader("Authorization");
        log.info("get token: {}",token);
        if (StringUtils.hasLength(token)) {
            String split[] = token.split(" ");
            if(!Bearer.equals(split[0])){
                log.info("not valid token {}",token);
                return new User();
            }
            User user = this.resolveFromJwt(split[1]);
            return user;
        }
        return new User();
    }
}
