package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname CashStreamDto
 * @Description 计算结束后返回给前端的现金流dto
 * @Date 2023/9/19 10:59
 */
@Data
@ApiModel(value = "联合贷现金流")
public class ProductCalUnionLoanCashStreamDto {

    @ApiModelProperty("月")
    private Integer month;

    @ApiModelProperty("日期")
    private String unionCashStreamDate;

    @ApiModelProperty("联合贷现金流我方本金")
    private Double unionCashStreamOurPrincipal;

    @ApiModelProperty("联合贷现金流我方利息")
    private Double unionCashStreamOurInterest;

    @ApiModelProperty("联合贷现金流银行返还手续费")
    private Double unionCashStreamBankReturnMoney;

    @ApiModelProperty("联合贷现金流银行本金")
    private Double unionCashStreamBankPrincipal;

    @ApiModelProperty("联合贷现金流银行利息")
    private Double unionCashStreamBankInterest;

    @ApiModelProperty("联合贷现金流服务费")
    private Double unionCashStreamServiceFee;

    @ApiModelProperty("联合贷现金流贷款损失")
    private Double unionCashStreamLoanLoss;

    @ApiModelProperty("联合贷现金流资金成本")
    private Double unionCashStreamCapitalCost;

    @ApiModelProperty("联合贷现金流税金")
    private Double unionCashStreamTaxMoney;

    @ApiModelProperty("联合贷现金流变动成本")
    private Double unionCashStreamVariableCost;

    @ApiModelProperty("联合贷现金流100%税金")
    private Double unionCashStreamPercentTaxMoney;

    @ApiModelProperty("联合贷现金流")
    private Double unionCashStream;
}
