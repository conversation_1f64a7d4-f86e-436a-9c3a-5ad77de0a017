package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname CarIndustryDealerInventoryWarningIndexEntity
 * @Description 汽车行业经销商库存预警指数
 * @Date 2024/11/6 8:56
 */
@Data
@TableName("car_industry_dealer_inventory_warning_index")
@ExcelIgnoreUnannotated
public class CarIndustryDealerInventoryWarningIndexEntity extends BaseEntity{
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期")@TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("指标")
    @TableFieldMapping(value = "index_name", comment = "指标")
    private String indexName;

    @ExcelProperty("预警值")
    @TableFieldMapping(value = "growth_rate", comment = "预警值")
    private String growthRate;

    @ExcelProperty("网页地址")
    @TableFieldMapping(value = "page_url", comment = "网页地址")
    private String pageUrl;
}
