<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.ProductProposqlCalDao">

    <select id="getProductCalBasicInfoEntityList" resultType="com.gwmfc.entity.ProductCalBasicInfoEntity">
        select
            *
        from
            product_cal_basic_info pcbi
        where
            pcbi.business_type = #{businessType} and pcbi.repayment_method = #{repaymentMethod} and pcbi.consider_early_square = #{considerEarlySquare} and pcbi.state = '1'
        <if test="isUnionLoan != null">
            and exists (select 1 from product_cal_union_loan_param where basic_info_id = pcbi.id and item = 'bank_loan_ratio' and value is not null) order by pcbi.create_time desc
        </if>
        <if test="isUnionLoan == null">
            and not exists (select 1 from product_cal_union_loan_param where basic_info_id = pcbi.id and item = 'bank_loan_ratio' and value is not null) order by pcbi.create_time desc
        </if>
    </select>
</mapper>