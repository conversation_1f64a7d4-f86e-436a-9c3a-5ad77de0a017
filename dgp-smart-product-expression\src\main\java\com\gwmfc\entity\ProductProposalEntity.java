package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import com.gwmfc.dto.ProductCalProfitCollectDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品提案
 * <AUTHOR>
 * @date 2023年10月16日 14:42
 */
@Data
@TableName("product_proposal")
public class ProductProposalEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "name", comment = "产品提案名称")
    private String name;
    @TableFieldMapping(value = "business_type", comment = "业务类型")
    private Integer businessType;
    @TableFieldMapping(value = "market_analysis_annex", comment = "市场分析附件")
    private String marketAnalysisAnnex;
    @TableFieldMapping(value = "product_proposal_annex", comment = "市场分析附件")
    private String productProposalAnnex;

    @TableFieldMapping(value = "weighted_between_groups_or_not", comment = "是否组间加权")
    private Integer weightedBetweenGroupsOrNot;

    @TableFieldMapping(value = "product_proposal_template_group_json", comment = "提案JSON")
    private String productProposalTemplateGroupJson;

    @TableFieldMapping(value = "product_elements_description", comment = "产品要素说明")
    private String productElementsDescription;
    @TableFieldMapping(value = "risk_control_strategy_description", comment = "风控策略说明")
    private String riskControlStrategyDescription;
    @TableFieldMapping(value = "business_policy_description", comment = "商务政策说明")
    private String businessPolicyDescription;
    @TableFieldMapping(value = "irr_description", comment = "IRR测算说明")
    private String irrDescription;
    @TableFieldMapping(value = "sales_forecast_description", comment = "销量预测说明")
    private String salesForecastDescription;
    @ApiModelProperty("利润汇总")
    private String productCalProfitCollectDtoJson;

    @TableFieldMapping(value = "current_step", comment = "当前步骤")
    private String currentStep;

    @TableFieldMapping(value = "reset_id", comment = "退回关联ID（退回步骤关联ID）")
    private Long resetId;

    @TableFieldMapping(value = "create_time", comment = "创建日期")
    private LocalDateTime createTime;

    @TableField(exist = false)
    private LocalDateTime approveTime;

    @TableFieldMapping(value = "create_user", comment = "创建人")
    private String createUser;

    @TableFieldMapping(value = "update_time", comment = "修改日期")
    private LocalDateTime updateTime;

    @TableFieldMapping(value = "update_user", comment = "修改人")
    private String updateUser;
}
