package com.gwmfc.entity.data;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * 欧拉融租申请数据
 *
 * 数据源：邮箱
 *
 * 数据格式：excel
 *
 * 更新频率：每天
 *
 * 获取方式：自动
 *
 * 字段信息：数据日期（邮件日期，‘YYYYMMDD’）、订单来源、项目号、审批状态、产品名称、品牌、车型、车辆价款、融资额、期限、经销商名称、、放款日期、首付款、首付款比例、客户利率、总利率、否决原因
 *  * <AUTHOR>
 *  * @date 2023/8/17
 */
@Data
@TableName("ola_lease_contract")
@ExcelIgnoreUnannotated
public class OlaLeaseContractEntity extends BaseEntity {

  @ExcelIgnore
  @TableId(type = IdType.AUTO)
  private Long id;

  @ExcelProperty("放款时间") @TableFieldEnumMapping(dateEnum = true)
  @TableFieldMapping(value = "data_date", comment = "放款时间", queryItem = true)
  private String dataDate;

  @ExcelProperty("订单来源")
  @TableFieldMapping(value = "order_source", comment = "订单来源")
  private String orderSource;

  @ExcelProperty("项目号")
  @TableFieldMapping(value = "project_number", comment = "项目号")
  private String projectNumber;

  @ExcelProperty("产品名称")
  @TableFieldMapping(value = "product_name", comment = "产品名称", queryItem = true)
  private String productName;

  @ExcelProperty("品牌")
  @TableFieldMapping(value = "brand", comment = "品牌", queryItem = true)
  private String brand;

  @ExcelProperty("车型")
  @TableFieldMapping(value = "vehicle_model", comment = "车型", queryItem = true)
  private String vehicleModel;

  @ExcelProperty("车辆价款")
  @TableFieldMapping(value = "vehicle_price", comment = "车辆价款")
  private String vehiclePrice;

  @ExcelProperty("融资额")
  @TableFieldMapping(value = "financed_amount", comment = "融资额")
  private String financedAmount;

  @ExcelProperty("期限")
  @TableFieldMapping(value = "deadline", comment = "期限")
  private String deadline;

  @ExcelProperty("经销商名称")
  @TableFieldMapping(value = "dealer_name", comment = "经销商名称")
  private String dealerName;

  @ExcelProperty("车架号")
  @TableFieldMapping(value = "vin", comment = "车架号")
  private String vin;

  @ExcelProperty("首付款")
  @TableFieldMapping(value = "initial_payment", comment = "首付款")
  private String initialPayment;

  @ExcelProperty("首付比例")
  @TableFieldMapping(value = "initial_payment_ratio", comment = "首付比例")
  private String initialPaymentRatio;

  @ExcelProperty("客户利率")
  @TableFieldMapping(value = "customer_interest_rate", comment = "客户利率")
  private String customerInterestRate;

  @ExcelProperty("总利率")
  @TableFieldMapping(value = "total_interest_rate", comment = "总利率")
  private String totalInterestRate;

  @ExcelProperty("是否厂家贴息")
  @TableFieldMapping(value = "manufacturer_discount_or_not", comment = "是否厂家贴息")
  private String manufacturerDiscountOrNot;

}
