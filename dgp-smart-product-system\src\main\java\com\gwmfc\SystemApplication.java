package com.gwmfc;


import com.alibaba.nacos.client.naming.utils.NetUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 * @Description : 此模块为用户管理，权限管理，菜单管理等系统相关模块，钉钉审批流程模块
 * @Date 2020-08-04
 */
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication
public class SystemApplication {
    public static void main(String[] args) {
        SpringApplication.run(SystemApplication.class, args);
    }


}
