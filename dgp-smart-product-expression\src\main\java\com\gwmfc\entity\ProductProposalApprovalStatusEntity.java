package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *  风险损失 商务政策审核状态
 * <AUTHOR>
 * @date 2023年10月16日 17:16
 */
@Data
@TableName("product_proposal_approval_status")
public class ProductProposalApprovalStatusEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "product_proposal_id", comment = "关联产品提案ID")
    private Long productProposalId;

    @TableFieldMapping(value = "status", comment = "当前通过状态（1：通过；-1：拒绝）")
    private Integer status;

    @TableFieldMapping(value = "approve_status", comment = "审批状态（1：通过；-1：拒绝）")
    private Integer approveStatus;

    @TableFieldMapping(value = "type", comment = "类型（1：风险；2：商务；3：财务）")
    private Integer type;

    @TableFieldMapping(value = "step", comment = "退回步骤（一方否定，联动退回）")
    private String step;

    @TableFieldMapping(value = "reason", comment = "退回原因")
    private String reason;

    @TableFieldMapping(value = "create_time", comment = "操作日期")
    private LocalDateTime createTime;

    @TableFieldMapping(value = "create_user", comment = "操作人员")
    private String createUser;

    @TableFieldMapping(value = "deal_tag", comment = "历史标签")
    private Boolean dealTag = false;

    @TableFieldMapping(value = "revocation", comment = "是否撤回")
    private Boolean revocation = false;
}
