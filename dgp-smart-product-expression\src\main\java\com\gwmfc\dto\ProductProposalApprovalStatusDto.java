package com.gwmfc.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 *  风险损失 商务政策审核状态
 * <AUTHOR>
 * @date 2023年10月16日 17:16
 */
@Data
@ApiModel(value = "风险损失 财务 商务政策审核状态")
public class ProductProposalApprovalStatusDto {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("产品提案id")
    private Long productProposalId;

    @ApiModelProperty("通过状态（1：通过；-1：拒绝）")
    private Integer status;

    @ApiModelProperty("审批状态（1：通过；-1：拒绝）")
    private Integer approveStatus;

    @ApiModelProperty("类型（0：全部（查询用）；1：风险；2：商务；3：财务）")
    private Integer type;

    @ApiModelProperty("退回步骤（一方否定，联动退回）")
    private String step;

    @ApiModelProperty("退回原因")
    private String reason;

    @ApiModelProperty("操作日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("操作人员")
    private String createUser;

    @ApiModelProperty("是否撤回")
    private Boolean revocation = false;

}
