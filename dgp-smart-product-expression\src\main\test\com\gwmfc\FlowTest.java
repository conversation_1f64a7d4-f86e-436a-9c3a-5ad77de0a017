package com.gwmfc;

import com.gwmfc.dto.ProductProposalTemplateDto;
import com.gwmfc.dto.ProductProposalTemplateGroupDto;
import com.gwmfc.util.GsonUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年03月14日 8:46
 */
@SpringBootTest
public class FlowTest {
    @Test
    public void list2() {

        List<ProductProposalTemplateDto> oldProductProposalTemplateDtoList = new ArrayList<>();
        ProductProposalTemplateDto productProposalTemplateDto = new ProductProposalTemplateDto();
        productProposalTemplateDto.setBaseCommissionRatio(1.0);
        productProposalTemplateDto.setChanged(false);
        productProposalTemplateDto.setId(1L);
        ProductProposalTemplateDto productProposalTemplateDto1 = new ProductProposalTemplateDto();
        productProposalTemplateDto1.setBaseCommissionRatio(1.0);
        productProposalTemplateDto1.setChanged(false);
        productProposalTemplateDto1.setId(2L);
        oldProductProposalTemplateDtoList.add(productProposalTemplateDto);
        oldProductProposalTemplateDtoList.add(productProposalTemplateDto1);

        List<ProductProposalTemplateDto> productProposalTemplateDtoList = new ArrayList<>();
        ProductProposalTemplateDto productProposalTemplate = new ProductProposalTemplateDto();
        productProposalTemplate.setBrand("aaa");
        productProposalTemplate.setContractsProportion(1.0);
        productProposalTemplate.setId(1L);
        ProductProposalTemplateDto productProposalTemplate1 = new ProductProposalTemplateDto();
        productProposalTemplate1.setCreateUser("aaaadfasdfsdf");
        productProposalTemplate1.setContractsProportion(2.0);
        productProposalTemplate1.setId(2L);
        productProposalTemplateDtoList.add(productProposalTemplate);
        productProposalTemplateDtoList.add(productProposalTemplate1);

        List<ProductProposalTemplateDto> mergeProductProposalTemplateDtoList = new ArrayList<>();
        oldProductProposalTemplateDtoList.forEach(oldProductProposalEntityTmp -> {
            List<ProductProposalTemplateDto> copyTmpList = productProposalTemplateDtoList.stream().filter(productProposalTemplateDtoa -> productProposalTemplateDtoa.getId().equals(oldProductProposalEntityTmp.getId())).collect(Collectors.toList());
            if (copyTmpList.size() != 0) {
                ProductProposalTemplateDto copyTmp = copyTmpList.get(0);
                try {
                    mergeProductProposalTemplateDtoList.add(mergeObjects(oldProductProposalEntityTmp, copyTmp));
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        });
        System.out.println();
    }

    @Test
    public void listGroup() {
        List<ProductProposalTemplateGroupDto> oldProductProposalTemplateGroupDtoList = new ArrayList<>();
        ProductProposalTemplateGroupDto oldProductProposalTemplateGroupDto1 = new ProductProposalTemplateGroupDto();
        List<ProductProposalTemplateDto> oldProductProposalTemplateDtoList = new ArrayList<>();
        ProductProposalTemplateDto productProposalTemplateDto2 = new ProductProposalTemplateDto();
        productProposalTemplateDto2.setBaseCommissionRatio(1.0);
        productProposalTemplateDto2.setChanged(false);
        productProposalTemplateDto2.setId(1L);
        productProposalTemplateDto2.setCurrentTimeSeqNo("a");
        ProductProposalTemplateDto productProposalTemplateDto1 = new ProductProposalTemplateDto();
        productProposalTemplateDto1.setBaseCommissionRatio(1.0);
        productProposalTemplateDto1.setChanged(false);
        productProposalTemplateDto1.setCurrentTimeSeqNo("b");
        productProposalTemplateDto1.setId(2L);
        oldProductProposalTemplateDtoList.add(productProposalTemplateDto2);
        oldProductProposalTemplateDtoList.add(productProposalTemplateDto1);
        oldProductProposalTemplateGroupDto1.setProductProposalTemplateList(oldProductProposalTemplateDtoList);
        oldProductProposalTemplateGroupDtoList.add(oldProductProposalTemplateGroupDto1);

        List<ProductProposalTemplateGroupDto> productProposalTemplateGroupDtoList = new ArrayList<>();
        ProductProposalTemplateGroupDto productProposalTemplateGroupDto = new ProductProposalTemplateGroupDto();
        List<ProductProposalTemplateDto> productProposalTemplateDtoList = new ArrayList<>();
        ProductProposalTemplateDto productProposalTemplate2 = new ProductProposalTemplateDto();
        productProposalTemplate2.setBrand("aaa");
        productProposalTemplate2.setContractsProportion(1.0);
        productProposalTemplate2.setId(1L);
        productProposalTemplate2.setCurrentTimeSeqNo("a");
        ProductProposalTemplateDto productProposalTemplate1 = new ProductProposalTemplateDto();
        productProposalTemplate1.setCreateUser("aaaadfasdfsdf");
        productProposalTemplate1.setContractsProportion(2.0);
        productProposalTemplate1.setId(2L);
        productProposalTemplate1.setCurrentTimeSeqNo("b");
        productProposalTemplateDtoList.add(productProposalTemplate2);
        productProposalTemplateDtoList.add(productProposalTemplate1);
        productProposalTemplateGroupDto.setProductProposalTemplateList(productProposalTemplateDtoList);
        productProposalTemplateGroupDtoList.add(productProposalTemplateGroupDto);

        oldProductProposalTemplateGroupDtoList.forEach(oldProductProposalTemplateGroupDto -> {
            List<ProductProposalTemplateGroupDto> newCopyTmpGroupList = productProposalTemplateGroupDtoList;
            if (!newCopyTmpGroupList.isEmpty()) {
                ProductProposalTemplateGroupDto newProductProposalTemplateGroupDto = newCopyTmpGroupList.get(0);
                List<ProductProposalTemplateDto> mergeProductProposalTemplateDtoList = new ArrayList<>();
                oldProductProposalTemplateDtoList.forEach(oldProductProposalEntityTmp -> {
                    List<ProductProposalTemplateDto> copyTmpList = newProductProposalTemplateGroupDto.getProductProposalTemplateList().stream().filter(productProposalTemplateDto -> productProposalTemplateDto.getCurrentTimeSeqNo().equals(oldProductProposalEntityTmp.getCurrentTimeSeqNo())).collect(Collectors.toList());
                    if (copyTmpList.size() != 0) {
                        ProductProposalTemplateDto copyTmp = copyTmpList.get(0);
                        try {
                            mergeProductProposalTemplateDtoList.add(mergeObjects(oldProductProposalEntityTmp, copyTmp));
                        } catch (IllegalAccessException e) {
//                            log.error("模板复制报错，{}", e);
                        }
                    }
                });
                oldProductProposalTemplateGroupDto.setProductProposalTemplateList(mergeProductProposalTemplateDtoList);
            }
        });
        System.out.println();
    }

    public static <T> T mergeObjects(T obj1, T obj2) throws IllegalAccessException {
        Class<?> clazz = obj1.getClass();
        T mergedObj;
        try {
            mergedObj = (T) clazz.newInstance();
        } catch (InstantiationException e) {
            throw new RuntimeException("无法创建新对象： " + clazz.getName(), e);
        }

        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Object value1 = field.get(obj1);
            Object value2 = field.get(obj2);
            if (value1 != null) {
                field.set(mergedObj, value1);
            } else if (value2 != null) {
                field.set(mergedObj, value2);
            }
        }

        return mergedObj;
    }
}
