package com.gwmfc.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.config.MarketAnalyzeConfig;
import com.gwmfc.constant.*;
import com.gwmfc.dao.VehicleSalesDao;
import com.gwmfc.domain.User;
import com.gwmfc.dto.VehicleExcelQueryDto;
import com.gwmfc.dto.VehicleSalesDto;
import com.gwmfc.dto.VehicleSalesQueryDto;
import com.gwmfc.entity.data.VehicleSalesEntity;
import com.gwmfc.exception.SystemRuntimeException;
import com.gwmfc.util.StatusCodeEnum;
import com.gwmfc.util.TimeStampStringConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * 商用车销量
 *
 * @Date: 2023/11/9
 * @Author: zhangxinyu
 */

@Slf4j
@Service
public class VehicleSalesService extends ServiceImpl<VehicleSalesDao, VehicleSalesEntity> {

    @Resource
    private MarketAnalyzeConfig marketAnalyzeConfig;

    @Resource
    private WebClient.Builder webClient;

    @Resource
    private ProductDataMartService productDataMartService;

    @Resource
    private VehicleSalesDao vehicleSalesDao;

    /**
     * 分页查询
     *
     * @param current
     * @param size
     * @param param
     * @return
     */
    public IPage<VehicleSalesEntity> listPage(VehicleSalesQueryDto param, Integer current, Integer size) {
        IPage<VehicleSalesEntity> pageParam = new Page<>(current, size);
        LambdaQueryWrapper<VehicleSalesEntity> wrapper = new QueryWrapper<VehicleSalesEntity>().lambda()
                .ge(StringUtils.hasText(param.getStartDate()), VehicleSalesEntity::getDataDate, param.getStartDate())
                .le(StringUtils.hasText(param.getEndDate()), VehicleSalesEntity::getDataDate, param.getEndDate())
                .eq(Objects.nonNull(param.getType()), VehicleSalesEntity::getType, param.getType())
                .orderByDesc(VehicleSalesEntity::getDataDate).orderByDesc(VehicleSalesEntity::getId);
        return this.page(pageParam, wrapper);
    }

    /**
     * 根据id删除商用车销量
     *
     * @param id
     * @return
     */
    public VehicleSalesEntity getVehicleSalesById(Integer id) {
        if (Objects.isNull(id)) {
            throw new SystemRuntimeException("id为空");
        }
        VehicleSalesEntity vehicleSales = this.getById(id);
        if (Objects.isNull(vehicleSales)) {
            throw new SystemRuntimeException("该条数据未找到");
        }
        return vehicleSales;
    }

    /**
     * 批量保存商用车销量
     *
     * @param vehicleSalesEntityList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveVehicleSales(List<VehicleSalesEntity> vehicleSalesEntityList) {
        if (!CollectionUtils.isEmpty(vehicleSalesEntityList)) {
            return this.saveBatch(vehicleSalesEntityList);
        } else {
            throw new SystemRuntimeException(StatusCodeEnum.SUCCESS.getCode(), "无新增数据");
        }
    }

    /**
     * 新增/编辑商用车销量
     *
     * @param vehicleSalesDto
     * @param user
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveVehicleSales(VehicleSalesDto vehicleSalesDto, User user) {
        boolean equals = Objects.equals(VehicleTypeEnum.NEW_ENERGY_VEHICLE_SALES.getVehicleType(), vehicleSalesDto.getType());
        boolean idIsNull = Objects.isNull(vehicleSalesDto.getId());
        if (equals && idIsNull) {
            LambdaQueryWrapper<VehicleSalesEntity> wrapper = new QueryWrapper<VehicleSalesEntity>().lambda()
                    .eq(VehicleSalesEntity::getType, vehicleSalesDto.getType())
                    .eq(StringUtils.hasText(vehicleSalesDto.getDataDate()),
                            VehicleSalesEntity::getDataDate, vehicleSalesDto.getDataDate())
                    .eq(StringUtils.hasText(vehicleSalesDto.getCommercialVehicleType()),
                            VehicleSalesEntity::getCommercialVehicleType, vehicleSalesDto.getCommercialVehicleType());
            VehicleSalesEntity vehicleSalesEntity = this.getOne(wrapper);
            if (Objects.nonNull(vehicleSalesEntity)) {
                throw new SystemRuntimeException("该条数据已存在，请勿重复新增数据");
            }
        } else if (!equals && idIsNull) {
            throw new SystemRuntimeException("该表禁止手动新增数据");
        }

        VehicleSalesEntity vehicleSales = new VehicleSalesEntity();
        BeanUtils.copyProperties(vehicleSalesDto, vehicleSales);
        if (Objects.isNull(vehicleSalesDto.getId())) {
            vehicleSales.setCreateUser(user.getUserName());
        }
        vehicleSales.setUpdateUser(user.getUserName());
        return this.saveOrUpdate(vehicleSales);
    }

    /**
     * 根据id删除商用车销量
     *
     * @param id
     * @return
     */
    public boolean delVehicleSalesById(Integer id) {
        return this.removeById(id);
    }

    /**
     * 手动更新
     *
     * @return
     */
    public String updateData() {
        return webClient.build()
                .get()
                .uri(marketAnalyzeConfig.getCommercialVehicleUrl())
                .retrieve()
                .bodyToMono(String.class)
                .block();
    }

    /**
     * 通用导出Excel
     *
     * @param queryDto
     * @param response
     */
    public void export(VehicleExcelQueryDto queryDto, HttpServletResponse response) {
        // 数据库表名
        Integer tableType = queryDto.getTableType();
        String tableName = VehicleTableTypeEnum.getTableNameByType(tableType);

        // 数据库查询条件
        Map<String, Object> conditionParams = queryDto.getConditionParams();
        // 具体表中的类别
        Integer type = Integer.valueOf(conditionParams.get(constant.TYPE).toString());

        // 数据库查询字段及表头列表
        Map<String, String> headParams = this.getHead(tableType, type);

        // Excel表头列表
        List<List<String>> headList = new ArrayList<>();
        // 数据库字段列表
        List<String> columnList = new ArrayList<>();
        // 获取表头列表及查询字段列表
        this.getColumnListAndHeadList(columnList, headList, headParams);

        // 获取Excel数据
        List<List<Object>> dataList = this.getDataList(tableName, columnList, conditionParams);

        // 获取文件名
        String fileName;
        if (conditionParams.containsKey(constant.TYPE)) {
            String typeName = this.getTypeName(tableType, type);
            fileName = String.format("%s-%s", productDataMartService.getFormNameByTableName(tableName), typeName);
        } else {
            fileName = productDataMartService.getFormNameByTableName(tableName);
        }
        fileName = String.format("%s.xlsx", UriUtils.encode(fileName, "UTF-8"));

        try {
            response.setCharacterEncoding("utf-8");
            response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);

            ExcelWriterBuilder writerBuilder = EasyExcel.write(response.getOutputStream()).head(headList);
            writerBuilder.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 处理列宽自适应
                    .registerConverter(new TimeStampStringConverter()) // 处理mysql中datetime类型转换异常
                    .sheet("Sheet1")
                    .doWrite(dataList);
        } catch (IOException e) {
            throw new SystemRuntimeException("Excel文件导出失败");
        }
    }

    /**
     * 获取Excel数据
     *
     * @param tableName       数据库表名
     * @param columnList      数据库字段
     * @param conditionParams 查询条件
     * @return
     */
    private List<List<Object>> getDataList(String tableName, List<String> columnList, Map<String, Object> conditionParams) {
        List<Map<String, Object>> fieldList;
        try {
            fieldList = vehicleSalesDao.selectByType(tableName, columnList, conditionParams);
        } catch (Exception e) {
            throw new SystemRuntimeException("数据查询异常", e);
        }

        if (CollectionUtils.isEmpty(fieldList)) {
            throw new SystemRuntimeException("导出Excel数据为空");
        }

        List<List<Object>> dataList = new ArrayList<>();
        for (Map<String, Object> map : fieldList) {
            List<Object> data = new ArrayList<>();
            for (Map.Entry<String, Object> stringObjectEntry : map.entrySet()) {
                data.add(stringObjectEntry.getValue());
            }
            dataList.add(data);
        }
        return dataList;
    }

    /**
     * 获取列表页中具体类型名
     *
     * @param tableType   数据库表名类型名称
     * @param vehicleType 列表页分类类型名称
     * @return
     */
    private String getTypeName(Integer tableType, Integer vehicleType) {
        switch (tableType) {
            case 1:
                return VehicleTypeEnum.getVehicleNameByType(vehicleType);
            case 2:
                return UsedCarWeeklyTradingTypeEnum.getTradingNameByType(vehicleType);
            case 3:
                return UsedCarMonthlyTradingTypeEnum.getTradingNameByType(vehicleType);
            default:
                throw new SystemRuntimeException("查询类型不存在");
        }
    }

    /**
     * 获取表头及数据库查询字段
     *
     * @param tableType 数据库表名
     * @param type      具体表中的类别
     * @return
     */
    private Map<String, String> getHead(Integer tableType, Integer type) {
        Map<String, String> headerMap = new LinkedHashMap<>();
        switch (tableType) {
            case 1:
                VehicleTypeEnum.getVehicleSalesTableEnumByType(headerMap, type);
                return headerMap;
            case 2:
                UsedCarWeeklyTradingTypeEnum.getUsedCarWeeklyTradingTableEnumByType(headerMap, type);
                return headerMap;
            case 3:
                UsedCarMonthlyTradingTypeEnum.getUsedCarMonthlyTradingTableEnumByType(headerMap, type);
                return headerMap;
            default:
                throw new SystemRuntimeException("查询类型不存在");
        }
    }

    /**
     * 获取表头列表和mysql查询字段
     *
     * @param columnList mysql列名列表
     * @param headList   表头列表
     * @param headParams 请求参数
     */
    private void getColumnListAndHeadList(List<String> columnList, List<List<String>> headList, Map<String, String> headParams) {
        for (Map.Entry<String, String> entry : headParams.entrySet()) {
            String column = entry.getKey();
            columnList.add(column);
            List<String> nameList = new ArrayList<>();
            nameList.add(entry.getValue());
            headList.add(nameList);
        }
    }
}
