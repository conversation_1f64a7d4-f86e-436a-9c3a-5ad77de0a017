package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023年08月23日 15:05
 */
@Data
@TableName("ecl")
@ExcelIgnoreUnannotated
public class EclEntity extends BaseEntity {
    @ExcelIgnore
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("截止月份")
    @TableFieldMapping(value = "start_time", comment = "截止月份")
    private String startTime;

    @ExcelProperty("合同号")
    @TableFieldMapping(value = "contract_no", comment = "合同号", queryItem = true)
    private String contractNo;

    @ExcelProperty("合同开始时间") @TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "合同开始时间", queryItem = true)
    private String dataDate;

    @ExcelProperty("核准贷款金额")
    @TableFieldMapping(value = "approval_loan_amt", comment = "核准贷款金额")
    private double approvalLoanAmt;

    @ExcelProperty("ECL")
    @TableFieldMapping(value = "ecl", comment = "ECL")
    private String ecl;
}
