package com.gwmfc.util;


import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.*;

/**
 * <AUTHOR>
 * @Classname MergeColumnCellWriteHandler
 * @Description TODO
 * @Date 2025/4/23 16:27
 */
public class MergeColumnCellWriteHandler extends AbstractMergeStrategy {

    private int[] mergeColumnIndexArray;

    // 记录mergeColumnIndexArray中每一列的上一行的值和索引
    private Map<Integer, ValueAndIndex> lastValueMap;

    /**
     * 用来记录上一次的值和索引，用于判断是否需要合并
     */
    @Data
    @AllArgsConstructor
    static class ValueAndIndex {
        private String value;
        private int index;
    }

    public MergeColumnCellWriteHandler(int[] mergeColumnIndexArray) {
        this.mergeColumnIndexArray = mergeColumnIndexArray;
        int initialCapacity = (int) Math.ceil(mergeColumnIndexArray.length / 0.75);
        lastValueMap = new HashMap<>(initialCapacity);
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        if (head == null) {
            return;
        }
        if (mergeColumnIndexArray == null) {
            return;
        }
        for (int i = 0; i < mergeColumnIndexArray.length; i++) {
            int mergeIndex = mergeColumnIndexArray[i];
            if (head.getColumnIndex() == mergeIndex) {
                String value = cell.getStringCellValue();
                int rowIndex = cell.getRowIndex();
                if (lastValueMap.get(mergeIndex) == null) {
                    lastValueMap.put(mergeIndex, new ValueAndIndex(value, rowIndex));
                }
                ValueAndIndex valueAndIndex = lastValueMap.get(mergeIndex);
                Integer lastRowIndex = valueAndIndex.getIndex();
                String lastValue = valueAndIndex.getValue();
                // 合并值相同的相邻单元格，当前单元格合并上一个单元格
                if (Objects.equals(value, lastValue) && lastRowIndex != rowIndex) {
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(
                            lastRowIndex, // 起始行
                            rowIndex,     // 结束行
                            mergeIndex,   // 起始列
                            mergeIndex   // 结束列
                    ));
                }
                lastValueMap.put(mergeIndex, new ValueAndIndex(value, rowIndex));
            }
        }
    }
}
