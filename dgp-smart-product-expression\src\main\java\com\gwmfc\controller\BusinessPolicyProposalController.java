package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.GlobalFormBusinessDto;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import com.gwmfc.vo.GlobalFormBusinessVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * 商务政策提案
 * <AUTHOR>
 * @date 2023年10月12日 13:40
 */
public class BusinessPolicyProposalController {
    /**
     * 新增
     *
     * @param saveDto
     * @return
     */
    @ApiOperation("通用业务表新增")
    @PostMapping("/add")
    public Result add(@RequestBody @Valid GlobalFormBusinessDto saveDto, @ApiIgnore @CurrentUser User user) {

        return Result.ok();
    }

    /**
     * 修改
     *
     * @return
     */
    @ApiOperation("通用业务表修改")
    @PostMapping("/update")
    public Result update(@RequestBody @Valid GlobalFormBusinessDto saveDto, @ApiIgnore @CurrentUser User user) {

        return Result.ok();
    }

    /**
     * 删除
     *
     * @return
     */
    @ApiOperation("单条删除")
    @GetMapping("/delete")
    public Result delete(@RequestParam Integer formId, @RequestParam Long recordId) {
        return Result.ok();
    }

    /**
     * 详情
     *
     * @param
     * @return
     */
    @ApiOperation(value = "详情", produces = "application/json")
    @GetMapping("/detail")
    public Result<List<String>> catchAllProvince() {
        Result result = new Result<>();

        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    /**
     * 列表查询
     *
     * @param pageForm
     * @return
     */
    @ApiOperation("通用业务表列表查询")
    @PostMapping("/list")
    public Result<GlobalFormBusinessVo> selectByPage(@RequestBody @Valid PageForm<GlobalFormBusinessDto> pageForm) throws IOException {
        Result<GlobalFormBusinessVo> result = new Result<>();
        result.setCode(SUCCESS.getCode());
        return result;
    }
}
