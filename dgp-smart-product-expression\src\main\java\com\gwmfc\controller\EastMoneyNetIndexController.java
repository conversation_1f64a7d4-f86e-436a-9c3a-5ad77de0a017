package com.gwmfc.controller;

import com.gwmfc.service.EastMoneyNetIndexService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Classname ProductDataCaptureController
 * @Description TODO
 * @Date 2024/10/31 15:40
 */
@Api(tags = "东方财富网指数接口")
@RestController
@RefreshScope
@RequestMapping("/eastMoney")
public class EastMoneyNetIndexController {
    @Autowired
    private EastMoneyNetIndexService eastMoneyNetIndexService;

    @ApiOperation("新增信贷指数获取")
    @GetMapping("/newCreditIndex/capture")
    public Result newCreditIndexCapture(){
        return eastMoneyNetIndexService.newCreditIndexCapture();
    }

    @ApiOperation("消费者信心指数获取")
    @GetMapping("/consumerConfidenceIndex/capture")
    public Result consumerConfidenceIndexCapture(){
        return eastMoneyNetIndexService.consumerConfidenceIndexCapture();
    }
}
