package com.gwmfc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.dto.CapitalDto;
import com.gwmfc.dto.UserAndDepartmentDto;
import com.gwmfc.entity.CapitalEntity;
import com.gwmfc.service.CapitalService;
import com.gwmfc.service.DingDingService;
import com.gwmfc.util.BeanListCopyUtil;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @date 2023年09月07日 15:25
 */
@Api(tags = "资方数据")
@RestController
@RefreshScope
@RequestMapping("/capital")
public class CapitalController {
    @Resource
    private CapitalService capitalService;

    @Resource
    private DingDingService dingDingService;
    /**
     * 文件目录
     */
    @Value("${allowed-deptId}")
    private String allowedDeptId;
    /**
     * 新增
     *
     * @param
     * @return
     */
    @ApiOperation("校验用户")
    @PostMapping(value = "/checkUser")
    public Result checkUser(@CurrentUser User user) {
        if (StringUtils.hasText(user.getDingNo())) {
            UserAndDepartmentDto userAndDepartmentDto = dingDingService.getUserAndDepartmentInfoForSmart(user.getDingNo());
            List<String> deptList = Arrays.asList(allowedDeptId.split(","));
            if (null != userAndDepartmentDto && userAndDepartmentDto.getDeptId() != null && deptList.contains(String.valueOf(userAndDepartmentDto.getDeptId()))) {
                return Result.ok();
            } else {
                return Result.error("用户没有权限访问！");
            }
        }
        return Result.error("用户没有权限访问！");
    }

    /**
     * 新增
     *
     * @param capitalDto
     * @return
     */
    @ApiOperation("通用业务表新增")
    @PostMapping(value = "/add", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result add(@RequestPart("capitalDto") CapitalDto capitalDto, @RequestPart(name = "attachmentList", required = false) List<MultipartFile> attachmentList, @CurrentUser User user) {
        UserAndDepartmentDto userAndDepartmentDto = dingDingService.getUserAndDepartmentInfoForSmart(user.getDingNo());
        capitalService.add(capitalDto, attachmentList, userAndDepartmentDto);
        return Result.ok();
    }

    /**
     * 修改
     */
    @ApiOperation("通用业务表修改")
    @PostMapping(value = "/update", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result update(@RequestPart("capitalDto") CapitalDto capitalDto, @RequestPart(name = "attachmentList", required = false) List<MultipartFile> attachmentList, @CurrentUser User user) {
        UserAndDepartmentDto userAndDepartmentDto = dingDingService.getUserAndDepartmentInfoForSmart(user.getDingNo());
        capitalService.update(capitalDto, attachmentList, userAndDepartmentDto);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @return
     */
    @ApiOperation("单条删除")
    @GetMapping("/delete")
    public Result delete(@RequestParam Long recordId) {
        capitalService.delete(recordId);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @return
     */
    @ApiOperation("批量删除")
    @PostMapping("/deleteBatch")
    public Result deleteBatch(@RequestBody List<Long> recordIdList) {
        capitalService.deleteBatch(recordIdList);
        return Result.ok();
    }

    /**
     * 列表查询
     *
     * @param pageForm
     * @return
     */
    @ApiOperation("通用业务表列表查询")
    @PostMapping("/list")
    public Result selectByPage(@RequestBody @Valid PageForm<CapitalDto> pageForm, @CurrentUser User user) {
        IPage<CapitalEntity> capitalEntityIPage = capitalService.queryPage(pageForm, user);
        Result<List<CapitalDto>> result = new Result<>();
        List<CapitalDto> productDataMartDtoList = BeanListCopyUtil.copyListProperties(capitalEntityIPage.getRecords(), CapitalDto::new);
        result.setData(productDataMartDtoList);
        result.setTotal(capitalEntityIPage.getTotal());
        result.setCode(200);
        return result;
    }

    /**
     * 详情查询接口
     *
     * @param
     * @return
     */
    @ApiOperation(value = "详情查询接口", produces = "application/json")
    @GetMapping("/detail")
    public Result<CapitalDto> detail(@RequestParam Long recordId, @RequestParam Boolean origin) {
        Result<CapitalDto> result = new Result<>();
        CapitalDto capitalDto = capitalService.detail(recordId, origin);
        result.setData(capitalDto);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    /**
     * 下载文件
     *
     * @param
     * @return
     */
    @ApiOperation(value = "下载附件接口", produces = "application/json")
    @GetMapping("/downloadFile")
    public void downloadFile(@RequestParam String fileName, @RequestParam String filePath, HttpServletResponse response) throws IOException {
        capitalService.downloadFile(fileName, filePath, response);
    }

    /**
     * 计算对客年利率
     *
     * @param
     * @return
     */
    @ApiOperation(value = "计算对客年利率", produces = "application/json")
    @GetMapping("/calPerAnnumRate")
    public Result calPerAnnumRate(@RequestParam Integer period, @RequestParam String value, @RequestParam String type) {
        Result result = new Result<>();
        Double res = capitalService.calPerAnnumRate(period, value, type);
        if (res != null) {
            result.setData(String.format("%.2f", res * 100.0));
            return result;
        }
        return Result.error("计算年利率错误！");
    }
}
