package com.gwmfc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gwmfc.dao.ProductProposalDao;
import com.gwmfc.dto.ProductProposalTemplateDto;
import com.gwmfc.dto.ProductProposalTemplateGroupDto;
import com.gwmfc.entity.ProductProposalEntity;
import com.gwmfc.entity.ProductProposalTemplateEntity;
import com.gwmfc.util.BeanListCopyUtil;
import com.gwmfc.util.GsonUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024年04月03日 17:00
 */
@SpringBootTest
public class GroupTest {
    @Resource
    private ProductProposalDao productProposalDao;

    @Test
    public void downloadByYearMonth() {
        ProductProposalEntity productProposalEntity = new ProductProposalEntity();
        QueryWrapper queryWrapper = new QueryWrapper<>(productProposalEntity);
        List<ProductProposalEntity> productProposalEntities = productProposalDao.selectList(queryWrapper);
        productProposalEntities.forEach(productProposalEntity1 -> {

        });
    }

//    @Test
//    public void suffle() {
//        ProductProposalEntity productProposalEntity = new ProductProposalEntity();
//        QueryWrapper queryWrapper = new QueryWrapper<>(productProposalEntity);
////        queryWrapper.isNull("product_proposal_template_group_json");
//        List<ProductProposalEntity> productProposalEntities = productProposalDao.selectList(queryWrapper);
//        productProposalEntities.forEach(productProposalEntity1 -> {
//            try {
//                List<ProductProposalTemplateGroupDto> productProposalTemplateGroupDtoList = new ArrayList<>();
//
//                List<ProductProposalTemplateDto> productProposalTemplateEntityList = GsonUtil.jsonToList(productProposalEntity1.getProductProposalTemplateJson(), ProductProposalTemplateDto.class);
//
//                ProductProposalTemplateGroupDto productProposalTemplateGroupDto = new ProductProposalTemplateGroupDto();
////                List<ProductProposalTemplateDto> productProposalTemplateDtoList = BeanListCopyUtil.copyListProperties(productProposalTemplateEntityList, ProductProposalTemplateDto::new);
//                productProposalTemplateGroupDto.setProductProposalTemplateList(productProposalTemplateEntityList);
//                productProposalTemplateGroupDto.setUuid(UUID.randomUUID().toString());
//                productProposalTemplateGroupDto.setWeightedOrNot(productProposalEntity1.getWeightedComputationOrNot());
//                productProposalTemplateGroupDtoList.add(productProposalTemplateGroupDto);
//                productProposalEntity1.setWeightedBetweenGroupsOrNot(0);
//                productProposalEntity1.setProductProposalTemplateGroupJson(GsonUtil.toJson(productProposalTemplateGroupDtoList));
//                productProposalDao.updateById(productProposalEntity1);
//            } catch (Exception e) {
//                e.printStackTrace();
//                System.out.println("1111111111111111111111111111111productProposalEntity1.getProductProposalTemplateJson():" + productProposalEntity1.getProductProposalTemplateJson());
//            }
//        });
//    }
}
