package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.domain.User;
import com.gwmfc.service.CpcaautoService;
import com.gwmfc.service.DomesticPriceIndexService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023年08月15日 14:02
 */
@Api(tags = "国内价格指数数据获取")
@RestController
@RequestMapping("/domestic/price")
public class DomesticPriceIndexController {
    @Resource
    private DomesticPriceIndexService domesticPriceIndexService;

    /**
     * 爬取月度销量排名
     *
     * @return
     */
    @ApiOperation("国内价格指数数据获取")
    @GetMapping("/catchDomesticPriceIndex")
    public Result catchDomesticPriceIndex(@RequestParam Integer frequency, @RequestParam String tableName, @CurrentUser User user) {
        domesticPriceIndexService.catchDomesticPriceIndex(frequency, tableName, user.getUserName());
        return Result.ok();
    }

}
