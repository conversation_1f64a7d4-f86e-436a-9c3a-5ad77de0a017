package com.gwmfc.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gwmfc.dao.EastMoneyNewCreditIndexDao;
import com.gwmfc.entity.data.EastMoneyNewCreditIndexEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Classname CreditIncreaseDataService
 * @Description TODO
 * @Date 2024/11/1 9:35
 */
@Service
@Slf4j
@RefreshScope
public class EastMoneyNewCreditIndexService extends ServiceImpl<EastMoneyNewCreditIndexDao, EastMoneyNewCreditIndexEntity> {
}
