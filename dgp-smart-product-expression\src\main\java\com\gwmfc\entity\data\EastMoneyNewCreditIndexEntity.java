package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname creditIncreaseDataEntity
 * @Description 信贷新增数据实体类
 * @Date 2024/10/31 17:23
 */
@Data
@TableName("east_money_new_credit_index")
@ExcelIgnoreUnannotated
public class EastMoneyNewCreditIndexEntity extends BaseEntity{
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期")@TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("当月(亿元)")
    @TableFieldMapping(value = "rmb_loan", comment = "当月(亿元)")
    private String rmbLoan;

    @ExcelProperty("同比增长")
    @TableFieldMapping(value = "rmb_loan_same", comment = "同比增长")
    private String rmbLoanSame;

    @ExcelProperty("环比增长")
    @TableFieldMapping(value = "rmb_loan_sequential", comment = "环比增长")
    private String rmbLoanSequential;

    @ExcelProperty("累计(亿元)")
    @TableFieldMapping(value = "rmb_loan_accumulate", comment = "累计(亿元)")
    private String rmbLoanAccumulate;

    @ExcelProperty("同比增长")
    @TableFieldMapping(value = "loan_accumulate_same", comment = "同比增长")
    private String loanAccumulateSame;

    @ExcelProperty("网页地址")
    @TableFieldMapping(value = "page_url", comment = "网页地址")
    private String pageUrl;
}
