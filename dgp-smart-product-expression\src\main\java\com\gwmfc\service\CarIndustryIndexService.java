package com.gwmfc.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.gwmfc.dao.CarIndustryDealerInventoryWarningIndexDao;
import com.gwmfc.domain.User;
import com.gwmfc.entity.CpcaautoCallRecordEntity;
import com.gwmfc.entity.data.CarIndustryDealerInventoryWarningIndexEntity;
import com.gwmfc.util.JsoupUtil;
import com.gwmfc.util.Result;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * <AUTHOR>
 * @Classname CarIndustryIndexService
 * @Description 汽车行业指数
 * @Date 2024/11/5 16:20
 */
@Service
@Slf4j
@RefreshScope
public class CarIndustryIndexService {

    @Value("${dataCapture.dealerInventoryWarningIndexUrl}")
    private String url;

    @Resource
    private JsoupUtil jsoupUtil;

    @Resource
    private GlobalFormBusinessService globalFormBusinessService;

    @Autowired
    private MonthlyUsedCarTransactionVolumeService monthlyUsedCarTransactionVolumeService;

    @Resource
    private CpcaautoService cpcaautoService;

    @Autowired
    private CarIndustryDealerInventoryWarningIndexDao carIndustryDealerInventoryWarningIndexDao;

    public Result dealerInventoryWarningIndexCapture(String date, User user){
        log.info("处理经销商库存预警指数开始，传入时间：{}",date);
        Result result = new Result();
        try{
            /**
             * 解析日期date得到指定的年月
             */
            String dateStr;
            String year;
            String month;
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            if(user == null || StringUtils.isEmpty(user.getUserName())){
                LocalDate localDate = LocalDate.parse(date,dateTimeFormatter);
                LocalDate localDateBefore = localDate.minusMonths(1);
                year = String.valueOf(localDateBefore.getYear());
                month = String.valueOf(localDateBefore.getMonth().getValue());

                if(localDateBefore.getMonth().getValue() < 10){
                    dateStr = year.concat("-0").concat(month);
                }else{
                    dateStr = year.concat("-").concat(month);
                }

            }else{
                dateStr = date;
                year = dateStr.substring(0,4);
                month = dateStr.substring(5,7);

                if(Integer.parseInt(month)<10){
                    month = month.substring(1,2);
                }
            }

            String dataDate = "data_date";
            Set<String> pageUrlSet = new HashSet();
            pageUrlSet.add(url);

            /**
             * 拿到所有符合title关键字描述的页码对应的url
             */
            Document pageNumDocument = jsoupUtil.connect(url);
            if(pageNumDocument.select(".pagebar").size()>0){
                Elements elements = pageNumDocument.select(".pagebar").get(1).select("a");
                elements.forEach(element -> {
                    if(StringUtils.isNotEmpty(element.attr("href"))){
                        pageUrlSet.add("http://www.cpcaauto.com/".concat(element.attr("href")));
                    }
                });
            }

            /**
             * 分析每页符合title关键字的title描述
             */
            for (String pageUrl : pageUrlSet) {
                Document documentListPage = jsoupUtil.connect(pageUrl);
                Elements links = documentListPage.select(".list_d").select("ul").select("li");
                for (Element link : links) {
                    boolean linkExitFlag = link.select("a").text().contains(year.concat("年").concat(month).concat("月"));
                    if(linkExitFlag){
                        long dataCount = globalFormBusinessService.selectRecordCountByDate("car_industry_dealer_inventory_warning_index",dateStr,dataDate);
                        if(dataCount>0){
                            log.info("该月:{}数据已存在",dateStr);
                            result.setCode(FAIL.getCode());
                            result.setMessage("该月数据已存在");
                            return result;
                        }
                        DateTimeFormatter dateTimeFormatterCpcaauto = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
                        /**
                         * 保存乘联会请求记录表信息
                         */
                        CpcaautoCallRecordEntity cpcaautoCallRecordEntity = new CpcaautoCallRecordEntity();
                        cpcaautoCallRecordEntity.setPageTitle(link.select("a").text());
                        cpcaautoCallRecordEntity.setPageUrl("http://www.cpcaauto.com/".concat(link.select("a").attr("href")));
                        cpcaautoCallRecordEntity.setCallTime(dateTimeFormatterCpcaauto.format(LocalDateTime.now()));
                        if(user == null || StringUtils.isEmpty(user.getUserName())){
                            cpcaautoCallRecordEntity.setTriggerMethod(1);
                            cpcaautoCallRecordEntity.setCreateUser("Scheduled");
                        }else{
                            cpcaautoCallRecordEntity.setTriggerMethod(2);
                            cpcaautoCallRecordEntity.setCreateUser(user.getUserName());
                        }
                        cpcaautoCallRecordEntity.setYear(year);
                        cpcaautoCallRecordEntity.setMonth(Integer.parseInt(month)<10?"0"+month:month);
                        cpcaautoCallRecordEntity.setSource("car_industry_dealer_inventory_warning_index");
                        cpcaautoService.save(cpcaautoCallRecordEntity);
                        log.info("保存乘联会请求记录表成功");

                        /**
                         * 保存经销商库存预警指数表
                         */
                        CarIndustryDealerInventoryWarningIndexEntity carIndustryDealerInventoryWarningIndexEntity = new CarIndustryDealerInventoryWarningIndexEntity();
                        carIndustryDealerInventoryWarningIndexEntity.setDataDate(dateStr);
                        carIndustryDealerInventoryWarningIndexEntity.setIndexName("经销商库存预警指数");
                        String pattern = "(\\d+\\.\\d+)%$";
                        Pattern r = Pattern.compile(pattern);
                        Matcher m = r.matcher(link.select("a").text());
                        if(m.find()){
                            carIndustryDealerInventoryWarningIndexEntity.setGrowthRate(m.group(1));
                        }
                        carIndustryDealerInventoryWarningIndexEntity.setPageUrl("http://www.cpcaauto.com/".concat(link.select("a").attr("href")));
                        carIndustryDealerInventoryWarningIndexEntity.setCreateUser(cpcaautoCallRecordEntity.getCreateUser());
                        Long createTime = Long.parseLong(cpcaautoCallRecordEntity.getCallTime());
                        carIndustryDealerInventoryWarningIndexEntity.setCreateTime(createTime);
                        carIndustryDealerInventoryWarningIndexDao.insert(carIndustryDealerInventoryWarningIndexEntity);
                        log.info("保存经销商库存预警指数表成功");
                    }
                }
            }
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            log.info("处理经销商库存预警指数成功");
            return result;
        }catch(Exception e){
            log.error("爬取经销商库存预警指数失败",e);
            result.setCode(FAIL.getCode());
            result.setMessage("爬取经销商库存预警指数失败");
            return result;
        }
    }
}
