package com.gwmfc.util;
import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;



public class FinancialFunctionUtil {

    private static Random rand;

    static {
        try {
            rand = SecureRandom.getInstanceStrong();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
    }

//    public static double IPMT(double r, int per, int nper, double pv, int type){
//        double ipmt =
//    }

    public static void PPMT(){

    }

    public static void IRR(){

    }

    public static void XIRR(){

    }

    public static double NPV(double rate,List<Double> values){
        double npv = 0;
        double counter = 0;
        for (double value : values) {
            npv += value / Math.pow(1+rate,++counter);
        }

        return BigDecimal.valueOf(npv).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static double PMT(double rate, double nper, double pv, double fv, double type) {
        if (rate == 0) {
            return -pv/nper;
        }
        double pmt = (pv*Math.pow(1 + rate, nper)+fv)/((Math.pow(1 + rate, nper)-1)/rate)/(1 + rate*type)*(-1);
        return BigDecimal.valueOf(pmt).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     *参考：https://support.microsoft.com/zh-cn/office/pv-%E5%87%BD%E6%95%B0-23879d31-0e02-4321-be01-da16e8168cbd
     *
     * PV 是一个财务函数，用于根据固定利率计算贷款或投资的现值。
     * 可以将 PV 与定期付款、固定付款（如按揭或其他贷款）或投资目标的未来值结合使用。
     *
     * @param rate  必需。 各期利率。 例如，如果您获得年利率为 10% 的汽车贷款，并且每月还款一次，则每月的利率为 10%/12（即 0.83%）。 您需要在公式中输入 10%/12（即 0.83%）或 0.0083 作为利率。
     * @param nper  必需。 年金的付款总期数。 例如，如果您获得为期四年的汽车贷款，每月还款一次，则贷款期数为 4*12（即 48）期。 您需要在公式中输入 48 作为 nper。
     * @param pmt   必需。 每期的付款金额，在年金周期内不能更改。 通常，pmt 包括本金和利息，但不含其他费用或税金。 例如，对于金额为 ￥100,000、利率为 12% 的四年期汽车贷款，每月付款为 ￥2633.30。 您需要在公式中输入 -2633.30 作为 pmt。 如果省略 pmt，则必须包括 fv 参数。
     * @param fv    可选。 未来值，或在最后一次付款后希望得到的现金余额。 如果省略 fv，则假定其值为 0（例如，贷款的未来值是 0）。 例如，如果要在 18 年中为支付某个特殊项目而储蓄 ￥500,000，则 ￥500,000 就是未来值。 然后，您可以对利率进行保守的猜测，并确定每月必须储蓄的金额。 如果省略 fv，则必须包括 pmt 参数。
     * @param type  可选。 数字 0 或 1，用以指定各期的付款时间是在期初还是期末。
     *
     * PS： pmt与fv至少出现一个！
     *
     * @return
     */
    public static double PV(double rate, double nper, double pmt, double fv, double type) {

        /**
         * 如果年化收益率为零，monthRate作分母会出现NaN错误，需要对年利率为零进行特殊处理
         */
        if (rate == 0) {
            return -fv;
        }
        double result = -(fv+pmt*(1 + rate*type)*((Math.pow(1 + rate, nper)-1)/rate))/Math.pow(1 + rate, nper);
        return BigDecimal.valueOf(result).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    // FV（rate，nper，pmt，pv，type）
    /**
     *参考：https://support.microsoft.com/zh-cn/office/fv-%E5%87%BD%E6%95%B0-2eef9f44-a084-4c61-bdd8-4fe4bb1b71b3
     *
     * FV 是一个财务函数，用于根据固定利率计算投资的未来值。
     *    可以将 FV 与定期付款、固定付款或一次付清总额付款结合使用。
     *
     * @param rate  必需。 各期利率。年利率，月利率=年利率/12
     * @param nper  必需。 年金的付款总期数。
     * @param pmt   必需。 各期所应支付的金额，在整个年金期间保持不变。 通常 pmt 包括本金和利息，但不包括其他费用或税款。 如果省略 pmt，则必须包括 pv 参数。
     * @param pv    可选。 现值，或一系列未来付款的当前值的累积和。 如果省略 pv，则假定其值为 0（零），并且必须包括 pmt 参数。
     * @param type  可选。 数字 0 或 1，用以指定各期的付款时间是在期初-1还是期末-0。 如果省略 type，则假定其值为 0。
     *
     * PS： pmt与fv至少出现一个！
     *
     * @return
     */
    public static double FV(double rate, double nper, double pmt, double pv, double type) {

        /**
         * 如果年化收益率为零，monthRate作分母会出现NaN错误，需要对年利率为零进行特殊处理
         */
        if (rate == 0) {
            return nper * pmt;
        }

        // "1 + (rate / 12)"的nper次幂
        BigDecimal powers = BigDecimal.valueOf(Math.pow(1 + (rate / 12), nper));

        double fv = -1*(pv*powers.doubleValue()+pmt*(1 + (rate / 12)*type)*((powers.doubleValue()-1)/(rate / 12)));
        return BigDecimal.valueOf(fv).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static List<BigDecimal> getRandomProportion(int count){
        List<Integer> integerList = new ArrayList<>(count);
        List<BigDecimal> bigDecimalList = new ArrayList<>(count);
        int sum = 0;
        for (int i=0;i<count;i++){
            int rand = getRandom(90, 110);
            sum += rand;
            integerList.add(rand);
        }
        for (int i = 0; i < integerList.size(); i++) {
            BigDecimal value = BigDecimal.valueOf(integerList.get(i)).divide(BigDecimal.valueOf(sum),8,BigDecimal.ROUND_HALF_UP);
            bigDecimalList.add(value);
        }
        return bigDecimalList;
    }
    public static int getRandom(int MIN, int MAX) {
        return rand.nextInt(MAX - MIN + 1) + MIN;
    }

}
