package com.gwmfc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

@Configuration
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "static")
@Data
@RefreshScope
public class WhiteUrlMapping {
    private Map<String, String> blackUrlMaps;
    private List<String> whiteUrlList;
    private List<String> ssoUrlList;
}
