package com.gwmfc.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@Component
@RefreshScope
@ConfigurationProperties("ftp")
public class FtpProperties {

    /**
     * 机器的ip
     */
    private String hostname;

    /**
     * 端口号
     */
    private int port;

    /**
     * 账号
     */
    private String username;

    /**
     * 密码
     */
    private String password;
}
