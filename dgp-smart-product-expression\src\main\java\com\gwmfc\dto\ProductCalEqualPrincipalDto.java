package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalSubsectionLendDto
 * @Description TODO
 * @Date 2023/10/26 15:17
 */
@Data
@ApiModel(value = "还款方式参数-本金等比分段贷dto")
public class ProductCalEqualPrincipalDto {
    @ApiModelProperty("每阶段期次")
    private Integer equalPrincipalPerStageTimeLimit;
    @ApiModelProperty("阶段本金比例（%）")
    private Double equalPrincipalRatio;
    @ApiModelProperty("阶段本金月供")
    private Double equalPrincipalMonthPayment;
    @ApiModelProperty("阶段数")
    private Integer equalPrincipalStage;
}
