package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据集市类
 */
@Data
@TableName("product_data_mart")
public class ProductDataMartEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 数据分类
     */
    private Integer dataCategory;

    /**
     * 表单名称
     */
    private String formName;

    /**
     * 有效标识
     */
    private Integer valid;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 更新频率
     */
    private Integer updateFrequency;

    /**
     * 数据获取方式
     */
    private Integer dataAcquisitionMethod;

    /**
     * 更新宽限期（天）
     */
    private Integer gracePeriod;

    /**
     * 最近更新时间
     */
    private String latestUpdateTime;

    /**
     * 数据联系人
     */
    private String contactPerson;

    /**
     * 是否及时更新
     */
    private Boolean updateInTime;

    /**
     * 数据源类型: 网站、邮箱、钉钉、其它
     */
    private Integer dataSourceType;

    /**
     * 单批导入多少条数据
     */
    private Long singleBatchCount;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateUser;

}
