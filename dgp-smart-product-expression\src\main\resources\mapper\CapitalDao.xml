<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.CapitalDao">

    <delete id="updateStatus">
        update
            `capital`
        set
            `status` = 3
        where
            id = #{delId}
    </delete>
    <select id="selectByOriginId" resultType="com.gwmfc.entity.CapitalEntity">
        select * from capital where origin_id = #{recordId} and status != 3 limit 1
    </select>
</mapper>