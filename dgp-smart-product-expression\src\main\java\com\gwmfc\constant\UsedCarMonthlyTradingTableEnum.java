package com.gwmfc.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * 流通协会二手车月交易量
 *
 * @Date: 2024/01/31
 * @Author: zhang<PERSON>yu
 */

@Getter
@AllArgsConstructor
public enum UsedCarMonthlyTradingTableEnum {

    TRADING_VOLUME {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("update_month", "更新月份");
            headMap.put("data_date", "数据日期");
            headMap.put("trading_volume", "交易量（万辆）");
            headMap.put("img_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    },
    REGIONAL_TRADING_DATA {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("update_month", "更新月份");
            headMap.put("data_date", "数据日期");
            headMap.put("region", "区域");
            headMap.put("trading_volume", "销量");
            headMap.put("img_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    },
    YEAR_TRADING_DATA {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("update_month", "更新月份");
            headMap.put("data_date", "数据日期");
            headMap.put("useful_life", "使用年限");
            headMap.put("trading_proportion", "交易量占比");
            headMap.put("img_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    },
    VEHICLE_TYPE_TRADING_VOLUME {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("update_month", "更新月份");
            headMap.put("data_date", "数据日期");
            headMap.put("car_type", "车型分类");
            headMap.put("vehicle_type", "车型");
            headMap.put("trading_volume", "交易量");
            headMap.put("img_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    },
    CROSS_REGIONAL_CIRCULATION_VOLUME {
        @Override
        public void getHeader(Map<String, String> headMap) {
            headMap.put("update_month", "更新月份");
            headMap.put("data_date", "数据日期");
            headMap.put("transfer_rate", "转籍率");
            headMap.put("img_url", "图片地址");
            headMap.put("create_time", "创建时间");
        }
    },
    ;

    public abstract void getHeader(Map<String, String> headMap);
}
