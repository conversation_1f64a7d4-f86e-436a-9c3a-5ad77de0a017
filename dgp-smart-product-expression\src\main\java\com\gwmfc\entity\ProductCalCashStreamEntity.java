package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldMapping;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductCalCashStreamEntity
 * @Description 计算结束后返回给前端的现金流dto
 * @Date 2023/9/19 10:59
 */
@Data
@TableName("product_cal_cash_stream")
public class ProductCalCashStreamEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableFieldMapping(value = "basic_info_id", comment = "基本参数id")
    private Long basicInfoId;

    @TableFieldMapping(value = "month", comment = "月")
    private Integer month;

    @TableFieldMapping(value = "cash_stream_date", comment = "日期")
    private String cashStreamDate;

    @TableFieldMapping(value = "cash_stream_incom", comment = "收")
    private Double cashStreamIncom;

    @TableFieldMapping(value = "cash_stream_expend", comment = "支")
    private Double cashStreamExpend;

    @TableFieldMapping(value = "cash_stream", comment = "现金流")
    private Double cashStream;

    @TableFieldMapping(value = "cash_stream_turnover_tax", comment = "流转税")
    private Double cashStreamTurnoverTax;

    @TableFieldMapping(value = "cash_stream_after_market_income", comment = "后市场收入")
    private Double cashStreamAfterMarketIncome;

    @TableFieldMapping(value = "cash_stream_loan_hand_charge", comment = "放款手续费")
    private Double cashStreamLoanHandCharge;

    @TableFieldMapping(value = "cash_stream_deduct_money_hand_charge", comment = "扣款手续费")
    private Double cashStreamDeductMoneyHandCharge;

    @TableFieldMapping(value = "cash_stream_other_change_cost", comment = "其他变动成本")
    private Double cashStreamOtherChangeCost;

    @TableFieldMapping(value = "cash_stream_special_reward", comment = "特殊奖励")
    private Double cashStreamSpecialReward;

    @TableFieldMapping(value = "cash_stream_badness", comment = "不良")
    private Double cashStreamBadness;

    @TableFieldMapping(value = "cash_stream_collection_fee_total", comment = "催收费用合计")
    private Double cashStreamCollectionFeeTotal;

    @TableFieldMapping(value = "cash_stream_outsourcing_collection_fee", comment = "外包催收费")
    private Double cashStreamOutsourcingCollectionFee;

//    @TableFieldMapping(value = "cash_stream_lawsuit_service_fee", comment = "诉讼服务费")
//    private Double cashStreamLawsuitServiceFee;

    @TableFieldMapping(value = "cash_stream_dz_outsourcing_fee", comment = "短账外包费")
    private Double cashStreamDzOutsourcingFee;

    @TableFieldMapping(value = "cash_stream_receivable_customer_interest", comment = "应收客户利息")
    private Double cashStreamReceivableCustomerInterest;

    @TableFieldMapping(value = "cash_stream_principal", comment = "本金")
    private Double cashStreamPrincipal;

    @TableFieldMapping(value = "cash_stream_actual_interest", comment = "实际利息")
    private Double cashStreamActualInterest;

    @TableFieldMapping(value = "cash_stream_actual_principal", comment = "实际本金")
    private Double cashStreamActualPrincipal;

    @TableFieldMapping(value = "cash_stream_remain_principal", comment = "剩余本金")
    private Double cashStreamRemainPrincipal;

    @TableFieldMapping(value = "cash_stream_remain_money", comment = "剩余金额")
    private Double cashStreamRemainMoney;

    @TableFieldMapping(value = "cash_stream_interest_expend", comment = "利息支出")
    private Double cashStreamInterestExpend;

    /**
     * 尾款贷使用
     */
    @TableFieldMapping(value = "cash_stream_current_custom_month_payment", comment = "当期客户月供")
    private Double cashStreamCurrentCustomMonthPayment;

    /**
     * 分段贷使用
     */
    @TableFieldMapping(value = "cash_stream_repayment_plan", comment = "还款计划")
    private Double cashStreamRepaymentPlan;

    @TableFieldMapping(value = "cash_stream_actual_remain_principal", comment = "实际剩余本金")
    private Double cashStreamActualRemainPrincipal;

    @TableFieldMapping(value = "cash_stream_early_square_hand_charge", comment = "提前结清终止手续费")
    private Double cashStreamEarlySquareHandCharge;

    @TableFieldMapping(value = "cash_stream_early_square_commission_deduct", comment = "提前结清佣金扣回")
    private Double cashStreamEarlySquareCommissionDeduct;

    @TableFieldMapping(value = "cash_stream_value_added_tax", comment = "增值税")
    private Double cashStreamValueAddedTax;

    @TableFieldMapping(value = "cash_stream_education_fee_append", comment = "教育费附加")
    private Double cashStreamEducationFeeAppend;

    @TableFieldMapping(value = "cash_stream_interest_expend_share", comment = "利息支出分摊")
    private Double cashStreamInterestExpendShare;

    @TableFieldMapping(value = "cash_stream_manage_fee_share", comment = "管理费分摊")
    private Double cashStreamManageFeeShare;

    @TableFieldMapping(value = "cash_stream_interest_incom", comment = "利息收入")
    private Double cashStreamInterestIncom;

    @TableFieldMapping(value = "cash_stream_sub_interest", comment = "贴息收入")
    private Double cashStreamSubInterest;

    @TableFieldMapping(value = "cash_stream_commission_share", comment = "佣金摊销")
    private Double cashStreamCommissionShare;

    @TableFieldMapping(value = "cash_stream_provision", comment = "拨备")
    private Double cashStreamProvision;

    @TableFieldMapping(value = "cash_stream_profit", comment = "利润")
    private Double cashStreamProfit;

    @TableFieldMapping(value = "present_value", comment = "现值")
    private Double presentValue;

    @TableFieldMapping(value = "discount_rate", comment = "折现率")
    private Double discountRate;

    @TableFieldMapping(value = "post_floating_discount_rate", comment = "浮动后折现率")
    private Double postFloatingDiscountRate;

    @TableFieldMapping(value = "cash_stream_union_loan_customer_principal", comment = "联合贷客户本金")
    private Double cashStreamUnionLoanCustomerPrincipal;

    @TableFieldMapping(value = "cash_stream_union_loan_customer_interest", comment = "联合贷客户利息")
    private Double cashStreamUnionLoanCustomerInterest;

    @TableFieldMapping(value = "cash_stream_union_loan_bank_principal", comment = "联合贷银行本金")
    private Double cashStreamUnionLoanBankPrincipal;

    @TableFieldMapping(value = "cash_stream_union_loan_bank_interest", comment = "联合贷银行利息")
    private Double cashStreamUnionLoanBankInterest;

    @TableFieldMapping(value = "cash_stream_union_loan_bank_return_interest", comment = "联合贷银行返回利息")
    private Double cashStreamUnionLoanBankReturnInterest;

    @TableFieldMapping(value = "cash_stream_union_loan_early_square_bank_return_interest", comment = "联合贷银行考虑提前结清返回利息")
    private Double cashStreamUnionLoanEarlySquareBankReturnInterest;

    @TableFieldMapping(value = "cash_stream_union_loan_month_payment", comment = "联合贷月供")
    private Double cashStreamUnionLoanMonthPayment;

    @TableFieldMapping(value = "cash_stream_stamp_tax", comment = "现金流印花税")
    private Double cashStreamStampTax;

    @TableFieldMapping(value = "cash_stream_loan_money", comment = "现金流放款")
    private Double cashStreamLoanMoney;

    @TableFieldMapping(value = "cash_stream_service_fee", comment = "现金流服务费")
    private Double cashStreamServiceFee;

    @TableFieldMapping(value = "cash_stream_month_payment", comment = "现金流月供")
    private Double cashStreamMonthPayment;

    @TableFieldMapping(value = "cash_stream_early_square_month_payment", comment = "现金流提前结清月供")
    private Double cashStreamEarlySquareMonthPayment;

    @TableFieldMapping(value = "cash_stream_early_square_principal", comment = "现金流提前结清本金")
    private Double cashStreamEarlySquarePrincipal;

    @TableFieldMapping(value = "cash_stream_actual_interest_principal", comment = "现金流实际利率本金")
    private Double cashStreamActualInterestPrincipal;

    @TableFieldMapping(value = "create_time", comment = "创建日期")
    private Date createTime;

    @TableFieldMapping(value = "create_user", comment = "创建人")
    private String createUser;

    @TableFieldMapping(value = "update_time", comment = "修改日期")
    private Date updateTime;

    @TableFieldMapping(value = "update_user", comment = "修改人")
    private String updateUser;

}
