package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname RepaymentMethodParamDto
 * @Description 还款方式参数
 * @Date 2023/9/19 14:23
 */
@Data
@ApiModel(value = "还款方式参数")
public class ProductCalRepaymentMethodParamDto {

    @ApiModelProperty("打包车价")
    private Double packageCarPrice;

    @ApiModelProperty("首付比例(%)")
    private Double downpaymentsRatio;

    @ApiModelProperty("首付金额")
    private Double downpaymentsMoney;

    @ApiModelProperty("尾款比例(%)")
    private Double balanceRatio;

    @ApiModelProperty("尾款金额")
    private Double balanceMoney;

    @ApiModelProperty("动态分段贷参数")
    private List<ProductCalSubsectionLendDto> productCalSubsectionLendDtoList;

    @ApiModelProperty("特殊期限")
    private Integer specialTimeLimit;

    @ApiModelProperty("特殊期限月供")
    private Double specialTimeLimitMonthPayment;

    @ApiModelProperty("月供下降比例")
    private Double decreaseInMonthlyPaymentRatio;

    @ApiModelProperty("每阶段期限")
    private Integer periodTimeLimit;

    @ApiModelProperty("本金等比分段贷动态参数")
    private List<ProductCalEqualPrincipalDto> productCalEqualPrincipalDtoList;
}
