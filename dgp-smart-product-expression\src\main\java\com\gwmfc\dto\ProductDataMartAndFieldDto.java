package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年11月23日 11:04
 */
@Data
@ApiModel(value = "表名及字段详情")
public class ProductDataMartAndFieldDto {
    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("名称")
    private String formName;

    @ApiModelProperty("表名")
    private String tableName;

    @ApiModelProperty("字段详情")
    private List<TableFieldDetailDto> list;
}