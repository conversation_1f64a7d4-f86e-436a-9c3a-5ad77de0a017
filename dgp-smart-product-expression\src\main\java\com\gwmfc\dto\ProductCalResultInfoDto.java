package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ResultInfoDto
 * @Description 计算结果信息
 * @Date 2023/9/19 11:27
 */
@Data
@ApiModel(value = "计算结果信息")
public class ProductCalResultInfoDto {

    @ApiModelProperty("IRR")
    private Double resultIrr;

    @ApiModelProperty("XIRR")
    private Double resultXirr;

    @ApiModelProperty("净利润额")
    private Double resultNetProfitMoney;

    @ApiModelProperty("边际利润")
    private Double resultMarginalProfit;

    @ApiModelProperty("税前收益")
    private Double resultBeforeTaxIncome;

    @ApiModelProperty("ROA(%)")
    private Double resultRoa;

    @ApiModelProperty("万元收益")
    private Double resultPerTenThousandIncome;

    @ApiModelProperty("我司全额贷款Irr")
    private Double resultOurFullLoanIrr;

    @ApiModelProperty("我司全额贷款净利润")
    private Double resultOurFullLoanNetProfitMoney;

    @ApiModelProperty("我司全额贷款边际利润")
    private Double resultOurFullLoanMarginalProfit;

    @ApiModelProperty("我司全额贷款Xirr")
    private Double resultOurFullLoanXirr;

    @ApiModelProperty("我司全额贷款Roa")
    private Double resultOurFullLoanRoa;

    @ApiModelProperty("综合Irr")
    private Double resultIntegrativeIrr;

    @ApiModelProperty("综合净利润")
    private Double resultIntegrativeNetProfitMoney;

    @ApiModelProperty("综合边际利润")
    private Double resultIntegrativeMarginalProfit;

    @ApiModelProperty("综合Xirr")
    private Double resultIntegrativeXirr;

    @ApiModelProperty("综合ROA")
    private Double resultIntegrativeRoa;
}
