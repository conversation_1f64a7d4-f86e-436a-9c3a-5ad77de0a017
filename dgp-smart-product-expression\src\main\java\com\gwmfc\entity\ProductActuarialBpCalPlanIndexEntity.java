package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Classname ProductActuarialBpCalPlanIndexEntity
 * @Description TODO
 * @Date 2025/4/21 13:45
 */
@Data
@TableName("product_actuarial_bp_cal_plan_index")
public class ProductActuarialBpCalPlanIndexEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "cal_id")
    @ApiModelProperty("测算id")
    private Long calId;

    @TableField(value = "average_loan_money_start_date")
    @ApiModelProperty("平均贷款金额开始日期")
    private String averageLoanMoneyStartDate;

    @TableField(value = "average_loan_money_end_date")
    @ApiModelProperty("平均贷款金额结束日期")
    private String averageLoanMoneyEndDate;

    @TableField(value = "customer_rate_start_date")
    @ApiModelProperty("客户利率开始日期")
    private String customerRateStartDate;

    @TableField(value = "customer_rate_end_date")
    @ApiModelProperty("客户利率结束日期")
    private String customerRateEndDate;

    @TableField(value = "actual_rate_start_date")
    @ApiModelProperty("实际利率开始日期")
    private String actualRateStartDate;

    @TableField(value = "actual_rate_end_date")
    @ApiModelProperty("实际利率结束日期")
    private String actualRateEndDate;

    @TableField(value = "loss_rate_start_date")
    @ApiModelProperty("损失率开始日期")
    private String lossRateStartDate;

    @TableField(value = "loss_rate_end_date")
    @ApiModelProperty("损失率结束日期")
    private String lossRateEndDate;

    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField(value = "create_user")
    @ApiModelProperty("创建人")
    private String createUser;

    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @TableField(value = "update_user")
    @ApiModelProperty("更新人")
    private String updateUser;
}
