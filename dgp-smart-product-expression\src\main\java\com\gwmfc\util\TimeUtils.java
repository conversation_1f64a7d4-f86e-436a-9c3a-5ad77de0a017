package com.gwmfc.util;

import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Calendar;

/**
 * 时间工具类
 *
 * @className: TimeUtil
 * @date: 2022-04-13 09:56
 * @author: feijunhui
 */
public class TimeUtils {

    private TimeUtils() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * yyyyMMdd
     */
    public static final String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";

    /**
     * yyyy-MM-dd
     */
    public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 时间戳格式化
     *
     * @param time    毫秒时间戳
     * @param pattern 要转换的格式
     * @return
     */
    public static String format(long time, String pattern) {
        return new DateTime(time).toString(pattern);
    }

    /**
     * 时间戳转换成yyyyMMdd(int)值
     *
     * @param time
     * @return
     */
    public static int timeToDay(long time) {
        return Integer.parseInt(format(time, DATE_FORMAT_YYYYMMDD));
    }

    /**
     * 获取今天日期yyyyMMdd
     *
     * @return
     */
    public static int getToday() {
        return timeToDay(System.currentTimeMillis());
    }

    /**
     * 日期转换成时间戳
     *
     * @param day 日期，格式是 20191103
     * @return
     */
    public static long dayToTime(int day) {
        return DateTimeFormat.forPattern(DATE_FORMAT_YYYYMMDD).parseDateTime(String.valueOf(day)).getMillis();
    }

    /**
     * 日期增加或者减少nums天
     *
     * @param day  日期，格式是 20191103
     * @param nums 要增加或者减少的天数
     * @return
     */
    public static int plusDay(int day, int nums) {
        long time = dayToTime(day);
        return timeToDay(time + Duration.ofDays(1).toMillis() * nums);
    }

    /**
     * 获取某一天的开始时间戳
     *
     * @param plusDays 正数表示几天后，负数表示几天前
     * @return
     */
    public static long getStartTime(int plusDays) {
        return new DateTime().plusDays(plusDays).withTimeAtStartOfDay().getMillis();
    }

    /**
     * 获取某几天之前或者之后的日期，格式是 yyyyMMdd
     *
     * @param days 正数表示几天后，负数表示几天前
     * @return
     */
    public static int plusDay(int days) {
        return timeToDay(getStartTime(days));
    }

    /**
     * 获取两个日期相差多少天
     * @param startDay
     * @param endDay
     * @return
     */
    public static int getDelta(int startDay, int endDay) {
        DateTime startTime = new DateTime(format(dayToTime(startDay), DATE_FORMAT_YYYY_MM_DD));
        DateTime endTime = new DateTime(format(dayToTime(endDay), DATE_FORMAT_YYYY_MM_DD));
        return Days.daysBetween(startTime, endTime).getDays();
    }

    /**
     * 返回上delta个周的自然月的第一天
     * (20220210,-1)-> 20220101
     * (20220210,0) -> 20220201
     * (20220210,1) -> 20220301
     *
     * @param day YYYYMMDD
     * @param delta 要增加或者减少的月数
     * @return 自然月开始的日期(YYYYMMDD)
     */
    public static int getDeltaMonthStartDay(int day, int delta) {
        DateTime dateTime = new DateTime(format(dayToTime(day), DATE_FORMAT_YYYY_MM_DD)).plusMonths(delta);
        return Integer.parseInt(dateTime.toString("yyyyMM01"));
    }

    /**
     * 返回上delta个周的自然月的最后一天
     * (20220210,-1)-> 20220131
     * (20220210,0) -> 20220228
     * (20220210,1) -> 20220331
     *
     * @param day YYYYMMDD
     * @param delta 要增加或者减少的月数
     * @return 自然月开始的日期(YYYYMMDD)
     */
    public static int getDeltaMonthEndDay(int day, int delta) {
        int firstDay = getDeltaMonthStartDay(day, delta);
        return Integer.parseInt(new DateTime(format(dayToTime(firstDay), DATE_FORMAT_YYYY_MM_DD)).plusMonths(1).minusDays(1).toString(DATE_FORMAT_YYYYMMDD));
    }

    /**
     * 获取上季度最后一天
     * @return
     */
    public static int getLastQuarterLastDay() {
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTimeInMillis(System.currentTimeMillis());
        endCalendar.set(Calendar.MONTH, (endCalendar.get(Calendar.MONTH) / 3 - 1) * 3 + 2);
        endCalendar.set(Calendar.DAY_OF_MONTH, endCalendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return timeToDay(endCalendar.getTime().getTime());
    }

    /**
     * 获取去年最后一天
     * @return
     */
    public static int getLastYearLastDay() {
        Calendar calendar = Calendar.getInstance();
        int currentYear = calendar.get(Calendar.YEAR);
        calendar.clear();
        calendar.set(Calendar.YEAR, currentYear - 1);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        return timeToDay(calendar.getTime().getTime());
    }

    /**
     * 获取本年一月最后一天
     * @return
     */
    public static int getCurrentYearFirstMonthEndDay() {
        int lastYearLastDay = getLastYearLastDay();
        return getDeltaMonthEndDay(lastYearLastDay, 1);
    }

    /**
     * 获取当天的00:00:00
     *
     *
     * @return
     */
    public static LocalDateTime getDayStart(LocalDateTime time) {
        return time.with(LocalTime.MIN);
    }

    /**
     * 获取当天的23:59:59
     *
     *
     * @return
     */
    public static  LocalDateTime getDayEnd(LocalDateTime time) {
        return time.with(LocalTime.MAX);
    }


}
