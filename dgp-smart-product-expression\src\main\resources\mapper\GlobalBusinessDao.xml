<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.GlobalBusinessDao">

    <!-- 查询总条数 -->
    <select id="selectTotalCountByTableName" resultType="long">
        select count(*)
        from ${tableName}
    </select>

    <!-- 分页查询 -->
    <select id="selectRecordByPage" resultType="java.util.Map">
        select * from ${queryDto.tableName}
        where data_date is not null
        <if test="queryDto.conditionParams != null and queryDto.conditionParams != ''">
            <foreach collection="queryDto.conditionParams" item="value" index="key">
                <if test="key != 'start_data_date' and key != 'end_data_date' and !key.contains('@'.toString()) and value != null and value != ''">
                    and ${key} like CONCAT('%', #{value}, '%')
                </if>
                <if test="key != 'start_data_date' and key != 'end_data_date' and key.contains('@'.toString()) and value != null and value.size > 0">
                    and  <trim prefixOverrides ="@">${key}</trim>  in
                    <foreach collection="value" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryDto.updateFrequency == 3 or queryDto.updateFrequency == 4">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'-01'),'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'-01'),'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
                <if test="queryDto.updateFrequency == 6">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'-01-01'),'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'-01-01'),'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
                <if test="queryDto.updateFrequency != 3 and queryDto.updateFrequency != 6 and queryDto.updateFrequency != 4">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(data_date,'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(data_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
            </foreach>
        </if>
        order by data_date DESC,create_time desc,id asc
        limit ${current},${size}
    </select>

    <!-- 分页查询 -->
    <select id="selectRecordByCount" resultType="java.lang.Long">
        select count(*) from ${tableName}
        where 1=1
        <if test="conditionParams != null and conditionParams != ''">
            <foreach collection="conditionParams" item="value" index="key">
                <if test="key != 'start_data_date' and key != 'end_data_date' and !key.contains('@'.toString()) and value != null and value != ''">
                    and ${key} like CONCAT('%', #{value}, '%')
                </if>
                <if test="key != 'start_data_date' and key != 'end_data_date' and key.contains('@'.toString()) and value != null and value.size > 0">
                    and  <trim prefixOverrides ="@">${key}</trim>  in
                    <foreach collection="value" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="updateFrequency == 3 or updateFrequency == 4">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'-01'),'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'-01'),'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
                <if test="updateFrequency == 6">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'01-01'),'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'01-01'),'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
                <if test="updateFrequency != 3 and updateFrequency != 6">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(data_date,'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(data_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
            </foreach>
        </if>
        order by data_date DESC,create_time desc
    </select>

    <select id="selectRecordByPageCursor" resultType="java.util.Map">
        select * from ${queryDto.tableName}
        where  <![CDATA[
               id > #{id}
            ]]>
        <if test="queryDto.conditionParams != null and queryDto.conditionParams != ''">
            <foreach collection="queryDto.conditionParams" item="value" index="key">
                <if test="key != 'start_data_date' and key != 'end_data_date' and !key.contains('@'.toString()) and value != null and value != ''">
                    and ${key} like CONCAT('%', #{value}, '%')
                </if>
                <if test="key != 'start_data_date' and key != 'end_data_date' and key.contains('@'.toString()) and value != null and value != ''">
                    and  <trim prefixOverrides ="@">${key}</trim>  in
                    <foreach collection="value" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryDto.updateFrequency == 3 or queryDto.updateFrequency == 4">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'-01'),'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'-01'),'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
                <if test="queryDto.updateFrequency == 6">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'01-01'),'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'01-01'),'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
                <if test="queryDto.updateFrequency != 3 and queryDto.updateFrequency != 6 and queryDto.updateFrequency != 4">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(data_date,'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(data_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
            </foreach>
        </if>
        order by id asc
        limit ${size}
    </select>


    <!-- 查询条数 -->
    <select id="selectRecordCount" resultType="long">
        select count(*) from ${tableName}
        where data_date is not null
        <if test="conditionParams != null and conditionParams != ''">
            <foreach collection="conditionParams" item="value" index="key">
                <if test="key != 'start_data_date' and key != 'end_data_date' and !key.contains('@'.toString()) and value != null and value != ''">
                    and ${key} like CONCAT('%', #{value}, '%')
                </if>
                <if test="key != 'start_data_date' and key != 'end_data_date' and key.contains('@'.toString()) and value != null and value.size > 0">
                    and  <trim prefixOverrides ="@">${key}</trim>  in
                    <foreach collection="value" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="updateFrequency == 3 or updateFrequency == 4">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'-01'),'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'-01'),'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
                <if test="updateFrequency == 6">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'01-01'),'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(concat(data_date,'01-01'),'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
                <if test="updateFrequency != 3 and updateFrequency != 6">
                    <if test="key == 'start_data_date' and value != null and value != ''">
                        and DATE_FORMAT(data_date,'%Y-%m-%d') &gt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                    <if test="key == 'end_data_date' and value != null and value != ''">
                        and DATE_FORMAT(data_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{value},'%Y-%m-%d')
                    </if>
                </if>
            </foreach>
        </if>
    </select>

    <!--新增-->
    <insert id="addData" parameterType="java.util.Map" useGeneratedKeys="true">
        insert into ${tableName} ( id,
        <foreach collection="columnMap" item="value" index="key" separator=",">
            <if test="key != 'id' and value != null">
                ${key}
            </if>
        </foreach>
        ) values ( 0,
        <foreach collection="columnMap" item="value" index="key" separator=",">
            <if test="key != 'id' and value != null">
                #{value}
            </if>
        </foreach>
        )
    </insert>

    <!--新增-->
    <insert id="batchAddData" parameterType="java.util.Map" useGeneratedKeys="true">
        insert into ${tableName} (
        <foreach collection="columnSet" item="value" index="key" separator=",">
            <if test="value != 'id' and value != null">
                ${value}
            </if>
        </foreach>
        ) values
        <foreach collection="paramsMapList" item="paramsMap" index="index" separator=",">
            <foreach collection="paramsMap" item="value" index="key" open="(" separator="," close=")">
                <if test="key != 'id'">
                    #{value}
                </if>
            </foreach>
        </foreach>
    </insert>

    <!-- 更新数据 -->
    <update id="updateData" parameterType="java.util.Map">
        update ${tableName}
        <set>
            <foreach collection="columnMap" item="value" index="key" separator=",">
                <if test="key != 'id'">
                    ${key} = #{value}
                </if>
            </foreach>
        </set>
        where id = #{columnMap.id}
    </update>

    <!-- 根据id查询 -->
    <select id="queryById" resultType="java.util.Map">
        select *
        from ${tableName}
        where id = #{id}
    </select>

    <select id="sumBatchNum" resultType="java.lang.Integer">
        select count(*)
        from ${tableName}
        where create_time = #{batch}
    </select>

    <select id="selectGroupValueByField" resultType="java.lang.String">
        select ${name} from ${tableName} e group by ${name}
    </select>

    <select id="selectRecordCountByDate" resultType="java.lang.Long">
        select count(*)
        from ${tableName}
        where ${dataDate} = #{dateStr}
    </select>

    <delete id="delete">
        delete
        from ${tableName}
        where id
                  =
              #{id}
    </delete>

    <delete id="deleteBatch">
        delete from
        ${tableName}
        where
        id in
        <foreach collection="recordIdList" item="recordId" separator="," open="(" close=")">
            #{recordId}
        </foreach>;
    </delete>

    <delete id="deleteByCreateTime">
        delete
        from ${tableName}
        where create_time = #{batch}
    </delete>

    <delete id="truncateTable">
        TRUNCATE TABLE ${tableName};
    </delete>
</mapper>