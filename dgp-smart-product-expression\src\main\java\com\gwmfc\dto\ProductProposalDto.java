package com.gwmfc.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gwmfc.bo.UploadFileBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023年10月12日 15:05
 */
@Data
@ApiModel(value = "产品提案")
public class ProductProposalDto {
    @TableId(type = IdType.AUTO)
    private Long id;
    @ApiModelProperty("业务类型")
    private Integer businessType;
    @ApiModelProperty("产品提案名称")
    private String name;
    @ApiModelProperty("市场分析附件")
    private List<UploadFileBo> marketAnalysisAnnex;
    @ApiModelProperty("产品提案附件")
    private List<UploadFileBo> productProposalAnnex;

    @ApiModelProperty("是否组间加权")
    private Integer weightedBetweenGroupsOrNot;
    @ApiModelProperty("提案组实体")
    private List<ProductProposalTemplateGroupDto> productProposalTemplateGroupDtoList;

    @ApiModelProperty("产品要素说明")
    private String productElementsDescription;
    @ApiModelProperty("风控策略说明")
    private String riskControlStrategyDescription;
    @ApiModelProperty("商务政策说明")
    private String businessPolicyDescription;
    @ApiModelProperty("当前步骤")
    private String currentStep;
    @ApiModelProperty("退回关联ID（退回步骤关联ID）")
    private Long resetId;

    @ApiModelProperty("通过状态（1：通过；-1：拒绝）只有页面查询用")
    private Integer status;
    @ApiModelProperty("审核类型（1：风险；2：商务；3：财务）只有页面查询用")
    private Integer type;
    @ApiModelProperty("利润汇总")
    private List<ProductCalProfitCollectDto> productCalProfitCollectDtoList;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate startTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate endTime;

    @ApiModelProperty("审批时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人(提案人)")
    private String createUser;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 是否进行了风险测算
     */
    @ApiModelProperty("是否进行了风险测算")
    private Boolean riskMeasureOrNot;

    @ApiModelProperty("IRR测算结果说明")
    private String irrDescription;

    @ApiModelProperty("销量预测说明")
    private String salesForecastDescription;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateUser;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProductProposalDto that = (ProductProposalDto) o;
        return Objects.equals(id, that.id) && Objects.equals(businessType, that.businessType) && Objects.equals(name, that.name) && Objects.equals(marketAnalysisAnnex, that.marketAnalysisAnnex) && Objects.equals(productProposalTemplateGroupDtoList, that.productProposalTemplateGroupDtoList) && Objects.equals(productElementsDescription, that.productElementsDescription) && Objects.equals(riskControlStrategyDescription, that.riskControlStrategyDescription) && Objects.equals(businessPolicyDescription, that.businessPolicyDescription) && Objects.equals(currentStep, that.currentStep) && Objects.equals(resetId, that.resetId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, businessType, name, marketAnalysisAnnex, productProposalTemplateGroupDtoList, productElementsDescription, riskControlStrategyDescription, businessPolicyDescription, currentStep, resetId);
    }
}
