<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.gwmfc</groupId>
    <artifactId>dgp-smart-product</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-RELEASE</version>
    <name>dgp-smart-product</name>
    <modules>
        <module>dgp-smart-product-gateway</module>
        <module>dgp-smart-product-common</module>
        <module>dgp-smart-product-system</module>
        <module>dgp-smart-product-expression</module>
    </modules>

    <properties>
        <mvn.url>http://10.50.133.122:7081/nexus/repository/gwmfc-releases/</mvn.url>
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring.cloud.version>2020.0.2</spring.cloud.version>
        <spring.cloud.alibaba.version>2020.0.RC1</spring.cloud.alibaba.version>
        <spring.cloud.gateway.version>3.0.2</spring.cloud.gateway.version>
        <spring.cloud.loadbalancer.version>3.0.2</spring.cloud.loadbalancer.version>
        <spring.cloud.nacos.version>2020.0.RC1</spring.cloud.nacos.version>
        <mybatis.plus.version>3.4.2</mybatis.plus.version>
        <mybatis.dynamic.datasource.version>3.3.6</mybatis.dynamic.datasource.version>
        <swagger.version>2.9.2</swagger.version>
        <swagger.bootstrap.ui.version>1.9.6</swagger.bootstrap.ui.version>
        <gson.version>2.8.1</gson.version>
        <redis.version>2.4.5</redis.version>
        <presto.version>0.252</presto.version>
        <mustache.version>0.9.7</mustache.version>
        <jwt.version>3.3.0</jwt.version>
        <ojdbc.version>11.2.0.4</ojdbc.version>
        <ms.sqlserver>9.2.1.jre8</ms.sqlserver>
        <shiro.version>1.6.0</shiro.version>
        <mongo.driver.version>4.2.3</mongo.driver.version>
        <hadoop.version>3.0.0</hadoop.version>
        <flink.version>1.12.0</flink.version>
    </properties>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.4.4</version>
    </parent>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-gateway</artifactId>
                <version>${spring.cloud.gateway.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-loadbalancer</artifactId>
                <version>${spring.cloud.loadbalancer.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>3.0.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring.cloud.nacos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring.cloud.nacos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${mybatis.dynamic.datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>${swagger.bootstrap.ui.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${redis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </dependency>

            <dependency>
                <groupId>com.facebook.presto</groupId>
                <artifactId>presto-jdbc</artifactId>
                <version>${presto.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.spullara.mustache.java</groupId>
                <artifactId>compiler</artifactId>
                <version>${mustache.version}</version>
            </dependency>

            <dependency>
                <groupId> com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc6</artifactId>
                <version>${ojdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${ms.sqlserver}</version>
            </dependency>

            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>5.3</version>
            </dependency>

            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-spring</artifactId>
                <version>${shiro.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hive</groupId>
                <artifactId>hive-jdbc</artifactId>
                <version>2.1.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.eclipse.jetty.aggregate</groupId>
                        <artifactId>jetty-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty.orbit</groupId>
                        <artifactId>javax.servlet</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>0.1.54</version>
            </dependency>

            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-sync</artifactId>
                <version>${mongo.driver.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-yarn-client</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-common</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-hdfs</artifactId>
                <version>${hadoop.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-clients_2.11</artifactId>
                <version>${flink.version}</version>
            </dependency>


            <dependency>
                <groupId>org.apache.flink</groupId>
                <artifactId>flink-yarn_2.11</artifactId>
                <version>${flink.version}</version>
            </dependency>


            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.20</version>
                <scope>compile</scope>
            </dependency>


            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>5.0.1</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>gwmfc</id>
             <url>${mvn.url}</url>
        </repository>

        <snapshotRepository>
            <id>gwmfc</id>
            <url>http://10.50.133.122:7081/nexus/repository/gwmfc-snapshots/</url>
        </snapshotRepository>


    </distributionManagement>

    <build>
            <plugins>
                <plugin>
                    <groupId>org.sonarsource.scanner.maven</groupId>
                    <artifactId>sonar-maven-plugin</artifactId>
                    <version>3.7.0.1746</version>
                </plugin>
            </plugins>
    </build>

</project>
