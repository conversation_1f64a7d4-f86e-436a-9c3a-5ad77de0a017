package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.bo.UploadFileBo;
import com.gwmfc.domain.User;
import com.gwmfc.dto.ProductCalRiskLossDto;
import com.gwmfc.dto.ProductCalculateGroupDto;
import com.gwmfc.dto.ProductCalculateRangeDto;
import com.gwmfc.dto.ProductProposalHistoryQueryDto;
import com.gwmfc.service.ProductProposalCalService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;


/**
 * <AUTHOR>
 * @Classname ProductProposalCalController
 * @Description TODO
 * @Date 2023/10/18 17:17
 */
@Api(tags = "产品提案计算部分")
@RestController
@RequestMapping("/product/proposal/calculate")
@Slf4j
public class ProductProposalCalController {

    @Autowired
    private ProductProposalCalService productProposalCalService;

    @ApiOperation("计算IRR")
    @PostMapping("/calculateIrr")
    public Result calculateIrr(@RequestBody ProductCalculateRangeDto productCalculateRangeDto){
        return productProposalCalService.calculateIrr(productCalculateRangeDto);
    }

    @ApiOperation("查询历史数据，计算相关数据均值")
    @PostMapping("/getHistoryProductAvgValue")
    public Result getHistoryProductAvgValue(@RequestBody ProductProposalHistoryQueryDto productProposalHistoryQueryDto){
        return productProposalCalService.getHistoryProductAvgValue(productProposalHistoryQueryDto);
    }

    @ApiOperation("获取风险容忍度列表")
    @PostMapping("/getRiskLossTolerate")
    public Result getRiskLossTolerate(@RequestBody ProductCalRiskLossDto productCalRiskLossDto){
        return productProposalCalService.getRiskLossTolerate(productCalRiskLossDto);
    }

    @ApiOperation("计算一组加权irr并计算最终总加权irr")
    @PostMapping("/getGroupWeightingIrr")
    public Result getGroupWeightingIrr(@RequestBody List<ProductCalculateGroupDto> productCalculateGroupDtoList,@CurrentUser User user){
        return productProposalCalService.getGroupWeightingIrr(productCalculateGroupDtoList,user);
    }

    @ApiOperation("计算单量利润")
    @PostMapping("/getAmountProfit")
    public Result getAmountProfit(@RequestBody List<ProductCalculateRangeDto> productCalculateRangeDtoList){
        return productProposalCalService.getAmountProfit(productCalculateRangeDtoList);
    }


    @ApiOperation("生成IRR对应word文档")
    @GetMapping("/generateIrrWord/{id}")
    public void generateIrrWord(@PathVariable("id") String id, HttpServletResponse response){
        productProposalCalService.generateIrrWord(id,response);
    }

    @ApiOperation("IRR批量测算模板下载")
    @GetMapping("/downLoadIrrBatchCalTemplate")
    public void downLoadIrrBatchCalTemplate(@RequestParam(value = "sourceType") String sourceType, HttpServletResponse response){
        productProposalCalService.downLoadIrrBatchCalTemplate(sourceType,response);
    }

    @ApiOperation("IRR批量测算Excel上传解析")
    @PostMapping("/upLoadIrrBatchCalFile")
    public Result upLoadIrrBatchCalFile(@RequestParam("sourceType") String sourceType, @RequestPart("file") MultipartFile file){
        return productProposalCalService.upLoadIrrBatchCalFile(sourceType,file);
    }
}
