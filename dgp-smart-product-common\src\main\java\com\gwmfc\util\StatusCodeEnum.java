package com.gwmfc.util;

/**
 * <AUTHOR>
 * @Classname StatusCodeEnum
 * @Description TODO
 * @Date 2021/4/12 17:09
 */
public enum StatusCodeEnum {


    /**
     * 密码错误
     */
    PASSWORD_ERROR(704,"密码错误"),

    /**
     * 用户不存在
     */
    USER_NOT_EXIST(703,"用户不存在"),

    /**
     *  建表异常
     */
    TABLE_NOT_EXSIT(702,"表不存在"),
    /**
     *  禁止删除
     */
    CANT_DEL(205,"数据被引用不能删除"),


    REFERENCE_DATA(601,"数据被引用"),
    /**
     * 未授权 请联系管理员
     */

    NO_AUTH_FOUND(401,"未授权 请联系管理员"),

    /**
     * 服务定义尚未进行,请联系大数据部
     */
    DATA_SERVICE_NOT_DEFINE(602,"服务定义尚未进行,请联系大数据部"),

    /**
     * 成功
     */
    SUCCESS(200,"成功"),

    /**
     * 进行中
     */
    PENDING(300,"进行中"),

    /**
     * 重复数据
     */

    DUPLICATE_KEY(501,"重复数据"),

    /**
     * 未授权
     */
    UNAUTHORIZED(1002,"未授权"),

    /**
     * 成功
     */
    MATRIX_SUCCESS(1,"成功"),

    /**
     * token 创建失败
     */
    TOKEN_ERROR(1003,"token 创建失败"),

    /**
     * token 过期
     */
    TOKEN_EXPIR(1004,"token 过期"),
    /**
     * 未找到有效的appkey或appSercret
     */

    UNVALID_APPKEY(1005,"未找到有效的appkey或appSercret"),
    /**
     * 失败
     */

    FAIL(500,"失败");


    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 详细信息
     */
    private final String desc;

    StatusCodeEnum(Integer code,String desc){
        this.code=code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
