package com.gwmfc.util;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * @Classname JSchSSHUtil
 * @Description ssh execution utils
 * @Date 2022/9/19 11:30
 * <AUTHOR> jay
 */
@Slf4j
public class JSchSSHUtil {
    /**
     * execute linux commands
     *
     * @param host
     * @param port
     * @param username
     * @param password
     * @param commands
     */
    public static void ssh2Shell(String host, int port, String username, String password, String[] commands) {
        JSch jsch = new JSch();
        Session session = null;
        ChannelShell channelShell = null;
        try {
            session = jsch.getSession(username, host, port);
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");
            session.setConfig(sshConfig);
            session.setPassword(password);
            session.connect();
            channelShell = (ChannelShell) session.openChannel("shell");
            channelShell.setPty(true);
            channelShell.connect();
            try (InputStream in = channelShell.getInputStream();
                 OutputStream out = channelShell.getOutputStream();
                 PrintWriter pw = new PrintWriter(out);) {
                for (String cmd : commands) {
                    pw.println(cmd);
                }
                pw.println("exit");
                pw.flush();

                //日志返回输出
                byte[] buffer = new byte[1024];
                for (; ; ) {
                    while (in.available() > 0) {
                        int i = in.read(buffer, 0, 1024);
                        if (i < 0) {
                            break;
                        }
                    }
                    if (channelShell.isClosed()) {
                        log.info("SSH2 Exit: " + channelShell.getExitStatus());
                        break;
                    }
                    TimeUnit.SECONDS.sleep(2);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            if (channelShell != null) {
                channelShell.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }
    }
}
