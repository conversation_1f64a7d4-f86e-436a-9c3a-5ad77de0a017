package com.gwmfc.controller;

import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.bo.RecordBatchDeleteBo;
import com.gwmfc.domain.User;
import com.gwmfc.service.CpcaautoService;
import com.gwmfc.util.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023年08月15日 14:02
 */
@Api(tags = "乘联会数据获取")
@RestController
@RequestMapping("/cpcaauto")
public class CpcaautoController {
    @Resource
    private CpcaautoService cpcaautoService;

    /**
     * 爬取月度销量排名
     *
     * @return
     */
    @ApiOperation("爬取月度销量排名")
    @GetMapping("/monthlySalesRanking")
    public Result monthlySalesRanking(@RequestParam String year, @RequestParam String month, @CurrentUser User user) {
        String url = "http://cpcaauto.com/news.php?types=csjd&anid=129&nid=27";
        cpcaautoService.monthlySalesRanking(url, year, month, 2, user.getUserName());
        return Result.ok();
    }

    /**
     * 爬取月度销量排名
     *
     * @return
     */
    @ApiOperation("爬取月度销量排名")
    @GetMapping("/catchPagePictureByUrl")
    public Result monthlySalesRanking(@RequestParam String url, @RequestParam String year, @RequestParam String month, @CurrentUser User user) {
        cpcaautoService.monthlySalesRanking(url, year, month, 2, user.getUserName());
        return Result.ok();
    }

    /**
     * 批次删除
     *
     * @return
     */
    @ApiOperation("批次删除")
    @GetMapping("/deleteByCreateTime")
    public Result deleteByCreateTime(@RequestParam String batch) {
        if (cpcaautoService.deleteByCreateTime(batch) > 0) {
            return Result.ok();
        }
        return Result.error("删除记录失败！");
    }

}
