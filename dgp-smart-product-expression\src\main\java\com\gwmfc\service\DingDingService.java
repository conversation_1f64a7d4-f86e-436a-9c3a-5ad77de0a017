package com.gwmfc.service;

import com.google.gson.JsonObject;
import com.gwmfc.api.DataApplyFeignApi;
import com.gwmfc.bo.DingNotification;
import com.gwmfc.bo.DingReponseBo;
import com.gwmfc.dto.DepartmentUserDto;
import com.gwmfc.dto.UserAndDepartmentDto;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.MustacheGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gwmfc.constant.DingDingConstant.*;

/**
 * <AUTHOR> jay
 * @Classname DingDingService
 * @Description DingDingService描述
 * @Date 2021/06/28
 */
@Service
@RefreshScope
@Slf4j
public class DingDingService {
    @Value("${dingding.captial.appKey}")
    private String captialAppKey;
    @Value("${dingding.captial.appSecret}")
    private String captialAppSecret;

    @Value("${dingding.smart.appKey}")
    private String smartAppKey;
    @Value("${dingding.smart.appSecret}")
    private String smartAppSecret;

    @Value("${dingding.nobee_approval_url}")
    private String nobeeApprovalUrl;
    @Value("${dingding.bee_approval_url}")
    private String beeApprovalUrl;
    @Value("${dingding.smart_department_id}")
    private String smartDepartmentId;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private DataApplyFeignApi dataApplyFeignApi;

    private static final String ERROR_CODE = "errcode";
    private static final String USER_ID = "userid";

    /**
     * acquire access token
     *
     * @return
     */
    public String getAccessToken(String key,String secret) {
        Map<String, String> variables = new HashMap<>(2);
        variables.put("appkey", key);
        variables.put("appsecret", secret);
        String json = restTemplate.getForObject("https://oapi.dingtalk.com/gettoken?appkey={appkey}&appsecret={appsecret}", String.class, variables);
        return GsonUtil.gsonToBean(json, JsonObject.class).get(ACCESS_TOKEN).getAsString();
    }

    /**
     * acquire access token
     *
     * @return
     */
    public String acquireCaptialAccessToken() {
        return getAccessToken(captialAppKey, captialAppSecret);
    }

    /**
     * acquire access token
     *
     * @return
     */
    public String acquireSmartAccessToken() {
        return getAccessToken(smartAppKey, smartAppSecret);
    }

    /**
     * push notification to users
     *
     * @param dingNotification
     * @return
     */
    public Long pushNotification(DingNotification dingNotification, String agentId) {
        Map<String, String> variables = new HashMap<>(1);
        variables.put(ACCESS_TOKEN, dingNotification.getAccessToken());
        String template = CAPITAL_NOTIFICATION_TEMPLATE;
        dingNotification.setAgentId(agentId);
        dingNotification.setStatusBg(NOTIFICATION_COLOR);
        log.info("pushNotification dingNotification: {}",dingNotification);
        HttpEntity<String> objectHttpEntity = new HttpEntity<>(MustacheGenerator.generateTemplate(template, dingNotification), getHeader());
        ResponseEntity<String> response = restTemplate.postForEntity("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token={access_token}", objectHttpEntity, String.class, variables);
        DingReponseBo dingReponseBo = GsonUtil.gsonToBean(response.getBody(), DingReponseBo.class);
        log.info("pushNotification result: {}",response.getBody());
        return dingReponseBo.getTask_id();
    }

    private HttpHeaders getHeader() {
        HttpHeaders httpHeaders = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json;charset=UTF-8");
        httpHeaders.setContentType(type);
        return httpHeaders;
    }

    /**
     * get approval url for DingDing notification
     *
     * @param id
     * @param source
     * @return
     */
    public String getCaptialApprovalUrl(Long id, Integer source) {
        Long timeMillis = System.currentTimeMillis();
        String url = "";
        if (BEE_SOURCE.equals(source)) {
            url = beeApprovalUrl;
        } else {
            url = nobeeApprovalUrl;
        }
        log.info("getApprovalUrl id:{}, originId:{}, source:{}", id, source);
        return url + "?id=" + id +  "&timeMillis=" + timeMillis;

    }

    /**
     * 获取用户及部门相关信息
     * @param userId 钉钉号
     * @return UserAndDepartmentDto
     */
    public UserAndDepartmentDto getUserAndDepartmentInfoForSmart(String userId) {
        return dataApplyFeignApi.getUserAndDepartmentInfoForSmart(userId, smartDepartmentId);
    }

    /**
     * 获取部门下用户列表
     * @param departmentId 部门id
     * @return List
     */
    public List<DepartmentUserDto> getUsersByDepartmentId(String departmentId) {
        return dataApplyFeignApi.getUsersByDepartmentId(null, departmentId);
    }
}
