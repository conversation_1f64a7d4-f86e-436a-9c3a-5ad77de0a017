package com.gwmfc.controller;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gwmfc.annotation.CurrentUser;
import com.gwmfc.bo.ProductProfitDetailBo;
import com.gwmfc.bo.UploadFileBo;
import com.gwmfc.domain.User;
import com.gwmfc.dto.*;
import com.gwmfc.entity.AdjustCalResultHistoryEntity;
import com.gwmfc.entity.ProductProposalEntity;
import com.gwmfc.entity.ProductProposalTemplateEntity;
import com.gwmfc.service.ProductProposalService;
import com.gwmfc.service.ProductProposalTemplateService;
import com.gwmfc.service.approve.ProductProposalApprovalStrategyContext;
import com.gwmfc.util.BeanListCopyUtil;
import com.gwmfc.util.GsonUtil;
import com.gwmfc.util.PageForm;
import com.gwmfc.util.Result;
import com.gwmfc.vo.GlobalFormBusinessVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gwmfc.constant.ProductProposalEnum.PRODUCT_PROPOSAL_SUBMIT;
import static com.gwmfc.util.StatusCodeEnum.FAIL;
import static com.gwmfc.util.StatusCodeEnum.SUCCESS;

/**
 * 产品要素提案
 *
 * <AUTHOR>
 * @date 2023年10月12日 13:19
 */
@Api(tags = "产品要素提案")
@RestController
@RequestMapping("/product/proposal")
public class ProductProposalController {
    @Resource
    private ProductProposalService productProposalService;
    @Resource
    private ProductProposalTemplateService productProposalTemplateService;
    @Resource
    private ProductProposalApprovalStrategyContext productProposalApprovalStrategyContext;

    /**
     * 新增
     *
     * @param productProposalDto
     * @return
     */
    @ApiOperation("产品要素新增")
    @PostMapping(value = "/add", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result add(@RequestPart("productProposalDto") ProductProposalDto productProposalDto, @RequestPart("dingDingNotifyDto") DingDingNotifyDto dingDingNotifyDto, @RequestPart(name = "addFileList", required = false) List<MultipartFile> addFileList, @ApiIgnore @CurrentUser User user) {
        productProposalService.save(productProposalDto, addFileList, user);
        ProductProposalApprovalStatusDto productProposalApprovalStatusDto = new ProductProposalApprovalStatusDto();
        productProposalApprovalStatusDto.setStatus(2);
        ProductProposalApprovalDto productProposalApprovalDto = new ProductProposalApprovalDto();
        productProposalApprovalDto.setProductProposalApprovalStatusDto(productProposalApprovalStatusDto);
        productProposalApprovalDto.setProductProposalDto(productProposalDto);
        productProposalApprovalDto.setDingDingNotifyDto(dingDingNotifyDto);
        productProposalApprovalStrategyContext.dingdingNotify(productProposalApprovalDto, PRODUCT_PROPOSAL_SUBMIT, user);
        return Result.ok();
    }

    /**
     * 修改
     *
     * @return
     */
    @ApiOperation("产品要素修改")
    @PostMapping(value = "/update", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result update(@RequestPart("productProposalDto") ProductProposalDto productProposalDto, @RequestPart("dingDingNotifyDto") DingDingNotifyDto dingDingNotifyDto, @RequestPart(name = "addFileList", required = false) List<MultipartFile> addFileList, @ApiIgnore @CurrentUser User user) {
        productProposalService.update(productProposalDto, addFileList, user);
        ProductProposalApprovalStatusDto productProposalApprovalStatusDto = new ProductProposalApprovalStatusDto();
        ProductProposalApprovalDto productProposalApprovalDto = new ProductProposalApprovalDto();
        productProposalApprovalStatusDto.setStatus(3);
        productProposalApprovalDto.setProductProposalApprovalStatusDto(productProposalApprovalStatusDto);
        productProposalApprovalDto.setProductProposalDto(productProposalDto);
        productProposalApprovalDto.setDingDingNotifyDto(dingDingNotifyDto);
        productProposalApprovalStrategyContext.dingdingNotify(productProposalApprovalDto, PRODUCT_PROPOSAL_SUBMIT, user);
        return Result.ok();
    }

    /**
     * 删除
     *
     * @return
     */
    @ApiOperation("单条删除")
    @GetMapping("/delete")
    public Result delete(@RequestParam Long id) {
        productProposalService.delete(id);
        return Result.ok();
    }

    /**
     * 撤销
     *
     * @return
     */
    @ApiOperation("撤销 主动回滚")
    @GetMapping("/revocation")
    public Result revocation(@RequestParam Long id, @ApiIgnore @CurrentUser User user) {
        productProposalService.revocation(id, user);
        return Result.ok();
    }

    /**
     * 详情
     *
     * @param
     * @return
     */
    @ApiOperation(value = "详情", produces = "application/json")
    @GetMapping("/detail")
    public Result<List<String>> detail(@RequestParam Long id) {
        Result result = new Result<>();
        ProductProposalDto productProposalDto = productProposalService.detail(id);
        result.setData(productProposalDto);
        result.setCode(SUCCESS.getCode());
        result.setMessage(SUCCESS.getDesc());
        return result;
    }

    /**
     * 列表查询
     *
     * @param productProposalDtoPageForm
     * @return
     */
    @ApiOperation("审批列表查询")
    @PostMapping("/selectByPage")
    public Result<List<ProductProposalDto>> selectByPage(@RequestBody @Valid PageForm<ProductProposalDto> productProposalDtoPageForm) {
        Result<List<ProductProposalDto>> result = new Result<>();
        IPage<ProductProposalEntity> productProposalEntityIPage = productProposalService.queryPage(productProposalDtoPageForm);
        List<ProductProposalDto> productDataMartDtoList = BeanListCopyUtil.copyListProperties(productProposalEntityIPage.getRecords(), ProductProposalDto::new);
        result.setData(productDataMartDtoList);
        result.setTotal(productProposalEntityIPage.getTotal());
        result.setCode(SUCCESS.getCode());
        return result;
    }

    /**
     * 列表查询
     *
     * @param productProposalDtoPageForm
     * @return
     */
    @ApiOperation("列表查询")
    @PostMapping("/queryProductPage")
    public Result<List<ProductProposalDto>> queryProductPage(@RequestBody @Valid PageForm<ProductProposalDto> productProposalDtoPageForm) {
        Result<List<ProductProposalDto>> result = new Result<>();
        IPage<ProductProposalEntity> productProposalEntityIPage = productProposalService.queryProductPage(productProposalDtoPageForm);
        if (productProposalEntityIPage.getRecords() != null && !productProposalEntityIPage.getRecords().isEmpty()) {
            List<ProductProposalDto> productDataMartDtoList = new ArrayList<>(productProposalEntityIPage.getRecords().size());
            productProposalEntityIPage.getRecords().forEach(productProposalEntity -> {
                ProductProposalDto productProposalDto = new ProductProposalDto();
                BeanUtils.copyProperties(productProposalEntity, productProposalDto);
                productProposalDto.setMarketAnalysisAnnex(StringUtils.isEmpty(productProposalEntity.getMarketAnalysisAnnex()) ? null : GsonUtil.jsonToList(productProposalEntity.getMarketAnalysisAnnex(), UploadFileBo.class));
                productProposalDto.setProductProposalAnnex(StringUtils.isEmpty(productProposalEntity.getProductProposalAnnex()) ? null : GsonUtil.jsonToList(productProposalEntity.getProductProposalAnnex(), UploadFileBo.class));
                productDataMartDtoList.add(productProposalDto);
            });
            result.setData(productDataMartDtoList);
        }
        result.setTotal(productProposalEntityIPage.getTotal());
        result.setCode(SUCCESS.getCode());
        return result;
    }

    /**
     * 预测算
     *
     * @param
     * @return
     */
    @ApiOperation("预测算")
    @PostMapping("/predictiveComputation")
    public Result<GlobalFormBusinessVo> predictiveComputation() {
        Result result = new Result<>();
        result.setCode(SUCCESS.getCode());
        return result;
    }

    /**
     * 保存模板
     *
     * @param productTemplateSaveDto
     * @return
     */
    @ApiOperation("保存模板")
    @PostMapping("/saveTemplate")
    public Result saveTemplate(@RequestBody @Valid ProductTemplateSaveDto productTemplateSaveDto, @ApiIgnore @CurrentUser User user) {
        Result result = new Result<>();
        if (productProposalTemplateService.selectTemplateCountByName(productTemplateSaveDto.getTemplateName()) == null) {
            result.setCode(FAIL.getCode());
            result.setMessage("模板名称重复！");
            return result;
        }
        productProposalTemplateService.saveTemplate(productTemplateSaveDto.getProductProposalTemplateDtoList(), productTemplateSaveDto.getTemplateName(), productTemplateSaveDto.getBusinessType(), user);
        result.setCode(SUCCESS.getCode());
        return result;
    }

    /**
     * 查询模板
     *
     * @param pageForm
     * @return
     */
    @ApiOperation("查询模板")
    @PostMapping("/selectTemplate")
    public Result<List<ProductTemplateSaveDto>> selectTemplate(@RequestBody @Valid PageForm<ProductProposalTemplateDto> pageForm) {
        Result<List<ProductTemplateSaveDto>> result = new Result<>();
        List<ProductProposalTemplateEntity> productElementTemplateEntityList = productProposalTemplateService.selectTemplate(pageForm);
        Map<String, List<ProductProposalTemplateDto>> map = productElementTemplateEntityList.stream().map(productProposalTemplateEntity -> {
                    ProductProposalTemplateDto productProposalTemplateDto = new ProductProposalTemplateDto();
                    BeanUtils.copyProperties(productProposalTemplateEntity, productProposalTemplateDto);
                    productProposalTemplateDto.setSelfDefinedParameter(GsonUtil.jsonToMap(productProposalTemplateEntity.getSelfDefinedParameter()));
                    if (StringUtils.isNotEmpty(productProposalTemplateEntity.getSalesForecast())) {
                        productProposalTemplateDto.setSalesForecast(GsonUtil.gsonToBean(productProposalTemplateEntity.getSalesForecast(), ProductProfitDetailBo.class));
                    }
                    return productProposalTemplateDto;
                })
                .collect(Collectors.groupingBy(ProductProposalTemplateDto::getName));
        List<ProductTemplateSaveDto> proposalTemplateShowDtoList = new ArrayList<>();
        map.forEach((k, v) -> {
            ProductTemplateSaveDto productProposalTemplateShowDto = new ProductTemplateSaveDto();
            productProposalTemplateShowDto.setTemplateName(k);
            productProposalTemplateShowDto.setBusinessType(v.get(0).getBusinessType());
            productProposalTemplateShowDto.setCreateTime(v.get(0).getCreateTime());
            productProposalTemplateShowDto.setCreateUser(v.get(0).getCreateUser());
            productProposalTemplateShowDto.setProductProposalTemplateDtoList(v);
            proposalTemplateShowDtoList.add(productProposalTemplateShowDto);
        });
        result.setCode(SUCCESS.getCode());
        result.setData(proposalTemplateShowDtoList);
        ProductProposalTemplateDto productProposalTemplateDto = pageForm.getParam();
        String templateName = null;
        Integer businessType = null;
        String createUser = null;
        LocalDateTime createTime = null;
        if (null != productProposalTemplateDto) {
            templateName = productProposalTemplateDto.getName();
            businessType = productProposalTemplateDto.getBusinessType();
            createUser = productProposalTemplateDto.getCreateUser();
            createTime = productProposalTemplateDto.getCreateTime();
        }
        result.setTotal(Long.valueOf(productProposalTemplateService.selectTemplateCount(templateName,businessType,createUser, createTime)));
        return result;
    }

    /**
     * 查询模板
     *
     * @param name
     * @return
     */
    @ApiOperation("查询模板")
    @GetMapping("/selectTemplateDetail")
    public Result<ProductTemplateSaveDto> selectTemplateDetail(@RequestParam @Valid String name) {
        Result<ProductTemplateSaveDto> result = new Result<>();
        List<ProductProposalTemplateEntity> productProposalTemplateEntityList = productProposalTemplateService.selectTemplateDetail(name);
        List<ProductProposalTemplateDto> productProposalTemplateDtoList = new ArrayList<>(productProposalTemplateEntityList.size());

        productProposalTemplateEntityList.forEach(productProposalTemplateEntity -> {
            ProductProposalTemplateDto productProposalTemplateDto = new ProductProposalTemplateDto();
            BeanUtils.copyProperties(productProposalTemplateEntity, productProposalTemplateDto);
            productProposalTemplateDto.setSelfDefinedParameter(GsonUtil.jsonToMap(productProposalTemplateEntity.getSelfDefinedParameter()));
            if (StringUtils.isNotEmpty(productProposalTemplateEntity.getSalesForecast())) {
                productProposalTemplateDto.setSalesForecast(GsonUtil.gsonToBean(productProposalTemplateEntity.getSalesForecast(), ProductProfitDetailBo.class));
            }
            if (StringUtils.isNotEmpty(productProposalTemplateEntity.getProductCalRepaymentMethodParamDto())) {
                productProposalTemplateDto.setProductCalRepaymentMethodParamDto(GsonUtil.gsonToBean(productProposalTemplateEntity.getProductCalRepaymentMethodParamDto(), ProductCalRepaymentMethodParamDto.class));
            }
            if(StringUtils.isNotEmpty(productProposalTemplateEntity.getProductCalUnionLoanParamDto())){
                productProposalTemplateDto.setProductCalUnionLoanParamDto(GsonUtil.gsonToBean(productProposalTemplateEntity.getProductCalUnionLoanParamDto(), ProductCalUnionLoanParamDto.class));
            }
            if(StringUtils.isNotEmpty(productProposalTemplateEntity.getProductCalEarlySquareParamSubDto())){
                productProposalTemplateDto.setProductCalEarlySquareParamSubDto(GsonUtil.gsonToBean(productProposalTemplateEntity.getProductCalEarlySquareParamSubDto(), ProductCalEarlySquareParamSubDto.class));
            }
            productProposalTemplateDtoList.add(productProposalTemplateDto);
        });
        ProductTemplateSaveDto productTemplateSaveDto = new ProductTemplateSaveDto();
        productTemplateSaveDto.setTemplateName(name);
        productTemplateSaveDto.setProductProposalTemplateDtoList(productProposalTemplateDtoList);
        result.setCode(SUCCESS.getCode());
        result.setData(productTemplateSaveDto);
        return result;
    }

    /**
     * 删除模板
     *
     * @param templateName
     * @return
     */
    @ApiOperation("删除模板")
    @GetMapping("/deleteTemplate")
    public Result<List<ProductTemplateSaveDto>> deleteTemplate(@RequestParam @Valid String templateName) {
        Result<List<ProductTemplateSaveDto>> result = new Result<>();
        if (productProposalTemplateService.deleteTemplate(templateName) > 0) {
            result.setCode(SUCCESS.getCode());
            result.setMessage(SUCCESS.getDesc());
            return result;
        }
        result.setCode(FAIL.getCode());
        result.setMessage(FAIL.getDesc());
        return result;
    }

    @ApiOperation("保存调整测算结果")
    @PostMapping("/adjustCalResult")
    public Result adjustCalResultHistory(@RequestBody AdjustCalResultHistoryDto adjustCalResultHistoryDto,  @ApiIgnore @CurrentUser User user) {
        if (productProposalTemplateService.adjustCalResultHistory(adjustCalResultHistoryDto, user) == 1) {
            return Result.ok();
        }
        return Result.error("保存调整历史失败！");
    }

    @ApiOperation("调整测算结果")
    @PostMapping("/selectAdjustCalResult")
    public Result selectAdjustCalResult(@RequestBody @Valid PageForm<AdjustCalResultHistoryDto> pageForm) {
        Result<List<AdjustCalResultHistoryDto>> result = new Result<>();
        IPage<AdjustCalResultHistoryEntity> adjustCalResultHistoryDtoPage = productProposalTemplateService.selectAdjustCalResult(pageForm);
        List<AdjustCalResultHistoryEntity> adjustCalResultHistoryEntityList = adjustCalResultHistoryDtoPage.getRecords();
        List<AdjustCalResultHistoryDto> adjustCalResultHistoryDtoList = new ArrayList<>();

        for (AdjustCalResultHistoryEntity adjustCalResultHistoryEntity : adjustCalResultHistoryEntityList) {
            AdjustCalResultHistoryDto adjustCalResultHistoryDto = new AdjustCalResultHistoryDto();
            BeanUtils.copyProperties(adjustCalResultHistoryEntity, adjustCalResultHistoryDto);

            if(adjustCalResultHistoryEntity.getProductCalUnionLoanParamDto() != null){
                adjustCalResultHistoryDto.setProductCalUnionLoanParamDto(GsonUtil.gsonToBean(adjustCalResultHistoryEntity.getProductCalUnionLoanParamDto(), ProductCalUnionLoanParamDto.class));
            }

            if(adjustCalResultHistoryEntity.getProductCalEarlySquareParamSubDto() != null){
                adjustCalResultHistoryDto.setProductCalEarlySquareParamSubDto(GsonUtil.gsonToBean(adjustCalResultHistoryEntity.getProductCalEarlySquareParamSubDto(), ProductCalEarlySquareParamSubDto.class));
            }
            adjustCalResultHistoryDtoList.add(adjustCalResultHistoryDto);
        }

        result.setData(adjustCalResultHistoryDtoList);
        result.setTotal(adjustCalResultHistoryDtoPage.getTotal());
        result.setCode(200);
        return result;
    }
}
