package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname TaxFeeParamDto
 * @Description 税费参数
 * @Date 2023/9/19 13:28
 */
@Data
@ApiModel(value = "税费参数")
public class ProductCalTaxFeeParamDto {

    @ApiModelProperty("进项税抵扣比例(%)")
    private Double inputTaxDeductionRatio;

    @ApiModelProperty("销项税比例(%)")
    private Double outputTaxRatio;

    @ApiModelProperty("教育税比例(%)")
    private Double educationTaxRatio;

    @ApiModelProperty("印花税比例(%)")
    private Double stampTaxRatio;

    @ApiModelProperty("增值税比例(%)")
    private Double valueAddedTaxRatio;

    @ApiModelProperty("其他费用比例(%)")
    private Double otherCostRatio;

    @ApiModelProperty("所得税费用比例")
    private Double incomeTaxFeeRatio;

    @ApiModelProperty("月均资产余额系数")
    private Double monthAverageAssetBalanceCoefficient;

    @ApiModelProperty("印花税")
    private Double stampTax;

    @ApiModelProperty("印花税费率")
    private Double stampTaxFeeRate;

    @ApiModelProperty("其他变动费率")
    private Double otherChangeFeeRate;

    @ApiModelProperty("所得税费率")
    private Double incomeTaxRate;

    @ApiModelProperty("月均资产余额")
    private Double monthAverageAssetBalance;

    @ApiModelProperty("利息支出费率")
    private Double interestExpendFeeRate;

    @ApiModelProperty("教育费附加费率")
    private Double educationFeeAppendFeeRate;

    @ApiModelProperty("不良费率")
    private Double badnessFeeRate;

    @ApiModelProperty("费率合计")
    private Double totalFeeRate;

    @ApiModelProperty("利息支出金额")
    private Double interestExpendMoney;

    @ApiModelProperty("教育费附加")
    private Double educationFeeAppend;

    @ApiModelProperty("服务费率")
    private Double serviceFeeRate;

    @ApiModelProperty("增值税费率")
    private Double valueAddedTaxFeeRate;

    @ApiModelProperty("固定支出费率")
    private Double fixedExpendFeeRate;

    @ApiModelProperty("利润率")
    private Double profitRate;

    @ApiModelProperty("服务费额")
    private Double serviceFeeMoney;

    @ApiModelProperty("增值税")
    private Double valueAddedTax;

    @ApiModelProperty("不良")
    private Double badness;

    @ApiModelProperty("费用合计")
    private Double totalCost;

    @ApiModelProperty("其他费用")
    private Double otherCost;

    @ApiModelProperty("净利润额")
    private Double netProfitMoney;

    @ApiModelProperty("其他变动费用")
    private Double otherChangeCost;

    @ApiModelProperty("所得税费用")
    private Double incomeTaxCost;
}
