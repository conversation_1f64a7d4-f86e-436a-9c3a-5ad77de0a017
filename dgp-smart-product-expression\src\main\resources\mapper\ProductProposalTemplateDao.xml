<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gwmfc.dao.ProductProposalTemplateDao">


    <select id="selectTemplateName" resultType="com.gwmfc.entity.ProductProposalTemplateEntity">
        select
        distinct name,create_time,create_user,business_type
        from
        smart_product_expression.product_proposal_template where 1=1
        <if test="name != '' and name != null">
           and  name like CONCAT('%', #{name}, '%')
        </if>
        <if test="businessType != null">
            and business_type = #{businessType}
        </if>
        <if test="createUser != '' and createUser != null">
            and create_user = #{createUser}
        </if>
        <if test="createTime != null">
            <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(#{createTime},'%Y-%m-%d')]]>
        </if>
        group by
        name,business_type,create_time,create_user
        order by
        create_time desc
    </select>

    <select id="selectTemplateCount" resultType="java.lang.Integer">
        select count(*) from (select distinct name from smart_product_expression.product_proposal_template where 1=1
        <if test="name != '' and name != null">
            and name like CONCAT('%', #{name}, '%')
        </if>
        <if test="businessType != null">
            and business_type = #{businessType}
        </if>
        <if test="createUser != '' and createUser != null">
            and create_user like CONCAT('%', #{createUser}, '%')
        </if>
        <if test="createTime != null">
            <![CDATA[ AND DATE_FORMAT(create_time, '%Y-%m-%d') = DATE_FORMAT(#{createTime},'%Y-%m-%d')]]>
        </if>
        group by name) a
    </select>
    <select id="selectTemplateDetail" resultType="com.gwmfc.entity.ProductProposalTemplateEntity">
        select * from smart_product_expression.product_proposal_template
        <if test="name != '' and name != null">
            where name = #{name}
        </if>
    </select>
    <select id="selectTemplateCountByName" resultType="java.lang.Integer">
        select count(*) from (select distinct name from smart_product_expression.product_proposal_template where 1=1
        <if test="name != '' and name != null">
            and name = #{name}
        </if>
        group by name) a
    </select>
</mapper>