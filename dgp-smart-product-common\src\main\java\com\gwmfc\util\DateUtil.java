package com.gwmfc.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> jay
 * @Classname DateUtils
 * @Description DateUtils描述
 * @Date 2021/07/12
 */
public class DateUtil {

    private static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * format date time
     *
     * @param dateTime
     * @return
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        return dateTime.format(dateTimeFormatter);
    }

    /**
     * acquire local date
     *
     * @return
     */
    public static String getCurrentDate() {
        return LocalDateTime.now().format(dateFormatter);
    }


    /**
     * acquire local date time
     *
     * @return
     */
    public static String getCurrentDateTime() {
        return LocalDateTime.now().format(dateTimeFormatter);
    }
}
