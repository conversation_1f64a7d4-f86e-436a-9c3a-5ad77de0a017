package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalEarlySquareHandChargeAndCommissionRebatePam
 * @Description TODO
 * @Date 2024/7/24 13:57
 */
@Data
@ApiModel(value = "提前结清手续费和扣返比例计算参数")
public class ProductCalEarlySquareHandChargeAndCommissionRebatePamDto {
    @ApiModelProperty("业务类型 0-本品  1-全品新车  2-二手车")
    private Integer businessType;

    @ApiModelProperty("业务类型 0-本品  1-全品新车  2-二手车")
    private String businessTypeStr;

    @ApiModelProperty("产品分类")
    private String productClassification;

    @ApiModelProperty("客户利率（%）")
    private Double customerInterestRate;

    @ApiModelProperty("实际利率（%）")
    private Double actualInterestRate;

    @ApiModelProperty("期限")
    private Integer timeLimit;

    @ApiModelProperty("融资类型")
    private String financingType;

    @ApiModelProperty("基础服务费比例")
    private Double dealerBasicCommissionRatio;

    @ApiModelProperty("阶梯奖金比例")
    private Double dealerLadderBonusRatio;

    @ApiModelProperty("促销奖金比例")
    private Double dealerSaleBonusRatio;

    @ApiModelProperty("代理商类型")
    private String agentType;

    @ApiModelProperty("赛道")
    private String track;

    @ApiModelProperty("是否总对总 1-是 0-否")
    private Integer isCorpToCorp;

    @ApiModelProperty("是否总对总 1-是 0-否")
    private String isCorpToCorpStr;
}
