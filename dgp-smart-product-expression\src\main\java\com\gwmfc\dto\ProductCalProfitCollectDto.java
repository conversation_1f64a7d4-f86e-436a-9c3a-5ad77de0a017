package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname ProductCalProfitCollectDto
 * @Description TODO
 * @Date 2023/12/13 9:26
 */
@Data
@ApiModel(value = "利润汇总dto")
public class ProductCalProfitCollectDto {
    @ApiModelProperty("投放月")
    private Integer putMonth;
    @ApiModelProperty("还款年")
    private String dueYear;
    @ApiModelProperty("利润")
    private Double profitMoney;
}
