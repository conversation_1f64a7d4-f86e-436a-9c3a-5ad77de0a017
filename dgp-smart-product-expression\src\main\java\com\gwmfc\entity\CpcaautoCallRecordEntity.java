package com.gwmfc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年08月15日 14:39
 */
@Data
@TableName("cpcaauto_call_record")
public class CpcaautoCallRecordEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String pageTitle;

    private String pageUrl;

    private String pictureUrl;

    private String pictureUrlList;

    private Integer callbackStatus;

    private String callTime;

    private LocalDateTime callbackTime;

    private Integer triggerMethod;

    private Integer recallNum;

    private String year;

    private String month;

    private String createUser;

    private String source;

}
