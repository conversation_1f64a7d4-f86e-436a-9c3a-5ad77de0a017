package com.gwmfc.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR> jay
 * @Classname DepartmentNode
 * @Description DepartmentNode
 * @Date 2021/07/05
 */
@Data
public class DepartmentNode implements Serializable {
    private Map<String, DepartmentNode> departmentNodeMap;
    private String id;
    private String name;
    private Boolean isStaff;

    public DepartmentNode(String id, String name, Boolean isStaff) {
        super();
        this.id = id;
        this.name = name;
        this.isStaff = isStaff;
    }
}
