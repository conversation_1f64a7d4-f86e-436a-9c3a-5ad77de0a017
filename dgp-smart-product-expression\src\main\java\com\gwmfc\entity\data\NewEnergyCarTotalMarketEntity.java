package com.gwmfc.entity.data;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gwmfc.annotation.TableFieldEnumMapping;
import com.gwmfc.annotation.TableFieldMapping;
import lombok.Data;

/**
 * <AUTHOR>
 * @Classname NewEnergyCarTotalMarketEntity
 * @Description TODO
 * @Date 2025/1/18 14:20
 */
@Data
@TableName("new_energy_car_total_market")
@ExcelIgnoreUnannotated
public class NewEnergyCarTotalMarketEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("数据日期")@TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "data_date", comment = "数据日期", queryItem = true)
    private String dataDate;

    @ExcelProperty("更新月份")@TableFieldEnumMapping(dateEnum = true)
    @TableFieldMapping(value = "update_date", comment = "更新月份")
    private String updateDate;

    @ExcelProperty("细分市场")
    @TableFieldMapping(value = "segment_market", comment = "细分市场")
    private String segmentMarket;

    @ExcelProperty("产量")
    @TableFieldMapping(value = "output_volume", comment = "产量")
    private String outputVolume;

    @ExcelProperty("批发量")
    @TableFieldMapping(value = "wholesale_volume", comment = "批发量")
    private String wholesaleVolume;

    @ExcelProperty("零售量")
    @TableFieldMapping(value = "retail_volume", comment = "零售量")
    private String retailVolume;

    @ExcelProperty("出口量")
    @TableFieldMapping(value = "export_volume", comment = "出口量")
    private String exportVolume;

    @ExcelProperty("图片地址")
    @TableFieldMapping(value = "picture_url", comment = "图片地址")
    private String pictureUrl;
}
