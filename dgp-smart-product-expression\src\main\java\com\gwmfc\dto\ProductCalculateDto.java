package com.gwmfc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Classname ProductCalculateDto
 * @Description 产品测算dto
 * @Date 2023/9/19 14:19
 */
@Data
@ApiModel(value = "产品测算dto")
public class ProductCalculateDto {
    @ApiModelProperty("基本信息")
    private ProductCalBasicInfoDto productCalBasicInfoDto;

    @ApiModelProperty("结果信息")
    private ProductCalResultInfoDto productCalResultInfoDto;

    @ApiModelProperty("基本参数")
    private ProductCalBasicParamDto productCalBasicParamDto;

    @ApiModelProperty("税费参数")
    private ProductCalTaxFeeParamDto productCalTaxFeeParamDto;

    @ApiModelProperty("还款方式参数")
    private ProductCalRepaymentMethodParamDto productCalRepaymentMethodParamDto;

    @ApiModelProperty("提前结清参数")
    private ProductCalEarlySquareParamDto productCalEarlySquareParamDto;

    @ApiModelProperty("提前结清参数附加")
    private ProductCalEarlySquareParamSubDto productCalEarlySquareParamSubDto;

    @ApiModelProperty("现金流量表")
    private List<ProductCalCashStreamDto> productCalCashStreamDtoList;

    @ApiModelProperty("提前结清测算数据")
    private ProductCalculateEarlySquareDto productCalculateEarlySquareDto;

    @ApiModelProperty("联合贷参数")
    private ProductCalUnionLoanParamDto productCalUnionLoanParamDto;

    @ApiModelProperty("利润测算数据")
    private ProductCalProfitDetailDto productCalProfitDetailDto;

}
